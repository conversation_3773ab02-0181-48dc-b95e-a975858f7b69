import React from 'react';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { MatchingItemType } from '../../../../interfaces/quizs/questionBase';

interface ClickableItemProps {
  id: string;
  item: MatchingItemType;
  isSelected: boolean;
  isPaired: boolean;
  pairColor?: string;
  isMatched?: boolean;
  isIncorrect?: boolean;
  isLeftItem?: boolean;
  onClick: (id: string) => void;
}

const ClickableItem: React.FC<ClickableItemProps> = ({
  id,
  item,
  isSelected,
  isPaired,
  pairColor,
  isMatched,
  isIncorrect,
  isLeftItem = false,
  onClick,
}) => {
  // Determine background and border colors
  const style: React.CSSProperties = {
    backgroundColor: isPaired
      ? pairColor + '15' // Lighter background for paired items
      : isSelected
      ? '#e6f7ff'
      : 'white',
    borderColor: isPaired ? pairColor : isSelected ? '#1890ff' : undefined,
    borderWidth: isPaired || isSelected ? '2px' : '1px',
    position: 'relative',
  };

  // Determine connection point style based on whether it's on the left or right
  const connectionPointStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    [isLeftItem ? 'right' : 'left']: '-5px',
    width: '10px',
    height: '10px',
    borderRadius: '50%',
    backgroundColor: isPaired ? pairColor : 'transparent',
    border: isPaired ? 'none' : '2px solid #d9d9d9',
    transform: 'translateY(-50%)',
    transition: 'all 0.3s ease',
    zIndex: 3,
  };

  const handleClick = () => {
    onClick(id);
  };

  return (
    <div
      className={`matching-item 
        ${isSelected ? 'selected' : ''} 
        ${isPaired ? 'paired' : ''} 
        ${isMatched ? 'matched' : ''} 
        ${isIncorrect ? 'incorrect' : ''}
        ${isMatched ? 'matching-success-pulse' : ''}
      `}
      style={style}
      onClick={handleClick}
      data-item-id={id} // Add data attribute for connection line targeting
    >
      {/* Connection point indicator */}
      <div style={connectionPointStyle} />

      {item.type === 'text' ? (
        <div className="matching-text">{item.content}</div>
      ) : (
        <div className="matching-image">
          {typeof item.content === 'string' ? (
            <img src={item.content} alt="Matching item" loading="lazy" />
          ) : (
            item.content
          )}
        </div>
      )}

      {isMatched && (
        <CheckOutlined className="match-status-icon match-correct" />
      )}
      {isIncorrect && (
        <CloseOutlined className="match-status-icon match-incorrect" />
      )}
    </div>
  );
};

export default ClickableItem;

import React, { useContext, useEffect, useState } from 'react';
import {
  Card,
  Radio,
  Button,
  Space,
  Typography,
  Input,
  Form,
  PopconfirmProps,
  Collapse,
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  PlusOutlined,
  DeleteFilled,
  UndoOutlined,
  RedoOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  QuizAnswer,
} from '../../../interfaces/quizs/questionBase';
import './QuizAnimations.css';
import { QuizComponentProps } from '../../../interfaces/quizs/quizComponent.interface';
import practiceLocalization, { quizLocalization } from '../localization';
import { Image } from 'antd';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import DOMPurify from 'dompurify';
import { DeleteIcon } from '../../icons/IconIndex';

const { Title, Text } = Typography;
const { TextArea } = Input;

// Define modules for the rich text editor
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image'],
    ['clean'],
  ],
};

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
];

const QuizComponent = ({
  question,
  onComplete,
  showFeedback = true,
  hideSaveButton = false,
  configMode = false,
}: QuizComponentProps) => {
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [focus, setFocus] = useState<string | null>(null);
  const { handleDeleteQuestion: externalHandleDeleteQuestion } = useContext(
    PracticeEngineContext
  );
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [imageModal, setImageModal] = useState<{
    visible: boolean;
    imageUrl: string;
  }>({
    visible: false,
    imageUrl: '',
  });
  const { handleUpdateQuestion } = useUpdateQuestion();

  const [questionTextProps, setQuestionTextProps] = useState<string>(
    () => question.question
  );
  const [title, setTitle] = useState(question.title);

  const [form] = Form.useForm();

  // Handle answer selection
  const handleAnswerSelect = (answerId: string) => {
    if (!isSubmitted) {
      setSelectedAnswer(answerId);
      // Store answer without auto-submitting
      const selectedOption = question.answers?.find(
        (answer) => answer.id === answerId
      );
      if (onComplete) {
        onComplete(question.id, selectedOption);
      }
    }
  };

  // Reset the question
  const handleReset = () => {
    setSelectedAnswer(question.userSelect?.id ?? null);
    setIsSubmitted(false);
    setIsCorrect(false);
    if (typeof question.question === 'string') {
      setQuestionTextProps(question.question ?? '');
    } else {
      setQuestionTextProps(question.question ?? '');
    }
  };

  // Get class name for answer option
  const getAnswerClassName = (answer: QuizAnswer) => {
    if (!isSubmitted) return '';
    if (answer.id === selectedAnswer) {
      return answer.isCorrect ? 'correct-answer' : 'incorrect-answer';
    }
    if (answer.isCorrect) {
      return 'correct-answer-highlight';
    }
    return '';
  };

  // Config mode functions
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
    handleUpdateQuestion({ ...question, title: title });
  };

  const handleQuestionTextChange = (updates: string) => {
    if (updates) {
      const newQuestion = {
        ...question,
        question: updates,
        description: updates,
      };
      handleUpdateQuestion(newQuestion);
      setQuestionTextProps(updates);
    }
  };

  const handleExplanationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newQuestion = { ...question, explanation: e.target.value };
    handleUpdateQuestion(newQuestion);
  };

  const handleOptionTextChange = (id: string, newText: string) => {
    const newAnswers =
      question.answers?.map((answer) =>
        answer.id === id ? { ...answer, text: newText } : answer
      ) ?? [];
    const newQuestion = { ...question, answers: newAnswers };
    handleUpdateQuestion(newQuestion);
  };

  const handleCorrectAnswerChange = (id: string) => {
    const newAnswers =
      question.answers?.map((answer) => ({
        ...answer,
        isCorrect: answer.id === id,
      })) ?? [];
    const newQuestion = { ...question, answers: newAnswers };
    handleUpdateQuestion(newQuestion);
  };

  const addNewOption = () => {
    const newId = `${question.id}-${String.fromCharCode(
      65 + (question.answers?.length ?? 0)
    )}`;
    const newAnswers = [
      ...(question.answers ?? []),
      {
        id: newId,
        text: 'New option',
        isCorrect: false,
        order: question.answers?.length ?? 0,
      },
    ];
    const newQuestion = { ...question, answers: newAnswers };
    handleUpdateQuestion(newQuestion);
  };

  const deleteOption = (id: string) => {
    const newAnswers =
      question.answers?.filter((answer) => answer.id !== id) ?? [];
    const newQuestion = { ...question, answers: newAnswers };
    handleUpdateQuestion(newQuestion);
  };

  // Handle save configuration
  const handleSaveConfig = () => {
    if (handleUpdateQuestion) {
      handleUpdateQuestion(question);
    }
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.id);
  };

  useEffect(() => {
    document.addEventListener('fullscreenchange', () => {
      if (document.fullscreenElement) {
        setIsFullScreen(true);
      } else {
        setIsFullScreen(false);
      }
    });

    return () => {
      document.removeEventListener('fullscreenchange', () => {
        if (document.fullscreenElement) {
          setIsFullScreen(true);
        } else {
          setIsFullScreen(false);
        }
      });
    };
  }, []);

  useEffect(() => {
    handleReset();
  }, [question.id]);

  const collapseItems = [
    {
      key: '1',
      label: practiceLocalization['Advanced Options'],
      children: (
        <>
          <Form.Item required>
            <Input
              placeholder={quizLocalization.questionType.label}
              value={quizLocalization.questionType.multipleChoice}
              disabled
            />
          </Form.Item>
          <Form.Item label={quizLocalization.form.explanation.label}>
            <TextArea
              defaultValue={question.explanation}
              onChange={handleExplanationChange}
              placeholder={quizLocalization.form.explanation.placeholder}
              autoSize={{ minRows: 2 }}
            />
          </Form.Item>
        </>
      ),
    },
  ];

  const renderConfigMode = () => {
    return (
      <div className="quiz-config-mode">
        <Form form={form} className="form-config" layout="vertical">
          <Form.Item required>
            <Input
              variant="underlined"
              autoFocus={focus === 'title'}
              value={title}
              onChange={handleTitleChange}
              placeholder={quizLocalization.form.questionTitle.placeholder}
            />
          </Form.Item>

          <Form.Item required>
            <ReactQuill
              theme="snow"
              // autoFocus={focus === 'question'}
              value={questionTextProps}
              onChange={handleQuestionTextChange}
              placeholder={quizLocalization.form.questionText.placeholder}
              modules={modules}
              formats={formats}
            />
          </Form.Item>

          {/* <Form.Item>
            <QuestionImagesUpload
              images={question.images || []}
              onImagesChange={handleImagesChange}
            />
          </Form.Item> */}

          <Form.Item className="taildwind-m-0" required>
            {question.answers?.map((answer, index) => (
              <div
                key={answer.id}
                className="option-item"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '8px',
                  gap: '8px',
                }}
              >
                <Radio
                  checked={answer.isCorrect}
                  onChange={() => handleCorrectAnswerChange(answer.id)}
                />
                <Input
                  variant="underlined"
                  autoFocus={focus === 'options'}
                  defaultValue={answer.text}
                  onChange={(e) =>
                    handleOptionTextChange(answer.id, e.target.value)
                  }
                  placeholder={quizLocalization.form.options.optionPlaceholder(
                    String.fromCharCode(65 + index)
                  )}
                  style={{ flex: 1 }}
                />
                <Button
                  danger
                  icon={<DeleteIcon />}
                  onClick={() => deleteOption(answer.id)}
                  disabled={(question.answers?.length ?? 0) <= 2}
                />
              </div>
            ))}
            <Button
              type="dashed"
              onClick={addNewOption}
              icon={<PlusOutlined />}
              style={{ width: '100%', marginTop: '8px' }}
            >
              {quizLocalization.form.options.addOption}
            </Button>
          </Form.Item>

          <Form.Item>
            <Collapse defaultActiveKey={[]} ghost items={collapseItems} />
          </Form.Item>

          <Space>
            {!hideSaveButton && (
              <Button type="primary" onClick={handleSaveConfig}>
                {quizLocalization.buttons.saveChanges}
              </Button>
            )}
            <PopconfirmAntdCustom
              title={quizLocalization.buttons.deleteQuestion.confirmTitle}
              onConfirm={deleteConfirm}
              onCancel={() => {}}
              okText={quizLocalization.buttons.deleteQuestion.yes}
              cancelText={quizLocalization.buttons.deleteQuestion.no}
            >
              <Button danger icon={<DeleteFilled />}>
                {quizLocalization.buttons.deleteQuestion.button}
              </Button>
            </PopconfirmAntdCustom>
          </Space>
        </Form>
      </div>
    );
  };

  return (
    <Card className="quiz-component">
      {configMode ? (
        renderConfigMode()
      ) : (
        <div className="quiz-content">
          <div
            className="quiz-header item-config"
            style={{ marginBottom: '24px' }}
          >
            {configMode && (
              <div
                style={{
                  backgroundColor: '#f0f2f5',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  marginBottom: '16px',
                  display: 'inline-block',
                }}
              >
                <Text type="secondary">
                  {quizLocalization.questionType.label}
                </Text>
                <Title level={5} style={{ margin: '4px 0 0 0' }}>
                  {quizLocalization.questionType.multipleChoice}
                </Title>
              </div>
            )}

            <div className="quiz-question item-config">
              <Text
                style={{
                  marginBottom: '16px',
                  fontWeight: 600,
                  color: '#1a1a1a',
                }}
                onClick={() => setFocus('title')}
              >
                {question.title}
              </Text>
              <div
                style={{
                  color: '#1a1a1a',
                }}
                onClick={() => setFocus('question')}
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(question.question),
                }}
              />

              {question.images && question.images.length > 0 && (
                <div className="question-images-display">
                  <Space wrap>
                    {question.images.map(
                      (img: { url: string; alt?: string }, index: number) => (
                        <Image
                          key={index}
                          src={
                            img.url.startsWith('http')
                              ? img.url
                              : (import.meta.env.PROD
                                  ? '/'
                                  : 'https://localhost:44348/') + img.url
                          }
                          alt={img.alt || 'Question image'}
                          style={{
                            maxWidth: '300px',
                            maxHeight: '300px',
                            objectFit: 'contain',
                            margin: '8px',
                          }}
                          preview={
                            !isFullScreen
                              ? {
                                  mask: null,
                                  maskClassName: 'image-preview-mask',
                                }
                              : false
                          }
                          onClick={() => {
                            if (isFullScreen)
                              setImageModal({
                                visible: true,
                                imageUrl: img.url,
                              });
                          }}
                        />
                      )
                    )}
                  </Space>
                </div>
              )}
            </div>
          </div>

          {configMode && (
            <div className="quiz-toolbar" style={{ marginBottom: '16px' }}>
              <Space>
                <Button icon={<UndoOutlined />} />
                <Button icon={<RedoOutlined />} />
                <div
                  style={{
                    width: '1px',
                    background: '#d9d9d9',
                    height: '24px',
                  }}
                />
                <Button icon={<BoldOutlined />} />
                <Button icon={<ItalicOutlined />} />
                <Button icon={<UnderlineOutlined />} />
                <div
                  style={{
                    width: '1px',
                    background: '#d9d9d9',
                    height: '24px',
                  }}
                />
                <Button icon={<OrderedListOutlined />} />
                <Button icon={<UnorderedListOutlined />} />
              </Space>
            </div>
          )}

          <div
            className="quiz-options item-config"
            onClick={() => setFocus('options')}
          >
            <Radio.Group
              value={selectedAnswer}
              onChange={(e) => handleAnswerSelect(e.target.value)}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {question.answers?.map((answer, _) => (
                  <Radio
                    key={answer.id}
                    value={answer.id}
                    className={`quiz-option ${getAnswerClassName(answer)}`}
                    style={{
                      width: '100%',
                      marginBottom: '12px',
                      padding: '12px 16px',
                      borderRadius: '8px',
                      border: '1px solid #d9d9d9',
                      transition: 'all 0.3s',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span
                        style={{
                          fontSize: '16px',
                        }}
                        dangerouslySetInnerHTML={{
                          __html: DOMPurify.sanitize(answer.text),
                        }}
                      />
                      {isSubmitted && (
                        <span>
                          {answer.isCorrect ? (
                            <CheckOutlined style={{ color: '#52c41a' }} />
                          ) : (
                            answer.id === selectedAnswer && (
                              <CloseOutlined style={{ color: '#ff4d4f' }} />
                            )
                          )}
                        </span>
                      )}
                    </div>
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </div>

          {isSubmitted && question.explanation && showFeedback && (
            <div
              className={`quiz-explanation ${
                isCorrect ? 'correct' : 'incorrect'
              }`}
              style={{
                marginTop: '24px',
                padding: '16px',
                borderRadius: '8px',
                backgroundColor: isCorrect ? '#f6ffed' : '#fff2f0',
                border: `1px solid ${isCorrect ? '#b7eb8f' : '#ffccc7'}`,
              }}
            >
              <Text strong>
                {isCorrect
                  ? quizLocalization.feedback.explanation
                  : quizLocalization.feedback.correctAnswer}{' '}
              </Text>
              <Text>{question.explanation}</Text>
            </div>
          )}
        </div>
      )}
      <ModalAntdCustom
        className="image-modal"
        open={imageModal.visible}
        footer={null}
        onCancel={() => setImageModal({ visible: false, imageUrl: '' })}
      >
        <img
          alt="preview"
          style={{ width: '100%' }}
          src={
            imageModal.imageUrl.startsWith('http')
              ? imageModal.imageUrl
              : (import.meta.env.PROD ? '/' : 'https://localhost:44348/') +
                imageModal.imageUrl
          }
        />
      </ModalAntdCustom>
    </Card>
  );
};

export default QuizComponent;

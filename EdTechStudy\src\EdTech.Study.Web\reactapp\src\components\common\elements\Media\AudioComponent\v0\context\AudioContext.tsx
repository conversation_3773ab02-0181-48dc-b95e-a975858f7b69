import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
} from 'react';
import { Modal } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import H5AudioPlayer from 'react-h5-audio-player';
import { AudioComponentProps, AudioItem } from '../../shared/types';
import tempFileApi from '../../../../../../../api/tempFileApi';
import {
  processAudioUrl,
  getFileNameWithoutExtension,
  getFileExtension,
} from '../../../shared/utils';
import { UploadedFile } from '../../../shared/MediaFileUploader';

const { confirm } = Modal;

// Define AudioContext type
interface AudioContextType {
  // State
  config: AudioComponentProps;
  isEditing: boolean;
  currentAudio: AudioItem | null;
  tempUrl: string;
  fileList: UploadFile[];
  selectedIndex: number;
  editingAudioName: number | null;
  editingAudioNameValue: string;
  containerRef: React.RefObject<HTMLDivElement>;
  audioPlayerRef: React.RefObject<H5AudioPlayer>;

  // Actions
  setTempUrl: (url: string) => void;
  setEditingAudioNameValue: (value: string) => void;
  handlePrevAudio: () => void;
  handleNextAudio: () => void;
  handleSelectAudio: (index: number) => void;
  handleUrlSubmit: () => void;
  handleToggleChange: (name: string, checked: boolean) => void;
  handleInputChange: (name: string, value: any) => void;
  handleDownloadAudio: () => void;
  handlePlaybackRateChange: (value: number) => void;
  handleVolumeChange: (value: number) => void;
  handleEditAudioName: (index: number) => void;
  handleSaveAudioName: () => void;
  handleCancelEditAudioName: () => void;
  showDeleteConfirmation: (index: number) => void;
  showDeleteAllConfirmation: () => void;
  handleUploadSuccess: (files: UploadedFile[]) => void;
  handleTitleUpdate: (newTitleProps: any) => void;
}

// Create the context
export const AudioContext = createContext<AudioContextType | undefined>(
  undefined
);

// Provider component
interface AudioProviderProps {
  children: React.ReactNode;
  params: AudioComponentProps;
  isEditing: boolean;
  addOrUpdateParamComponent: (params: AudioComponentProps) => void;
  path: string;
}

export const AudioProvider: React.FC<AudioProviderProps> = ({
  children,
  params,
  isEditing,
  addOrUpdateParamComponent,
}) => {
  // Config setup
  const config = useMemo(() => ({ ...params }), [params]);

  // State for player
  const [tempUrl, setTempUrl] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);

  // State for editing audio names
  const [editingAudioName, setEditingAudioName] = useState<number | null>(null);
  const [editingAudioNameValue, setEditingAudioNameValue] =
    useState<string>('');

  // Container ref for drag and drop
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Audio player ref để có thể điều khiển player từ bên ngoài
  const audioPlayerRef = useRef<H5AudioPlayer>(null);

  // Get the current audio to display
  const currentAudio = useMemo(() => {
    if (config.medias && config.medias.length > 0) {
      return config.medias[selectedIndex];
    } else if (config.mediaUrl) {
      return {
        mediaUrl: config.mediaUrl,
        mediaType: config.mediaType,
        name: config.name || '',
        artist: config.artist || '',
        blobKey: config.blobKey,
        blobContext: config.blobContext,
      } as AudioItem;
    }
    return null;
  }, [config.medias, config.mediaUrl, selectedIndex]);

  // Ensure we have an audios array
  useEffect(() => {
    // If we have a legacy audioUrl but no audios array, convert it
    if (
      config.mediaUrl &&
      config.blobKey &&
      config.blobContext &&
      (!config.medias || config.medias.length === 0)
    ) {
      addOrUpdateParamComponent({
        ...config,
        medias: [
          {
            mediaUrl: config.mediaUrl,
            mediaType: config.mediaType || 'upload',
            name: config.name || '',
            artist: config.artist || '',
            blobKey: config.blobKey,
            blobContext: config.blobContext,
          },
        ],
      });
    }
  }, [
    config.mediaUrl,
    config.medias,
    config.blobKey,
    config.blobContext,
    config.mediaType,
    config.name,
    config.artist,
    addOrUpdateParamComponent,
  ]);

  // Initialize file list from audios array
  useEffect(() => {
    if (config.medias && config.medias.length > 0 && fileList.length === 0) {
      const newFileList = config.medias
        .filter((audio) => audio.mediaType === 'upload')
        .map((audio, index) => {
          const fileName =
            audio.mediaUrl.split('/').pop() || `audio-${index + 1}`;
          return {
            uid: `-${index + 1}`,
            name: fileName,
            status: 'done' as const,
            url: audio.mediaUrl,
          };
        });
      setFileList(newFileList);
    }
  }, [config.medias, fileList.length]);

  // Handle when switching to previous audio - auto play
  const handlePrevAudio = useCallback(() => {
    if (!config.medias || config.medias.length <= 1) return;
    const prevIndex = selectedIndex > 0 ? selectedIndex - 1 : config.medias.length - 1;
    setSelectedIndex(prevIndex);
    
    // Auto play the previous audio
    setTimeout(() => {
      if (audioPlayerRef.current && audioPlayerRef.current.audio && audioPlayerRef.current.audio.current) {
        audioPlayerRef.current.audio.current.play().catch(error => {
          console.log('Play failed:', error);
        });
      }
    }, 100);
  }, [config.medias, selectedIndex, audioPlayerRef]);

  // Handle when switching to next audio - auto play
  const handleNextAudio = useCallback(() => {
    if (!config.medias || config.medias.length <= 1) return;
    const nextIndex = selectedIndex < config.medias.length - 1 ? selectedIndex + 1 : 0;
    setSelectedIndex(nextIndex);
    
    // Auto play the next audio
    setTimeout(() => {
      if (audioPlayerRef.current && audioPlayerRef.current.audio && audioPlayerRef.current.audio.current) {
        audioPlayerRef.current.audio.current.play().catch(error => {
          console.log('Play failed:', error);
        });
      }
    }, 100);
  }, [config.medias, selectedIndex, audioPlayerRef]);

  // Handle selecting a specific audio
  const handleSelectAudio = useCallback(
    (index: number) => {
      if (!config.medias || index >= config.medias.length) return;
      
      const wasCurrentlySelected = index === selectedIndex;
      setSelectedIndex(index);

      // If clicking on the currently selected audio, play it
      // Or if selecting a different audio, it will auto-play via useEffect
      if (wasCurrentlySelected || index !== selectedIndex) {
        setTimeout(() => {
          if (audioPlayerRef.current && audioPlayerRef.current.audio && audioPlayerRef.current.audio.current) {
            audioPlayerRef.current.audio.current.play().catch(error => {
              console.log('Play failed:', error);
            });
          }
        }, 100); // Small delay to ensure audio is loaded
      }

      // Scroll the selected audio into view with smooth animation
      setTimeout(() => {
        const audioElements = document.querySelectorAll(
          '.audio-item-container'
        );
        if (audioElements && audioElements[index]) {
          audioElements[index].scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
          });
        }
      }, 50);
    },
    [config.medias, selectedIndex, audioPlayerRef]
  );

  // Handle URL submission for multiple audios
  const handleUrlSubmit = useCallback(() => {
    if (!tempUrl) {
      Modal.error({ content: 'Vui lòng nhập URL âm thanh' });
      return;
    }

    // Process the URL using our utility function
    const { fullUrl } = processAudioUrl(tempUrl);

    // Create a new audio item
    const newAudio: AudioItem = {
      mediaUrl: fullUrl,
      mediaType: 'embed' as const,
      name: 'Âm thanh từ URL',
      artist: '',
      blobKey: '',
      blobContext: '',
    };

    // Add to audios array
    const newAudios = config.medias ? [...config.medias, newAudio] : [newAudio];

    // Update component
    addOrUpdateParamComponent({
      ...config,
      medias: newAudios,
    });

    // Clear URL input
    setTempUrl('');
  }, [tempUrl, config, addOrUpdateParamComponent]);

  // Handle toggle changes
  const handleToggleChange = useCallback(
    (name: string, checked: boolean) => {
      // Update component params
      addOrUpdateParamComponent({
        ...config,
        [name]: checked,
      });
      
      // Currently only handling autoPlay since loop and muted are now hardcoded
    },
    [config, addOrUpdateParamComponent]
  );

  // Handle input changes
  const handleInputChange = useCallback(
    (name: string, value: any) => {
      addOrUpdateParamComponent({
        ...config,
        [name]: value,
      });
    },
    [config, addOrUpdateParamComponent]
  );

  // Handle downloading the audio
  const handleDownloadAudio = useCallback(() => {
    if (currentAudio && currentAudio.mediaUrl) {
      const link = document.createElement('a');
      link.href = currentAudio.mediaUrl;
      link.download = currentAudio.name || 'audio-file';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [currentAudio]);

  // Handle playback rate change - delegate to H5AudioPlayer
  const handlePlaybackRateChange = useCallback((value: number) => {
    // Store the playback rate preference in the config
    addOrUpdateParamComponent({
      ...config,
      playbackRate: value
    });
    
    // Apply playback rate directly to H5AudioPlayer if ref is available
    if (audioPlayerRef.current && audioPlayerRef.current.audio && audioPlayerRef.current.audio.current) {
      audioPlayerRef.current.audio.current.playbackRate = value;
    }
  }, [config, addOrUpdateParamComponent]);

  // Handle volume change - delegate to H5AudioPlayer
  const handleVolumeChange = useCallback((value: number) => {
    // Store the volume preference in the config
    addOrUpdateParamComponent({
      ...config,
      volume: value
    });
    
    // Apply volume directly to H5AudioPlayer if ref is available
    if (audioPlayerRef.current && audioPlayerRef.current.audio && audioPlayerRef.current.audio.current) {
      audioPlayerRef.current.audio.current.volume = value;
    }
  }, [config, addOrUpdateParamComponent]);



  // Handle editing audio name
  const handleEditAudioName = useCallback(
    (index: number) => {
      if (!config.medias || index >= config.medias.length) return;

      const currentName = config.medias[index].name || '';
      setEditingAudioName(index);
      // Extract filename without extension and set it as the editing value
      const nameWithoutExtension = getFileNameWithoutExtension(currentName);
      setEditingAudioNameValue(nameWithoutExtension);
    },
    [config.medias]
  );

  // Handle saving audio name
  const handleSaveAudioName = useCallback(() => {
    if (
      editingAudioName === null ||
      !config.medias ||
      editingAudioName >= config.medias.length
    )
      return;

    // Get the current name from the config
    const currentName = config.medias[editingAudioName].name || '';
    // Extract the extension from the current name
    const extension = getFileExtension(currentName);
    // Add the extension to the edited name
    const newName = editingAudioNameValue + (extension ? '.' + extension : '');

    // Create a new array with the updated name
    const newAudios = [...config.medias];
    newAudios[editingAudioName] = {
      ...newAudios[editingAudioName],
      name: newName,
    };

    // Update the component with the new array
    addOrUpdateParamComponent({
      ...config,
      medias: newAudios,
    });

    // Reset editing state
    setEditingAudioName(null);
    setEditingAudioNameValue('');
  }, [
    editingAudioName,
    config.medias,
    editingAudioNameValue,
    addOrUpdateParamComponent,
  ]);

  // Handle cancel editing audio name
  const handleCancelEditAudioName = useCallback(() => {
    setEditingAudioName(null);
    setEditingAudioNameValue('');
  }, []);

  // Show delete confirmation using Modal.confirm
  const showDeleteConfirmation = useCallback((index: number) => {
    confirm({
      title: 'Xác nhận xóa âm thanh',
      content: (
        <>
          <p>Bạn có chắc chắn muốn xóa âm thanh này không?</p>
          <p className="tailwind-text-gray-500 tailwind-text-sm">
            Hành động này không thể hoàn tác.
          </p>
        </>
      ),
      okText: 'Xóa',
      cancelText: 'Hủy',
      okButtonProps: { danger: true },
      onOk: () => performDeleteAudio(index),
    });
  }, []);

  // Show delete all confirmation using Modal.confirm
  const showDeleteAllConfirmation = useCallback(() => {
    confirm({
      title: 'Xác nhận xóa tất cả âm thanh',
      content: (
        <>
          <p>
            Bạn có chắc chắn muốn xóa <strong>tất cả</strong> âm thanh không?
          </p>
          <p className="tailwind-text-gray-500 tailwind-text-sm">
            Hành động này không thể hoàn tác.
          </p>
        </>
      ),
      okText: 'Xóa tất cả',
      cancelText: 'Hủy',
      okButtonProps: { danger: true },
      onOk: () => performDeleteAudio(),
    });
  }, []);

  // Perform the actual audio deletion after confirmation
  const performDeleteAudio = useCallback(
    async (index?: number) => {
      // If index is provided, delete specific audio
      if (index !== undefined && config.medias && config.medias.length > 0) {
        const audioToDelete = config.medias[index];

        // Try to delete from server if it's an upload
        if (audioToDelete.mediaType === 'upload') {
          try {
            let url = audioToDelete.mediaUrl;
            if (!url.startsWith('data:') && audioToDelete.blobKey) {
              await tempFileApi.Delete(audioToDelete.blobKey);
            }
          } catch (error) {
            console.error('Error deleting audio:', error);
            // Continue with UI deletion even if server deletion fails
          }
        }

        // Create new audios array without the deleted item
        const newAudios = [...config.medias];
        newAudios.splice(index, 1);

        // Update component
        addOrUpdateParamComponent({
          ...config,
          medias: newAudios,
        });

        // Update selected index if needed
        if (selectedIndex >= newAudios.length) {
          setSelectedIndex(Math.max(0, newAudios.length - 1));
        }

        // Update file list
        if (audioToDelete.mediaType === 'upload') {
          const newFileList = fileList.filter((_, i) => i !== index);
          setFileList(newFileList);
        }

        // Show success message
        Modal.success({ content: 'Đã xóa âm thanh thành công' });
      }
      // If no index is provided, delete all audios
      else if (config.medias && config.medias.length > 0) {
        // Try to delete all uploaded audios from server
        for (const audio of config.medias) {
          if (audio.mediaType === 'upload') {
            try {
              let url = audio.mediaUrl;
              if (!url.startsWith('data:') && audio.blobKey) {
                await tempFileApi.Delete(audio.blobKey);
              }
            } catch (error) {
              console.error('Error deleting audio:', error);
              // Continue with next deletion
            }
          }
        }

        // Clear all state
        setFileList([]);
        setTempUrl('');
        setSelectedIndex(0);

        // Update component
        addOrUpdateParamComponent({
          ...config,
          medias: [],
        });

        // Show success message
        Modal.success({ content: 'Đã xóa tất cả âm thanh thành công' });
      }
      // Legacy support
      else if (config.mediaUrl) {
        if (config.mediaType === 'upload') {
          try {
            let url = config.mediaUrl;
            if (!url.startsWith('data:') && config.blobKey) {
              await tempFileApi.Delete(config.blobKey);
            }
          } catch (error) {
            console.error('Error deleting audio:', error);
          }
        }

        setFileList([]);
        setTempUrl('');
        addOrUpdateParamComponent({
          ...config,
          mediaUrl: '',
        });

        // Show success message
        Modal.success({ content: 'Đã xóa âm thanh thành công' });
      }
    },
    [config, selectedIndex, fileList, addOrUpdateParamComponent]
  );

  // Xử lý khi upload thành công
  const handleUploadSuccess = useCallback(
    (files: UploadedFile[]) => {
      // Tạo mảng các đối tượng audio mới từ các file đã upload
      const newUploadedAudios = files.map((item) => {
        // Process the URL using our utility function
        const { fullUrl } = processAudioUrl(item.url);

        return {
          mediaUrl: fullUrl,
          mediaType: 'upload' as const,
          name: item.name || 'Âm thanh',
          artist: '',
          blobKey: item.blobKey || '',
          blobContext: item.blobContext || '',
        } as AudioItem;
      });

      // Kết hợp với audio hiện có
      const updatedAudios = config.medias
        ? [...config.medias, ...newUploadedAudios]
        : newUploadedAudios;

      // Cập nhật component với audio mới
      addOrUpdateParamComponent({
        ...config,
        medias: updatedAudios,
      });
    },
    [config, addOrUpdateParamComponent]
  );

  // Handle title update
  const handleTitleUpdate = useCallback(
    (newTitleProps: any) => {
      addOrUpdateParamComponent({
        ...config,
        titleProps: newTitleProps,
      });
    },
    [config, addOrUpdateParamComponent]
  );

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      // State
      config,
      isEditing,
      currentAudio,
      tempUrl,
      fileList,
      selectedIndex,
      editingAudioName,
      editingAudioNameValue,
      containerRef,
      audioPlayerRef,

      // Actions
      setTempUrl,
      setEditingAudioNameValue,
      handlePrevAudio,
      handleNextAudio,
      handleSelectAudio,
      handleUrlSubmit,
      handleToggleChange,
      handleInputChange,
      handleDownloadAudio,
      handlePlaybackRateChange,
      handleVolumeChange,
      handleEditAudioName,
      handleSaveAudioName,
      handleCancelEditAudioName,
      showDeleteConfirmation,
      showDeleteAllConfirmation,
      handleUploadSuccess,
      handleTitleUpdate,
    }),
    [
      config,
      isEditing,
      currentAudio,
      tempUrl,
      fileList,
      selectedIndex,
      editingAudioName,
      editingAudioNameValue,
      handlePrevAudio,
      handleNextAudio,
      handleSelectAudio,
      handleUrlSubmit,
      handleToggleChange,
      handleInputChange,
      handleDownloadAudio,
      handlePlaybackRateChange,
      handleVolumeChange,
      handleEditAudioName,
      handleSaveAudioName,
      handleCancelEditAudioName,
      showDeleteConfirmation,
      showDeleteAllConfirmation,
      handleUploadSuccess,
      handleTitleUpdate,
    ]
  );

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
    </AudioContext.Provider>
  );
};

// Custom hook to use the Audio context
export const useAudio = () => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};

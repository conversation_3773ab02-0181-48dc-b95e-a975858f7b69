import React, { useState } from 'react';
import { Input, Button, Space } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { FillInQuestion } from '../QuestionBankConfig';

interface FillInQuestionProps {
  question: FillInQuestion;
  onAnswer: (answer: string) => void;
  disabled?: boolean;
}

const FillInQuestionComponent: React.FC<FillInQuestionProps> = ({
  onAnswer,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState<string>('');

  const handleSubmit = () => {
    onAnswer(inputValue);
    setInputValue('');
  };

  return (
    <div className="tailwind-w-full">
      <Space.Compact className="tailwind-w-full">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onPressEnter={handleSubmit}
          disabled={disabled}
          placeholder="Nhập câu trả lời của bạn"
          className="tailwind-flex-1"
        />
        <Button
          type="primary"
          onClick={handleSubmit}
          disabled={disabled}
          icon={<RightOutlined />}
        >
          Trả lời
        </Button>
      </Space.Compact>
    </div>
  );
};

export default FillInQuestionComponent;

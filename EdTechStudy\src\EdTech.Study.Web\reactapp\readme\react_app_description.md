# EdTechWeb ReactApp (EdTech)

## Giới thiệu

EdTech Web là một ứng dụng giảng dạy trực tuyến, cho phép tạo mô phỏng 3D, mini game, câu hỏi trắc nghiệm và bài học nhằm hỗ trợ thuyết trình, gi<PERSON><PERSON> dạy và học tập. Công cụ này giúp bạn tạo nội dung trực quan, dễ thao tác và linh ho<PERSON>t, gi<PERSON><PERSON> người dùng theo dõi, tương tác với mô phỏng đa chiều và tham gia các hoạt động học tập thú vị.

## Công nghệ sử dụng

EdTech Web được phát triển dựa trên các công nghệ:

- Backend: ABP MVC, ASP NET CORE
- Frontend: HTML, CSS, JavaScript, Bootstrap, React-js(Typescript), Ant-Design
- 3D Graphics: <PERSON>.js, OBJLoader.js, M<PERSON><PERSON>oader.js, OrbitControls.js
- Drag & Drop: Sortable.js
- Tr<PERSON><PERSON> quan hóa dữ liệu: d3js
  Tính năng chính
- Mô phỏng các hiện tượng khoa học, thí nghiệm, chuyển động vật lý phục vụ giảng dạy.
- Tạo mini game để hỗ trợ học tập và củng cố kiến thức.
- Cung cấp hệ thống câu hỏi trắc nghiệm để ôn tập kiến thức.
- Giao diện trực quan, tối ưu tương tác với mô phỏng 3D, hỗ trợ điều khiển camera bằng chuột.
- Hạn chế văn bản, tập trung vào trải nghiệm mô phỏng.
- Hỗ trợ hoặc không hỗ trợ real-time giữa các client (mặc định không có).
- Tạo Checklist kiểm thử tính năng bằng tiếng Việt theo cấu trúc
  [Tên Tính năng]
  | ID | Mục kiểm thử | Cách thực hiện | Kết quả mong đợi | Kết quả kiểm thử | Ghi chú|

## Yêu cầu đặc biệt

- Ui với các button nên có thêm icon
- Chỉ tạo các file liên quan đến SignalR và hub khi có yêu cầu hỗ trợ real-time.
  Lưu ý khi tạo code:

1. Escape < bằng &lt; với trường hợp hiển thị text đề Razor sẽ hiển thị < mà không nhầm với HTML
2. Api để đọc file media: '/api/edtech-files'

- Sử dụng với thẻ img:

```tsx
<img
  src={
    process.env.production
      ? `/api/edtech-files?path=${filePathHere}`
      : `https://localhost:44348/api/edtech-files?path=${filePathHere}`
  }
/>
```

- Sử dụng với axiosClient:

```tsx
axiosClient({
  method: 'get',
  url: `api/edtech-files?path=${filePathHere}`,
});
```

3. Api để lưu file media: '/api/edtech-files'

- Method : post

4. Đây là phần front-end code của dự án

```
edtech-react-app/
	|
	node_modules/
	|
	public/
	|
	src/
	|   index.css
	|
	+---assets
	|   \---images
	|       |   .keep
	|       |
	|       +---defaultThumbnails
	|       |       Demo.png
	|       |       Demo2.jpg
	|       |       Demo3.jpg
	|       |       Demo4.jpg
	|       |       Demo5.jpg
	|       |       Demo6.jpg
	|       |       Game.jpg
	|       |       Game2.jpg
	|       |       Game3.jpg
	|       |       Layout.png
	|       |       Layout2.png
	|       |       Quiz.jpg
	|       |       Quiz2.jpg
	|       |       Simulator.jpg
	|       |       Simulator2.jpg
	|       |
	|       \---miniGames
	|           \---thumbnails
	|                   CoordinateFinderGame.jpg
	|                   GoldMineGame.jpg
	|                   MapPuzzleGame.jpg
	|                   MillionaireGame.jpg
	|                   MountainClimbingGame.jpg
	|                   OlympiaGame.jpg
	|                   QuizCardGame.jpg
	|                   TreasureHuntGame.jpg
	|
	+---components
	|   +---common
	|   |   |   AppHeader.tsx
	|   |   |   EditingModeTest.tsx
	|   |   |   EditModeToggle.tsx
	|   |   |   SidebarLessonComponent.tsx
	|   |   |
	|   |   +---LessonCommonComponent
	|   |   |   |   index.ts
	|   |   |   |   interface.ts
	|   |   |   |   LessonCommonComponent.tsx
	|   |   |   |   LessonConfig.tsx
	|   |   |   |
	|   |   |   \---components
	|   |   |           DeleteButton.tsx
	|   |   |           LessonContent.tsx
	|   |   |           LessonGames.tsx
	|   |   |           LessonHeader.tsx
	|   |   |           LessonObjectives.tsx
	|   |   |           LessonQuiz.tsx
	|   |   |
	|   |   +---LessonConfigComponent
	|   |   |       CustomTree.css
	|   |   |       index.tsx
	|   |   |       LessonConfigTreeComponent.tsx
	|   |   |
	|   |   +---LessonRenderComponent
	|   |   |       EdTechRenderComponent.tsx
	|   |   |       LayoutComponentBase.tsx
	|   |   |
	|   |   +---LessonSelectorComponent
	|   |   |   |   LessonSelectorComponent.tsx
	|   |   |   |
	|   |   |   +---components
	|   |   |   |       CategoryBadge.tsx
	|   |   |   |       ComponentCard.tsx
	|   |   |   |       ComponentGrid.tsx
	|   |   |   |       SearchAndFilter.tsx
	|   |   |   |       SelectedComponents.tsx
	|   |   |   |
	|   |   |   \---utils
	|   |   |           thumbnailUtils.ts
	|   |   |           typeIcons.ts
	|   |   |
	|   |   \---MapCommonComponent
	|   |       |   map.css
	|   |       |   MapUtils.ts
	|   |       |   README.md
	|   |       |
	|   |       \---components
	|   |           |   MapComponent.tsx
	|   |           |   MapControls.tsx
	|   |           |
	|   |           \---controls
	|   |                   CompassControl.tsx
	|   |                   CoordinatesControl.tsx
	|   |                   index.ts
	|   |                   ScaleControl.tsx
	|   |                   SearchControl.tsx
	|   |
	|   +---demo
	|   |   |   index.ts
	|   |   |
	|   |   +---constants
	|   |   |       edtech_data_export.json
	|   |   |
	|   |   +---LessonGames
	|   |   |       DemoLessonGames1.tsx
	|   |   |       DemoLessonGames2.tsx
	|   |   |
	|   |   \---LessonQuiz
	|   |           DemoLessonQuiz1.tsx
	|   |           DemoLessonQuiz2.tsx
	|   |
	|   +---icons
	|   |       .keep
	|   |
	|   +---lessonContents
	|   |       lessonTest.tsx
	|   |
	|   +---miniGames
	|   |   |   .keep
	|   |   |   GameConfigContainer.tsx
	|   |   |   GameContainer.tsx
	|   |   |   GameLoader.tsx
	|   |   |   GameManager.tsx
	|   |   |   GameRegistry.tsx
	|   |   |   LessonGameManager.tsx
	|   |   |
	|   |   +---CoordinateFinderGame
	|   |   |       CoordinateFinderGame.tsx
	|   |   |       CoordinateFinderGameComponent.tsx
	|   |   |       CoordinateFinderGameConfig.ts
	|   |   |       CoordinateFinderGameModal.tsx
	|   |   |
	|   |   +---MillionaireGame
	|   |   |       MillionaireGame.tsx
	|   |   |       MillionaireGameComponent.tsx
	|   |   |       MillionaireGameConfig.ts
	|   |   |       MillionaireGameModal.tsx
	|   |   |
	|   |   +---MountainClimbingGame
	|   |   |       MountainClimbingGame.tsx
	|   |   |       MountainClimbingGameComponent.tsx
	|   |   |       MountainClimbingGameConfig.ts
	|   |   |       MountainClimbingGameModal.tsx
	|   |   |
	|   |   +---OlympiaGame
	|   |   |   |   OlympiaGame.tsx
	|   |   |   |   OlympiaGameComponent.tsx
	|   |   |   |   OlympiaGameConfig.ts
	|   |   |   |   OlympiaGameHeader.tsx
	|   |   |   |   OlympiaGameInstructions.tsx
	|   |   |   |   OlympiaGameModal.tsx
	|   |   |   |   OlympiaGameRoundInfo.tsx
	|   |   |   |   OlympiaGameSummary.tsx
	|   |   |   |
	|   |   |   \---rounds
	|   |   |           OlympiaRoundAcceleration.tsx
	|   |   |           OlympiaRoundFinish.tsx
	|   |   |           OlympiaRoundObstacle.tsx
	|   |   |           OlympiaRoundWarmup.tsx
	|   |   |
	|   |   +---QuestionBank
	|   |   |       QuestionBankConfig.ts
	|   |   |
	|   |   +---QuizCardGame
	|   |   |       QuizCardGame.tsx
	|   |   |       QuizCardGameComponent.tsx
	|   |   |       QuizCardGameConfig.ts
	|   |   |       QuizCardGameModal.tsx
	|   |   |
	|   |   \---TreasureHuntGame
	|   |           TreasureHuntGame.tsx
	|   |           TreasureHuntGameComponent.tsx
	|   |           TreasureHuntGameConfig.ts
	|   |           TreasureHuntGameModal.tsx
	|   |
	|   +---quizs
	|   |   |   .keep
	|   |   |   localization.ts  (Updated)
	|   |   |   README.MD
	|   |   |
	|   |   +---fillblanks
	|   |   |       FillBlanksComponent.css
	|   |   |       FillBlanksComponent.tsx
	|   |   |       index.ts
	|   |   |       SimpleFillBlanksWrapper.tsx
	|   |   |
	|   |   +---matching
	|   |   |   |   index.ts
	|   |   |   |   MatchingComponent.css
	|   |   |   |   MatchingComponent.tsx
	|   |   |   |   MatchingWrapper.tsx
	|   |   |   |
	|   |   |   +---components
	|   |   |   |       MatchingControls.tsx
	|   |   |   |       MatchingFeedback.tsx
	|   |   |   |       SortableItem.tsx
	|   |   |   |       StaticItem.tsx
	|   |   |   |
	|   |   |   \---hooks
	|   |   |           useMatchingLogic.ts
	|   |   |           useMatchResults.ts
	|   |   |
	|   |   +---multiselect (New directory)
	|   |   |       index.ts
	|   |   |       MultiSelectQuizComponent.tsx
	|   |   |       MultiSelectWrapper.tsx
	|   |   |       multiSelectTemplates.ts
	|   |   |
	|   |   +---practiceEngines
	|   |   |       index.ts
	|   |   |       ModalSelectQuestionType.tsx  (Updated)
	|   |   |       PracticeEngines.css
	|   |   |       PracticeEngines.tsx  (Updated)
	|   |   |       PracticeEngineSidebar.tsx
	|   |   |       PracticeEngineWrapper.tsx  (Updated)
	|   |   |       questionTemplates.ts  (Updated)
	|   |   |
	|   |   \---quiz
	|   |           FireworksComponent.tsx
	|   |           index.ts
	|   |           QuizAnimations.css
	|   |           QuizComponent.tsx
	|   |           SimpleQuizWrapper.tsx
	|   |
	|   \---simulators
	|       |   LessonSimulatorManager.tsx
	|       |   SimulationLoader.tsx
	|       |   SimulationManager.tsx
	|       |   SimulationStore.tsx
	|       |   SimulatorConfigContainer.tsx
	|       |   SimulatorContainer.tsx
	|       |   SimulatorRegistry.tsx
	|       |
	|       +---Box
	|       |       BoxComponent.tsx
	|       |
	|       +---FreefallSimulator
	|       |       FreefallSimulator.tsx
	|       |       FreefallSimulatorConfig.ts
	|       |       FreefallSimulatorConfigModal.tsx
	|       |       FreefallSimulatorContainer.tsx
	|       |       FreefallUtils.ts
	|       |       README.md
	|       |
	|       +---MapSimulator
	|       |       MapSimulator.tsx
	|       |       MapSimulatorComponent.tsx
	|       |       MapSimulatorConfig.ts
	|       |       MapSimulatorConfigModal.tsx
	|       |
	|       +---SetTheorySimulator
	|       |       SetTheoryConfig.ts
	|       |       SetTheoryConfigModal.tsx
	|       |       SetTheoryContainer.tsx
	|       |       SetTheorySimulator.tsx
	|       |       SetTheoryUtils.ts
	|       |
	|       \---TrigonometricValueSimulator
	|               TrigonometricConfig.ts
	|               TrigonometricConfigModal.tsx
	|               TrigonometricUtils.ts
	|               TrigonometricVisualizer.tsx
	|               TrigonometricVisualizerContainer.tsx
	|
	+---constants
	|       AppContants.ts
	|       EdTechComponent.ts  (Updated)
	|
	+---enums
	|       AppEnums.ts
	|
	+---examples
	|       EditModeUsage.tsx
	|
	+---hocs
	|       withEdComponentParams.ts
	|
	+---interfaces
	|   |   AppComponents.ts
	|   |
	|   \---quizs
	|           fillblanks.interfaces.ts
	|           mapping.interface.ts
	|           multiSelectQuiz.interface.ts  (New file)
	|           practiceEngines.interface.ts
	|           questionBase.ts  (Updated)
	|           quizComponent.interface.ts
	|
	+---pages
	|       DemoLessonPage.tsx
	|       TestPage.tsx
	|
	+---providers
	|       EdTechConfigProvider.tsx
	|       EdTechProvider.interface.ts
	|       EdTechProvider.tsx
	|
	+---services
	|       axiosClient.tsx
	|
	+---store
	|   |   store.ts
	|   |
	|   \---slices
	|       +---AppConfig
	|       |       AppConfigSlice.ts
	|       |
	|       +---AppSlices
	|       |       EdComponentParamsSlice.ts
	|       |       EdTechRenderDataSlice.ts
	|       |
	|       +---Configs
	|       |       EdComponentConfigParamsSlice.ts
	|       |       EdTechConfigRenderDataSlice.ts
	|       |
	|       \---utils
	|               createParamsSlice.ts
	|               createTreeDataSlice.ts
	|               README.md
	|
	+---styles
	|       global.css
	|
	+---types
	|       images.d.ts
	|
	\---utils
			AppFunctions.tsx
			HashHelper.ts
			localizationUtils.ts
```

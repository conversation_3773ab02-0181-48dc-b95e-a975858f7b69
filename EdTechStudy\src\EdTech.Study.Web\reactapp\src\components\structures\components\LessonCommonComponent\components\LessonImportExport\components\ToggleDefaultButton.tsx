import React from 'react';
import { Switch, Modal, message, Space } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { EdTechRootState } from '../../../../../../../store/store';
import { AppFunctions } from '../../../../../../../utils/AppFunctions';

interface ToggleDefaultButtonProps {
  className?: string;
}

const ToggleDefaultButton: React.FC<ToggleDefaultButtonProps> = ({
  className,
}) => {
  const dispatch = useDispatch();

  // Lấy trạng thái isDefaultData từ AppConfigSlice
  const isDefaultData = useSelector(
    (state: EdTechRootState) => state.appConfig.isDefaultData
  );

  // Xử lý đặt lại mặc định hoặc change toggle
  const onToggleDefault = (checked: boolean) => {
    if (checked === isDefaultData) {
      return; // Không có gì thay đổi
    }

    if (checked) {
      // <PERSON><PERSON>ch hoạt chế độ dữ liệu mặc định
      Modal.confirm({
        title: '<PERSON>á<PERSON> nhận đặt lại',
        content: 'Bạn có chắc chắn muốn đặt lại dữ liệu mặc định không?',
        onOk: () => {
          // Reset dữ liệu về mặc định
          AppFunctions.resetDefaultDataFromJsonFile(dispatch);
        },
        onCancel: () => {
          // Không cần xử lý gì đặc biệt, vì React sẽ điều chỉnh lại Switch dựa vào state của isDefaultData
        },
      });
    } else {
      // Cố gắng chuyển từ dữ liệu mặc định sang custom - chỉ cần thông báo
      message.info(
        'Hãy chỉnh sửa dữ liệu hoặc nhập dữ liệu mới để tạo phiên bản tùy chỉnh'
      );
    }
  };

  return (
    <Space className={`toggle-default-button ${className || ''}`}>
      <Switch
        checked={isDefaultData}
        onChange={onToggleDefault}
        checkedChildren={<span>Dữ liệu mặc định</span>}
        unCheckedChildren={<span>Đặt lại mặc định</span>}
        className="custom-toggle-switch"
      />
    </Space>
  );
};

export default ToggleDefaultButton;

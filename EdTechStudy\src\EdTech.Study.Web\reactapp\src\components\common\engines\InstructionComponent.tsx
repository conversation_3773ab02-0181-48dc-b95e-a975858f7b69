import React from 'react';
import { Alert, Typography } from 'antd';
import { InfoCircleOutlined, BookOutlined } from '@ant-design/icons';

// Import custom styles
import './InstructionComponent.css';

const { Text, Paragraph } = Typography;

/**
 * InstructionComponent - Component hiển thị hướng dẫn sử dụng với giao diện nhất quán
 *
 * <PERSON><PERSON>ch sử dụng với props có cấu trúc:
 * ```
 * <InstructionComponent
 *   title="Hướng dẫn sử dụng"
 *   objective="Giúp bạn khám phá các quy luật vật lý của chuyển động rơi tự do trong các môi trường khác nhau."
 *   steps={[
 *     "Điều chỉnh các thông số ban đầu như chiều cao, vận tốc ban đầu và môi trường",
 *     "Bấm nút 'Bắt đầu' để chạy mô phỏng và quan sát chuyển động của vật thể",
 *     "Điều chỉnh tốc độ mô phỏng để quan sát chuyển động nhanh hơn hoặc chậm hơn",
 *     "Theo dõi các đại lượng vật lý như vị trí, vận tốc và gia tốc theo thời gian",
 *     "Thay đổi môi trường để so sánh chuyển động rơi tự do trên Trái Đất, Mặt Trăng và Sao Hỏa"
 *   ]}
 *   notes="Lưu ý: Kết quả mô phỏng có thể khác với thực tế do đã đơn giản hóa một số yếu tố."
 * />
 * ```
 */

// Props cho component chính
interface InstructionComponentProps {
  // Props cho tiêu đề
  title?: string; // Tiêu đề hướng dẫn (nếu không cung cấp, sẽ dùng mặc định)

  // Các props nội dung có cấu trúc
  objective: string; // Mục tiêu của hướng dẫn (bắt buộc)
  steps: string[]; // Các bước thực hiện (bắt buộc)
  notes?: string; // Ghi chú bổ sung (tùy chọn)
  isFullscreen?: boolean; // Trạng thái toàn màn hình

  // Tiêu đề của các phần
  stepsTitle?: string; // Tiêu đề của phần các bước
  notesTitle?: string; // Tiêu đề của phần ghi chú

  // Các props styling
  className?: string; // CSS class tùy chỉnh
  style?: React.CSSProperties; // Inline style tùy chỉnh
}

const InstructionComponent: React.FC<InstructionComponentProps> = ({
  title = 'Hướng dẫn sử dụng',
  objective,
  steps,
  notes = '',
  isFullscreen = false,
  stepsTitle,
  notesTitle,
  className = '',
  style = {},
}) => {
  // Determine font sizes based on fullscreen mode
  const fontSizes = {
    header: isFullscreen ? '1rem' : '0.85rem',
    content: isFullscreen ? '0.95rem' : '0.8rem',
    additionalInfo: isFullscreen ? '0.95rem' : '0.8rem',
  };

  // Render a single step
  const renderSingleStep = (step: string, index: number) => {
    return (
      <li key={index} style={{ fontSize: fontSizes.content }}>
        {step}
      </li>
    );
  };

  // No longer needed for general instructions

  // Render the steps section
  const renderStepsSection = () => {
    return (
      <div className="instruction-steps">
        <Paragraph
          strong
          className={`tailwind-mb-2 ${
            isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
          }`}
        >
          {stepsTitle}
        </Paragraph>
        <ol className="tailwind-space-y-2">{steps.map(renderSingleStep)}</ol>
      </div>
    );
  };

  // No longer needed for general instructions

  // Render notes section if provided
  const renderNotesSection = () => {
    if (!notes) return null;

    return (
      <div className="instruction-notes tailwind-mt-3">
        <Paragraph
          strong
          className={`tailwind-mb-1 ${
            isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
          }`}
        >
          {notesTitle}
        </Paragraph>
        <Paragraph
          style={{
            fontSize: fontSizes.additionalInfo,
            fontStyle: 'italic',
          }}
          className="tailwind-ml-1"
        >
          {notes}
        </Paragraph>
      </div>
    );
  };

  // Render the structured content
  const renderInstructionContent = () => {
    return (
      <div className="instruction-content tailwind-space-y-2 tailwind-mt-2">
        {/* Objective Section */}
        <Paragraph
          className="instruction-objective"
          style={{
            marginBottom: '12px',
            fontSize: fontSizes.header,
            display: 'flex',
            alignItems: 'flex-start',
          }}
        >
          <Text
            strong
            style={{
              marginRight: '8px',
              display: 'inline-flex',
              alignItems: 'center',
            }}
          >
            <BookOutlined style={{ marginRight: '4px' }} /> Mục tiêu:
          </Text>
          {objective}
        </Paragraph>

        {/* Steps Section */}
        {renderStepsSection()}

        {/* Notes Section */}
        {renderNotesSection()}
      </div>
    );
  };

  return (
    <Alert
      message={
        <div className="tailwind-flex tailwind-items-center">
          <InfoCircleOutlined className="tailwind-mr-2" />
          <span
            className={`tailwind-font-medium ${
              isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
            }`}
          >
            {title}
          </span>
        </div>
      }
      description={renderInstructionContent()}
      type="info"
      showIcon={false}
      className={`instruction-component tailwind-mb-5 ${className}`}
      style={{
        padding: isFullscreen ? '16px 20px' : '12px 16px',
        borderRadius: '8px',
        ...style,
      }}
    />
  );
};

export default InstructionComponent;

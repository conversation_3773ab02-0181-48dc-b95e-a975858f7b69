import {
  createSlice,
  PayloadAction,
  Draft,
  createAction,
  createAsyncThunk,
} from '@reduxjs/toolkit';
import {
  IEdTechRenderTreeData,
  IEdTechComponent,
} from '../../../interfaces/AppComponents';
import { isEmpty } from '@tsp/utils';
import { ED_TECH_COMPONENT_RENDER_ROOT } from '../../../components/structures/structures/lessonCommonStructure';
import pubsub from '../../../utils/pubsub';
import { EventChange } from '../../../constants/eventChange';
import { nanoid } from 'nanoid';

// Types
export interface ITreeDataState {
  id: string;
  data: IEdTechRenderTreeData;
  isInitialized: boolean;
  error: string | null;
  loading: boolean;
}

// Type for node search result
export interface NodeSearchResult {
  node: IEdTechRenderTreeData | null;
  parentArray: IEdTechRenderTreeData[];
  parentNode: IEdTechRenderTreeData | null;
  index: number;
}

// Type for update payload
export interface UpdateItemPayload {
  path: string;
  data: Partial<IEdTechRenderTreeData>;
}

// Type for common payloads
export interface NodeWithItemsPayload {
  parentNode?: IEdTechRenderTreeData;
  items: IEdTechComponent[];
}
export interface NodeWithStructurePayload {
  parentNode?: IEdTechRenderTreeData;
  items: IEdTechRenderTreeData[];
}

// Initial state
export const initialTreeDataState: ITreeDataState = {
  id: '',
  data: ED_TECH_COMPONENT_RENDER_ROOT,
  error: null,
  isInitialized: false,
  loading: false,
};

export interface IEdTechUpdateStructurePayload {
  parentNode?: IEdTechRenderTreeData;
  items: IEdTechRenderTreeData[];
}

/**
 * Utility functions for tree operations
 */
export const TreeUtils = {
  /**
   * Find node in the tree by its path
   * @param tree - Root nodes array
   * @param path - Path in format "parent/child/grandchild"
   * @returns Object with node, parentArray, parentNode and index
   */
  findNodeByPath(
    tree: IEdTechRenderTreeData | undefined,
    path: string
  ): NodeSearchResult {
    // Default result
    const defaultResult: NodeSearchResult = {
      node: null,
      parentArray: [],
      parentNode: null,
      index: -1,
    };

    if (!path || !tree) return defaultResult;

    // Handle root node case
    if (tree.id === path) {
      return {
        node: tree,
        parentArray: [],
        parentNode: null,
        index: 0,
      };
    }

    // Clean the path to handle potential path formatting issues
    const cleanPath = path
      .replace(/\/+/g, '/')
      .replace(/^\//, '')
      .replace(/\/$/, '');

    // Split path and filter out empty segments
    const pathParts = cleanPath.split('/').filter((part) => part.length > 0);

    if (pathParts.length === 0) return defaultResult;

    // Handle root level nodes (no slashes in path)
    if (pathParts.length === 1) {
      if (!tree.subItems) return defaultResult;

      const index = tree.subItems.findIndex((node) => node.id === pathParts[0]);
      if (index !== -1) {
        return {
          node: tree.subItems[index],
          parentArray: tree.subItems,
          parentNode: tree,
          index,
        };
      }
      return defaultResult;
    }

    // Navigate through the path hierarchy
    let currentLevel = [tree];
    let parentNode: IEdTechRenderTreeData | null = tree;

    // Navigate to the level before the last one
    for (let i = 0; i < pathParts.length - 1; i++) {
      const nodeId = pathParts[i];

      if (!currentLevel || currentLevel.length === 0) return defaultResult;

      const nodeIndex = currentLevel.findIndex((item) => item?.id === nodeId);

      if (nodeIndex === -1) return defaultResult;

      parentNode = currentLevel[nodeIndex];

      // Ensure subItems exists
      if (!parentNode.subItems) {
        parentNode.subItems = [];
      }

      currentLevel = parentNode.subItems;
    }

    // Find node at the last level
    if (!currentLevel) return defaultResult;

    const lastNodeId = pathParts[pathParts.length - 1];
    const index = currentLevel.findIndex((item) => item?.id === lastNodeId);

    if (index !== -1) {
      return {
        node: currentLevel[index],
        parentArray: currentLevel,
        parentNode,
        index,
      };
    }

    return defaultResult;
  },

  /**
   * Get the target array based on parent node
   * @param tree - Root nodes array
   * @param parentNode - Optional parent node
   * @returns Target array where operations should be performed
   */
  getTargetArray(
    tree: IEdTechRenderTreeData | undefined,
    parentNode?: IEdTechRenderTreeData
  ): IEdTechRenderTreeData[] {
    // If tree is undefined, return empty array
    if (!tree) return [];

    // If no parent node, return the tree's subItems or empty array
    if (!parentNode) {
      return tree.subItems || [];
    }

    // If parent node has a path, use it to find the exact location
    if (parentNode.path) {
      const result = this.findNodeByPath(tree, parentNode.path);
      if (result.node) {
        if (!result.node.subItems) {
          result.node.subItems = [];
        }
        return result.node.subItems;
      }
    }

    // Fallback: Find by name at root level if path isn't available or node wasn't found
    if (tree.subItems) {
      const nodeIndex = tree.subItems.findIndex(
        (node) => node.name === parentNode.name
      );
      if (nodeIndex !== -1) {
        if (!tree.subItems[nodeIndex].subItems) {
          tree.subItems[nodeIndex].subItems = [];
        }
        return tree.subItems[nodeIndex].subItems || [];
      }
    }

    // Return empty array if parent node cannot be found
    return [];
  },

  /**
   * Sort items by order property recursively
   * @param items - Array to sort
   * @param recursive - Whether to sort subItems recursively
   */
  sortByOrder(items: IEdTechRenderTreeData[], recursive: boolean = true): void {
    items.sort((a, b) => a.order - b.order);

    // Sort subItems recursively if requested
    if (recursive) {
      items.forEach((item) => {
        if (item.subItems && item.subItems.length > 0) {
          this.sortByOrder(item.subItems, recursive);
        }
      });
    }
  },

  /**
   * Update paths for all children of a node
   * @param node - Parent node
   * @param basePath - Base path of the parent
   */
  updateChildPaths(node: IEdTechRenderTreeData, basePath: string): void {
    if (!node.subItems || node.subItems.length === 0) return;

    const nodePath = basePath ? `${basePath}/${node.id}` : node.id;

    node.subItems.forEach((child) => {
      child.path = `${nodePath}/${child.id}`;
      this.updateChildPaths(child, nodePath);
    });
  },
};
/**
 * Common function publish update EdTechRenderTreeData
 * @description This function is used to publish an update event for EdTechRenderTreeData.
 * IF not isEditing or forceUpdate is true, it will publish the update event.
 * @param state - The current state of the tree data slice
 * @param forceUpdate - Flag to force update the component
 * @returns void
 */
export const publishUpdateEdTechRenderTreeData = (
  state: ITreeDataState,
  forceUpdate: boolean = false
) => {
  // Use setTimeout to defer the execution outside of the reducer cycle
  if (forceUpdate) {
    pubsub.publish(EventChange.EdTechRenderTreeDataChange, {
      id: state.id,
      data: state.data,
    });
  }
};

/**
 * Common reducers for tree data slices
 */
export const treeDataReducers = {
  /**
   * Initialize tree data
   */
  initData: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<IEdTechRenderTreeData>
  ) => {
    const rootData = isEmpty(action.payload)
      ? ED_TECH_COMPONENT_RENDER_ROOT
      : action.payload;

    state.data = rootData;
    state.isInitialized = true;
  },
  /**
   * Add items to the tree
   */
  addItems: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<NodeWithItemsPayload>
  ) => {
    const { parentNode, items } = action.payload;

    // Early return if no items to add
    if (!items?.length) return;

    // Ensure subItems exists in the state data
    if (!state.data.subItems) {
      state.data.subItems = [];
    }

    // Get target array where items will be added
    const targetItems = TreeUtils.getTargetArray(state.data, parentNode);

    // Get parent path for use in creating child paths
    const parentPath = parentNode?.path || '';

    // Process and add each item
    items.forEach((item) => {
      if (!item.components) return;

      const newId = nanoid(8);
      const lastVersion = item.components[item.components.length - 1].version;

      const treeItem: IEdTechRenderTreeData = {
        ...item,
        id: newId,
        order: targetItems.length,
        path: parentPath ? `${parentPath}/${newId}` : newId,
        version: lastVersion,
        subItems: [],
      };

      // Update paths for any children if there's a parent path
      if (parentPath) {
        TreeUtils.updateChildPaths(treeItem, parentPath);
      }

      targetItems.push(treeItem);
    });

    // Sort items recursively
    TreeUtils.sortByOrder(targetItems, true);
    publishUpdateEdTechRenderTreeData(state);
  },

  /**
   * Update existing items in the tree
   */
  updateItems: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<{
      items: UpdateItemPayload[];
    }>
  ) => {
    const { items } = action.payload;

    if (!items || items.length === 0) return;

    // Ensure subItems exists
    if (!state.data.subItems) {
      state.data.subItems = [];
    }

    // Process each item to update
    items.forEach(({ path, data }) => {
      const result = TreeUtils.findNodeByPath(state.data, path);

      if (!result.node || result.index === -1) return;

      const { parentArray, index, node } = result;

      // Handle name or path changes that would affect paths
      const nameChanged = data.name !== undefined && data.name !== node.name;
      const pathChanged = data.path !== undefined && data.path !== node.path;

      // Create updated node by merging existing with new data
      const updatedNode = { ...node, ...data };

      // Special handling for path changes to move item to new location
      if (pathChanged) {
        // Remove from old position
        parentArray.splice(index, 1);

        // Parse the new path to find the new parent
        const newPathParts = updatedNode.path.split('/');
        newPathParts.pop(); // Remove the item's own ID from path
        const newParentPath = newPathParts.join('/');

        // Find the new parent node
        let newParentArray: IEdTechRenderTreeData[] = state.data.subItems || [];
        let newParentNode: IEdTechRenderTreeData | null = null;

        if (newParentPath) {
          const newParentResult = TreeUtils.findNodeByPath(
            state.data,
            newParentPath
          );
          if (newParentResult.node) {
            newParentNode = newParentResult.node;
            // Ensure the new parent has subItems array
            if (!newParentNode.subItems) {
              newParentNode.subItems = [];
            }
            newParentArray = newParentNode.subItems;
          } else {
            console.error(
              `Cannot find new parent node with path: ${newParentPath}`
            );
            return; // Skip this update if new parent not found
          }
        }

        // Set the order if not already set
        if (data.order === undefined) {
          updatedNode.order = newParentArray.length;
        }

        // Find appropriate position based on order
        const newIndex = newParentArray.findIndex(
          (item) => item.order > updatedNode.order
        );

        // Insert at new position
        if (newIndex === -1) {
          // If no appropriate position found, add to the end
          newParentArray.push(updatedNode);
        } else {
          // Insert at appropriate position
          newParentArray.splice(newIndex, 0, updatedNode);
        }

        // Update paths for all children
        if (updatedNode.subItems && updatedNode.subItems.length > 0) {
          TreeUtils.updateChildPaths(updatedNode, updatedNode.path);
        }
      }
      // Special handling for order changes to maintain sorting
      else if (data.order !== undefined && data.order !== node.order) {
        // Remove from old position
        parentArray.splice(index, 1);

        // Find appropriate position based on new order
        const newIndex = parentArray.findIndex(
          (item) => item.order > updatedNode.order
        );

        // Insert at new position
        if (newIndex === -1) {
          // If no appropriate position found, add to the end
          parentArray.push(updatedNode);
        } else {
          // Insert at appropriate position
          parentArray.splice(newIndex, 0, updatedNode);
        }

        // Update paths if name changed
        if (nameChanged && updatedNode.subItems) {
          TreeUtils.updateChildPaths(
            updatedNode,
            updatedNode.path || result.parentNode?.path || ''
          );
        }
      } else {
        // Update node at current position (safe with Immer)
        // Check if both parentArray[index] and data are valid objects before assigning
        if (
          parentArray[index] &&
          data &&
          typeof parentArray[index] === 'object' &&
          typeof data === 'object'
        ) {
          Object.assign(parentArray[index], data);

          // Update paths if name changed
          if (nameChanged && parentArray[index].subItems) {
            TreeUtils.updateChildPaths(
              parentArray[index],
              parentArray[index].path || result.parentNode?.path || ''
            );
          }
        } else {
          // update chính nó
          Object.assign(node, data);
        }
      }
    });
    // Publish update event
    publishUpdateEdTechRenderTreeData(state);
  },

  /**
   * Add new items or update existing ones
   */
  addOrUpdateItems: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<NodeWithStructurePayload>
  ) => {
    const { parentNode, items } = action.payload;

    if (!items || items.length === 0) return;

    // Ensure subItems exists
    if (!state.data.subItems) {
      state.data.subItems = [];
    }

    const targetItems = TreeUtils.getTargetArray(state.data, parentNode);
    const parentPath = parentNode?.path || '';

    // Process each item
    items.forEach((item) => {
      // Check if item already exists by ID
      const existingIndex = targetItems.findIndex(
        (existingItem) => existingItem.id === item.id
      );

      // Create a deep copy of the item to avoid reference issues
      const newItem: IEdTechRenderTreeData = {
        ...item,
        id: item.id || nanoid(8),
        order:
          existingIndex !== -1
            ? targetItems[existingIndex].order
            : targetItems.length,
        path: parentPath ? `${parentPath}/${item.id}` : item.id,
        subItems: item.subItems ? [...item.subItems] : [],
      };

      // Update paths for all children recursively
      const updatePaths = (node: IEdTechRenderTreeData, basePath: string) => {
        if (!node.subItems || node.subItems.length === 0) return;

        node.subItems = node.subItems.map((child) => ({
          ...child,
          path: `${basePath}/${child.id}`,
          subItems: child.subItems ? [...child.subItems] : [],
        }));

        node.subItems.forEach((child) => {
          updatePaths(child, child.path);
        });
      };

      updatePaths(newItem, newItem.path);

      if (existingIndex !== -1) {
        // Update existing item
        targetItems[existingIndex] = newItem;
      } else {
        // Add new item
        targetItems.push(newItem);
      }
    });

    // Sort items recursively
    TreeUtils.sortByOrder(targetItems, true);

    // Publish update event
    publishUpdateEdTechRenderTreeData(state);
  },

  /**
   * Remove multiple items from the tree
   */
  removeItem: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<{
      targetNode: IEdTechRenderTreeData | IEdTechRenderTreeData[];
    }>
  ) => {
    const { targetNode: targetNodes } = action.payload;

    // Handle both single node and array of nodes
    const nodesToRemove = Array.isArray(targetNodes)
      ? targetNodes
      : [targetNodes];

    if (nodesToRemove.length === 0 || !state.data) return;

    // Function to remove a node from an array
    const removeNodeFromArray = (
      array: IEdTechRenderTreeData[],
      nodes: IEdTechRenderTreeData[]
    ): boolean => {
      let anyRemoved = false;

      // First try to remove nodes directly at this level
      for (const node of nodes) {
        // Skip root node
        if (node.name === ED_TECH_COMPONENT_RENDER_ROOT.name) {
          continue;
        }

        // Find by id
        const index = array.findIndex((item) => item.id === node.id);

        if (index !== -1) {
          array.splice(index, 1);
          anyRemoved = true;
          // Continue checking other nodes
        }
      }

      // Then recursively check in subItems for any remaining nodes
      const remainingNodes = nodes.filter(
        (node) =>
          (node.name !== ED_TECH_COMPONENT_RENDER_ROOT.name && !anyRemoved) ||
          !array.some((item) => item.id === node.id)
      );

      if (remainingNodes.length > 0) {
        // If there are still nodes to remove, check in subItems
        for (const item of array) {
          if (item.subItems && item.subItems.length > 0) {
            if (removeNodeFromArray(item.subItems, remainingNodes)) {
              anyRemoved = true;
            }
          }
        }
      }

      return anyRemoved;
    };

    if (state.data.subItems) {
      removeNodeFromArray(state.data.subItems, nodesToRemove);
    }

    // Publish update event
    publishUpdateEdTechRenderTreeData(state);
  },

  /**
   * Clear all tree data
   */
  clearData: (state: Draft<ITreeDataState>) => {
    state.data = ED_TECH_COMPONENT_RENDER_ROOT;
    state.id = '';
  },

  /**
   * Set loading state
   */
  setLoading: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<boolean>
  ) => {
    state.loading = action.payload;
  },

  /**
   * Set error state
   */
  setError: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<string | null>
  ) => {
    state.error = action.payload;
  },

  /**
   * Add or update an existing tree structure under a parent node
   */
  addOrUpdateTreeStructure: (
    state: Draft<ITreeDataState>,
    action: PayloadAction<IEdTechUpdateStructurePayload>
  ) => {
    const { parentNode, items } = action.payload;

    if (!items || items.length === 0) return;
    if (!parentNode) {
      state.data = items[0];
      return;
    }
    // Ensure subItems exists
    if (!state.data.subItems) {
      state.data.subItems = [];
    }

    const targetItems = TreeUtils.getTargetArray(state.data, parentNode);
    const parentPath = parentNode?.path || '';

    // Process each item
    items.forEach((item) => {
      // Check if item already exists by ID
      const existingIndex = targetItems.findIndex(
        (existingItem) => existingItem.id === item.id
      );

      // Create a deep copy of the item to avoid reference issues
      const newItem: IEdTechRenderTreeData = {
        ...item,
        id: item.id || nanoid(8),
        order:
          existingIndex !== -1
            ? targetItems[existingIndex].order
            : targetItems.length,
        path: parentPath ? `${parentPath}/${item.id}` : item.id,
        subItems: item.subItems ? [...item.subItems] : [],
      };

      // Update paths for all children recursively
      const updatePaths = (node: IEdTechRenderTreeData, basePath: string) => {
        if (!node.subItems || node.subItems.length === 0) return;

        node.subItems = node.subItems.map((child) => ({
          ...child,
          path: `${basePath}/${child.id}`,
          subItems: child.subItems ? [...child.subItems] : [],
        }));

        node.subItems.forEach((child) => {
          updatePaths(child, child.path);
        });
      };

      updatePaths(newItem, newItem.path);

      if (existingIndex !== -1) {
        // Update existing item
        targetItems[existingIndex] = newItem;
      } else {
        // Add new item
        targetItems.push(newItem);
      }
    });

    // Sort items recursively
    TreeUtils.sortByOrder(targetItems, true);

    // Publish update event
    publishUpdateEdTechRenderTreeData(state);
  },
};

export const toggleEditing = createAction('ED_TECH_EDITING');
// export const applyTempToData = createAction('ED_TECH_APPLY_TEMP_TO_DATA');
export const setId = createAction('ED_TECH_SET_ID');
export const applyTempToData = createAsyncThunk(
  'ED_TECH_APPLY_TEMP_TO_DATA',
  async (_, { getState }) => {
    const state = getState();
    return state;
  }
);

/**
 * Factory function to create a tree data slice with common reducers
 * @param sliceName - Name of the slice
 * @returns A configured slice object with common reducers
 */
export function createTreeDataSlice(sliceName: string) {
  return createSlice({
    name: sliceName,
    initialState: initialTreeDataState,
    reducers: treeDataReducers,
    extraReducers: (builder) => {
      /**
       * toggle Editing
       */

      /**
       * Apply temp to data
       */
      builder.addCase(applyTempToData.fulfilled, (_, action) => {
        const payload = action.payload as any;
        publishUpdateEdTechRenderTreeData(payload.edTechRenderTreeData, true);
      });
      /**
       * setId
       */
      builder.addCase(setId, (state, action: any) => {
        state.id = action.payload;
      });
    },
  });
}

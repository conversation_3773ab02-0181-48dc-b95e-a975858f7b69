import React from 'react';
import { Input, Select } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

const { Option } = Select;

interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  tags: string[];
  selectedTags: string[];
  onTagsChange: (value: string[]) => void;
}

const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchTerm,
  onSearchChange,
  tags,
  selectedTags,
  onTagsChange,
}) => {
  // X<PERSON> lý khi người dùng thay đổi lựa chọn tags
  const handleTagsChange = (value: string[]) => {
    // Nếu không chọn tag nào, tức là chọn tất cả
    if (value.length === 0) {
      onTagsChange([]);
    } else {
      onTagsChange(value);
    }
  };

  return (
    <div className="tailwind-mb-6 tailwind-flex tailwind-flex-col tailwind-gap-4 tailwind-w-full">
      <div className="tailwind-flex tailwind-flex-col sm:tailwind-flex-row tailwind-gap-4 tailwind-w-full">
        <Input
          placeholder="Tìm kiếm component..."
          prefix={<SearchOutlined className="tailwind-text-gray-400" />}
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="tailwind-flex-1 tailwind-min-w-0"
          size="large"
        />

        <Select
          className="tailwind-w-full sm:tailwind-w-96 tailwind-flex-shrink-0"
          mode="multiple"
          value={selectedTags}
          onChange={handleTagsChange}
          size="large"
          placeholder="Chọn danh mục"
          allowClear
          maxTagCount={3}
        >
          {tags.map((tag) => (
            <Option key={tag.toString()} value={tag.toString()}>
              {tag}
            </Option>
          ))}
        </Select>
      </div>
    </div>
  );
};

export default SearchAndFilter;

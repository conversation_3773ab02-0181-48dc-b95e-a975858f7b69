/**
 * Converts all keys in an object to camelCase (only lowercases the first character)
 * @param obj The object whose keys need to be converted to camelCase
 * @returns A new object with all keys in camelCase
 */
export function camelCaseKeys<T extends Record<string, any>>(
  obj: T
): Record<string, any> {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => camelCaseKeys(item));
  }

  return Object.keys(obj).reduce((result, key) => {
    const camelKey = toCamelCase(key);
    const value = obj[key];

    result[camelKey] =
      typeof value === 'object' && value !== null
        ? camelCaseKeys(value)
        : value;

    return result;
  }, {} as Record<string, any>);
}

/**
 * Converts a string to camelCase by simply lowercasing the first character
 * Example: 'DifficultyLevel' becomes 'difficultyLevel'
 */
function toCamelCase(str: string): string {
  if (!str || str.length === 0) return str;

  // Simply lowercase the first character and keep the rest as is
  return str.charAt(0).toLowerCase() + str.slice(1);
}

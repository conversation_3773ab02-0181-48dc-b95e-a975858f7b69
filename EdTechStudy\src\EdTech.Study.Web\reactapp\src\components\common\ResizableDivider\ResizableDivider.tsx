import React, { useRef, useEffect, useState } from 'react';
import './ResizableDivider.css';

interface ResizableDividerProps {
  onChange: (leftPanelWidth: number) => void;
  initialPosition?: number; // Percentage (0-100)
  minWidth?: number; // Minimum width for panels in pixels
}

const ResizableDivider: React.FC<ResizableDividerProps> = ({
  onChange,
  initialPosition = 50,
  minWidth = 300,
}) => {
  const dividerRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef<boolean>(false);
  const [isDragging, setIsDragging] = useState(false);

  // Set initial position
  useEffect(() => {
    if (dividerRef.current) {
      // Position divider at the specified percentage
      dividerRef.current.style.left = `${initialPosition}%`;
    }
  }, [initialPosition]);

  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      isDraggingRef.current = true;
      setIsDragging(true);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    };

    const handleMouseUp = () => {
      if (isDraggingRef.current) {
        isDraggingRef.current = false;
        setIsDragging(false);
        document.body.style.removeProperty('cursor');
        document.body.style.removeProperty('user-select');
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current) return;

      const container = dividerRef.current?.parentElement;
      if (!container) return;

      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Calculate the new position based on mouse position
      const newPosition =
        ((e.clientX - containerRect.left) / containerWidth) * 100;

      // Check minimum width constraints
      const minWidthPercent = (minWidth / containerWidth) * 100;

      if (
        newPosition < minWidthPercent ||
        newPosition > 100 - minWidthPercent
      ) {
        return;
      }

      // Update divider position
      if (dividerRef.current) {
        dividerRef.current.style.left = `${newPosition}%`;
      }

      // Call the onChange callback with the new position
      onChange(newPosition);
    };

    const divider = dividerRef.current;
    divider?.addEventListener('mousedown', handleMouseDown);

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      divider?.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [onChange, minWidth]);

  return (
    <div
      ref={dividerRef}
      className={`resizable-divider ${isDragging ? 'active' : ''}`}
    />
  );
};

export default ResizableDivider;

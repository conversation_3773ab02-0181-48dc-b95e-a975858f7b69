import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';
import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';
import { canNodeHaveChildren } from './treeUtils';

// Component type identifiers
const COMPONENT_TYPES = {
  TITLE: 'LessonTitleComponent',
  COLUMN: 'LessonColumnComponent',
  ROW: 'LessonRowComponent',
};

// Engine component types
const ENGINE_COMPONENT_TYPES = [
  ETypeEdTechComponent.SIMULATORS,
  ETypeEdTechComponent.MINI_GAMES,
  ETypeEdTechComponent.QUIZ,
];

/**
 * Check if a node is an engine component
 */
export const isEngineComponent = (
  node: IEdTechRenderTreeData | null
): boolean => {
  if (!node || !node.type) return false;
  return ENGINE_COMPONENT_TYPES.includes(node.type);
};

/**
 * Check if a node is a title component
 */
export const isTitleComponent = (
  node: IEdTechRenderTreeData | null
): boolean => {
  if (!node) return false;
  return node.name === COMPONENT_TYPES.TITLE;
};

/**
 * Check if a node is an engine or title component
 */
export const isEngineOrTitleComponent = (
  node: IEdTechRenderTreeData | null
): boolean => {
  return isEngineComponent(node) || isTitleComponent(node);
};

/**
 * Check if a node is a column
 */
export const isColumnComponent = (
  node: IEdTechRenderTreeData | null
): boolean => {
  return node?.name === COMPONENT_TYPES.COLUMN;
};

/**
 * Check if a node is a row
 */
export const isRowComponent = (node: IEdTechRenderTreeData | null): boolean => {
  return node?.name === COMPONENT_TYPES.ROW;
};

/**
 * Find parent and items for a given path
 */
export const findParentAndItems = (
  data: IEdTechRenderTreeData[],
  path: string[]
): {
  parentNode: IEdTechRenderTreeData | null;
  items: IEdTechRenderTreeData[];
} => {
  if (path.length <= 1) {
    return { parentNode: null, items: data };
  }

  let currentItems = data;
  let parentNode: IEdTechRenderTreeData | null = null;

  for (let i = 0; i < path.length - 1; i++) {
    const nodeId = path[i];
    const foundNode = currentItems.find((item) => item.id === nodeId);

    if (!foundNode) {
      console.error(
        `Node with ID ${nodeId} not found in path ${path.join('/')}`
      );
      return { parentNode: null, items: [] };
    }

    if (i === path.length - 2) {
      parentNode = foundNode;
      currentItems = foundNode.subItems || [];
      return { parentNode, items: currentItems };
    }

    currentItems = foundNode.subItems || [];
  }

  return { parentNode: null, items: [] };
};

/**
 * Update child paths recursively
 * @returns An array of path updates for all children
 */
export const updateChildPaths = (
  node: IEdTechRenderTreeData,
  basePath: string
): { oldPath: string; newPath: string }[] => {
  if (!node.subItems || node.subItems.length === 0) return [];

  const pathUpdates: { oldPath: string; newPath: string }[] = [];

  node.subItems.forEach((child) => {
    const oldPath = child.path || child.id;
    const newPath = `${basePath}/${child.id}`;

    if (oldPath !== newPath) {
      child.path = newPath;
      pathUpdates.push({ oldPath, newPath });
    }

    // Add child updates recursively
    const childUpdates = updateChildPaths(child, newPath);
    pathUpdates.push(...childUpdates);
  });

  return pathUpdates;
};

/**
 * Update item orders
 */
export const updateItemOrders = (items: IEdTechRenderTreeData[]): void => {
  items.forEach((item, index) => {
    item.order = index;
  });
};

/**
 * Calculate insert index based on hover position and drop position
 */
export const calculateInsertIndex = (hoverIndex: number): number => {
  // Currently just returns the hover index, but could be enhanced to handle
  // more complex logic if needed in the future
  return hoverIndex;
};

/**
 * Find parent node
 */
export const findParentNode = (
  items: IEdTechRenderTreeData[],
  targetId: string
): IEdTechRenderTreeData | null => {
  for (const item of items) {
    if (
      item.subItems &&
      item.subItems.some((subItem) => subItem.id === targetId)
    ) {
      return item;
    }
    if (item.subItems) {
      const parent = findParentNode(item.subItems, targetId);
      if (parent) return parent;
    }
  }
  return null;
};

/**
 * Build path for a node
 */
export const buildPath = (
  items: IEdTechRenderTreeData[],
  targetId: string,
  currentPath: string[] = []
): string[] | null => {
  for (const item of items) {
    if (item.id === targetId) {
      return [...currentPath, item.id];
    }
    if (item.subItems) {
      const path = buildPath(item.subItems, targetId, [
        ...currentPath,
        item.id,
      ]);
      if (path) return path;
    }
  }
  return null;
};

/**
 * Find node index in items array
 */
export const findNodeIndex = (
  items: IEdTechRenderTreeData[],
  nodeId: string
): number => {
  return items.findIndex((item) => item.id === nodeId);
};

/**
 * Validates a drag and drop operation based on component types
 * @returns Object containing validation result and error message if invalid
 */
export const validateDragAndDrop = (
  dragItem: IEdTechRenderTreeData | null,
  dropItem: IEdTechRenderTreeData | null,
  dropParentNode: IEdTechRenderTreeData | null,
  dropPosition: 'top' | 'bottom' | 'child' | null
): { isValid: boolean; errorMessage?: string } => {
  if (!dragItem || !dropItem) {
    return { isValid: false, errorMessage: 'Không tìm thấy mục kéo hoặc thả' };
  }

  // Case 1: Dragging a column
  if (isColumnComponent(dragItem)) {
    if (dropPosition === 'child') {
      // Target must be a row when dropped as child
      if (!isRowComponent(dropItem)) {
        return {
          isValid: false,
          errorMessage: 'Cột chỉ có thể được đặt bên trong hàng',
        };
      }
    } else if (dropPosition === 'top' || dropPosition === 'bottom') {
      // Parent must be a row when dropped as sibling
      if (!isRowComponent(dropParentNode)) {
        return {
          isValid: false,
          errorMessage: 'Cột chỉ có thể được đặt bên trong hàng',
        };
      }
    }
  }

  // Case 2: Dropping into a column
  if (isColumnComponent(dropItem) && dropPosition === 'child') {
    if (isColumnComponent(dragItem)) {
      return {
        isValid: false,
        errorMessage: 'Không thể đặt cột bên trong cột',
      };
    }
  }

  // Case 3: Dragging an engine or title component
  if (isEngineOrTitleComponent(dragItem)) {
    if (dropPosition === 'child') {
      // Target must be a column when dropped as child
      if (!isColumnComponent(dropItem)) {
        return {
          isValid: false,
          errorMessage: 'Engine và tiêu đề chỉ có thể được đặt bên trong cột',
        };
      }
    } else if (dropPosition === 'top' || dropPosition === 'bottom') {
      // Parent must be a column when dropped as sibling
      if (!isColumnComponent(dropParentNode)) {
        return {
          isValid: false,
          errorMessage: 'Engine và tiêu đề chỉ có thể được đặt bên trong cột',
        };
      }
    }
  }

  // Case 4: Check if target can have children
  if (dropPosition === 'child' && !canNodeHaveChildren(dropItem)) {
    return {
      isValid: false,
      errorMessage: 'Mục này không thể chứa các mục con',
    };
  }

  return { isValid: true };
};

/**
 * Creates path update objects for Redux
 */
export const createPathUpdates = (
  oldPath: string,
  newPath: string,
  order: number,
  childUpdates: { oldPath: string; newPath: string }[] = []
): { path: string; data: any }[] => {
  const updates: { path: string; data: any }[] = [
    {
      path: oldPath,
      data: { path: newPath, order },
    },
  ];

  // Add child path updates
  childUpdates.forEach((update) => {
    updates.push({
      path: update.oldPath,
      data: { path: update.newPath },
    });
  });

  return updates;
};

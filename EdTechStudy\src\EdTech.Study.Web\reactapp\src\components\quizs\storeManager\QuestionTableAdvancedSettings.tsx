import React, { useState, useEffect } from 'react';
import {
  Drawer,
  Typography,
  Checkbox,
  Space,
  Divider,
  Select,
  InputNumber,
  Button,
} from 'antd';
import {
  TableOutlined,
  BookOutlined,
  SettingOutlined,
  TagsOutlined,
  ProfileOutlined,
} from '@ant-design/icons';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

const { Title, Text } = Typography;
const { Option } = Select;

export interface TableColumn {
  key: string;
  title: string;
  visible: boolean;
  width: number;
}

export interface Subject {
  id: string;
  name: string;
  code: string;
}

interface QuestionTableAdvancedSettingsProps {
  visible: boolean;
  columns: TableColumn[];
  subjects: Subject[];
  selectedSubjectIds: string[];
  pageSize: number;
  onColumnsChange: (columns: TableColumn[]) => void;
  onSubjectFilterChange: (subjectIds: string[]) => void;
  onPageSizeChange: (pageSize: number) => void;
  onDrawerClose: () => void;
}

const QuestionTableAdvancedSettings: React.FC<
  QuestionTableAdvancedSettingsProps
> = ({
  visible,
  columns,
  subjects,
  selectedSubjectIds,
  pageSize,
  onColumnsChange,
  onSubjectFilterChange,
  onPageSizeChange,
  onDrawerClose,
}) => {
  // Make sure we have all required columns - especially tags and topics
  const ensureAllColumns = (cols: TableColumn[]): TableColumn[] => {
    const requiredColumns = [
      { key: 'id', title: 'ID', visible: true, width: 100 },
      { key: 'type', title: 'Loại câu hỏi', visible: true, width: 150 },
      { key: 'title', title: 'Tiêu đề', visible: true, width: 150 },
      { key: 'preview', title: 'Nội dung', visible: true, width: 500 },
      { key: 'points', title: 'Độ khó', visible: false, width: 80 },
      { key: 'statusEntity', title: 'Trạng thái', visible: true, width: 120 },
      { key: 'tags', title: 'Thẻ', visible: true, width: 150 },
      { key: 'topics', title: 'Chủ đề', visible: true, width: 150 },
      { key: 'creationTime', title: 'Ngày tạo', visible: false, width: 150 },
      {
        key: 'lastModificationTime',
        title: 'Ngày cập nhật',
        visible: false,
        width: 150,
      },
      { key: 'subject', title: 'Môn học', visible: false, width: 150 },
      { key: 'action', title: 'Thao tác', visible: true, width: 100 },
    ];

    // Create a map of existing columns
    const existingColumnsMap = new Map<string, TableColumn>();
    cols.forEach((col) => {
      existingColumnsMap.set(col.key, col);
    });

    // Return updated columns ensuring all required ones exist
    return requiredColumns.map((requiredCol) => {
      return existingColumnsMap.get(requiredCol.key) || requiredCol;
    });
  };

  // Initialize local columns with ensured columns
  const [localColumns, setLocalColumns] = useState<TableColumn[]>(
    ensureAllColumns(columns)
  );

  const [localSelectedSubjectIds, setLocalSelectedSubjectIds] =
    useState<string[]>(selectedSubjectIds);
  const [localPageSize, setLocalPageSize] = useState<number>(pageSize);

  // Update local state when props change, ensuring all columns exist
  useEffect(() => {
    setLocalColumns(ensureAllColumns(columns));
  }, [columns]);

  useEffect(() => {
    setLocalSelectedSubjectIds(selectedSubjectIds);
  }, [selectedSubjectIds]);

  useEffect(() => {
    setLocalPageSize(pageSize);
  }, [pageSize]);

  // Handle column visibility toggling
  const handleColumnVisibilityChange = (
    key: string,
    e: CheckboxChangeEvent
  ) => {
    const newColumns = localColumns.map((col) =>
      col.key === key ? { ...col, visible: e.target.checked } : col
    );
    setLocalColumns(newColumns);
    onColumnsChange(newColumns);
  };

  // Handle subject filter change
  const handleSubjectFilterChange = (value: string[]) => {
    setLocalSelectedSubjectIds(value);
    onSubjectFilterChange(value);
  };

  // Handle page size change
  const handlePageSizeChange = (value: number | null) => {
    if (value !== null) {
      setLocalPageSize(value);
      onPageSizeChange(value);
    }
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <SettingOutlined style={{ marginRight: 8 }} />
          <span>Cài đặt nâng cao</span>
        </div>
      }
      placement="right"
      onClose={onDrawerClose}
      open={visible}
      width={350}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* Column visibility section */}
        <div>
          <Title level={5} style={{ display: 'flex', alignItems: 'center' }}>
            <TableOutlined style={{ marginRight: 8 }} />
            Cột hiển thị
          </Title>
          <div style={{ marginLeft: 8 }}>
            {localColumns.map((column) => (
              <div key={column.key} style={{ marginBottom: 8 }}>
                <Checkbox
                  checked={column.visible}
                  onChange={(e) => handleColumnVisibilityChange(column.key, e)}
                  disabled={column.key === 'action'} // Action column always visible
                >
                  {column.title}
                </Checkbox>
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Subject filter section */}
        <div>
          <Title level={5} style={{ display: 'flex', alignItems: 'center' }}>
            <BookOutlined style={{ marginRight: 8 }} />
            Lọc theo môn học
          </Title>
          <Select
            mode="multiple"
            style={{ width: '100%' }}
            placeholder="Chọn môn học cần lọc"
            value={localSelectedSubjectIds}
            onChange={handleSubjectFilterChange}
            optionFilterProp="children"
          >
            {subjects.map((subject) => (
              <Option key={subject.id} value={subject.id}>
                {subject.name} ({subject.code})
              </Option>
            ))}
          </Select>
        </div>

        <Divider />

        {/* Page size section */}
        <div>
          <Title level={5}>Số dòng mỗi trang</Title>
          <InputNumber
            min={10}
            max={100}
            value={localPageSize}
            onChange={handlePageSizeChange}
            style={{ width: 120 }}
          />
          <Text type="secondary" style={{ marginLeft: 8 }}>
            (10-100 dòng)
          </Text>
        </div>

        <Divider />

        {/* Reset button */}
        <Button
          onClick={() => {
            // Reset to all columns visible
            const resetColumns = localColumns.map((col) => ({
              ...col,
              visible: true,
            }));
            setLocalColumns(resetColumns);
            onColumnsChange(resetColumns);

            // Reset other settings
            setLocalSelectedSubjectIds([]);
            onSubjectFilterChange([]);
            setLocalPageSize(10);
            onPageSizeChange(10);
          }}
        >
          Đặt lại mặc định
        </Button>
      </Space>
    </Drawer>
  );
};

export default QuestionTableAdvancedSettings;

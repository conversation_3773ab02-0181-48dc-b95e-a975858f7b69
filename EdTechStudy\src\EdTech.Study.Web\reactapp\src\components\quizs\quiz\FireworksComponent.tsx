import React, { useEffect, useState } from 'react';

interface FireworkParticle {
  id: number;
  x: number;
  y: number;
  color: string;
}

interface ConfettiParticle {
  id: number;
  x: number;
  size: number;
  color: string;
  rotation: number;
  type: 'square' | 'circle';
}

interface FireworksProps {
  show: boolean;
  duration?: number;
}

const FireworksComponent: React.FC<FireworksProps> = ({
  show,
  duration = 1500,
}) => {
  const [fireworks, setFireworks] = useState<FireworkParticle[]>([]);
  const [confetti, setConfetti] = useState<ConfettiParticle[]>([]);

  // Generate random color
  const randomColor = () => {
    const colors = [
      '#ff0000',
      '#00ff00',
      '#0000ff',
      '#ffff00',
      '#ff00ff',
      '#00ffff',
      '#ff8000',
      '#8000ff',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Create firework particles
  const createFireworks = () => {
    const numberOfFireworks = 8;
    const numberOfParticles = 12;
    const newFireworks: FireworkParticle[] = [];

    // Create multiple fireworks at different positions
    for (let f = 0; f < numberOfFireworks; f++) {
      const centerX = Math.random() * window.innerWidth;
      const centerY = 100 + Math.random() * (window.innerHeight - 200);
      const color = randomColor();

      // Create particles for each firework
      for (let i = 0; i < numberOfParticles; i++) {
        const angle = (Math.PI * 2 * i) / numberOfParticles;
        const x = centerX + Math.cos(angle) * 50;
        const y = centerY + Math.sin(angle) * 50;

        newFireworks.push({
          id: Date.now() + i + f * 100,
          x: x,
          y: y,
          color,
        });
      }
    }

    setFireworks(newFireworks);
  };

  // Create confetti particles
  const createConfetti = () => {
    const numberOfConfetti = 100;
    const newConfetti: ConfettiParticle[] = [];

    for (let i = 0; i < numberOfConfetti; i++) {
      newConfetti.push({
        id: Date.now() + i,
        x: Math.random() * window.innerWidth,
        size: 5 + Math.random() * 10,
        color: randomColor(),
        rotation: Math.random() * 360,
        type: Math.random() > 0.5 ? 'square' : 'circle',
      });
    }

    setConfetti(newConfetti);
  };

  useEffect(() => {
    if (show) {
      createFireworks();
      createConfetti();

      // Clear fireworks and confetti after duration
      const timer = setTimeout(() => {
        setFireworks([]);
        setConfetti([]);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [show, duration]);

  if (!show) return null;

  return (
    <>
      <div className="firework-container">
        {fireworks.map((fw) => (
          <div
            key={fw.id}
            className="firework"
            style={
              {
                left: `${fw.x}px`,
                top: `${fw.y}px`,
                backgroundColor: fw.color,
                '--x': `${Math.random() * 100 - 50}px`,
                '--y': `${Math.random() * 100 - 50}px`,
              } as React.CSSProperties
            }
          />
        ))}
      </div>

      <div className="confetti-container">
        {confetti.map((conf) => (
          <div
            key={conf.id}
            className="confetti"
            style={{
              left: `${conf.x}px`,
              top: '-10px',
              width: `${conf.size}px`,
              height: `${conf.size}px`,
              backgroundColor: conf.color,
              borderRadius: conf.type === 'circle' ? '50%' : '0',
              transform: `rotate(${conf.rotation}deg)`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>
    </>
  );
};

export default FireworksComponent;

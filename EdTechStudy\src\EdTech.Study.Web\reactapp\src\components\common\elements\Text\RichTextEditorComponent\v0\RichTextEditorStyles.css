/* Import Syncfusion theme styles */
@import '@syncfusion/ej2-base/styles/bootstrap5.css';
@import '@syncfusion/ej2-icons/styles/bootstrap5.css';
@import '@syncfusion/ej2-buttons/styles/bootstrap5.css';
@import '@syncfusion/ej2-splitbuttons/styles/bootstrap5.css';
@import '@syncfusion/ej2-inputs/styles/bootstrap5.css';
@import '@syncfusion/ej2-lists/styles/bootstrap5.css';
@import '@syncfusion/ej2-navigations/styles/bootstrap5.css';
@import '@syncfusion/ej2-popups/styles/bootstrap5.css';
@import '@syncfusion/ej2-richtexteditor/styles/bootstrap5.css';

/* Editor container styles */
.e-richtexteditor {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Toolbar styles */
.e-richtexteditor .e-toolbar {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: var(--edtt-color-bg-secondary, #f8f9fa);
  border-color: var(--edtt-color-border-light, #e0e0e0);
  min-height: 50px; /* Minimum toolbar height */
  padding: 4px 8px;
  height: auto !important;
  position: relative;
  z-index: 10;
}

/* Toolbar items container - enable wrapping */
.e-richtexteditor .e-toolbar .e-toolbar-items {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%; /* Ensure full width */
}

/* Ensure all toolbar items are properly aligned */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item {
  display: flex;
  align-items: center;
  vertical-align: middle;
}

.e-toolbar .e-toolbar-item .e-tbar-btn {
  min-width: 26px;
}

.e-richtexteditor .e-rte-toolbar.e-toolbar.e-extended-toolbar .e-toolbar-items .e-dropdown-btn .e-rte-color-content{
  height: auto;
}

/* Toolbar dropdown styles */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn .e-btn-icon,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn .e-btn-text {
  color: var(--edtt-color-text-secondary, #434343);
  font-weight: 500;
}

/* Toolbar button styles */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn {
  border-radius: 4px;
  margin: 2px;
  color: var(--edtt-color-text-secondary, #434343);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* Toolbar item styles for better wrapping */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item {
  margin: 2px 0;
  display: flex;
  align-items: center;
}

/* Fix for multi-line toolbar */
.e-richtexteditor .e-toolbar.e-extended-toolbar,
.e-richtexteditor .e-toolbar.e-extended-toolbar .e-toolbar-items {
  height: auto !important;
  min-height: 50px;
  display: flex;
  flex-wrap: wrap;
}

/* Ensure content is properly positioned when toolbar wraps */
.e-richtexteditor .e-toolbar.e-extended-toolbar + .e-rte-content {
  position: relative;
  top: 0 !important;
  margin-top: 0 !important;
}

/* Separator styles for wrapped toolbar */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-separator {
  height: 24px;
  margin: 2px 5px;
  display: inline-flex;
}

/* Icon styles for better visibility */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn .e-icons {
  font-weight: bold;
  font-size: 16px;
  color: #333333; /* Darker color for better visibility */
}

.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn:hover,
.e-richtexteditor .e-rte-fontcolor-dropdown:hover .e-rte-color-content:hover,
.e-richtexteditor .e-rte-fontcolor-dropdown:hover .e-caret:hover,
.e-richtexteditor .e-rte-backgroundcolor-dropdown:hover .e-rte-color-content:hover,
.e-richtexteditor .e-rte-backgroundcolor-dropdown:hover .e-caret:hover {
  background-color: var(--edtt-color-bg-spotlight, rgba(234, 76, 137, 0.15));
  color: var(--edtt-color-primary, #EA4C89);
}

/* Enhance icon visibility on hover */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn:hover .e-icons,
.e-richtexteditor .e-toolbar-item .e-tbar-btn:hover .e-icons,
.e-richtexteditor .e-rte-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn:hover .e-icons,
.e-richtexteditor .e-rte-backgroundcolor-dropdown:hover .e-rte-color-content:hover .e-icons,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn:hover .e-btn-text,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn:hover .e-rte-dropdown-btn-text
{
  transform: scale(1.1);
  color: var(--edtt-color-primary, #EA4C89);
  font-weight: bold;
}

.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-active button.e-tbar-btn,
.e-richtexteditor .e-dropdown-btn.e-active,
.e-richtexteditor .e-toolbar-item .e-rte-fontcolor-dropdown.e-active,
.e-richtexteditor .e-toolbar-item .e-rte-backgroundcolor-dropdown.e-active {
  background-color: var(--edtt-color-bg-spotlight, rgba(234, 76, 137, 0.15));
  color: var(--edtt-color-primary, #EA4C89);
}

/* Make icons more visible in active state */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-active button.e-tbar-btn .e-icons,
.e-richtexteditor .e-rte-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn:active .e-icons,
.e-richtexteditor .e-rte-toolbar .e-toolbar-item .e-dropdown-btn.e-active .e-icons,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-active button.e-tbar-btn .e-btn-text,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-active button.e-tbar-btn .e-rte-dropdown-btn-text,
.e-richtexteditor .e-toolbar .e-toolbar-item .e-dropdown-btn.e-active .e-rte-dropdown-btn-text{
  transform: scale(1.1);
  color: var(--edtt-color-primary, #EA4C89);
  font-weight: bold;
}

/* Content area styles */
.e-richtexteditor .e-rte-content {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-color: #e0e0e0;
  min-height: 800px;
  max-height: 800px;
  overflow-y: auto !important; /* Chỉ giữ một thanh cuộn ở container cha */
  scrollbar-width: none !important; /* Ẩn thanh cuộn trên Firefox */
  -ms-overflow-style: none !important; /* Ẩn thanh cuộn trên IE và Edge */
  display: block !important; /* Thay đổi từ flex thành block để tránh vấn đề double scrollbar */
  position: relative; /* Ensure proper positioning */
  z-index: 5; /* Lower than toolbar z-index */
  margin-top: 0 !important; /* Ensure no unexpected margin */
  clear: both; /* Ensure it clears any floating elements */
}

/* Ẩn thanh cuộn trên Chrome, Safari và các trình duyệt WebKit khác */
.e-richtexteditor .e-rte-content::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* Xử lý vấn đề hai thẻ div e-rte-content lồng nhau khi có công thức toán học */
.e-richtexteditor .e-rte-content .e-rte-content {
  overflow: visible !important; /* Ẩn thanh cuộn của thẻ div e-rte-content con */
  max-height: none !important; /* Loại bỏ giới hạn chiều cao của thẻ div con */
  min-height: 0 !important; /* Loại bỏ chiều cao tối thiểu của thẻ div con */
  height: auto !important; /* Cho phép thẻ div con mở rộng theo nội dung */
  border: none !important; /* Loại bỏ viền của thẻ div con */
  display: inline !important; /* Đảm bảo hiển thị inline để không gây ra vấn đề với công thức */
  padding: 0 !important; /* Loại bỏ padding để tránh khoảng cách không cần thiết */
  margin: 0 !important; /* Loại bỏ margin để tránh khoảng cách không cần thiết */
  background: transparent !important; /* Đảm bảo nền trong suốt */
}

/* Đảm bảo các phần tử trong thẻ div e-rte-content con hiển thị đúng */
.e-richtexteditor .e-rte-content .e-rte-content * {
  display: inline-block !important; /* Hiển thị inline-block cho các phần tử con */
  vertical-align: middle !important; /* Căn giữa theo chiều dọc */
}

/* Fix for double scrollbar issue with MathJax formulas */
.e-richtexteditor .e-rte-content:has(.math-latex) {
  overflow: auto !important;
  display: block !important;
}

.e-richtexteditor .e-rte-content:has(.math-latex) .e-content {
  overflow: visible !important;
  height: auto !important;
}

.e-richtexteditor.e-rte-tb-expand .e-rte-content {
  height: 800px !important;
  overflow-y: auto !important;
  scrollbar-width: none !important; /* Ẩn thanh cuộn trên Firefox */
  -ms-overflow-style: none !important; /* Ẩn thanh cuộn trên IE và Edge */
}

/* Ẩn thanh cuộn trong chế độ expanded toolbar trên Chrome, Safari và các trình duyệt WebKit khác */
.e-richtexteditor.e-rte-tb-expand .e-rte-content::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* Editor content styles */
.e-richtexteditor .e-content {
  font-size: 16px;
  line-height: 1.8; /* Increased line height for better formula display */
  padding: 16px;
  background-color: #fff;
  overflow-y: visible !important; /* Thay đổi từ auto thành visible để tránh double scrollbar */
  height: auto !important; /* Cho phép nội dung mở rộng theo chiều cao tự nhiên */
}

/* Ensure paragraphs have enough height for formulas */
.e-richtexteditor .e-content p {
  min-height: 1.8em;
  margin: 0.8em 0;
  line-height: 1.8;
  overflow: visible; /* Đảm bảo không có thanh cuộn trong các phần tử con */
}

/* Focus styles */
.e-richtexteditor.e-focused {
  box-shadow: 0 0 0 2px rgba(234, 76, 137, 0.2);
}

.e-richtexteditor .e-rte-content.e-focused {
  border-color: var(--edtt-color-primary, #EA4C89);
}

/* Placeholder styles */
.e-richtexteditor .e-rte-content .e-content.e-placeholder::before {
  color: rgba(0, 0, 0, 0.4);
  font-style: italic;
  font-size: 14px;
}

/* Quick toolbar styles */
.e-rte-quick-popup {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: fixed !important; /* Ensure it's fixed relative to viewport */
  z-index: 1050 !important; /* Higher z-index to appear above content */
}

.e-rte-quick-popup .e-toolbar {
  border-radius: 4px;
}

/* Character count styles */
.e-richtexteditor .e-rte-character-count {
  font-size: 12px;
  color: #666;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

/* View mode content styles */
.rich-text-content-container {
  width: 100%;
}

.rich-text-content {
  font-size: 16px;
  line-height: 1.6;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none !important; /* Ẩn thanh cuộn trên Firefox */
  -ms-overflow-style: none !important; /* Ẩn thanh cuộn trên IE và Edge */
  height: auto;
  font-family: var(--edtt-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial);
  max-width: 100%;
  word-wrap: break-word;
}

/* Ẩn thanh cuộn trong chế độ view trên Chrome, Safari và các trình duyệt WebKit khác */
.rich-text-content::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* Empty content styles - ensure no placeholder is shown */
.rich-text-content.empty-content {
  min-height: 50px;
  background-color: transparent;
}

/* Ensure audio and video are visible in view mode */
.rich-text-content audio,
.rich-text-content video,
.rich-text-content iframe {
  display: block !important;
  max-width: 100% !important;
  margin: 10px 0 !important;
  border-radius: 4px !important;
  min-height: 54px !important;
}

/* Ensure audio and video containers are visible in view mode */
.rich-text-content figure.e-audio-wrap,
.rich-text-content figure.e-video-wrap {
  display: block !important;
  margin: 15px 0 !important;
  padding: 10px !important;
  background-color: #f8f9fa !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  width: 100% !important;
  max-width: 600px !important;
  clear: both !important;
} */

/* Dark mode support for audio and video in view mode */
[data-theme='dark'] .rich-text-content audio,
[data-theme='dark'] .rich-text-content video,
[data-theme='dark'] .rich-text-content iframe,
[data-theme='dark'] .rich-text-content figure.e-audio-wrap,
[data-theme='dark'] .rich-text-content figure.e-video-wrap {
  background-color: #333 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* Ensure audio controls are visible in view mode */
.rich-text-content audio::-webkit-media-controls,
.rich-text-content audio::-webkit-media-controls-panel {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
  background-color: #f8f9fa !important;
}

/* Dark mode audio controls */
[data-theme='dark'] .rich-text-content audio::-webkit-media-controls-panel {
  background-color: #444 !important;
}

/* Ensure images are properly displayed in view mode */
.rich-text-content img {
  max-width: 100% !important;
  height: auto !important;
  display: inline-block !important;
  margin: 5px 0 !important;
  border-radius: 4px !important;
}

/* Ensure tables are properly displayed in view mode */
.rich-text-content table {
  border-collapse: collapse !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 15px 0 !important;
  border: 1px solid #ddd !important;
}

.rich-text-content table th,
.rich-text-content table td {
  border: 1px solid #ddd !important;
  padding: 8px !important;
  text-align: left !important;
}

.rich-text-content table th {
  background-color: #f8f9fa !important;
  font-weight: bold !important;
}

/* Dark mode support for tables */
[data-theme='dark'] .rich-text-content table th {
  background-color: #333 !important;
  color: #f0f0f0 !important;
}

[data-theme='dark'] .rich-text-content table td,
[data-theme='dark'] .rich-text-content table th {
  border-color: #444 !important;
}

/* Ensure figure elements with images are properly displayed */
.rich-text-content figure.e-image-wrap {
  margin: 15px 0 !important;
  padding: 5px !important;
  display: block !important;
  max-width: 100% !important;
}

.rich-text-content figure.e-image-wrap img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 0 auto !important;
}

.rich-text-content figure.e-image-wrap figcaption {
  text-align: center !important;
  font-style: italic !important;
  color: #666 !important;
  margin-top: 5px !important;
  font-size: 14px !important;
}

/* Dark mode support for figure captions */
[data-theme='dark'] .rich-text-content figure.e-image-wrap figcaption {
  color: #ccc !important;
}

/* Special styles for content with formulas */
.rich-text-content:has(.katex-formula),
.rich-text-content:has(.katex) {
  min-height: 50px;
  position: relative;
}

/* Ensure no placeholder is shown in any element that contains formulas */
*:has(.katex-formula)::before,
*:has(.katex)::before {
  content: none !important;
  display: none !important;
}

/* Style for the formula spacer - make it invisible but take up space */
.formula-spacer {
  display: inline-block;
  width: 1px;
  height: 1px;
  opacity: 0;
  overflow: hidden;
  position: relative;
}

/* Hide placeholder in view mode */
.rich-text-content::before,
.rich-text-content-container::before,
.rich-text-content *::before {
  content: none !important;
  display: none !important;
}

/* Ensure no placeholder is shown when content has formulas */
.rich-text-content:has(.katex-formula)::before,
.rich-text-content:has(.katex)::before {
  content: none !important;
  display: none !important;
}

/* Typography styles for view mode */
.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: bold;
}

.rich-text-content h1 {
  font-size: 2em;
}

.rich-text-content h2 {
  font-size: 1.5em;
}

.rich-text-content h3 {
  font-size: 1.17em;
}

.rich-text-content h4 {
  font-size: 1em;
}

.rich-text-content h5 {
  font-size: 0.83em;
}

.rich-text-content h6 {
  font-size: 0.67em;
}

.rich-text-content p {
  margin-bottom: 1em;
}

.rich-text-content ul,
.rich-text-content ol {
  padding-left: 2em;
  margin-bottom: 1em;
}

.rich-text-content blockquote {
  border-left: 4px solid var(--edtt-color-primary, #EA4C89);
  padding-left: 16px;
  margin: 0 0 1em;
  color: #666;
}

.rich-text-content pre {
  background-color: #f0f0f0;
  border-radius: 3px;
  padding: 0.5em;
  white-space: pre-wrap;
  margin-bottom: 1em;
  font-family: monospace;
}

.rich-text-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.rich-text-content table th,
.rich-text-content table td {
  border: 1px solid #ddd;
  padding: 8px;
}

.rich-text-content table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.rich-text-content img {
  max-width: 100%;
  height: auto;
}

/* Audio element styles */
.rich-text-content audio {
  width: 100%;
  max-width: 600px;
  margin: 10px 0;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: block;
  min-height: 54px;
}

/* Audio controls styling */
.rich-text-content audio::-webkit-media-controls-panel {
  background-color: #f8f9fa;
}

/* Audio in editor styles */
.e-content audio {
  width: 100%;
  max-width: 600px;
  margin: 10px 0;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: block;
  min-height: 54px; /* Đảm bảo chiều cao tối thiểu cho audio player */
}

/* Ensure audio controls are visible in all layouts */
.e-content audio::-webkit-media-controls,
.rich-text-content audio::-webkit-media-controls {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix for audio in inline mode */
.e-richtexteditor .e-audio-wrap,
.e-rte-audio.e-audio-inline,
.e-content figure.e-audio-wrap[style*="display: inline"] audio,
.rich-text-content figure.e-audio-wrap[style*="display: inline"] audio {
  min-width: 300px;
}


/* Ensure text formatting is properly displayed */
.rich-text-content u {
  text-decoration: underline;
}

/* Dropdown menu styles */
.e-richtexteditor .e-dropdown-popup {
  border-radius: 4px;
  box-shadow: var(--edtt-shadow-dropdown, 0 2px 8px rgba(0, 0, 0, 0.15));
  border: 1px solid var(--edtt-color-border-light, #e0e0e0);
  background-color: var(--edtt-color-bg-default, #fff);
  min-width: 180px !important; /* Ensure dropdown menus are wide enough */
  padding: 4px 0;
  position: fixed !important; /* Ensure it's fixed relative to viewport */
  z-index: 1050 !important; /* Higher z-index to appear above content */
}

/* Style for dropdown buttons in toolbar */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn {
  border-radius: 4px;
  margin: 2px;
  color: #333333;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  padding: 4px 8px;
}

/* Dropdown button hover effect */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn:hover {
  background-color: var(--edtt-color-bg-spotlight, rgba(234, 76, 137, 0.15));
  color: var(--edtt-color-primary, #EA4C89);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Make font name dropdown wider to show full text */
.e-richtexteditor .e-toolbar-items .e-dropdown-btn.e-rte-fontname {
  min-width: 140px !important;
}

/* Make font size dropdown wider */
.e-richtexteditor .e-toolbar-items .e-dropdown-btn.e-rte-fontsize {
  min-width: 70px !important;
}

/* Dropdown list items */
.e-richtexteditor .e-dropdown-popup ul li {
  color: #333333;
  font-weight: normal;
  padding: 8px 12px;
  transition: all 0.2s ease;
  white-space: nowrap; /* Prevent text wrapping in dropdown items */
  border-radius: 2px;
  margin: 2px 4px;
}

/* Dropdown list item hover effect */
.e-richtexteditor .e-dropdown-popup ul li:hover {
  background-color: var(--edtt-color-bg-spotlight, rgba(234, 76, 137, 0.1));
  color: var(--edtt-color-primary, #EA4C89);
}

/* Active dropdown list item */
.e-richtexteditor .e-dropdown-popup ul li.e-active {
  background-color: var(--edtt-color-selected, rgba(234, 76, 137, 0.2));
  color: var(--edtt-color-primary, #EA4C89);
  font-weight: 500;
}

/* Font color and background color picker styles */
.e-richtexteditor .e-rte-color-picker-wrapper {
  border-radius: 4px;
  box-shadow: var(--edtt-shadow-dropdown, 0 2px 8px rgba(0, 0, 0, 0.15));
  border: 1px solid var(--edtt-color-border-light, #e0e0e0);
  padding: 8px;
  background-color: var(--edtt-color-bg-default, #fff);
}

/* Style for color buttons in toolbar */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-fontcolor,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-bgcolor {
  border-radius: 4px;
  margin: 2px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 4px;
  height: 28px !important; /* Fixed height to match other toolbar buttons */
  position: relative !important; /* Ensure proper positioning */
  top: 0 !important; /* Prevent shifting upward */
  vertical-align: middle !important; /* Align with other toolbar items */
}

/* Font color button specific styles */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-fontcolor .e-rte-dropdown-btn-text {
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative !important; /* Ensure proper positioning */
  top: 0 !important; /* Prevent shifting upward */
}

/* Background color button specific styles */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-bgcolor .e-rte-dropdown-btn-text {
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative !important; /* Ensure proper positioning */
  top: 0 !important; /* Prevent shifting upward */
}

/* Color indicator in buttons */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-fontcolor .e-rte-dropdown-btn-text::after,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-bgcolor .e-rte-dropdown-btn-text::after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 3px;
  margin-left: 5px;
  border-radius: 1px;
  position: relative !important; /* Ensure proper positioning */
  top: 0 !important; /* Prevent shifting upward */
}

/* Font color indicator */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-fontcolor .e-rte-dropdown-btn-text::after {
  background-color: currentColor;
}

/* Background color indicator */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-rte-bgcolor .e-rte-dropdown-btn-text::after {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

/* Color palette items */
.e-richtexteditor .e-rte-color-palette .e-row .e-item {
  border-radius: 2px;
  margin: 3px;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Fix for color palette dropdown in production builds */
.e-richtexteditor .e-rte-color-picker-wrapper {
  position: fixed !important;
  z-index: 1050 !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Hover effect for color palette items */
.e-richtexteditor .e-rte-color-palette .e-row .e-item:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Selected color item */
.e-richtexteditor .e-rte-color-palette .e-row .e-item.e-selected {
  outline: 2px solid var(--edtt-color-primary, #EA4C89);
  outline-offset: 1px;
  transform: scale(1.1);
}

/* Color palette header */
.e-richtexteditor .e-rte-color-palette-header {
  font-weight: 600;
  color: #333333;
  padding: 4px 8px;
}

/* Fullscreen mode styles */
.e-richtexteditor.e-rte-full-screen {
  z-index: 1000;
  width: 100% !important;
  height: 100% !important;
  position: fixed;
  top: 0;
  left: 0;
  background-color: var(--edtt-color-bg-default, #fff);
  display: flex !important;
  flex-direction: column !important;
}

/* Toolbar in fullscreen mode */
.e-richtexteditor.e-rte-full-screen .e-toolbar {
  flex: 0 0 auto !important;
}

/* Content area in fullscreen mode - take all available space */
.e-richtexteditor.e-rte-full-screen .e-rte-content {
  flex: 1 1 auto !important;
  height: calc(100vh - 120px) !important; /* Adjust for toolbar and potential padding */
  max-height: none !important;
  overflow-y: auto !important;
  scrollbar-width: none !important; /* Ẩn thanh cuộn trên Firefox */
  -ms-overflow-style: none !important; /* Ẩn thanh cuộn trên IE và Edge */
  display: flex !important;
  flex-direction: column !important;
}

/* Ẩn thanh cuộn trong chế độ fullscreen trên Chrome, Safari và các trình duyệt WebKit khác */
.e-richtexteditor.e-rte-full-screen .e-rte-content::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* Ensure the editable area takes all available space */
.e-richtexteditor.e-rte-full-screen .e-rte-content .e-content {
  flex: 1 1 auto !important;
  height: auto !important;
  min-height: calc(100vh - 150px) !important;
  overflow-y: visible !important;
}

/* Xử lý vấn đề hai thẻ div e-rte-content lồng nhau trong chế độ fullscreen */
.e-richtexteditor.e-rte-full-screen .e-rte-content .e-rte-content {
  overflow: visible !important;
  max-height: none !important;
  height: auto !important;
  border: none !important;
  display: inline !important;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
}

/* Xử lý đặc biệt cho các công thức toán học trong chế độ fullscreen */
.e-richtexteditor.e-rte-full-screen .katex-formula {
  display: inline-block !important;
  vertical-align: middle !important;
  overflow: visible !important;
}

/* Đảm bảo không có thanh cuộn kép khi có công thức toán học */
.e-richtexteditor:has(.katex-formula) .e-rte-content {
  overflow-y: auto !important;
}

.e-richtexteditor:has(.katex-formula) .e-rte-content .e-rte-content {
  overflow: visible !important;
  display: inline !important;
}

/* Formula icon styles */
.e-formula-icon::before {
  content: '\2211'; /* Sigma symbol for math */
  font-family: 'Cambria Math', 'Times New Roman', serif;
  font-weight: bold;
  font-size: 18px;
}

/* Boxtext icon styles */
.e-boxtext-icon::before {
  content: '\25A0'; /* Square symbol for boxtext */
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  font-size: 18px;
}

/* Math formula styles in editor and view mode */
.e-content .katex-formula,
.rich-text-content .katex-formula,
.e-content .math-latex,
.rich-text-content .math-latex {
  display: inline-block !important;
  margin: 0 4px !important;
  padding: 16px 4px !important; /* Further increased vertical padding */
  background-color: transparent !important;
  border-radius: 2px !important;
  vertical-align: middle !important;
  text-align: center !important;
  box-sizing: border-box !important;
  max-width: 100% !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
  line-height: normal !important;
  font-size: inherit !important;
  border: 1px solid transparent !important;
  /* Add extra space to prevent clipping */
  min-height: 3.5em !important;
  /* Force line height to be taller */
  line-height: 2 !important;
  /* Add margin to ensure space above formula */
  margin-top: 8px !important;
  margin-bottom: 8px !important;
  /* Improve cursor behavior around formulas */
  position: relative !important;
  z-index: 1 !important;
  /* Add a small space after the formula to make cursor positioning easier */
  margin-right: 2px !important;
  /* Đảm bảo không gây ra vấn đề với thanh cuộn */
  overflow: visible !important;
}

/* Hover effect for better visibility when editing */
.e-content .katex-formula:hover,
.e-content .math-latex:hover {
  background-color: rgba(234, 76, 137, 0.15) !important;
  border: 1px dashed rgba(234, 76, 137, 0.7) !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  /* Add a subtle shadow to make it stand out more */
  box-shadow: 0 0 4px rgba(234, 76, 137, 0.2) !important;
  /* Ensure it's above other content */
  z-index: 2 !important;
}

/* Make formulas selectable as a single unit */
.e-content .katex-formula,
.e-content .math-latex {
  user-select: all !important;
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  -ms-user-select: all !important;
  cursor: pointer !important;
}

/* Ensure KaTeX formulas are properly displayed */
.katex {
  font-size: 1.1em !important;
  text-rendering: auto !important;
  font-family: KaTeX_Main, 'Times New Roman', serif !important;
  line-height: 1.6 !important; /* Further increased line height */
  text-indent: 0 !important;
  text-align: left !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-wrap: normal !important;
  white-space: nowrap !important;
  direction: ltr !important;
  max-width: 100% !important;
  display: inline-block !important;
  /* Ensure proper vertical alignment */
  vertical-align: middle !important;
  /* Add significant padding to prevent clipping */
  padding: 12px 0 !important; /* Increased from 8px to 12px */
  /* Add margin to ensure space above formula */
  margin-top: 6px !important; /* Increased from 4px to 6px */
  /* Ensure minimum height */
  min-height: 2.5em !important; /* Increased from 2em to 2.5em */
  /* Position slightly lower to prevent top clipping */
  position: relative !important;
  top: 6px !important; /* Increased from 4px to 6px */
  /* Add transform to fix clipping issues */
  transform: translateY(4px) !important;
}

/* Ensure formulas are properly aligned with text */
.e-content p .katex-formula,
.rich-text-content p .katex-formula,
.e-content p .math-latex,
.rich-text-content p .math-latex {
  vertical-align: middle !important;
}

/* Specific styles for math-latex class */
.math-latex {
  display: inline-block !important;
  vertical-align: middle !important;
  padding: 4px !important;
  margin: 4px 2px !important;
  min-height: 1.5em !important;
  line-height: 1.5 !important;
  position: relative !important;
  overflow: visible !important;
  /* Add transform to fix clipping issues */
  transform: translateY(2px) !important;
}

/* Make sure KaTeX elements don't overflow */
.katex-html {
  max-width: 100% !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

/* Ensure KaTeX display mode works properly */
.katex-display {
  display: block !important;
  margin: 0 !important;
  text-align: center !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  padding: 0 !important;
}

/* Ensure consistent display between editor and preview */
.formula-preview .katex-display,
.e-content .katex-formula .katex-display,
.rich-text-content .katex-formula .katex-display {
  display: block !important;
  margin: 0 !important;
  text-align: center !important;
}

/* Fix for KaTeX specific elements */
.katex .base {
  position: relative !important;
  white-space: nowrap !important;
  width: min-content !important;
  max-width: 100% !important;
  /* Ensure proper vertical alignment */
  display: inline-flex !important;
  align-items: center !important;
  /* Add extra space to prevent clipping */
  min-height: 2em !important;
  /* Add significant padding to prevent clipping */
  padding: 8px 0 !important;
  /* Position slightly lower to prevent top clipping */
  top: 4px !important;
  /* Add margin to ensure space above formula */
  margin-top: 4px !important;
}

.katex .strut {
  display: inline-block !important;
  /* Ensure proper height for the strut */
  height: 2em !important; /* Increased height */
  vertical-align: middle !important;
  /* Add margin to ensure space above formula */
  margin-top: 4px !important;
}

.katex .mord, .katex .mbin, .katex .mrel, .katex .mopen, .katex .mclose,
.katex .mpunct, .katex .minner {
  display: inline-block !important;
  vertical-align: middle !important;
}

/* Fix for superscripts and subscripts */
.katex .msupsub {
  text-align: left !important;
  vertical-align: middle !important;
  margin-top: 0 !important; /* Prevent top margin that can cause clipping */
  padding-top: 0.5em !important; /* Increased padding to prevent clipping */
  /* Position slightly lower to prevent top clipping */
  position: relative !important;
  top: 4px !important;
}

/* Ensure superscripts are fully visible */
.katex .msupsub .msup {
  margin-bottom: 0.5em !important;
  padding-top: 0.5em !important; /* Increased padding */
  display: inline-block !important;
  /* Add margin to ensure space above formula */
  margin-top: 4px !important;
  /* Position slightly lower to prevent top clipping */
  position: relative !important;
  top: 4px !important;
}

/* Ensure subscripts are fully visible */
.katex .msupsub .msub {
  margin-top: 0.5em !important;
  display: inline-block !important;
  /* Add padding to prevent clipping */
  padding-top: 0.2em !important;
}

/* Fix for fractions */
.katex .mfrac {
  display: inline-block !important;
  vertical-align: middle !important;
  margin: 0 0.1em !important;
  padding: 0.5em 0 !important; /* Increased padding to prevent clipping */
  min-height: 3em !important; /* Increased minimum height for fractions */
  /* Position slightly lower to prevent top clipping */
  position: relative !important;
  top: 4px !important;
  /* Add margin to ensure space above formula */
  margin-top: 4px !important;
}

/* Ensure numerator is fully visible */
.katex .mfrac .mfracnum {
  display: inline-block !important;
  min-height: 1.5em !important; /* Increased height */
  padding-bottom: 0.2em !important; /* Increased padding */
  padding-top: 0.5em !important; /* Added top padding */
  /* Position slightly lower to prevent top clipping */
  position: relative !important;
  top: 4px !important;
}

/* Ensure denominator is fully visible */
.katex .mfrac .mfracden {
  display: inline-block !important;
  min-height: 1.5em !important; /* Increased height */
  padding-top: 0.2em !important; /* Increased padding */
}

/* Fix for tall elements like integrals and sum symbols */
.katex .op-symbol.large-op {
  margin-top: 0.5em !important; /* Increased margin */
  margin-bottom: 0.2em !important; /* Increased margin */
  padding-top: 0.5em !important; /* Added padding */
  min-height: 2em !important; /* Ensure minimum height */
  /* Position slightly lower to prevent top clipping */
  position: relative !important;
  top: 4px !important;
}

/* Fix for limits on operators like sum and integral */
.katex .op-limits {
  padding-top: 0.5em !important;
  min-height: 2.5em !important;
  position: relative !important;
  top: 4px !important;
}

/* Ensure all KaTeX elements are visible */
.katex * {
  border-color: currentColor !important;
}

/* General fix for all KaTeX elements to prevent clipping */
.katex .vlist-t,
.katex .vlist-r,
.katex .vlist,
.katex .mord,
.katex .mbin,
.katex .mrel,
.katex .mopen,
.katex .mclose,
.katex .mpunct,
.katex .minner {
  padding-top: 0.2em !important;
  min-height: 1.2em !important;
}

/* Specific fix for vlist element which controls vertical positioning */
.katex .vlist-t {
  position: relative !important;
  top: 6px !important; /* Move elements down to prevent top clipping */
}

.katex .vlist-t2 {
  position: relative !important;
  top: 6px !important; /* Move elements down to prevent top clipping */
  margin-top: 6px !important;
}

/* Fix for vertical alignment in editor */
.e-content p {
  padding: 2px 0 !important; /* Reduced padding to prevent cursor jumping */
  min-height: 1.2em !important; /* Further reduced from 1.5em to prevent cursor jumping */
  line-height: 1.5 !important; /* Further reduced from 1.8 to prevent cursor jumping */
  margin-top: 0 !important;
  margin-bottom: 0.3em !important; /* Reduced from 0.5em to prevent cursor jumping */
  overflow: visible !important; /* Đảm bảo không có thanh cuộn trong các phần tử con */
  position: relative !important; /* Ensure proper positioning context */
  white-space: pre-wrap !important; /* Preserve whitespace but allow wrapping */
}

/* Ensure video elements are properly displayed */
.e-content video,
.rich-text-content video {
  max-width: 100% !important;
  border-radius: 4px !important;
  background-color: #000 !important;
  display: block !important;
}

/* Fix for video in inline mode */
.e-content figure.e-video-wrap[style*="display: inline"] video,
.rich-text-content figure.e-video-wrap[style*="display: inline"] video {
  display: inline-block !important;
  min-width: 300px !important;
  width: 100% !important;
}

/* Ensure iframe videos (YouTube, etc.) are properly displayed */
.e-content figure.e-video-wrap iframe,
.rich-text-content figure.e-video-wrap iframe {
  max-width: 100% !important;
  border: none !important;
  border-radius: 4px !important;
  display: block !important;
  margin: 0 auto !important;
}

/* Dark mode support for math formulas */
[data-theme='dark'] .e-content .katex-formula:hover {
  background-color: rgba(234, 76, 137, 0.1) !important;
  border-color: rgba(234, 76, 137, 0.4) !important;
}

/* Ensure KaTeX formulas are visible in dark mode */
[data-theme='dark'] .katex {
  color: #f0f0f0 !important;
}

/* Boxtext styles in editor and view mode */
.e-content .e-rte-boxtext,
.rich-text-content .e-rte-boxtext {
  display: block;
  margin: 10px 0;
  position: relative;
  cursor: pointer;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

/* Hover effect for better visibility when editing */
.e-content .e-rte-boxtext:hover {
  outline: 2px dashed var(--edtt-color-primary, #EA4C89);
}

/* Boxtext content styles */
.e-content .e-rte-boxtext-content,
.rich-text-content .e-rte-boxtext-content {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
}

/* Resize handles */
.e-content .e-rte-boxtext-resize-handle {
  position: absolute;
  background-color: var(--edtt-color-primary, #EA4C89);
  z-index: 100;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.e-content .e-rte-boxtext-resizable:hover .e-rte-boxtext-resize-handle {
  opacity: 1;
}

.e-content .e-rte-boxtext-resize-handle-right {
  width: 6px;
  height: 100%;
  top: 0;
  right: -3px;
  cursor: e-resize;
}

.e-content .e-rte-boxtext-resize-handle-bottom {
  width: 100%;
  height: 6px;
  bottom: -3px;
  left: 0;
  cursor: s-resize;
}

.e-content .e-rte-boxtext-resize-handle-corner {
  width: 10px;
  height: 10px;
  bottom: -5px;
  right: -5px;
  border-radius: 50%;
  cursor: se-resize;
}

/* Dark mode support for boxtext */
[data-theme='dark'] .e-content .e-rte-boxtext:hover {
  outline-color: var(--edtt-color-primary, #EA4C89);
}

[data-theme='dark'] .e-content .e-rte-boxtext-resize-handle {
  background-color: var(--edtt-color-primary, #EA4C89);
}

/* Dark mode support */
[data-theme='dark'] .e-richtexteditor {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme='dark'] .e-richtexteditor .e-toolbar {
  background-color: var(--edtt-color-bg-secondary);
  border-color: var(--edtt-color-border-default);
}

[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-separator {
  border-right-color: var(--edtt-color-border-default);
}

[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn:hover {
  background-color: var(--edtt-color-bg-spotlight);
}

[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn:hover {
  background-color: var(--edtt-color-bg-spotlight);
  width: inherit !important;
  box-sizing: border-box !important;
}

[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn {
  color: var(--edtt-color-text-default);
}

[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn.e-active {
  background-color: var(--edtt-color-selected);
  color: var(--edtt-color-primary);
}

[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn,
[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn .e-btn-icon,
[data-theme='dark'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn .e-btn-text {
  color: var(--edtt-color-text-default);
}

/* Dark mode for color pickers */
[data-theme='dark'] .e-richtexteditor .e-rte-color-picker-wrapper {
  background-color: var(--edtt-color-bg-secondary);
  border-color: var(--edtt-color-border-default);
}

[data-theme='dark'] .e-richtexteditor .e-rte-color-palette-header {
  color: var(--edtt-color-text-default);
}

/* Dark mode for dropdowns */
[data-theme='dark'] .e-richtexteditor .e-dropdown-popup {
  background-color: var(--edtt-color-bg-secondary);
  border-color: var(--edtt-color-border-default);
}

[data-theme='dark'] .e-richtexteditor .e-dropdown-popup ul li {
  color: var(--edtt-color-text-default);
}

[data-theme='dark'] .e-richtexteditor .e-rte-content {
  border-color: var(--edtt-color-border-default);
}

[data-theme='dark'] .e-richtexteditor .e-rte-character-count {
  background-color: var(--edtt-color-bg-secondary);
  border-color: var(--edtt-color-border-default);
}

/* Learning theme support */
[data-theme='learning'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn:hover {
  background-color: rgba(0, 174, 239, 0.1);
}

[data-theme='learning'] .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn.e-active {
  background-color: rgba(0, 174, 239, 0.2);
}

[data-theme='learning'] .e-richtexteditor.e-focused {
  box-shadow: 0 0 0 2px rgba(0, 174, 239, 0.2);
}

/* Fix for audio and video elements in the editor and view mode */
.e-content figure.e-audio-wrap audio,
.rich-text-content figure.e-audio-wrap audio,
.e-content figure.e-video-wrap video,
.rich-text-content figure.e-video-wrap video,
.e-content figure.e-video-wrap iframe,
.rich-text-content figure.e-video-wrap iframe {
  width: 100% !important;
  max-width: 100% !important;
  display: block !important;
  margin: 0 auto !important;
}

/* Ensure audio and video elements are visible */
.e-content figure.e-audio-wrap,
.rich-text-content figure.e-audio-wrap,
.e-content figure.e-video-wrap,
.rich-text-content figure.e-video-wrap {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Production build specific fixes */
.e-richtexteditor {
  display: flex;
  flex-direction: column;
}

.e-richtexteditor .e-toolbar {
  flex: 0 0 auto;
  order: 1;
}

.e-richtexteditor .e-rte-content {
  flex: 1 1 auto;
  order: 2;
}

/* Fix for toolbar overflow in production builds */
.e-richtexteditor .e-toolbar.e-toolbar-multirow,
.e-richtexteditor .e-toolbar.e-extended-toolbar {
  height: auto !important;
  min-height: 50px;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
  padding: 0px;
}

/* Fix for color buttons in production builds */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn.e-rte-fontcolor,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn.e-rte-bgcolor {
  height: 28px !important;
  position: relative !important;
  top: 0 !important;
  vertical-align: middle !important;
  display: flex !important;
  align-items: center !important;
  margin: 2px !important;
}

/* Fix for color button text in production builds */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn.e-rte-fontcolor .e-rte-dropdown-btn-text,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn.e-rte-bgcolor .e-rte-dropdown-btn-text {
  position: relative !important;
  top: 0 !important;
  display: flex !important;
  align-items: center !important;
}

/* Fix for color button indicators in production builds */
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn.e-rte-fontcolor .e-rte-dropdown-btn-text::after,
.e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item .e-dropdown-btn.e-rte-bgcolor .e-rte-dropdown-btn-text::after {
  position: relative !important;
  top: 0 !important;
}

.e-richtexteditor .e-rte-toolbar .e-toolbar-items:not(.e-tbar-pos):not(.e-toolbar-multirow) .e-toolbar-item:first-child {
  margin-left: 0px;
}

.e-richtexteditor .e-rte-toolbar .e-toolbar .e-toolbar-items:first-child:not(.e-toolbar-multirow) > .e-toolbar-item:last-child {
  margin-right: 0px;
}

/* Ensure content doesn't overlap with multirow toolbar */
.e-richtexteditor .e-toolbar.e-toolbar-multirow + .e-rte-content,
.e-richtexteditor .e-toolbar.e-extended-toolbar + .e-rte-content {
  position: relative;
  top: 0 !important;
  margin-top: 0 !important;
}

/* Dialog styles for RichTextEditor */
/* General dialog styles */
.e-dialog.e-lib.e-popup.e-control.e-popup-open {
  position: fixed !important;
  z-index: 1050 !important; /* Higher z-index to appear above content */
  max-height: 90vh !important; /* Limit height to 90% of viewport height */
  overflow: auto !important; /* Add scrolling if needed */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Link dialog styles */
.e-rte-link-dialog.e-dialog {
  min-width: 300px !important;
  position: fixed !important;
  z-index: 1050 !important;
}

/* Image dialog styles */
.e-rte-image-dialog.e-dialog {
  min-width: 350px !important;
  position: fixed !important;
  z-index: 1050 !important;
}

/* Video dialog styles */
.e-rte-video-dialog.e-dialog {
  min-width: 350px !important;
  position: fixed !important;
  z-index: 1050 !important;
}

/* Audio dialog styles */
.e-rte-audio-dialog.e-dialog {
  min-width: 350px !important;
  position: fixed !important;
  z-index: 1050 !important;
}

/* Table dialog styles */
.e-rte-table-dialog.e-dialog {
  min-width: 350px !important;
  position: fixed !important;
  z-index: 1050 !important;
}

/* Dialog header styles */
.e-dialog .e-dlg-header-content {
  padding: 12px 24px !important;
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e0e0e0 !important;
}

/* Dialog content styles */
.e-dialog .e-dlg-content {
  padding: 16px 24px !important;
  max-height: 70vh !important; /* Limit content height */
  overflow: auto !important; /* Add scrolling if needed */
}

/* Dialog footer styles */
.e-dialog .e-footer-content {
  padding: 12px 24px !important;
  border-top: 1px solid #e0e0e0 !important;
}

/* Dialog button styles */
.e-dialog .e-btn {
  min-width: 80px !important;
}

/* Dialog overlay styles */
.e-dlg-overlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 1040 !important; /* Just below the dialog z-index */
}

/* Ensure dialogs are centered in the viewport */
.e-dialog.e-popup {
  margin: 0 auto !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  max-height: 90vh !important; /* Limit height to 90% of viewport height */
  overflow: auto !important; /* Add scrolling if needed */
}

/* Ensure dialog content is visible even with small editor height */
.e-dialog.e-popup .e-dlg-content {
  position: relative !important;
  max-height: 70vh !important; /* Limit content height */
  overflow: auto !important; /* Add scrolling if needed */
}

/* Ensure dialog is positioned correctly when editor has small height */
.e-richtexteditor:has([style*="height: 200px"]) ~ .e-dialog,
.e-richtexteditor:has([style*="height:200px"]) ~ .e-dialog,
.e-richtexteditor:has([style*="height: 300px"]) ~ .e-dialog,
.e-richtexteditor:has([style*="height:300px"]) ~ .e-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* Responsive toolbar adjustments */
@media screen and (max-width: 768px) {
  /* Adjust toolbar padding on small screens */
  .e-richtexteditor .e-toolbar {
    padding: 4px 4px;
  }

  /* Make toolbar items smaller on mobile */
  .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item button.e-tbar-btn {
    margin: 1px;
    padding: 3px;
  }

  /* Adjust separator spacing */
  .e-richtexteditor .e-toolbar .e-toolbar-items .e-toolbar-item.e-separator {
    margin: 1px 3px;
  }

  /* Make font name dropdown narrower on mobile */
  .e-richtexteditor .e-toolbar-items .e-dropdown-btn.e-rte-fontname {
    min-width: 100px !important;
  }

  /* Adjust audio and video width on mobile */
  .e-content audio,
  .rich-text-content audio,
  .e-content figure.e-audio-wrap,
  .rich-text-content figure.e-audio-wrap,
  .e-content figure.e-video-wrap,
  .rich-text-content figure.e-video-wrap {
    max-width: 100%;
    width: 100%;
  }

  /* Adjust dialog size on mobile */
  .e-dialog.e-lib.e-popup.e-control.e-popup-open {
    width: 95% !important;
    max-width: 95% !important;
    max-height: 80vh !important;
  }

  /* Adjust dialog content on mobile */
  .e-dialog .e-dlg-content {
    padding: 12px 16px !important;
    max-height: 60vh !important;
  }
}

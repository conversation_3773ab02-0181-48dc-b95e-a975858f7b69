/*
 * Icon styles for EdTech project
 *
 * IMPORTANT: This file is maintained for backward compatibility.
 * For new code, use Tailwind CSS classes instead of these CSS variables.
 *
 * Examples of Tailwind usage:
 * - Size: tailwind-w-6 tailwind-h-6
 * - Color: tailwind-text-primary, tailwind-text-red-500
 * - Transitions: tailwind-transition-colors tailwind-duration-200
 */

/* Base icon styles - kept for backward compatibility */
.svg-icon {
  /* Inherit text color by default */
  fill: currentColor;
  /* Ensure smooth transitions when theme changes */
  transition: fill 0.3s ease;
  /* Ensure icons maintain their size */
  flex-shrink: 0;
  box-sizing: content-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  /* Ensure SVG fills its container */
  overflow: visible;
  /* Ensure proper positioning */
  position: relative;
}

/* Ensure path elements are properly centered in the SVG container */
.svg-icon path {
  transform-origin: center;
  transform: scale(1.2); /* Scale up path to fill SVG container better */
  /* Ensure the path is centered within the SVG */
  position: relative;
  margin: auto;
  /* Use fill-box to ensure the transform is relative to the path's bounding box */
  transform-box: fill-box;
  /* Ensure the path is centered in the SVG */
  x: 0;
  y: 0;
}

/* Apply theme transitions to icons */
.svg-icon.theme-transitions-enabled {
  transition: fill var(--edtt-transition-normal, 0.2s ease-in-out);
}

/* Animation for spin effect */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.svg-icon-spin {
  animation: spin 1s infinite linear;
}

/* ==================== TAILWIND CLASSES ==================== */

/* Ensure width classes work correctly with SVG icons */
.svg-icon.tailwind-w-4 {
  min-width: 16px !important;
  width: 16px !important;
}

.svg-icon.tailwind-w-5 {
  min-width: 20px !important;
  width: 20px !important;
}

.svg-icon.tailwind-w-6 {
  min-width: 24px !important;
  width: 24px !important;
}

.svg-icon.tailwind-w-8 {
  min-width: 32px !important;
  width: 32px !important;
}

.svg-icon.tailwind-w-10 {
  min-width: 40px !important;
  width: 40px !important;
}

.svg-icon.tailwind-w-12 {
  min-width: 48px !important;
  width: 48px !important;
}

/* Ensure height classes work correctly with SVG icons */
.svg-icon.tailwind-h-4 {
  min-height: 16px !important;
  height: 16px !important;
}

.svg-icon.tailwind-h-5 {
  min-height: 20px !important;
  height: 20px !important;
}

.svg-icon.tailwind-h-6 {
  min-height: 24px !important;
  height: 24px !important;
}

.svg-icon.tailwind-h-8 {
  min-height: 32px !important;
  height: 32px !important;
}

.svg-icon.tailwind-h-10 {
  min-height: 40px !important;
  height: 40px !important;
}

.svg-icon.tailwind-h-12 {
  min-height: 48px !important;
  height: 48px !important;
}

/* ==================== PATH SCALING FOR DIFFERENT SIZES ==================== */

/* Ensure path elements are properly sized and centered based on tailwind classes */
.svg-icon.tailwind-w-4 path,
.svg-icon.tailwind-h-4 path {
  transform: scale(1.1) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon.tailwind-w-5 path,
.svg-icon.tailwind-h-5 path {
  transform: scale(1.15) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon.tailwind-w-6 path,
.svg-icon.tailwind-h-6 path {
  transform: scale(1.2) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon.tailwind-w-8 path,
.svg-icon.tailwind-h-8 path {
  transform: scale(1.25) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon.tailwind-w-10 path,
.svg-icon.tailwind-h-10 path {
  transform: scale(1.3) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon.tailwind-w-12 path,
.svg-icon.tailwind-h-12 path {
  transform: scale(1.35) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

/* Size-specific adjustments for inline style sizes */
.svg-icon[style*="width: 16px"] path,
.svg-icon[style*="width:16px"] path {
  transform: scale(1.1) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon[style*="width: 20px"] path,
.svg-icon[style*="width:20px"] path {
  transform: scale(1.15) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon[style*="width: 24px"] path,
.svg-icon[style*="width:24px"] path {
  transform: scale(1.2) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon[style*="width: 32px"] path,
.svg-icon[style*="width:32px"] path {
  transform: scale(1.25) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon[style*="width: 40px"] path,
.svg-icon[style*="width:40px"] path {
  transform: scale(1.3) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

.svg-icon[style*="width: 48px"] path,
.svg-icon[style*="width:48px"] path {
  transform: scale(1.35) !important;
  transform-origin: center !important;
  transform-box: fill-box !important;
}

/* ==================== DEMO COMPONENT STYLES ==================== */

.icon-demo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  min-width: 100px;
  text-align: center;
}

.icon-demo-item .ant-typography {
  margin-top: 8px;
  font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .icon-demo-item {
    min-width: 80px;
    padding: 12px;
  }
}

import React, { memo, useState } from 'react';
import { Upload, message, Button } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { MediaFileUploaderProps } from './types';
import tempFileApi from '../../../../../../api/tempFileApi';
import { ConfigManager } from '../../../../../../utils/ConfigManager';
import { fileToBase64, createBase64JSContent } from '../utils';
import {
  ArrowUploadIcon,
  MailOutlinedIcon,
} from '../../../../../icons/IconRegister';

const MediaFileUploader: React.FC<MediaFileUploaderProps> = ({
  mediaType,
  multiple = true,
  showUploadList = false,
  fileList: externalFileList,
  allowDrop = true,
  dropAreaHeight = '200px',
  dropAreaWidth = '100%',
  onUploadSuccess,
  onUploadError,
  onUploadStart,
  onUploadComplete,
  dropAreaContent,
  uploadButtonContent,
  className,
  style,
  uploadProps = {},
}) => {
  // State để quản lý danh sách file
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Xác định accept type dựa trên mediaType
  const getAcceptType = () => {
    switch (mediaType) {
      case 'image':
        return 'image/*';
      case 'video':
        return 'video/*';
      case 'audio':
        return 'audio/*';
      case 'model3d':
        return '.glb,.obj,.fbx';
      default:
        return '*/*';
    }
  };

  // Check file type validity
  const isValidFileType = (file: File) => {
    const fileName = file.name.toLowerCase();

    switch (mediaType) {
      case 'image':
        return file.type.startsWith('image/');
      case 'video':
        return file.type.startsWith('video/');
      case 'audio':
        return file.type.startsWith('audio/');
      case 'model3d':
        // 3D models might not have standard MIME types, so check extensions
        return (
          fileName.endsWith('.glb') ||
          fileName.endsWith('.obj') ||
          fileName.endsWith('.fbx')
        );
      default:
        return true;
    }
  };

  // Xử lý upload file
  const handleUpload = async (files: File[]) => {
    if (!files || files.length === 0) return;

    // Gọi callback bắt đầu upload
    onUploadStart?.();

    try {
      // Filter valid files by type
      const validFiles = files.filter(isValidFileType);

      if (validFiles.length === 0) {
        message.error(`Chỉ được tải lên ${getMediaTypeText()}!`);
        onUploadComplete?.();
        return;
      }

      const formData = new FormData();
      const Id = ConfigManager.getIdConfigFromParams();

      // Nếu là file 3D, convert thành base64 JS và chỉ upload file base64
      if (mediaType === 'model3d') {
        try {
          for (const file of validFiles) {
            // Convert file to base64
            const base64Content = await fileToBase64(file);

            // Tạo tên file base64
            const base64FileName = `${file.name}.b64.js`;

            // Tạo nội dung file JS
            const jsContent = createBase64JSContent(
              file.name,
              base64Content,
              file
            );

            // Tạo blob từ JS content
            const jsBlob = new Blob([jsContent], {
              type: 'application/javascript',
            });

            // Chỉ thêm file base64 JS vào formData (không thêm file gốc)
            formData.append('files', jsBlob, base64FileName);
          }
        } catch (error) {
          console.error('Error converting files to base64:', error);
          message.error('Không thể convert file 3D thành base64');
          return;
        }
      } else {
        // Với các loại file khác, upload file gốc
        validFiles.forEach((file) => {
          formData.append('files', file);
        });
      }

      // Upload files
      const response = await tempFileApi.UploadMultiple(Id, formData);
      if (response.data?.successful && response.data.successful.length > 0) {
        // Gọi callback khi upload thành công
        onUploadSuccess(response.data.successful);

        // Hiển thị thông báo thành công
        if (mediaType === 'model3d') {
          message.success(
            'Upload và convert file 3D thành base64 JS thành công!'
          );
        }
      }

      // Hiển thị thông báo lỗi nếu có
      if (response.data?.failed && response.data.failed.length > 0) {
        message.error(
          `Không thể tải lên ${
            response.data.failed.length
          } ${getMediaTypeText()}`
        );
      }
    } catch (error) {
      message.error(
        `Không thể tải lên ${getMediaTypeText()}. Vui lòng thử lại sau.`
      );

      // Gọi callback khi có lỗi
      onUploadError?.(error);
    } finally {
      // Xóa danh sách file và đóng thông báo loading
      setFileList([]);

      // Gọi callback khi kết thúc upload
      onUploadComplete?.();
    }
  };

  // Lấy text hiển thị dựa trên loại media
  const getMediaTypeText = () => {
    switch (mediaType) {
      case 'image':
        return 'hình ảnh';
      case 'video':
        return 'video';
      case 'audio':
        return 'âm thanh';
      case 'model3d':
        return 'mô hình 3D';
      default:
        return 'tệp';
    }
  };

  // Xử lý kéo thả file
  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files);

      // Upload the files
      await handleUpload(files);
    }
  };

  // Xử lý khi kéo file vào khu vực drop
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  // Xử lý khi kéo file ra khỏi khu vực drop
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  // Render nút upload
  const renderUploadButton = () => {
    return (
      <div className="tailwind-mt-4">
        <Upload
          accept={getAcceptType()}
          multiple={multiple}
          showUploadList={showUploadList}
          fileList={externalFileList || fileList}
          directory={false}
          beforeUpload={(file) => {
            // Kiểm tra loại file
            const isValidType = isValidFileType(file);
            if (!isValidType) {
              message.error(`Chỉ được tải lên ${getMediaTypeText()}!`);
              return Upload.LIST_IGNORE;
            }
            return true;
          }}
          onChange={async (info) => {
            // Khi files được chọn, xử lý trực tiếp
            if (info.fileList.length > 0) {
              const files = info.fileList
                .map((f) => f.originFileObj)
                .filter(Boolean) as File[];
              await handleUpload(files);
            }
          }}
          {...uploadProps}
        >
          {uploadButtonContent || (
            <Button
              size="middle"
              icon={<ArrowUploadIcon />}
              style={{
                backgroundColor: 'var(--edtt-color-primary)',
                color: 'var(--edtt-color-white)',
                border: 'none',
                borderRadius: '4px',
              }}
            >
              Tải {getMediaTypeText()} lên
            </Button>
          )}
        </Upload>
      </div>
    );
  };

  // Render khu vực kéo thả
  const renderDropArea = () => {
    if (!allowDrop) return renderUploadButton();

    return (
      <div
        className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full ${
          isDragging ? 'tailwind-border-primary' : 'tailwind-border-primary-300'
        } ${className || ''}`}
        style={{
          height: dropAreaHeight,
          width: dropAreaWidth,
          maxWidth: '100%',
          borderRadius: '4px',
          backgroundColor: 'var(--edtt-color-primary-100)',
          ...style,
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div
          className="tailwind-relative tailwind-w-full tailwind-h-full tailwind-flex tailwind-flex-col tailwind-justify-center tailwind-items-center"
          style={{
            overflow: 'hidden',
            maxWidth: '100%',
            backgroundColor: isDragging
              ? 'var(--edtt-color-primary-100)'
              : undefined,
          }}
        >
          {dropAreaContent || (
            <div className="tailwind-flex tailwind-w-full tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-py-4">
              <p className="tailwind-text-primary">
                <MailOutlinedIcon
                  className="tailwind-text-4xl"
                  style={{ transform: 'rotate(-5deg)' }}
                />
              </p>
              <p className="tailwind-mt-2 tailwind-font-medium tailwind-text-sm tailwind-text-center">
                Kéo & thả {getMediaTypeText()} hoặc nhấp để tải lên
              </p>
              <p className="tailwind-text-xs tailwind-text-gray-500 tailwind-text-center tailwind-px-2 tailwind-mt-1">
                {mediaType === 'image' && 'Hỗ trợ: JPG, PNG, GIF, SVG'}
                {mediaType === 'video' && 'Hỗ trợ: MP4, WebM, MOV'}
                {mediaType === 'audio' && 'Hỗ trợ: MP3, WAV, OGG'}
                {mediaType === 'model3d' && 'Hỗ trợ: GLB, OBJ, FBX'}
              </p>
              {renderUploadButton()}
            </div>
          )}
        </div>
      </div>
    );
  };

  return allowDrop ? renderDropArea() : renderUploadButton();
};

export default memo(MediaFileUploader);

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Button,
  message,
  Progress,
  Space,
  Input,
  Switch,
  Row,
  Col,
  Tooltip,
} from 'antd';
import {
  CheckCircleOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  LeftOutlined,
  FieldTimeOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  Question,
} from '../../../interfaces/quizs/questionBase';
import MultiSelectQuizComponent from './MultiSelectQuizComponent';
import { FireworksComponent } from '../quiz';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { Guid } from 'guid-typescript';
import {
  MultiSelectAnswer,
  MultiSelectQuestion,
} from '../../../interfaces/quizs/multiSelectQuiz.interface';
import { createMultiSelectQuestionTemplate } from './multiSelectTemplates';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

const { Title, Text } = Typography;

export interface IMultiSelectWrapperProps {
  id: string;
  question?: MultiSelectQuestion;
  showFireworks?: boolean;
  showTitle?: boolean;
  congratsMessage?: string;
  timeLimit?: number;
  showTimer?: boolean;
  autoSubmitOnTimeEnd?: boolean;
  countDirection?: 'up' | 'down';
  timerPosition?: 'top' | 'bottom';
  startButtonText?: string;
  startScreenTitle?: string;
  startScreenDescription?: string;
  showTimeCounter?: boolean; // Whether to show time spent counter
  timeSpent?: number; // Time spent on this question in seconds
}

export const multiSelectWrapperSchema = createComponentSchema({
  question: z.string().optional(),
  options: z.array(z.string()).optional(),
  correctAnswers: z.array(z.number()).optional(),
  showFeedback: z.boolean().optional(),
});

const MultiSelectWrapper: React.FC<
  IEdTechRenderProps<IMultiSelectWrapperProps>
> = (props) => {
  const { params = {} as IMultiSelectWrapperProps, addOrUpdateParamComponent } =
    props;

  // Extract values from params with defaults
  const {
    id = `multiselect-${Date.now()}`,
    question: initialQuestion,
    showFireworks = true,
    showTitle = true,
    congratsMessage = 'Chúc mừng bạn đã hoàn thành câu hỏi!',
    timeLimit: timeLimitParam = 0,
    showTimer = false,
    autoSubmitOnTimeEnd = true,
    countDirection = 'down',
    timerPosition = 'top',
    startButtonText = 'Bắt đầu',
    startScreenTitle = 'Bạn đã sẵn sàng?',
    startScreenDescription = 'Nhấn nút bắt đầu để làm bài.',
    showTimeCounter: initialShowTimeCounter = true,
    timeSpent: initialTimeSpent = 0,
  } = params;

  // State management
  const [question, setQuestion] = useState<MultiSelectQuestion>(
    initialQuestion ||
      createMultiSelectQuestionTemplate('Câu hỏi nhiều lựa chọn')
  );

  const [configMode, setConfigMode] = useState<boolean>(false);
  const [completed, setCompleted] = useState(false);
  const [showCongrats, setShowCongrats] = useState(false);
  const [showFireworksEffect, setShowFireworksEffect] = useState(false);
  const [started, setStarted] = useState(false);

  // Timer state
  const [timeLimit, setTimeLimit] = useState<number>(timeLimitParam);
  const [timeRemaining, setTimeRemaining] = useState(timeLimitParam);
  const [timerActive, setTimerActive] = useState(false);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const firstCreate = useRef<number>(0);

  // Time counter state
  const [timeSpent, setTimeSpent] = useState<number>(initialTimeSpent);
  const [showTimeCounter, setShowTimeCounter] = useState<boolean>(
    initialShowTimeCounter
  );
  const [timeCounterActive, setTimeCounterActive] = useState<boolean>(false);
  const timeCounterRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Update parameters in the parent component
  const handleUpdateParams = (values: { [key: string]: any }) => {
    addOrUpdateParamComponent({
      id,
      question,
      showFireworks,
      showTimer,
      showTitle,
      congratsMessage,
      timeLimit: timeLimit,
      autoSubmitOnTimeEnd,
      countDirection,
      startButtonText,
      startScreenDescription,
      startScreenTitle,
      timerPosition,
      showTimeCounter,
      timeSpent,
      ...values,
    });
  };

  // Start the quiz and timer if needed
  const handleStart = () => {
    console.log('Handle start called');
    setStarted(true);
    setConfigMode(false);

    // Force a state update cycle before starting timers
    setTimeout(() => {
      console.log('Starting timers after state update');

      // Start constraint timer if enabled
      if (timeLimit > 0 && showTimer) {
        startTimer();
      }

      // Always start time counter
      startTimeCounter();
    }, 50);
  };

  // Handle question completion
  const handleComplete = (
    questionId: string,
    selectedOptions?: MultiSelectAnswer[]
  ) => {
    setCompleted(true);
    stopTimer();
    stopTimeCounter();

    // Update question with results
    const updatedQuestion = {
      ...question,
      isCompleted: true,
      status: 'incorrect',
      userSelect: selectedOptions,
    };

    setQuestion(updatedQuestion);
    handleUpdateParams({
      question: updatedQuestion,
      timeSpent: timeSpent,
    });

    // Show congratulations and fireworks if correct
    // if (correct) {
    //   setShowCongrats(true);
    //   if (showFireworks) {
    //     setShowFireworksEffect(true);
    //     setTimeout(() => {
    //       setShowFireworksEffect(false);
    //     }, 2000);
    //   }
    // }
  };

  // Reset the quiz to initial state
  const handleReset = () => {
    setCompleted(false);
    setShowCongrats(false);
    setStarted(false);

    // Reset question status
    const resetQuestion = {
      ...question,
      isCompleted: false,
      status: undefined,
      userSelect: undefined,
    };

    setQuestion(resetQuestion);
    resetTimer();
    resetTimeCounter();
    handleUpdateParams({
      question: resetQuestion,
      timeSpent: 0,
    });
  };

  // Timer management functions
  const startTimer = () => {
    console.log('Starting timer. Current state:', {
      timerActive,
      timeLimit,
      timeRemaining,
      timerRef: !!timerRef.current,
    });

    if (timeLimit > 0 && !timerActive) {
      setTimerActive(true);
      setTimeRemaining(countDirection === 'down' ? timeLimit : 0);

      // Clear any existing interval first
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          const newValue = countDirection === 'down' ? prev - 1 : prev + 1;

          // Handle countdown completion
          if (countDirection === 'down' && newValue <= 0) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            setTimerActive(false);

            if (autoSubmitOnTimeEnd && !completed) {
              handleTimeUp();
            }
            return 0;
          }

          // Handle countup limit reached
          if (
            countDirection === 'up' &&
            timeLimit > 0 &&
            newValue >= timeLimit
          ) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            setTimerActive(false);

            if (autoSubmitOnTimeEnd && !completed) {
              handleTimeUp();
            }
            return timeLimit;
          }

          return newValue;
        });
      }, 1000);

      console.log('Timer started successfully');
    } else {
      console.log('Timer not started - conditions not met');
    }
  };

  const stopTimer = () => {
    console.log('Stopping timer. Current state:', {
      timerActive,
      timeRemaining,
      timerRef: !!timerRef.current,
    });

    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setTimerActive(false);
      console.log('Timer stopped successfully');
    } else {
      console.log('No timer interval to stop');
    }
  };

  const resetTimer = () => {
    stopTimer();
    setTimeRemaining(countDirection === 'down' ? timeLimit : 0);
    console.log('Timer reset to:', countDirection === 'down' ? timeLimit : 0);
  };

  // Time counter management functions
  const startTimeCounter = () => {
    console.log('Starting time counter. Current state:', {
      timeCounterActive,
      timeSpent,
      timeCounterRef: !!timeCounterRef.current,
    });

    if (!timeCounterActive) {
      setTimeCounterActive(true);

      // Clear any existing interval first
      if (timeCounterRef.current) {
        clearInterval(timeCounterRef.current);
        timeCounterRef.current = null;
      }

      // Create new interval
      timeCounterRef.current = setInterval(() => {
        setTimeSpent((prev) => {
          const newVal = prev + 1;
          return newVal;
        });
      }, 1000);

      console.log('Time counter started successfully');
    } else {
      console.log('Time counter was already active');
    }
  };

  const stopTimeCounter = () => {
    console.log('Stopping time counter. Current state:', {
      timeCounterActive,
      timeSpent,
      timeCounterRef: !!timeCounterRef.current,
    });

    if (timeCounterRef.current) {
      clearInterval(timeCounterRef.current);
      timeCounterRef.current = null;
      setTimeCounterActive(false);
      console.log('Time counter stopped successfully');
    } else {
      console.log('No time counter interval to stop');
    }
  };

  const resetTimeCounter = () => {
    stopTimeCounter();
    setTimeSpent(0);
    console.log('Time counter reset to 0');
  };

  // Submit when time is up
  const handleTimeUp = () => {
    handleComplete(question.id, []);
    message.warning('Hết thời gian làm bài!');
  };

  // Helper functions for UI
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  const calculateTimerPercentage = (): number => {
    if (timeLimit <= 0) return 0;
    return countDirection === 'down'
      ? (timeRemaining / timeLimit) * 100
      : (timeRemaining / timeLimit) * 100;
  };

  const getTimerStatus = (): 'success' | 'normal' | 'exception' | 'active' => {
    if (countDirection === 'down') {
      if (timeRemaining <= timeLimit * 0.25) return 'exception';
      if (timeRemaining <= timeLimit * 0.5) return 'normal';
      return 'success';
    } else {
      if (timeRemaining >= timeLimit * 0.75) return 'exception';
      if (timeRemaining >= timeLimit * 0.5) return 'normal';
      return 'success';
    }
  };

  // Component render functions
  const renderTimer = () => {
    if (!showTimer || timeLimit <= 0) return null;

    return (
      <div className="quiz-timer" style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ClockCircleOutlined style={{ marginRight: '8px' }} />
          <span style={{ marginRight: '16px' }}>
            Thời gian: {formatTime(timeRemaining)}
          </span>
          <Progress
            percent={calculateTimerPercentage()}
            status={getTimerStatus()}
            showInfo={false}
            style={{ flex: 1 }}
          />
        </div>
      </div>
    );
  };

  // Render time counter
  const renderTimeCounter = () => {
    if (!showTimeCounter) return null;

    return (
      <div className="time-counter" style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FieldTimeOutlined style={{ marginRight: '8px' }} />
          <span>Thời gian làm bài: {formatTime(timeSpent)}</span>
        </div>
      </div>
    );
  };

  const renderStartScreen = () => {
    return (
      <Card
        className="start-card"
        style={{
          textAlign: 'center',
          padding: '24px',
        }}
      >
        <div className="flex justify-end">
          <Button
            type="default"
            onClick={() => {
              setConfigMode(true);
              setStarted(true);
            }}
          >
            <SettingOutlined />
          </Button>
        </div>
        <Title level={3}>{startScreenTitle}</Title>
        <Text
          style={{ fontSize: '16px', display: 'block', marginBottom: '24px' }}
        >
          {startScreenDescription}
        </Text>

        {timeLimit > 0 && showTimer && (
          <Text
            style={{ display: 'block', marginBottom: '16px', color: '#1677ff' }}
          >
            <ClockCircleOutlined style={{ marginRight: '8px' }} />
            Thời gian làm bài: {formatTime(timeLimit)}
          </Text>
        )}

        <Button
          type="primary"
          size="large"
          onClick={handleStart}
          className="flex-1 bg-blue-500 hover:bg-blue-600"
          icon={<ClockCircleOutlined />}
        >
          {startButtonText}
        </Button>
      </Card>
    );
  };

  // Effect for initial state setup
  useEffect(() => {
    if (initialQuestion) {
      setQuestion(initialQuestion);
      setCompleted(!!initialQuestion.isCompleted);
      setShowCongrats(initialQuestion.status === 'correct');

      if (initialQuestion.isCompleted) {
        setStarted(true);
      }
    }
  }, [initialQuestion]);

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      stopTimer();
      stopTimeCounter();
    };
  }, []);

  // Stop timers when question is completed
  useEffect(() => {
    if (completed) {
      if (timerActive) {
        stopTimer();
      }
      if (timeCounterActive) {
        stopTimeCounter();
      }
    }
  }, [completed, timerActive, timeCounterActive]);

  // Set config mode on first render if no question is provided
  useEffect(() => {
    if (firstCreate.current < 1 && !props.params?.question) {
      setConfigMode(true);
      setStarted(true);
      firstCreate.current++;
    }
  }, [props.params?.question]);

  // Check if we need to start the timers when component mounts or state changes
  useEffect(() => {
    // If the question is already started but not completed, restart the time counter
    if (started && !completed && !configMode) {
      console.log('Auto-starting timers based on state change');

      // Start constraint timer if enabled
      if (timeLimit > 0 && showTimer && !timerActive) {
        startTimer();
      }

      // Start time counter if not already active
      if (!timeCounterActive) {
        startTimeCounter();
      }
    }
  }, [started, completed, configMode]);

  // PracticeEngine context handlers
  function handleChangeQuestion(update: Question) {
    setConfigMode(false);
    setStarted(false);
    setQuestion(update as MultiSelectQuestion);
    handleUpdateParams({
      id: id,
      question: update as MultiSelectQuestion,
    });
  }

  function handleDeleteQuestion(_id: string) {
    message.info('Xóa câu hỏi này sẽ được xử lý bởi component cha');
  }

  // Main render
  return (
    <PracticeEngineContext.Provider
      value={{
        handleChangePosition: () => {},
        handleChangeQuestion: handleChangeQuestion,
        handleDeleteQuestion: handleDeleteQuestion,
        handleToggleFullscreen: () => {},
        isFullscreen: false,
      }}
    >
      <div
        id={id}
        className="multiselect-quiz-wrapper bg-white rounded-lg shadow-md p-5 max-w-6xl mx-auto"
        style={{ position: 'relative' }}
      >
        {showTitle && (
          <Title level={4} style={{ marginBottom: '16px' }}>
            {question.title}
          </Title>
        )}

        {!started ? (
          renderStartScreen()
        ) : (
          <>
            {timerPosition === 'top' && renderTimer()}
            {showTimeCounter && renderTimeCounter()}

            {showCongrats ? (
              <Card
                className="congrats-card"
                style={{
                  textAlign: 'center',
                  padding: '24px',
                  backgroundColor: '#f6ffed',
                  borderColor: '#b7eb8f',
                }}
              >
                <TrophyOutlined
                  style={{
                    fontSize: '48px',
                    color: '#52c41a',
                    marginBottom: '16px',
                  }}
                />
                <Title level={3} style={{ color: '#52c41a' }}>
                  <CheckCircleOutlined /> {congratsMessage}
                </Title>
                <Text
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    marginBottom: '24px',
                  }}
                >
                  Bạn đã trả lời chính xác câu hỏi. Tiếp tục cố gắng nhé!
                </Text>
                <Button
                  type="primary"
                  onClick={handleReset}
                  className="flex-1 bg-blue-500 hover:bg-blue-600"
                >
                  Làm lại
                </Button>
              </Card>
            ) : (
              <>
                <Space className="flex mb-2 justify-between">
                  {configMode && (
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Row gutter={16} align="middle">
                        <Col>
                          <Input
                            type="number"
                            value={timeLimit}
                            onChange={(e) => {
                              const value = Number.parseInt(e.target.value);
                              setTimeLimit(value ?? 0);
                              setTimeRemaining(value ?? 0);
                              handleUpdateParams({
                                id,
                                question: question,
                                timeLimit: value,
                              });
                            }}
                            placeholder="Thời gian làm"
                          />
                        </Col>
                        <Col>
                          <Tooltip title="Hiển thị thời gian làm bài">
                            <Space>
                              <span>Hiện thời gian:</span>
                              <Switch
                                checked={showTimeCounter}
                                onChange={(checked) => {
                                  setShowTimeCounter(checked);
                                  handleUpdateParams({
                                    showTimeCounter: checked,
                                  });
                                }}
                              />
                            </Space>
                          </Tooltip>
                        </Col>
                      </Row>
                    </Space>
                  )}
                  <Button
                    type="default"
                    onClick={() => {
                      handleReset();
                    }}
                  >
                    <LeftOutlined />
                  </Button>
                </Space>
                <MultiSelectQuizComponent
                  id={Guid.create().toString()}
                  question={question}
                  onComplete={handleComplete}
                  showFeedback={true}
                  allowManyTimes={true}
                  configMode={configMode}
                  disabled={timeRemaining === 0 && timeLimit > 0}
                />
              </>
            )}

            {timerPosition === 'bottom' && renderTimer()}
          </>
        )}

        <FireworksComponent show={showFireworksEffect} />
      </div>
    </PracticeEngineContext.Provider>
  );
};

export default withEdComponentParams(MultiSelectWrapper);

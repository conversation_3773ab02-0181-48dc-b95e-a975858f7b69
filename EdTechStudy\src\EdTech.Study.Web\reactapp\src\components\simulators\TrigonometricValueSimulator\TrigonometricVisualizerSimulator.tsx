import React, { memo, useState } from 'react';
import {
  TrigonometricConfig,
  defaultTrigonometricConfig,
} from './TrigonometricConfig';
import TrigonometricConfigModal from './TrigonometricConfigModal';
import { getTranslations } from './TrigonometricUtils';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import TrigonometricVisualizerSimulatorComponent from './TrigonometricVisualizerSimulatorComponent';
import EngineContainer from '../../common/engines/EngineContainer';
import { ITextProps } from '../../core/title/CoreTitle';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

interface TrigonometricVisualizerSimulatorProps extends TrigonometricConfig {}

export const trigonometricVisualizerSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    function: z.enum(['sin', 'cos', 'tan']).optional(),
    amplitude: z.number().optional(),
    frequency: z.number().optional(),
    phase: z.number().optional(),
    showGrid: z.boolean().optional(),
    showLabels: z.boolean().optional(),
  },
});

const TrigonometricVisualizerSimulator: React.FC<
  IEdTechRenderProps<TrigonometricVisualizerSimulatorProps>
> = (props) => {
  const { params = {}, addOrUpdateParamComponent, path } = props;
  // Initialize visualization config
  const [visualizerConfig, setVisualizerConfig] = useState<TrigonometricConfig>(
    {
      ...defaultTrigonometricConfig,
      ...params,
    }
  );

  // UI state
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);

  // Get translations
  const translations = getTranslations(visualizerConfig.language);

  // Handle config modal submission
  const handleConfigSubmit = (config: TrigonometricConfig) => {
    setVisualizerConfig(config);
    if (addOrUpdateParamComponent) {
      addOrUpdateParamComponent(config);
    }
    setIsConfigModalOpen(false);
  };

  // Get titleProps from config
  const titleProps = visualizerConfig.titleProps || {
    text: translations.title,
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Handle title updates
  const handleTitleUpdate = (updates: Partial<ITextProps>) => {
    const updatedConfig = {
      ...visualizerConfig,
      titleProps: {
        ...visualizerConfig.titleProps,
        ...updates,
      },
    };
    setVisualizerConfig(updatedConfig);
    if (addOrUpdateParamComponent) {
      addOrUpdateParamComponent(updatedConfig);
    }
  };

  // Instructions content for EngineContainer using structured approach
  const instructionsContent = {
    objective: translations.instructionsText,
    steps: [
      translations.instruction1,
      translations.instruction2,
      translations.instruction3,
      translations.instruction4,
      translations.instruction5,
    ],
    isFullscreen: false,
  };

  // Main component for EngineContainer
  const mainComponent = (
    <TrigonometricVisualizerSimulatorComponent config={visualizerConfig} />
  );

  // Configuration modal for EngineContainer
  const configModal = (
    <TrigonometricConfigModal
      isOpen={isConfigModalOpen}
      onClose={() => setIsConfigModalOpen(false)}
      onSubmit={handleConfigSubmit}
      defaultConfig={visualizerConfig}
    />
  );

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      onTitleUpdate={handleTitleUpdate}
      mainComponent={mainComponent}
      configModal={configModal}
      instructionsContent={instructionsContent}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      id={path}
    />
  );
};

export default memo(withEdComponentParams(TrigonometricVisualizerSimulator));

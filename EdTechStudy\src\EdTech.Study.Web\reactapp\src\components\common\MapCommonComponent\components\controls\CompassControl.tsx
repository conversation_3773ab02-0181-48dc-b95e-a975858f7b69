import React, { useState, useEffect } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import { ControlPosition } from '../../MapUtils';

interface CompassControlProps {
  position?: ControlPosition;
  size?: number;
}

/**
 * Component hiển thị la bàn trên bản đồ
 */
const CompassControl: React.FC<CompassControlProps> = ({
  position = 'topright',
  size = 30,
}) => {
  const map = useMap();
  const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {
    // Tạo element cho la bàn
    const container = L.DomUtil.create('div');
    container.className = 'leaflet-control-compass leaflet-bar leaflet-control';
    container.style.width = `${size}px`;
    container.style.height = `${size}px`;
    container.style.background = 'white';
    container.style.cursor = 'pointer';
    container.style.display = 'flex';
    container.style.alignItems = 'center';
    container.style.justifyContent = 'center';
    container.style.marginBottom = '10px'; // Thêm margin để tạo khoảng cách với control phía dưới

    // Tạo element SVG cho la bàn
    const compassSvg = document.createElementNS(
      'http://www.w3.org/2000/svg',
      'svg'
    );
    compassSvg.setAttribute('viewBox', '0 0 100 100');
    compassSvg.setAttribute('width', '80%');
    compassSvg.setAttribute('height', '80%');

    // Vẽ mũi tên chỉ hướng Bắc
    const northArrow = document.createElementNS(
      'http://www.w3.org/2000/svg',
      'path'
    );
    northArrow.setAttribute('d', 'M50,10 L60,50 L50,45 L40,50 Z');
    northArrow.setAttribute('fill', 'red');

    // Vẽ mũi tên chỉ hướng Nam
    const southArrow = document.createElementNS(
      'http://www.w3.org/2000/svg',
      'path'
    );
    southArrow.setAttribute('d', 'M50,90 L60,50 L50,55 L40,50 Z');
    southArrow.setAttribute('fill', '#666');

    // Thêm vòng tròn nền
    const circle = document.createElementNS(
      'http://www.w3.org/2000/svg',
      'circle'
    );
    circle.setAttribute('cx', '50');
    circle.setAttribute('cy', '50');
    circle.setAttribute('r', '40');
    circle.setAttribute('fill', 'none');
    circle.setAttribute('stroke', '#666');
    circle.setAttribute('stroke-width', '2');

    // Thêm chữ N
    const textN = document.createElementNS(
      'http://www.w3.org/2000/svg',
      'text'
    );
    textN.setAttribute('x', '50');
    textN.setAttribute('y', '25');
    textN.setAttribute('text-anchor', 'middle');
    textN.setAttribute('fill', 'red');
    textN.setAttribute('font-size', '14');
    textN.setAttribute('font-weight', 'bold');
    textN.textContent = 'N';

    // Thêm các elements vào SVG
    compassSvg.appendChild(circle);
    compassSvg.appendChild(northArrow);
    compassSvg.appendChild(southArrow);
    compassSvg.appendChild(textN);
    container.appendChild(compassSvg);

    // Tạo popup hướng dẫn
    const popup = L.popup({
      className: 'compass-popup',
      closeButton: true,
      autoClose: true,
      closeOnEscapeKey: true,
      closeOnClick: true,
      offset: [0, -5],
    }).setContent(`
      <div style="padding: 10px;">
        <h3 style="font-weight: bold; margin-bottom: 8px;">Hướng dẫn sử dụng la bàn</h3>
        <ul style="list-style-type: disc; padding-left: 20px;">
          <li>Mũi tên đỏ chỉ hướng Bắc</li>
          <li>Mũi tên xám chỉ hướng Nam</li>
          <li>Nhấp vào la bàn để đưa bản đồ về hướng Bắc</li>
        </ul>
      </div>
    `);

    // Xử lý sự kiện click
    container.addEventListener('click', (e) => {
      e.stopPropagation();
      if (!showPopup) {
        // Tính toán vị trí popup dựa trên vị trí của control
        const containerPoint = L.DomUtil.getPosition(container);
        const point = map.containerPointToLatLng([
          containerPoint.x + size / 2,
          containerPoint.y + size + 10,
        ]);
        popup.setLatLng(point).openOn(map);
        setShowPopup(true);
      } else {
        map.closePopup();
        setShowPopup(false);
        // Reset map về hướng Bắc
        const center = map.getCenter();
        const zoom = map.getZoom();
        map.setView([center.lat, center.lng], zoom, { animate: true });
      }
    });

    // Tạo control
    const CompassInfo = L.Control.extend({
      options: {
        position: position as L.ControlPosition,
      },
      onAdd: function () {
        return container;
      },
    });

    const compassControl = new CompassInfo();
    compassControl.addTo(map);

    return () => {
      map.closePopup();
      if (compassControl) {
        compassControl.remove();
      }
    };
  }, [map, position, size, showPopup]);

  return null;
};

export default CompassControl;

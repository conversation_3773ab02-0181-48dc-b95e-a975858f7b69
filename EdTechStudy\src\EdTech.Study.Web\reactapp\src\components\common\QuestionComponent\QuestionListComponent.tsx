import React, { useState, useEffect, useMemo } from 'react';
import { Question, DifficultyLevel, QuestionType } from './QuestionBankConfig';
import {
  Table,
  Tag,
  Button,
  Input,
  Select,
  Space,
  Badge,
  Typography,
  Tooltip,
  message,
} from 'antd';
import {
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { updateQuestionsOrder } from './questionOrderUtils';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import { AddOulinedIcon, DeleteIcon, EditIcon } from '../../icons/IconIndex';

const { Option } = Select;
const { Text } = Typography;

// Define sort options
type SortField = 'questionText' | 'difficulty' | 'type' | 'points' | 'none';
type SortDirection = 'ascend' | 'descend';

interface QuestionListComponentProps {
  questions: Question[];
  onEdit: (question: Question, index: number) => void;
  onDelete: (questionIds: string[]) => void;
  onAdd?: () => void;
  onReorder?: (newQuestions: Question[]) => void;
  tableHeight?: string;
  showAddButton?: boolean;
  showEditButtons?: boolean;
  showDeleteButtons?: boolean;
  showFilters?: boolean;
  showSorting?: boolean;
  allowManualReordering?: boolean;
}

// Type for drag-drop items
interface DragItem {
  index: number;
  id: string;
  type: string;
}

// Draggable Row component for reordering
const DraggableRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}: any) => {
  const ref = React.useRef<HTMLTableRowElement>(null);

  // useDrop hook để xử lý khi một phần tử được thả vào hàng này
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: 'DraggableRow',
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? 'drop-over-downward' : 'drop-over-upward',
      };
    },
    drop: (item: DragItem) => {
      if (item.index !== index) {
        moveRow(item.index, index);
      }
    },
  });

  // useDrag hook để xử lý khi hàng này được kéo
  const [{ isDragging }, drag] = useDrag({
    type: 'DraggableRow',
    item: { index, type: 'DraggableRow', id: String(index) },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (_, monitor) => {
      // Kiểm tra xem có thả vào một mục đích hợp lệ hay không
      const didDrop = monitor.didDrop();
      if (!didDrop) {
        console.log('Drag cancelled');
      }
    },
  });

  // Kết hợp cả hai hook vào cùng một ref
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className} ${isOver ? dropClassName : ''} ${
        isDragging ? 'tailwind-opacity-50' : ''
      }`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};

const QuestionListComponent: React.FC<QuestionListComponentProps> = ({
  questions,
  onEdit,
  onDelete,
  onAdd,
  onReorder,
  tableHeight,
  showAddButton = true,
  showEditButtons = true,
  showDeleteButtons = true,
  showFilters = false,
  showSorting = true,
  allowManualReordering = false,
}) => {
  // State for filtering questions
  const [typeFilter, setTypeFilter] = useState<QuestionType | 'all'>('all');
  const [difficultyFilter, setDifficultyFilter] = useState<
    DifficultyLevel | 'all'
  >('all');
  const [searchQuery, setSearchQuery] = useState('');

  // State for sorting questions
  const [sortField, setSortField] = useState<SortField>(
    allowManualReordering ? 'none' : 'questionText'
  );
  const [sortDirection, setSortDirection] = useState<SortDirection>('ascend');

  // Add state for pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const pageSize = 10;

  // Add state for row selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Add state for questions with order information
  const [questionsWithOrder, setQuestionsWithOrder] = useState<Question[]>([]);

  // State to track whether the order has been changed
  const [orderChanged, setOrderChanged] = useState<boolean>(false);

  // Add this useEffect to ensure sortedQuestions reflects questionsWithOrder
  useEffect(() => {
    if (allowManualReordering) {
      // Disable automatic sorting when manual reordering is enabled
      setSortField('none' as any);
      setSortDirection('ascend');
    }
  }, [allowManualReordering]);

  // Effect to update questions with order information
  useEffect(() => {
    // Add order property to each question if not already present
    const updatedQuestions = questions.map((question, index) => ({
      ...question,
      order: question.order !== undefined ? question.order : index + 1,
    }));
    setQuestionsWithOrder(updatedQuestions);
    setOrderChanged(false); // Reset order changed flag when questions change
  }, [questions]);

  // Effect to reset selected keys when questions list changes
  useEffect(() => {
    // Reset selected keys when questions change (after deletion, etc.)
    setSelectedRowKeys((prevKeys) => {
      // Filter out any keys that no longer exist in the questions array
      const validKeys = prevKeys.filter((key) =>
        questions.some((q) => q.id === key)
      );
      return validKeys;
    });

    // Reset to first page when questions length changes significantly
    if (Math.ceil(questions.length / pageSize) < currentPage) {
      setCurrentPage(1);
    }
  }, [questions, pageSize, currentPage]);

  // Filter questions based on filters
  const filteredQuestions = questionsWithOrder.filter((question) => {
    // Type filter
    if (typeFilter !== 'all' && question.type !== typeFilter) return false;

    // Difficulty filter
    if (difficultyFilter !== 'all' && question.difficulty !== difficultyFilter)
      return false;

    // Search query (check if the query exists in the question text)
    if (
      searchQuery &&
      !question.questionText.toLowerCase().includes(searchQuery.toLowerCase())
    )
      return false;

    return true;
  });

  // Sort questions
  const sortedQuestions = useMemo(() => {
    // If we're in manual reordering mode and sortField is 'none',
    // return questions in their current order without sorting
    if (allowManualReordering && sortField === 'none') {
      return [...filteredQuestions];
    }

    return [...filteredQuestions].sort((a, b) => {
      let valueA: any;
      let valueB: any;

      // Extract values to compare based on the sort field
      switch (sortField) {
        case 'questionText':
          valueA = a.questionText.toLowerCase();
          valueB = b.questionText.toLowerCase();
          break;
        case 'difficulty': {
          // Convert difficulty to numeric value for sorting
          const difficultyOrder = { easy: 1, medium: 2, hard: 3, expert: 4 };
          valueA = difficultyOrder[a.difficulty] || 0;
          valueB = difficultyOrder[b.difficulty] || 0;
          break;
        }
        case 'type':
          valueA = a.type;
          valueB = b.type;
          break;
        case 'points':
          valueA = a.points;
          valueB = b.points;
          break;
        default:
          // For 'none' or any other value, sort by order if available
          valueA = a.order || 9999;
          valueB = b.order || 9999;
      }

      // Sort in ascending or descending order
      if (sortDirection === 'ascend') {
        return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
      } else {
        return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
      }
    });
  }, [filteredQuestions, sortField, sortDirection, allowManualReordering]);

  // Function to handle row movement (drag and drop)
  const moveRow = (dragIndex: number, hoverIndex: number) => {
    // Lấy dòng đang được kéo
    const dragRow = sortedQuestions[dragIndex];

    // Tạo dữ liệu mới với thứ tự đã thay đổi
    const newData = update(sortedQuestions, {
      $splice: [
        [dragIndex, 1],
        [hoverIndex, 0, dragRow],
      ],
    });

    // Cập nhật thứ tự dựa trên vị trí mới
    const reorderedData = updateQuestionsOrder(newData);

    // Tạo map để tra cứu nhanh
    const updatedQuestionsMap = new Map<string, Question>();
    reorderedData.forEach((q) => updatedQuestionsMap.set(q.id, q));

    // Cập nhật state với câu hỏi đã sắp xếp lại
    setQuestionsWithOrder(
      questionsWithOrder.map((q) => {
        return updatedQuestionsMap.get(q.id) || q;
      })
    );

    // Đảm bảo luôn gọi onReorder để thông báo cho component cha
    if (onReorder) {
      onReorder(reorderedData);
    }

    // Cập nhật trạng thái để UI biết rằng thứ tự đã thay đổi
    setOrderChanged(true);
  };

  // Modified rowSelection
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeys(selectedKeys);
    },
    getCheckboxProps: (record: Question) => ({
      // Make sure we can identify each row uniquely
      name: record.id,
    }),
  };

  // Handle delete multiple questions
  const handleDeleteMultiple = () => {
    if (selectedRowKeys.length > 0) {
      const idsToDelete = selectedRowKeys as string[];
      onDelete(idsToDelete);
      // Note: No need to reset selectedRowKeys here as the useEffect will handle that
      // when the questions prop changes after deletion
    }
  };

  // Function to get difficulty badge color
  const getDifficultyColor = (difficulty: DifficultyLevel): string => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'processing';
      case 'hard':
        return 'warning';
      case 'expert':
        return 'error';
      default:
        return 'default';
    }
  };

  // Function to get difficulty text
  const getDifficultyText = (difficulty: DifficultyLevel): string => {
    switch (difficulty) {
      case 'easy':
        return 'Dễ';
      case 'medium':
        return 'Trung bình';
      case 'hard':
        return 'Khó';
      case 'expert':
        return 'Chuyên gia';
      default:
        return '';
    }
  };

  // Function to get question type text
  const getQuestionTypeText = (type: QuestionType): string => {
    switch (type) {
      case 'multiplechoice':
        return 'Trắc nghiệm';
      case 'truefalse':
        return 'Đúng/Sai';
      case 'fillin':
        return 'Điền từ';
      case 'ordering':
        return 'Sắp xếp';
      case 'matching':
        return 'Ghép cặp';
      default:
        return '';
    }
  };

  // Function to get formatted answer text
  const getCorrectAnswerText = (question: Question): string => {
    switch (question.type) {
      case 'multiplechoice':
        return question.options[question.correctOptionIndex];
      case 'truefalse':
        return question.isCorrect ? 'True' : 'False';
      case 'fillin':
        return question.correctAnswer;
      case 'ordering':
        return 'Custom Order'; // Simplified for display
      case 'matching':
        return 'Matching Pairs'; // Simplified for display
      default:
        return '';
    }
  };

  // Function to truncate text if too long
  const truncateText = (text: string, maxLength: number = 100): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  };

  // Define table columns
  const columns: ColumnsType<Question> = [];

  // Add standard index column - this will serve as the visible order indicator
  columns.push({
    title: '#',
    key: 'index',
    width: 60,
    render: (_text, _record, index) => (currentPage - 1) * pageSize + index + 1,
  });

  // Add question text column
  columns.push({
    title: (
      <Space>
        Câu hỏi
        {showSorting && (
          <Button
            type="text"
            icon={
              sortField === 'questionText' ? (
                sortDirection === 'ascend' ? (
                  <SortAscendingOutlined />
                ) : (
                  <SortDescendingOutlined />
                )
              ) : (
                <SortAscendingOutlined />
              )
            }
            size="small"
            onClick={() => handleSort('questionText')}
          />
        )}
      </Space>
    ),
    dataIndex: 'questionText',
    key: 'questionText',
    render: (text: string, record: Question) => (
      <div>
        <Text>{truncateText(text)}</Text>
        {record.tags && record.tags.length > 0 && (
          <div className="tailwind-mt-1 tailwind-flex tailwind-flex-wrap tailwind-gap-1">
            {record.tags.map((tag, index) => (
              <Tag key={index} className="tailwind-m-0">
                {tag}
              </Tag>
            ))}
          </div>
        )}
      </div>
    ),
  });

  // Add answer column
  columns.push({
    title: 'Đáp án đúng',
    key: 'answer',
    render: (_text, record: Question) => (
      <Tooltip title={getCorrectAnswerText(record)}>
        <Text>{truncateText(getCorrectAnswerText(record), 60)}</Text>
      </Tooltip>
    ),
  });

  // Add difficulty column
  columns.push({
    title: (
      <Space>
        Độ khó
        {showSorting && (
          <Button
            type="text"
            icon={
              sortField === 'difficulty' ? (
                sortDirection === 'ascend' ? (
                  <SortAscendingOutlined />
                ) : (
                  <SortDescendingOutlined />
                )
              ) : (
                <SortAscendingOutlined />
              )
            }
            size="small"
            onClick={() => handleSort('difficulty')}
          />
        )}
      </Space>
    ),
    dataIndex: 'difficulty',
    key: 'difficulty',
    width: 120,
    render: (difficulty: DifficultyLevel) => (
      <Badge
        status={
          getDifficultyColor(difficulty) as
            | 'success'
            | 'processing'
            | 'error'
            | 'default'
            | 'warning'
        }
        text={getDifficultyText(difficulty)}
      />
    ),
  });

  // Add type column
  columns.push({
    title: (
      <Space>
        Loại
        {showSorting && (
          <Button
            type="text"
            icon={
              sortField === 'type' ? (
                sortDirection === 'ascend' ? (
                  <SortAscendingOutlined />
                ) : (
                  <SortDescendingOutlined />
                )
              ) : (
                <SortAscendingOutlined />
              )
            }
            size="small"
            onClick={() => handleSort('type')}
          />
        )}
      </Space>
    ),
    dataIndex: 'type',
    key: 'type',
    width: 120,
    render: (type: QuestionType) => (
      <Tag color="purple">{getQuestionTypeText(type)}</Tag>
    ),
  });

  // Add points column
  columns.push({
    title: (
      <Space>
        Điểm
        {showSorting && (
          <Button
            type="text"
            icon={
              sortField === 'points' ? (
                sortDirection === 'ascend' ? (
                  <SortAscendingOutlined />
                ) : (
                  <SortDescendingOutlined />
                )
              ) : (
                <SortAscendingOutlined />
              )
            }
            size="small"
            onClick={() => handleSort('points')}
          />
        )}
      </Space>
    ),
    dataIndex: 'points',
    key: 'points',
    width: 80,
  });

  // Handle sorting when user clicks on a column header
  const handleSort = (field: SortField) => {
    // If manual reordering is enabled, we shouldn't allow sorting by columns
    if (allowManualReordering) {
      message.info(
        'Không thể sắp xếp khi đang bật chế độ kéo thả thứ tự câu hỏi'
      );
      return;
    }

    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'ascend' ? 'descend' : 'ascend');
    } else {
      // Set new sort field and default to ascending direction
      setSortField(field);
      setSortDirection('ascend');
    }
  };

  // Add actions column if needed
  if (showEditButtons || showDeleteButtons) {
    columns.push({
      title: 'Thao tác',
      key: 'action',
      width: 120,
      render: (_text, record: Question, index: number) => (
        <Space>
          {showEditButtons && (
            <Button
              type="text"
              icon={<EditIcon />}
              onClick={() => onEdit(record, index)}
              className="tailwind-text-indigo-600 hover:tailwind-text-indigo-900"
            />
          )}
          {showDeleteButtons && (
            <PopconfirmAntdCustom
              title="Bạn có chắc chắn muốn xóa câu hỏi này?"
              onConfirm={() => onDelete([record.id])}
              okText="Có"
              cancelText="Không"
            >
              <Button type="text" danger icon={<DeleteIcon />} />
            </PopconfirmAntdCustom>
          )}
        </Space>
      ),
    });
  }
  // Components for drag-and-drop functionality
  const components = allowManualReordering
    ? {
        body: {
          row: DraggableRow,
        },
      }
    : undefined;

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="question-list-component">
        {/* Filters Section */}
        {showFilters && (
          <div className="question-list-header">
            <Space className="tailwind-flex-wrap" size="middle">
              <div style={{ minWidth: '240px' }}>
                <Text className="tailwind-block tailwind-mb-1">
                  Tìm kiếm câu hỏi
                </Text>
                <Input
                  placeholder="Nhập nội dung câu hỏi..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  prefix={<SearchOutlined />}
                  allowClear
                />
              </div>

              <div>
                <Text className="tailwind-block tailwind-mb-1">
                  Loại câu hỏi
                </Text>
                <Select
                  value={typeFilter}
                  onChange={(value) =>
                    setTypeFilter(value as QuestionType | 'all')
                  }
                  style={{ width: '160px' }}
                >
                  <Option value="all">Tất cả</Option>
                  <Option value="truefalse">Đúng/Sai</Option>
                  <Option value="multiplechoice">Nhiều lựa chọn</Option>
                  <Option value="fillin">Điền vào chỗ trống</Option>
                  <Option value="ordering">Sắp xếp thứ tự</Option>
                  <Option value="matching">Ghép cặp</Option>
                  <Option value="obstacle">Chướng ngại vật</Option>
                </Select>
              </div>

              <div>
                <Text className="tailwind-block tailwind-mb-1">Độ khó</Text>
                <Select
                  value={difficultyFilter}
                  onChange={(value) =>
                    setDifficultyFilter(value as DifficultyLevel | 'all')
                  }
                  style={{ width: '160px' }}
                >
                  <Option value="all">Tất cả</Option>
                  <Option value="easy">Dễ</Option>
                  <Option value="medium">Trung bình</Option>
                  <Option value="hard">Khó</Option>
                  <Option value="expert">Chuyên gia</Option>
                </Select>
              </div>

              {showSorting && (
                <div>
                  <Text className="tailwind-block tailwind-mb-1">
                    Sắp xếp theo
                  </Text>
                  <Select
                    value={sortField}
                    onChange={(value) => {
                      setSortField(value as SortField);
                      setSortDirection('ascend');
                    }}
                    style={{ width: '160px' }}
                  >
                    <Option value="questionText">Câu hỏi</Option>
                    <Option value="difficulty">Độ khó</Option>
                    <Option value="type">Loại câu hỏi</Option>
                    <Option value="points">Điểm</Option>
                  </Select>
                  <Button
                    type="text"
                    icon={
                      sortDirection === 'ascend' ? (
                        <SortAscendingOutlined />
                      ) : (
                        <SortDescendingOutlined />
                      )
                    }
                    onClick={() =>
                      setSortDirection(
                        sortDirection === 'ascend' ? 'descend' : 'ascend'
                      )
                    }
                    className="tailwind-ml-2"
                  />
                </div>
              )}
            </Space>
          </div>
        )}

        {/* Add Question Button and Delete Multiple Button */}
        <div className="tailwind-flex tailwind-justify-between tailwind-mb-2">
          <Space>
            {showAddButton && onAdd && (
              <Button
                type="primary"
                icon={<AddOulinedIcon />}
                onClick={onAdd}
                className="tailwind-bg-indigo-600 hover:tailwind-bg-indigo-700"
              >
                Thêm câu hỏi
              </Button>
            )}
          </Space>
          {showDeleteButtons && (
            <PopconfirmAntdCustom
              title={`Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} câu hỏi đã chọn?`}
              onConfirm={handleDeleteMultiple}
              okText="Có"
              cancelText="Không"
              disabled={selectedRowKeys.length === 0}
            >
              <Button
                type="primary"
                danger
                icon={<DeleteIcon />}
                disabled={selectedRowKeys.length === 0}
              >
                Xóa đã chọn{' '}
                {selectedRowKeys.length > 0
                  ? `(${selectedRowKeys.length})`
                  : ''}
              </Button>
            </PopconfirmAntdCustom>
          )}
        </div>

        {/* Question Table with drag-and-drop support */}
        <Table
          key={`question-table-${questions.length}-${orderChanged}`}
          rowKey="id"
          components={components}
          rowSelection={showDeleteButtons ? rowSelection : undefined}
          columns={columns}
          dataSource={sortedQuestions}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: sortedQuestions.length,
            onChange: (page) => setCurrentPage(page),
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={(_pagination, _filters, sorter) => {
            if (Array.isArray(sorter)) {
              // Handle multiple sorters if needed
            } else if (sorter.order) {
              setSortField(sorter.field as SortField);
              setSortDirection(sorter.order as SortDirection);
            } else {
              // Reset sorting if cleared
              setSortField(allowManualReordering ? 'none' : 'questionText');
              setSortDirection('ascend');
            }
          }}
          scroll={{ y: tableHeight ? tableHeight : undefined }}
          size="middle"
          locale={{
            emptyText: 'Chưa có câu hỏi nào. Vui lòng thêm câu hỏi mới!',
          }}
          className="tailwind-bg-white tailwind-rounded-lg tailwind-shadow"
          onRow={
            allowManualReordering
              ? (record, index) =>
                  ({
                    index,
                    moveRow,
                    // Add these properties to satisfy TypeScript
                    key: record.id,
                    'data-row-key': record.id,
                  } as any)
              : undefined
          }
        />

        {/* Add some CSS for drag indicators and better styling */}
        <style>{`
          .tailwind-drop-over-downward {
            border-bottom: 2px dashed #1890ff;
          }
          .tailwind-drop-over-upward {
            border-top: 2px dashed #1890ff;
          }
          .ant-table-row {
            transition: all 0.3s;
          }
          .ant-table-row:hover {
            background-color: #f5f5f5;
          }
          tr.drop-over-downward td {
            border-bottom: 2px dashed #1890ff;
          }
          tr.drop-over-upward td {
            border-top: 2px dashed #1890ff;
          }
          .tailwind-drag-handle {
            cursor: grab;
            font-size: 16px;
            display: flex;
            align-items: center;
          }
          .tailwind-drag-handle:hover {
            color: #1890ff;
          }
          .tailwind-reorder-cell {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 4px 0;
          }
          .ant-table-row:hover .tailwind-drag-handle {
            background-color: #f0f0f0;
            border-radius: 4px;
            padding: 4px;
          }
        `}</style>
      </div>
    </DndProvider>
  );
};

export default QuestionListComponent;

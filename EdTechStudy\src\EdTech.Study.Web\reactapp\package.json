{"name": "reactapp", "version": "0.1.0", "private": true, "scripts": {"start": "webpack-dev-server --config webpack.config.js --mode development", "build": "webpack --env production --mode production", "yarn build_prd": "yarn build && yarn buildJS", "preview": "vite preview"}, "devDependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/postcss": "^4.0.17", "@types/jest": "^27.5.2", "@types/leaflet": "^1.9.17", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-image-gallery": "^1.2.4", "@vitejs/plugin-react": "^4.2.0", "antd": "^5.24.9", "axios": "^1.8.4", "buffer": "^6.0.3", "clean-webpack-plugin": "^4.0.0", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.2", "node-polyfill-webpack-plugin": "^4.1.0", "node-stdlib-browser": "^1.3.1", "postcss-loader": "^8.1.1", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "stream-browserify": "^3.0.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "util": "^0.12.5", "vite": "^5.0.0", "vite-plugin-html": "^3.2.0", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.1", "copy-webpack-plugin": "^11.0.0", "worker-loader": "^3.0.8"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@react-three/drei": "9.92.0", "@react-three/fiber": "8.15.12", "@syncfusion/ej2-react-richtexteditor": "^29.2.4", "@tsp/utils": "^1.0.35", "@types/three": "0.158.3", "autoprefixer": "^10.4.21", "dompurify": "^3.2.5", "guid-typescript": "^1.0.9", "immutability-helper": "^3.1.1", "katex": "^0.16.22", "leaflet": "^1.9.4", "leaflet-compass": "^1.5.6", "leaflet-geometryutil": "^0.10.3", "lowlight": "^2.8.1", "mathjax": "^3.2.2", "mathlive": "^0.103.0", "nanoid": "^5.1.5", "p": "^0.2.0", "postcss": "^8.5.3", "rc-tree": "^5.13.1", "react-beautiful-dnd": "^13.1.1", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-h5-audio-player": "^3.10.0-rc.1", "react-leaflet": "^4.0.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "tailwindcss": "3.3.3", "three": "0.159.0", "vite-plugin-lib-inject-css": "^2.2.2", "zod": "^3.24.3"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
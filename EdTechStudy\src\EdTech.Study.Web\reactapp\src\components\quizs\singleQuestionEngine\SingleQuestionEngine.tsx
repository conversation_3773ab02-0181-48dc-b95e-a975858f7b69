import React, { useState, useContext, useEffect, useCallback } from 'react';
import { Button, Space, Divider, message } from 'antd';
import { SaveOutlined, EyeOutlined } from '@ant-design/icons';
import {
  BaseQuestion,
  mapQuestionComponents,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import <PERSON>hHel<PERSON> from '../../../utils/HashHelper';
import './SingleQuestionEngine.css';

interface SingleQuestionEngineProps {
  question: BaseQuestion;
  configMode: boolean;
  onQuestionChange?: (question: BaseQuestion) => void;
}

/**
 * SingleQuestionEngine - Optimized version of PracticeEngine for single question editing
 * This component is designed to render and handle a single question in edit or preview mode
 */
const SingleQuestionEngine: React.FC<SingleQuestionEngineProps> = ({
  question,
  configMode,
  onQuestionChange,
}) => {
  const [currentQuestion, setCurrentQuestion] =
    useState<BaseQuestion>(question);
  const [isPreviewMode, setIsPreviewMode] = useState(!configMode);
  const { handleChangeQuestion } = useContext(PracticeEngineContext);

  // Update local state when the question prop changes
  useEffect(() => {
    setCurrentQuestion(question);
  }, [question]);

  // Update preview mode when configMode changes
  useEffect(() => {
    setIsPreviewMode(!configMode);
  }, [configMode]);

  // Handle question update
  const handleInternalQuestionChange = useCallback(
    (updatedQuestion: BaseQuestion) => {
      setCurrentQuestion(updatedQuestion);
      if (onQuestionChange) {
        onQuestionChange(updatedQuestion);
      }
      if (handleChangeQuestion) {
        handleChangeQuestion(updatedQuestion);
      }
    },
    [onQuestionChange, handleChangeQuestion]
  );

  // Handle question complete (for preview mode)
  const handleQuestionComplete = useCallback(
    (questionId: string, answer?: any) => {
      // In preview mode, just handle the selection but don't save
      // message.success('Đã chọn đáp án thành công!');
    },
    []
  );

  // Toggle between preview and config modes
  const togglePreviewMode = useCallback(() => {
    setIsPreviewMode((prev) => !prev);
  }, []);

  // Save the question
  const handleSave = useCallback(() => {
    if (onQuestionChange) {
      onQuestionChange(currentQuestion);
      message.success('Đã lưu câu hỏi thành công!');
    }
  }, [currentQuestion, onQuestionChange]);

  // Render the appropriate question component based on type
  const renderQuestionComponent = () => {
    if (!currentQuestion) return null;

    // Get component from map based on question type
    const QuestionComponent = mapQuestionComponents.get(
      currentQuestion.type
    )?.component;

    if (!QuestionComponent) {
      return (
        <div className="question-not-found">
          Không tìm thấy loại câu hỏi phù hợp: {currentQuestion.type}
        </div>
      );
    }

    const componentId = HashHelper.computeHash([currentQuestion.id]);

    return (
      <div className="single-question-container" id={currentQuestion.id}>
        <QuestionComponent
          id={componentId}
          question={currentQuestion}
          showFeedback={true}
          configMode={!isPreviewMode}
          hideSaveButton={true}
          hideDeleteButton={true}
          onComplete={handleQuestionComplete}
        />
      </div>
    );
  };

  return (
    <div className="single-question-engine">
      <div className="single-question-content">{renderQuestionComponent()}</div>
    </div>
  );
};

export default SingleQuestionEngine;

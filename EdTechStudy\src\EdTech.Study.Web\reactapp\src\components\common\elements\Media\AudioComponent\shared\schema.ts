import { z } from "zod";
import { createComponentSchema } from "../../../../../../utils/schema/createComponentSchema";

/**
 * Zod schema for title properties
 */
const titlePropsSchema = z.object({
  text: z.string().optional(),
  fontSize: z.number().optional(),
  color: z.string().optional(),
  align: z.enum(['left', 'center', 'right']).optional(),
  bold: z.boolean().optional(),
  italic: z.boolean().optional(),
  underline: z.boolean().optional(),
  style: z.any().optional(),
}).optional();

/**
 * Zod schema for a single audio item
 */
export const audioItemSchema = z.object({
  mediaUrl: z.string(),
  mediaType: z.enum(['upload', 'embed']).optional(),
  name: z.string().optional(),
  artist: z.string().optional(),
});

// Zod schema for component validation
export const audioComponentSchema = createComponentSchema({
  paramsSchema: {
    // Title properties
    titleProps: titlePropsSchema,
    // New multi-audio support
    medias: z.array(audioItemSchema).optional(),

    // Legacy support
    mediaUrl: z.string().optional(),
    mediaType: z.enum(['upload', 'embed']).optional(),
    name: z.string().optional(),
    artist: z.string().optional(),
    description: z.string().optional(),

    // Playback options
    autoPlay: z.boolean().optional(),
    controls: z.boolean().optional(),
    loop: z.boolean().optional(),
    muted: z.boolean().optional(),
    startTime: z.number().optional(),
    endTime: z.number().optional(),

    // Display options
    showWaveform: z.boolean().optional(),
    showPlaybackRate: z.boolean().optional(),
    showDownload: z.boolean().optional(),
    themeColor: z.string().optional(),
    visualizerType: z.enum(['bars', 'wave', 'circle', 'none']).optional(),
    borderRadius: z.number().optional(),
    shadow: z.boolean().optional(),
    displayMode: z.enum(['horizontal', 'vertical']).optional(),
  },
});
import {
  QuestionComponentBaseP<PERSON>,
  BaseQuestion,
  BaseAnswer,
} from './questionBase';

// Interface for FillBlanks questions
export interface FillBlanksQuestion extends BaseQuestion {
  type: 'fillblanks';
  question: string;
  blanks: FillBlankItem[];
  caseSensitive?: boolean;
  showHints?: boolean;
  explanation?: string;
  points?: number;
  answers?: FillBlanksAnswer[];
  userSelect?: FillBlanksAnswer;
}

// Interface for each blank in the question
export interface FillBlankItem {
  id: string;
  correctAnswer: string;
  alternativeAnswers?: string[];
  hint?: string;
  userAnswer?: string; // User's input for this blank
  isCorrect?: boolean; // Whether the user's answer is correct
}

// Interface for tracking user selections/inputs
export interface FillBlanksAnswer extends BaseAnswer {
  answers: { [blankId: string]: string };
}

// Props for the FillBlanks component
export interface FillBlanksComponentProps extends QuestionComponentBaseProps {
  question: FillBlanksQuestion;
  allowManyTimes?: boolean;
  disabled?: boolean;
}

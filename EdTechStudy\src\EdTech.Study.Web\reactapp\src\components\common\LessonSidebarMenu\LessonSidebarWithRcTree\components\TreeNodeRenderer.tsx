import React from 'react';
import { TreeMenuItem } from '../types';
import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';
import TreeNodeTitle from './TreeNodeTitle';
import EditableNodeWithActions from './EditableNodeWithActions';
import EditableTreeNode from './EditableTreeNode';

interface TreeNodeRendererProps {
  nodeData: TreeMenuItem;
  isEditing: boolean;
  editingItemKey: string | null;
  editingItemValue: string;
  expandedKeys: string[];
  handleExpand: (keys: string[]) => void;
  handleStartEdit: (key: string, title: string) => void;
  handleAddNode: (key: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent, key: string) => void;
  handleSaveEdit: (key: string) => void;
  level: number;
}

const TreeNodeRenderer: React.FC<TreeNodeRendererProps> = ({
  nodeData,
  isEditing,
  editingItemKey,
  editingItemValue,
  expandedKeys,
  handleExpand,
  handleStartEdit,
  handleAddNode,
  handleInputChange,
  handleKeyDown,
  handleSaveEdit,
  level,
}) => {
  const canAddChildren =
    nodeData.type &&
    [
      ETypeEdTechComponent.STRUCTURE,
      ETypeEdTechComponent.CONTENT,
      ETypeEdTechComponent.LAYOUT,
    ].includes(nodeData.type);

  const hasChildren = !!(nodeData.children && nodeData.children.length > 0);
  const isExpanded = expandedKeys.includes(nodeData.key);

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newExpandedKeys = isExpanded
      ? expandedKeys.filter((key) => key !== nodeData.key)
      : [...expandedKeys, nodeData.key];
    handleExpand(newExpandedKeys);
  };

  // If in editing mode and this node is being edited
  if (isEditing && editingItemKey === nodeData.key) {
    return (
      <EditableTreeNode
        nodeData={nodeData}
        editingItemValue={editingItemValue}
        level={level}
        handleInputChange={handleInputChange}
        handleSaveEdit={handleSaveEdit}
        handleKeyDown={handleKeyDown}
      />
    );
  }

  // If in editing mode and node is not a config node
  if (isEditing && !nodeData.key.includes('config')) {
    return (
      <EditableNodeWithActions
        nodeData={nodeData}
        level={level}
        isExpanded={isExpanded}
        hasChildren={hasChildren}
        handleExpandClick={handleExpandClick}
        canAddChildren={canAddChildren || false}
        onAdd={(e) => {
          e.stopPropagation();
          handleAddNode(nodeData.key);
        }}
        onEdit={(e) => {
          e.stopPropagation();
          handleStartEdit(nodeData.key, nodeData.title as string);
        }}
      />
    );
  }

  // Default view mode
  return (
    <TreeNodeTitle
      nodeData={nodeData}
      isEditing={isEditing}
      isExpanded={isExpanded}
      hasChildren={hasChildren}
      level={level}
      handleExpandClick={handleExpandClick}
    />
  );
};

export default TreeNodeRenderer;

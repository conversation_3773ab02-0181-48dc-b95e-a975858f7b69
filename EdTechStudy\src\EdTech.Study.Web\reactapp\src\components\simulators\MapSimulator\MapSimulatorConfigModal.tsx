import React, { useState } from 'react';
import {
  Form,
  Input,
  Switch,
  Slider,
  Tabs,
  Button,
  message,
  Select,
} from 'antd';
import {
  defaultMapSimulatorConfig,
  MapSimulatorConfig,
  MapMarker,
} from './MapSimulatorConfig';
import { Guid } from 'guid-typescript';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';

interface MapSimulatorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: MapSimulatorConfig) => void;
  defaultConfig?: MapSimulatorConfig;
}

const MapSimulatorConfigModal: React.FC<MapSimulatorModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig = defaultMapSimulatorConfig,
}) => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<MapSimulatorConfig>(defaultConfig);

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const updatedConfig = deepMerge(config, values);

        const finalConfig: MapSimulatorConfig = {
          ...updatedConfig,
          markers: updatedConfig.markers.map(
            (marker: MapMarker, idx: number) => ({
              ...(config.markers[idx] || {}),
              ...marker,
              id: marker.id || `marker-${Guid.create().toString()}`, // Đảm bảo mỗi marker có ID
            })
          ),
        };

        onSubmit(finalConfig);
        message.success('Cấu hình mô phỏng bản đồ đã được lưu');
        onClose();
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo);
        message.error('Vui lòng kiểm tra lại thông tin cấu hình');
      });
  };

  const deepMerge = (prevObj: any, newValues: any) => {
    const result = { ...prevObj };
    Object.keys(newValues).forEach((key) => {
      if (Array.isArray(newValues[key])) {
        if (Array.isArray(result[key])) {
          newValues[key].forEach((item: any, index: number) => {
            if (item !== undefined && index < result[key].length) {
              result[key][index] = {
                ...result[key][index],
                ...item,
              };
            } else if (item !== undefined) {
              result[key].push(item); // Thêm mới nếu vượt quá độ dài ban đầu
            }
          });
        } else {
          result[key] = newValues[key];
        }
      } else if (
        typeof newValues[key] === 'object' &&
        newValues[key] !== null &&
        typeof result[key] === 'object' &&
        result[key] !== null
      ) {
        result[key] = deepMerge(result[key], newValues[key]);
      } else {
        result[key] = newValues[key];
      }
    });
    return result;
  };

  const handleAddMarker = () => {
    const newMarker: MapMarker = {
      id: `marker-${Guid.create().toString()}`,
      name: 'Điểm đánh dấu mới',
      description: '',
      latitude: config.initialCenter.lat,
      longitude: config.initialCenter.lng,
      visible: true,
    };
    const updatedMarkers = [...config.markers, newMarker];
    setConfig((prev) => ({ ...prev, markers: updatedMarkers }));
    form.setFieldsValue({ markers: updatedMarkers });
  };

  const handleRemoveMarker = (index: number) => {
    const updatedMarkers = config.markers.filter((_, i) => i !== index);
    setConfig((prev) => ({ ...prev, markers: updatedMarkers }));
    form.setFieldsValue({ markers: updatedMarkers });
  };

  const tabItems = [
    {
      key: '1',
      label: 'Thông tin cơ bản',
      children: (
        <>
          {/* Đã bỏ cấu hình tiêu đề bản đồ */}

          <Form.Item name="initialZoom" label="Mức độ phóng ban đầu">
            <Slider
              min={1}
              max={20}
              marks={{ 1: '1x', 5: '5x', 10: '10x', 15: '15x', 20: '20x' }}
            />
          </Form.Item>

          <Form.Item label="Vị trí ban đầu">
            <div style={{ display: 'flex', gap: '10px' }}>
              <Form.Item
                name={['initialCenter', 'lat']}
                rules={[{ required: true, message: 'Vui lòng nhập vĩ độ' }]}
                style={{ flex: 1, marginBottom: 0 }}
              >
                <Input
                  addonBefore="Vĩ độ"
                  placeholder="VD: 16.0"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                name={['initialCenter', 'lng']}
                rules={[{ required: true, message: 'Vui lòng nhập kinh độ' }]}
                style={{ flex: 1, marginBottom: 0 }}
              >
                <Input
                  addonBefore="Kinh độ"
                  placeholder="VD: 106.0"
                  type="number"
                />
              </Form.Item>
            </div>
          </Form.Item>

          <Form.Item name="description" label="Mô tả">
            <Input.TextArea rows={3} placeholder="Mô tả chi tiết về bản đồ" />
          </Form.Item>
        </>
      ),
    },
    {
      key: '2',
      label: 'Cấu hình Bản đồ',
      children: (
        <>
          {/* Đã chuyển cấu hình mức độ phóng ban đầu lên tab đầu tiên */}

          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
            }}
          >
            <Form.Item
              name={['controls', 'showSearchControl']}
              label="Hiển thị tìm kiếm"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['controls', 'searchControlPosition']}
              label="Vị trí ô tìm kiếm"
            >
              <Select>
                <Select.Option value="topleft">Trên trái</Select.Option>
                <Select.Option value="topright">Trên phải</Select.Option>
                <Select.Option value="bottomleft">Dưới trái</Select.Option>
                <Select.Option value="bottomright">Dưới phải</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name={['controls', 'showCompassControl']}
              label="Hiển thị la bàn"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['controls', 'compassControlPosition']}
              label="Vị trí la bàn"
            >
              <Select>
                <Select.Option value="topleft">Trên trái</Select.Option>
                <Select.Option value="topright">Trên phải</Select.Option>
                <Select.Option value="bottomleft">Dưới trái</Select.Option>
                <Select.Option value="bottomright">Dưới phải</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name={['controls', 'showMeasureControl']}
              label="Hiển thị công cụ đo"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['controls', 'measureControlPosition']}
              label="Vị trí công cụ đo"
            >
              <Select>
                <Select.Option value="topleft">Trên trái</Select.Option>
                <Select.Option value="topright">Trên phải</Select.Option>
                <Select.Option value="bottomleft">Dưới trái</Select.Option>
                <Select.Option value="bottomright">Dưới phải</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name={['controls', 'showRoutingControl']}
              label="Hiển thị tìm đường"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['controls', 'routingControlPosition']}
              label="Vị trí tìm đường"
            >
              <Select>
                <Select.Option value="topleft">Trên trái</Select.Option>
                <Select.Option value="topright">Trên phải</Select.Option>
                <Select.Option value="bottomleft">Dưới trái</Select.Option>
                <Select.Option value="bottomright">Dưới phải</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name={['controls', 'showLayerControl']}
              label="Hiển thị điều khiển lớp"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['controls', 'layerControlPosition']}
              label="Vị trí điều khiển lớp"
            >
              <Select>
                <Select.Option value="topleft">Trên trái</Select.Option>
                <Select.Option value="topright">Trôn phải</Select.Option>
                <Select.Option value="bottomleft">Dưới trái</Select.Option>
                <Select.Option value="bottomright">Dưới phải</Select.Option>
              </Select>
            </Form.Item>
          </div>
        </>
      ),
    },
    {
      key: '3',
      label: 'Lớp & Điểm đánh dấu',
      children: (
        <>
          {/* Đã bỏ cấu hình chế độ xem, luôn hiển thị tất cả 4 chế độ */}

          <div>
            <h3
              style={{
                fontSize: '16px',
                fontWeight: 500,
                marginBottom: '12px',
              }}
            >
              Điểm đánh dấu
            </h3>
            {config.markers.map((marker, index) => (
              <div
                key={marker.id}
                style={{
                  border: '1px solid #e8e8e8',
                  padding: '12px',
                  marginBottom: '12px',
                  borderRadius: '4px',
                  position: 'relative',
                }}
              >
                <Form.Item
                  name={['markers', index, 'name']}
                  label="Tên điểm đánh dấu"
                  rules={[{ required: true, message: 'Vui lòng nhập tên' }]}
                >
                  <Input placeholder="Nhập tên điểm đánh dấu" />
                </Form.Item>
                <Form.Item
                  name={['markers', index, 'description']}
                  label="Mô tả"
                >
                  <Input.TextArea rows={2} placeholder="Nhập mô tả" />
                </Form.Item>
                <div style={{ display: 'flex', gap: '10px' }}>
                  <Form.Item
                    name={['markers', index, 'latitude']}
                    label="Vĩ độ"
                    rules={[{ required: true, message: 'Vui lòng nhập vĩ độ' }]}
                    style={{ flex: 1, marginBottom: 0 }}
                  >
                    <Input type="number" placeholder="VD: 21.0285" />
                  </Form.Item>
                  <Form.Item
                    name={['markers', index, 'longitude']}
                    label="Kinh độ"
                    rules={[
                      { required: true, message: 'Vui lòng nhập kinh độ' },
                    ]}
                    style={{ flex: 1, marginBottom: 0 }}
                  >
                    <Input type="number" placeholder="VD: 105.8542" />
                  </Form.Item>
                </div>
                <Form.Item
                  name={['markers', index, 'visible']}
                  label="Hiển thị"
                  valuePropName="checked"
                  style={{ marginTop: '12px' }}
                >
                  <Switch />
                </Form.Item>
                <Button
                  type="link"
                  danger
                  onClick={() => handleRemoveMarker(index)}
                  style={{ position: 'absolute', top: '8px', right: '8px' }}
                >
                  Xóa
                </Button>
              </div>
            ))}
            <Button
              type="dashed"
              onClick={handleAddMarker}
              style={{ width: '100%', marginTop: '12px' }}
            >
              + Thêm điểm đánh dấu
            </Button>
          </div>
        </>
      ),
    },
  ];

  return (
    <ModalAntdCustom
      title="Cấu hình Mô phỏng Bản đồ"
      open={isOpen}
      onCancel={onClose}
      width={700} // Tăng chiều rộng để chứa nội dung mới
      footer={[
        <Button
          key="cancel"
          onClick={onClose}
          className="hover:tailwind-bg-gray-100"
        >
          Hủy
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          className="tailwind-bg-blue-500 hover:tailwind-bg-blue-600 tailwind-text-white tailwind-border-blue-500 hover:tailwind-border-blue-600"
        >
          Áp dụng
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultConfig}
        onValuesChange={(changedValues) => {
          setConfig((prevConfig) => deepMerge(prevConfig, changedValues));
        }}
      >
        <Tabs defaultActiveKey="1" items={tabItems} />
      </Form>
    </ModalAntdCustom>
  );
};

export default MapSimulatorConfigModal;

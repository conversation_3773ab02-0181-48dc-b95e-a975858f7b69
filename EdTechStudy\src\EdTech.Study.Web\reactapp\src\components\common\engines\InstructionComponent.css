/* Styles for the Instruction Component */
.instruction-component {
  border-color: var(--edtt-color-primary) !important;
  border-radius: 8px !important;
  background-color: var(--edtt-color-primary-100) !important;
}

.instruction-component .ant-alert-message {
  color: var(--edtt-color-primary) !important;
}

.instruction-component .ant-typography strong {
  color: var(--edtt-color-primary);
}

/* Objective section styling */
.instruction-component .instruction-objective {
  margin-bottom: 16px;
}

/* Steps section styling */
.instruction-component .instruction-steps {
  margin-bottom: 16px;
}

/* Notes section styling */
.instruction-component .instruction-notes {
  background-color: var(--edtt-color-primary-200);
  border-left: 3px solid var(--edtt-color-primary);
  padding: 8px 12px;
  border-radius: 0 4px 4px 0;
  margin-top: 16px;
}

.instruction-component .instruction-notes .ant-typography {
  color: #666;
  font-style: italic;
}

.instruction-component ol {
  counter-reset: item;
  list-style-type: none;
  padding-left: 0;
}

.instruction-component ol li {
  position: relative;
  padding-left: 28px;
  margin-bottom: 8px;
  counter-increment: item;
}

.instruction-component ol li:before {
  content: counter(item) ".";
  position: absolute;
  left: 0;
  top: 0;
  color: var(--edtt-color-primary);
  font-weight: 600;
  display: inline-block;
  width: 22px;
  text-align: right;
  margin-right: 6px;
}

.instruction-component ol li strong {
  color: var(--edtt-color-primary);
}

/* Hover effect for list items */
.instruction-component ol li:hover {
  background-color: var(--edtt-color-primary-200);
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .instruction-component ol li {
    padding-left: 24px;
  }

  .instruction-component ol li:before {
    width: 18px;
  }
}
import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  Space,
  Typography,
  Button,
  Divider,
  message,
  Tooltip,
} from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { IconProvider, useIconContext } from './IconIndex';
import {
  HomeOutlineIcon as HomeIcon,
  FileOutlineIcon,
  UserOutlineComponent,
  DocumentFilledIcon,
  FileFilledIcon,
} from './IconRegister';
import { IconCategory } from './IconUtils';
import './icon-styles.css';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const CodeDisplay: React.FC<{ text: string }> = ({ text }) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(text);
    message.success('Đã sao chép mã vào clipboard!');
  };

  return (
    <div style={{ position: 'relative' }}>
      <pre
        style={{
          backgroundColor: '#f5f5f5',
          padding: '16px',
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '14px',
          lineHeight: '1.5',
        }}
      >
        <code>{text}</code>
      </pre>
      <Tooltip title="Sao chép mã">
        <Button
          icon={<CopyOutlined />}
          size="small"
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            opacity: 0.7,
          }}
          onClick={copyToClipboard}
        />
      </Tooltip>
    </div>
  );
};
const IconCard: React.FC<{
  icon: any;
  onCopy: (code: string) => void;
}> = ({ icon, onCopy }) => {
  const IconComponent = icon.component;
  const [hovered, setHovered] = useState(false);

  const getIconCode = () => {
    // Only return the direct JSX component usage code
    return `<${icon.displayName.replace(/\s/g, '')}Icon />`;
  };

  return (
    <Card
      hoverable
      style={{
        textAlign: 'center',
        height: '100%',
        position: 'relative',
        padding: '12px',
      }}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <div style={{ marginBottom: 8 }}>
        <IconComponent width={32} height={32} />
      </div>
      <div style={{ fontSize: '12px', wordBreak: 'break-word' }}>
        {icon.displayName || icon.name}
      </div>
      {hovered && (
        <Tooltip title="Sao chép mã">
          <Button
            size="small"
            icon={<CopyOutlined />}
            style={{
              position: 'absolute',
              top: '5px',
              right: '5px',
              opacity: 0.7,
            }}
            onClick={() => onCopy(getIconCode())}
          />
        </Tooltip>
      )}
    </Card>
  );
};

const IconList: React.FC = () => {
  const { icons } = useIconContext();
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    message.success('Đã sao chép mã vào clipboard!');
  };

  // Calculate icon counts by category
  const categoryCounts = React.useMemo(() => {
    const counts: Record<string, number> = {};

    // Initialize counts for all categories
    Object.values(IconCategory).forEach((category) => {
      counts[category] = 0;
    });

    // Count icons in each category
    icons.forEach((icon) => {
      if (counts[icon.category] !== undefined) {
        counts[icon.category]++;
      }
    });

    return counts;
  }, [icons]);

  // Filter and sort icons based on search text and selected category
  const filteredIcons = React.useMemo(() => {
    // First filter icons based on search text and category
    const filtered = icons.filter(
      (icon) =>
        (selectedCategory === 'all' || icon.category === selectedCategory) &&
        (icon.displayName?.toLowerCase().includes(searchText.toLowerCase()) ||
          icon.name.toLowerCase().includes(searchText.toLowerCase()))
    );

    // Then sort them by category and then by name within each category
    return filtered.sort((a, b) => {
      // First sort by category
      if (a.category !== b.category) {
        return a.category.localeCompare(b.category);
      }

      // Then sort by name within the same category
      return a.name.localeCompare(b.name);
    });
  }, [icons, searchText, selectedCategory]);

  return (
    <div>
      <Space style={{ marginBottom: 16, width: '100%' }} direction="vertical">
        <Row gutter={16} align="middle">
          <Col span={16}>
            <Input
              placeholder="Tìm kiếm icon"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Typography.Text type="secondary">
              Hiển thị {filteredIcons.length} / {icons.length} icon
            </Typography.Text>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Select
              defaultValue="all"
              style={{ width: '100%' }}
              onChange={(value) => setSelectedCategory(value)}
            >
              <Option value="all">Tất cả danh mục ({icons.length})</Option>
              {Object.values(IconCategory).map((category) => (
                <Option key={category} value={category}>
                  {category
                    .split('_')
                    .map(
                      (word) =>
                        word.charAt(0).toUpperCase() +
                        word.slice(1).toLowerCase()
                    )
                    .join(' ')}{' '}
                  ({categoryCounts[category]})
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Space>

      {/* Group icons by category and display them with category headers */}
      {selectedCategory === 'all' ? (
        // When showing all categories, group icons by category
        Object.values(IconCategory).map((category) => {
          // Get icons for this category
          const categoryIcons = filteredIcons.filter(
            (icon) => icon.category === category
          );

          // Only show categories that have icons after filtering
          if (categoryIcons.length === 0) return null;

          return (
            <div key={category} style={{ marginBottom: '24px' }}>
              <Title
                level={4}
                style={{
                  marginTop: '16px',
                  marginBottom: '16px',
                  padding: '8px 16px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px',
                }}
              >
                {/* Format category name for better readability */}
                {category
                  .split('_')
                  .map(
                    (word) =>
                      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                  )
                  .join(' ')}{' '}
                <span
                  style={{
                    fontSize: '14px',
                    fontWeight: 'normal',
                    color: '#888',
                  }}
                >
                  ({categoryIcons.length} icon
                  {categoryIcons.length !== 1 ? 's' : ''})
                </span>
              </Title>
              <Row gutter={[16, 16]}>
                {categoryIcons.map((icon, index) => (
                  <Col
                    key={`${icon.name}-${index}`}
                    xs={12}
                    sm={8}
                    md={6}
                    lg={4}
                  >
                    <IconCard icon={icon} onCopy={handleCopyCode} />
                  </Col>
                ))}
              </Row>
            </div>
          );
        })
      ) : (
        // When a specific category is selected, just show those icons without a header
        <Row gutter={[16, 16]}>
          {filteredIcons.map((icon, index) => (
            <Col key={`${icon.name}-${index}`} xs={12} sm={8} md={6} lg={4}>
              <IconCard icon={icon} onCopy={handleCopyCode} />
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

// Component hiển thị cách icon thích ứng với theme
const IconThemeExamples: React.FC = () => {
  const themeUsageCode = `import { HomeOutlineIcon } from '@/components/icons/IconIndex';

// Icon sẽ tự động sử dụng màu từ theme hiện tại
<HomeOutlineIcon width={24} height={24} />

// Chỉ định màu sắc cụ thể (ghi đè theme)
<HomeOutlineIcon width={24} height={24} fill="red" />

// Sử dụng các class theme cụ thể
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-primary" />
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-secondary" />
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-tertiary" />
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-text" />
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-text-primary" />
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-text-secondary" />
<HomeOutlineIcon width={24} height={24} className="svg-icon-theme-disabled" />`;

  return (
    <div>
      <Title level={3}>Icon Theme Integration</Title>
      <Paragraph>
        Khi sử dụng icon mà không chỉ định màu sắc, icon sẽ tự động thích ứng
        với màu theme chung của dự án.
      </Paragraph>

      <Row gutter={[24, 24]}>
        <Col xs={24} md={12}>
          <Card title="Mã ví dụ">
            <CodeDisplay text={themeUsageCode} />
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="Kết quả">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <Text>Mặc định (sử dụng màu theme):</Text>
                <div style={{ marginTop: 16, display: 'flex', gap: '24px' }}>
                  <div className="icon-demo-item">
                    <HomeIcon width={32} height={32} />
                    <Text>HomeIcon</Text>
                  </div>
                  <div className="icon-demo-item">
                    <FileOutlineIcon width={32} height={32} />
                    <Text>FileOutlineIcon</Text>
                  </div>
                  <div className="icon-demo-item">
                    <UserOutlineComponent width={32} height={32} />
                    <Text>UserOutlineComponent</Text>
                  </div>
                </div>
              </div>

              <Divider />

              <div>
                <Text>Chỉ định màu sắc cụ thể:</Text>
                <div style={{ marginTop: 16, display: 'flex', gap: '24px' }}>
                  <div className="icon-demo-item">
                    <HomeIcon width={32} height={32} fill="red" />
                    <Text>fill="red"</Text>
                  </div>
                  <div className="icon-demo-item">
                    <FileOutlineIcon width={32} height={32} fill="blue" />
                    <Text>fill="blue"</Text>
                  </div>
                  <div className="icon-demo-item">
                    <DocumentFilledIcon width={32} height={32} fill="green" />
                    <Text>fill="green"</Text>
                  </div>
                </div>
              </div>

              <Divider />

              <div>
                <Text>Sử dụng các class theme:</Text>
                <div style={{ marginTop: 16, display: 'flex', gap: '24px' }}>
                  <div className="icon-demo-item">
                    <HomeIcon
                      width={32}
                      height={32}
                      className="svg-icon-theme-primary"
                    />
                    <Text>Primary Theme</Text>
                  </div>
                  <div className="icon-demo-item">
                    <FileOutlineIcon
                      width={32}
                      height={32}
                      className="svg-icon-theme-secondary"
                    />
                    <Text>Secondary Theme</Text>
                  </div>
                  <div className="icon-demo-item">
                    <UserOutlineComponent
                      width={32}
                      height={32}
                      className="svg-icon-theme-tertiary"
                    />
                    <Text>Tertiary Theme</Text>
                  </div>
                </div>

                <Divider />

                <div>
                  <Text>Các class theme khác:</Text>
                  <div style={{ marginTop: 16, display: 'flex', gap: '24px' }}>
                    <div className="icon-demo-item">
                      <DocumentFilledIcon
                        width={32}
                        height={32}
                        className="svg-icon-theme-text-primary"
                      />
                      <Text>Text Primary</Text>
                    </div>
                    <div className="icon-demo-item">
                      <FileFilledIcon
                        width={32}
                        height={32}
                        className="svg-icon-theme-text-secondary"
                      />
                      <Text>Text Secondary</Text>
                    </div>
                  </div>
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

const IconStoreDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'browse' | 'theme'>('browse');

  return (
    <IconProvider>
      <div style={{ padding: '20px' }}>
        <Title level={2}>Kho Icon</Title>
        <Paragraph>
          Kho Icon cho phép bạn sử dụng các icon SVG trong ứng dụng của mình.
          Bạn có thể duyệt qua các icon có sẵn, sao chép mã để sử dụng, và xem
          cách icon thích ứng với theme.
        </Paragraph>

        <Space style={{ marginBottom: 16 }}>
          <Button
            type={activeTab === 'browse' ? 'primary' : 'default'}
            onClick={() => setActiveTab('browse')}
          >
            Danh sách Icon
          </Button>
          <Button
            type={activeTab === 'theme' ? 'primary' : 'default'}
            onClick={() => setActiveTab('theme')}
          >
            Theme Integration
          </Button>
        </Space>

        <div style={{ marginTop: 16 }}>
          {activeTab === 'browse' ? <IconList /> : <IconThemeExamples />}
        </div>
      </div>
    </IconProvider>
  );
};

export default IconStoreDemo;

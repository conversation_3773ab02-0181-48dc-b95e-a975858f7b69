import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button, Input } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import TextSettings from './TextSettingsModal';

/**
 * T<PERSON><PERSON> hàm debounce để trì hoãn việc gọi hàm
 */
const debounce = (func: Function, wait: number) => {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export interface ITextProps {
  text?: string;
  fontSize?: number | string;
  color?: string;
  align?: 'left' | 'center' | 'right';
  style?: React.CSSProperties;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
}

export interface ICoreLessonTitleProps {
  titleProps?: ITextProps;
  isEditing?: boolean;
  isColorDisabled?: boolean; // Biến chặn sửa màu chữ
  isLockTextFormatting?: boolean; // Biến mới: chặn tất cả định dạng văn bản
  onUpdate?: (updates: Partial<ITextProps>) => void;
}

const CoreTitle: React.FC<ICoreLessonTitleProps> = (props) => {
  const {
    titleProps,
    isEditing = false,
    onUpdate,
    isColorDisabled = false,
    isLockTextFormatting = false,
  } = props;
  // Default values
  const {
    text = 'Tiêu đề',
    fontSize = 24,
    color = 'var(--edtt-color-text-default)',
    align = 'left',
    style = {},
    bold = false,
    italic = false,
    underline = false,
  } = titleProps || {};
  // State
  const [editMode, setEditMode] = useState(false);
  const [editedText, setEditedText] = useState(text);
  const [inputWidth, setInputWidth] = useState(
    `${Math.min(Math.max(text.length * 12, 200), 600)}px`
  );
  const [colorPickerActive, setColorPickerActive] = useState(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);

  // Effects
  useEffect(() => {
    setEditedText(text);
  }, [text]);

  /**
   * Tính toán độ rộng của input dựa trên độ dài text và font size
   */
  useEffect(() => {
    const charWidth = typeof fontSize === 'number' ? fontSize * 0.8 : 24 * 0.8;
    const calculatedWidth = editedText.length * charWidth;
    const minWidth = 200;
    const maxWidth = 600;

    setInputWidth(
      `${Math.min(Math.max(calculatedWidth, minWidth), maxWidth)}px`
    );
  }, [editedText, fontSize]);

  /**
   * Cập nhật text với debounce
   */
  const debouncedUpdate = useCallback(
    debounce((value: string) => {
      if (onUpdate && value !== text) {
        onUpdate({ ...titleProps, text: value });
      }
    }, 300),
    [onUpdate, titleProps, text]
  );

  /**
   * Xử lý trạng thái ColorPicker
   */
  const handleColorPickerChange = useCallback((open: boolean) => {
    setColorPickerActive(open);
  }, []);

  /**
   * Xử lý click outside để đóng edit mode
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Các thành phần UI không đóng modal khi click vào
      const isModalElement =
        event.target instanceof HTMLElement &&
        (event.target.closest('.text-settings-modal') ||
          event.target.closest('.ant-color-picker-dropdown') ||
          event.target.closest('.ant-tooltip') ||
          event.target.closest('.ant-input-number') ||
          event.target.closest('.ant-btn') ||
          event.target.closest('.ant-popover') ||
          event.target.closest('.ant-picker-dropdown'));

      // Nếu click vào thành phần modal hoặc ColorPicker đang active
      if (isModalElement || colorPickerActive) {
        return;
      }

      // Đóng edit mode khi click ra ngoài
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        editMode
      ) {
        setEditMode(false);
      }
    };

    if (editMode) {
      document.addEventListener('mousedown', handleClickOutside, true);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [editMode, colorPickerActive]);

  /**
   * Đóng edit mode khi isEditing = false
   */
  useEffect(() => {
    if (!isEditing) {
      setEditMode(false);
    }
  }, [isEditing]);

  /**
   * Xử lý thay đổi text và cập nhật độ rộng input
   */
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setEditedText(newValue);
    debouncedUpdate(newValue);

    // Cập nhật độ rộng input
    const charWidth = typeof fontSize === 'number' ? fontSize * 0.8 : 24 * 0.8;
    const calculatedWidth = newValue.length * charWidth;
    const minWidth = 200;
    const maxWidth = 600;

    setInputWidth(
      `${Math.min(Math.max(calculatedWidth, minWidth), maxWidth)}px`
    );
  };

  /**
   * Xử lý phím Enter và Escape trong input
   */
  const handleTextKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setEditMode(false);
    } else if (e.key === 'Escape') {
      setEditMode(false);
      setEditedText(text);
    }
  };

  /**
   * Cập nhật thiết lập văn bản từ TextSettings
   */
  const handleSettingsUpdate = (updates: Partial<ITextProps>) => {
    if (onUpdate) {
      // Lọc bỏ cập nhật màu sắc nếu isUnEditingColor = true
      let filteredUpdates = { ...updates };

      if (isColorDisabled && 'color' in updates) {
        delete filteredUpdates.color;
      }

      // Lọc bỏ cập nhật định dạng nếu isLockTextFormatting = true
      if (isLockTextFormatting) {
        ['bold', 'italic', 'underline', 'fontSize', 'align'].forEach((prop) => {
          if (prop in filteredUpdates) {
            delete filteredUpdates[prop as keyof ITextProps];
          }
        });
      }

      // Chỉ cập nhật nếu còn thuộc tính nào đó
      if (Object.keys(filteredUpdates).length > 0) {
        const updatedProps = {
          ...titleProps,
          ...filteredUpdates,
        };
        onUpdate(updatedProps);
      }
    }
  };

  // Styles
  const titleStyle: React.CSSProperties = {
    color,
    textAlign: align,
    margin: '0.5em 0',
    fontSize: typeof fontSize === 'number' ? `${fontSize}px` : fontSize,
    fontWeight: bold ? 'bold' : 'normal',
    fontStyle: italic ? 'italic' : 'normal',
    textDecoration: underline ? 'underline' : 'none',
    cursor: isEditing ? 'text' : 'default',
    ...style,
  };

  const inputStyle: React.CSSProperties = {
    fontSize: typeof fontSize === 'number' ? `${fontSize}px` : fontSize,
    fontWeight: bold ? 'bold' : 'normal',
    fontStyle: italic ? 'italic' : 'normal',
    textDecoration: underline ? 'underline' : 'none',
    color,
    width: inputWidth,
  };

  // Tạo chỉnh sửa cài đặt mặc định cho TextSettings
  const textSettingsProps = {
    text: editedText,
    fontSize,
    color,
    align,
    style,
    bold,
    italic,
    underline,
  };

  return (
    <div ref={containerRef} className="tailwind-relative">
      <div className="tailwind-relative tailwind-flex tailwind-items-center">
        {editMode ? (
          <div className="tailwind-relative tailwind-w-full">
            <Input
              value={editedText}
              onChange={handleTextChange}
              onKeyDown={handleTextKeyDown}
              className="tailwind-border tailwind-border-gray-300 tailwind-rounded"
              style={inputStyle}
              size="middle"
              autoFocus
            />
          </div>
        ) : (
          <>
            {/* Only render the title if text is not empty or we're in editing mode */}
            {(text || isEditing) && (
              <h1 ref={titleRef} className="tailwind-m-2" style={titleStyle}>
                {text || (isEditing ? '' : null)}
              </h1>
            )}
            {isEditing && (
              <Button
                icon={<EditOutlined style={{ fontSize: '16px' }} />}
                type="text"
                onClick={() => setEditMode(true)}
                className="tailwind-ml-2"
                style={{ width: '26px', height: '26px' }}
                size="middle"
              />
            )}
          </>
        )}
      </div>

      {editMode && isEditing && (
        <div
          className="tailwind-fixed tailwind-z-50 tailwind-shadow-md tailwind-rounded tailwind-p-2 tailwind-mt-2 text-settings-modal-container"
          style={{
            top: titleRef.current
              ? titleRef.current.getBoundingClientRect().bottom + 10
              : 0,
            left: titleRef.current
              ? titleRef.current.getBoundingClientRect().left
              : 0,
          }}
          onMouseDown={(e) => e.stopPropagation()}
        >
          <TextSettings
            settings={textSettingsProps}
            onUpdate={handleSettingsUpdate}
            onColorPickerChange={handleColorPickerChange}
            isColorDisabled={isColorDisabled}
            isFormattingDisabled={isLockTextFormatting}
          />
        </div>
      )}
    </div>
  );
};

export default CoreTitle;

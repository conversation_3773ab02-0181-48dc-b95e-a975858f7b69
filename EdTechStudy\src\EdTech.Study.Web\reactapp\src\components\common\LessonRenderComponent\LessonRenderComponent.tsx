import React, { useState, useEffect, Suspense, memo } from 'react';
import { IEdTechRenderTreeData } from '../../../interfaces/AppComponents';
import { AppFunctions } from '../../../utils/AppFunctions';
import { ETypeEdTechComponent, ETypeMode } from '../../../enums/AppEnums';
import { useSelector } from 'react-redux';
import { EdTechRootState } from '../../../store/store';
import LessonSelectorComponent from '../LessonSelectorComponent/LessonSelectorComponent';
import LoadingScreen from '../Loading/LoadingScreen';
import ComponentNotFoundAlert from './ComponentNotFoundAlert';
import { isEmpty } from '@tsp/utils';

interface LessonRenderComponentProps {
  data: IEdTechRenderTreeData;
}

// Component to render a single node in the tree
const TreeNode: React.FC<{
  node: IEdTechRenderTreeData;
}> = ({ node }) => {
  const [DynamicComponent, setDynamicComponent] =
    useState<React.ComponentType<any> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = useSelector(
    (state: EdTechRootState) => state.appConfig.mode === ETypeMode.CONFIGURATION
  );
  const engineState = useSelector(
    (state: EdTechRootState) => state.appConfig.engineState
  );

  const result = AppFunctions.getComponentRender({
    name: node.name,
    version: node.version,
  });
  // Handle dynamic imports
  useEffect(() => {
    if (
      result &&
      typeof result.component === 'function' &&
      result.component.toString().includes('import')
    ) {
      setIsLoading(true);
      result
        .component()
        .then((module: any) => {
          setDynamicComponent(() => module.default);
          setIsLoading(false);
        })
        .catch((error: any) => {
          console.error(
            `Failed to load dynamic component ${node.name}:`,
            error
          );
          setIsLoading(false);
        });
    }
  }, [result, node.name]);

  // If no component found, show warning card
  if (!result) {
    return <ComponentNotFoundAlert node={node} />;
  }

  // Render the subitems as children
  const renderChildren = () => {
    if (!node.subItems || node.subItems.length === 0) {
      return null;
    }

    return node.subItems.map((subItem, index) => (
      <TreeNode key={subItem.id || `${node.name}-${index}`} node={subItem} />
    ));
  };
  // Create the selector component for layout nodes
  const renderSelector = (node: IEdTechRenderTreeData) => {
    if (
      isEditing &&
      (node.type === ETypeEdTechComponent.LAYOUT ||
        node.type === ETypeEdTechComponent.CONTENT)
    ) {
      const isShowButtonAdd =
        node.path === engineState.pathEngineActive ||
        engineState.pathEngineHover?.includes(node.path) ||
        isEmpty(node.subItems);
      return (
        <div className="tailwind-flex tailwind-justify-center tailwind-items-center tailwind-w-full tailwind-mt-4 tailwind-h-[60px]">
          <div
            className="tailwind-rounded tailwind-p-2"
            style={{
              display: isShowButtonAdd ? 'block' : 'none',
            }}
          >
            <LessonSelectorComponent parentNode={node} />
          </div>
        </div>
      );
    }
    return null;
  };

  if (node.type === ETypeEdTechComponent.STRUCTURE) {
    const Component = result.component;
    return (
      <>
        <Component {...node} />
        {renderSelector(node)}
      </>
    );
  }

  // Render the component
  const renderComponent = () => {
    // If it's a dynamic import and still loading, show a loading state
    if (isLoading) {
      return (
        <div className="tailwind-border tailwind-border-blue-300 tailwind-bg-blue-50 tailwind-rounded">
          <p className="tailwind-text-blue-700">
            Loading component: <strong>{node.name}</strong>
          </p>
          {renderChildren()}
          {renderSelector(node)}
        </div>
      );
    }

    // If it's a dynamic import and we have the component, render it
    if (DynamicComponent) {
      return (
        <Suspense fallback={<LoadingScreen />}>
          <DynamicComponent {...node}>
            {renderChildren()}
            {renderSelector(node)}
          </DynamicComponent>
        </Suspense>
      );
    }

    // Regular component rendering
    return (
      <>
        {React.createElement(result.component, {
          ...node,
          children: (
            <>
              {renderChildren()}
              {renderSelector(node)}
            </>
          ),
        })}
      </>
    );
  };

  return renderComponent();
};

// Main component to render the entire tree
const LessonRenderComponent: React.FC<LessonRenderComponentProps> = ({
  data,
}) => {
  return <TreeNode node={data} />;
};

export default memo(LessonRenderComponent, (prev, next) => {
  return prev.data === next.data;
});

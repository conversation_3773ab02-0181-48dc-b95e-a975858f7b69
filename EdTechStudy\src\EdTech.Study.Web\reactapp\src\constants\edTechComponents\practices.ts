import { SimpleFillBlanksWrapper } from '../../components/quizs/fillblanks';
import { simpleFillBlanksSchema } from '../../components/quizs/fillblanks/SimpleFillBlanksWrapper';
import { MatchingWrapper } from '../../components/quizs/matching';
import { matchingWrapperSchema } from '../../components/quizs/matching/MatchingWrapper';
import { MultiSelectWrapper } from '../../components/quizs/multiselect';
import { multiSelectWrapperSchema } from '../../components/quizs/multiselect/MultiSelectWrapper';
import PracticeEngineWrapper from '../../components/quizs/practiceEngines/PracticeEngineWrapper';
import { SimpleQuizWrapper } from '../../components/quizs/quiz';
import { simpleQuizWrapperSchema } from '../../components/quizs/quiz/SimpleQuizWrapper';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const PRACTICES_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'PracticeEngineWrapper',
    title: 'Bài Kiểm Tra Bài Học',
    components: [
      {
        version: '1.0.0',
        component: PracticeEngineWrapper,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.QUIZ,
    tags: [
      'Đánh giá',
      'Thực hành',
      'Cá nhân',
      'Nhiều lựa chọn',
      'Đánh giá hiệu suất',
      'Ôn tập kiến thức',
      'Dựa trên văn bản',
    ],
    description:
      'Phần kiểm tra kiến thức bài học với các câu hỏi đa dạng, giúp người học đánh giá mức độ hiểu bài và rèn luyện kỹ năng.',
    schema: undefined,
  },
  {
    name: 'SimpleQuizWrapper',
    title: 'Trắc nghiệm nhanh',
    components: [
      {
        version: '1.0.0',
        component: SimpleQuizWrapper,
        schema: simpleQuizWrapperSchema,
      },
    ],
    type: ETypeEdTechComponent.QUIZ,
    tags: [
      'Câu đố',
      'Đánh giá',
      'Sơ cấp',
      'Nhiều lựa chọn',
      'Ôn tập kiến thức',
      'Dựa trên văn bản',
      'Ghi nhớ',
    ],
    description: 'Một câu hỏi trắc nghiệm chọn đáp án nhanh',
    schema: simpleQuizWrapperSchema,
  },
  {
    name: 'MatchingWrapper',
    title: 'Bài tập nối',
    components: [
      {
        version: '1.0.0',
        component: MatchingWrapper,
        schema: matchingWrapperSchema,
      },
    ],
    type: ETypeEdTechComponent.QUIZ,
    tags: [
      'Câu đố',
      'Tương tác',
      'Trung cấp',
      'Nối',
      'Kéo và thả',
      'Học khái niệm',
      'Phân tích',
      'Trực quan',
    ],
    description: 'Một câu hỏi nối các thẻ tương ứng từ hai cột',
    schema: matchingWrapperSchema,
  },
  {
    name: 'SimpleFillBlanksWrapper',
    title: 'Bài tập điền từ',
    components: [
      {
        version: '1.0.0',
        component: SimpleFillBlanksWrapper,
        schema: simpleFillBlanksSchema,
      },
    ],
    type: ETypeEdTechComponent.QUIZ,
    tags: [
      'Câu đố',
      'Thực hành',
      'Sơ cấp',
      'Điền vào chỗ trống',
      'Ôn tập kiến thức',
      'Dựa trên văn bản',
      'Ghi nhớ',
    ],
    description: 'Một câu hỏi nối các thẻ tương ứng từ hai cột',
    schema: simpleFillBlanksSchema,
  },
  {
    name: 'MultiSelectWrapper',
    title: 'Trắc nghiệm nhiều đáp án',
    components: [
      {
        version: '1.0.0',
        component: MultiSelectWrapper,
        schema: multiSelectWrapperSchema,
      },
    ],
    type: ETypeEdTechComponent.QUIZ,
    tags: [
      'Câu đố',
      'Đánh giá',
      'Trung cấp',
      'Nhiều lựa chọn',
      'Ôn tập kiến thức',
      'Dựa trên văn bản',
      'Tư duy phản biện',
    ],
    description: 'Câu hỏi trắc nghiệm cho phép chọn nhiều đáp án đúng',
    schema: multiSelectWrapperSchema,
  },
];

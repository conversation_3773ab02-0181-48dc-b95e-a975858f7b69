import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Table, Tooltip } from 'antd';
import {
  EyeInvisibleOutlined,
  FunctionOutlined,
  TableOutlined,
} from '@ant-design/icons';
import './TrigonometricVisualizer.css';
import type { ColumnsType } from 'antd/es/table';
import { TrigonometricConfig } from './TrigonometricConfig';
import {
  degreesToRadians,
  normalizeAngle,
  calculateTrigonometricValues,
  getTranslations,
  getPointOnCircle,
  isAngleInAllowedQuadrants,
  isSpecialAngle,
  specialAngleFractions,
  radiansToDegrees,
} from './TrigonometricUtils';

interface TrigonometricVisualizerSimulatorComponentProps {
  config: TrigonometricConfig;
  onAngleChange?: (angle: number) => void;
}

interface DataPoint {
  key: string;
  angle: number;
  sin: string;
  cos: string;
  tan: string;
  cot: string;
}

const TrigonometricVisualizerSimulatorComponent: React.FC<
  TrigonometricVisualizerSimulatorComponentProps
> = ({ config }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [angle, setAngle] = useState<number>(config.initialAngle);
  const [trigValues, setTrigValues] = useState(
    calculateTrigonometricValues(angle, config)
  );
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [isInitialMount, setIsInitialMount] = useState<boolean>(true);

  const translations = getTranslations(config.language);

  const calculateAngleFromCoords = (x: number, y: number) => {
    const newAngle = radiansToDegrees(Math.atan2(y, x));
    return normalizeAngle(newAngle);
  };

  // Ngưỡng độ lệch để snap vào góc đặc biệt (đơn vị: độ)
  const SNAP_THRESHOLD = 5;

  // Tìm góc đặc biệt gần nhất
  const snapToSpecialAngle = (currentAngle: number): number => {
    let closestSpecialAngle = currentAngle;
    let minDifference = SNAP_THRESHOLD;

    for (const specialAngleStr of Object.keys(specialAngleFractions)) {
      const specialAngle = parseFloat(specialAngleStr);
      if (!isAngleInAllowedQuadrants(specialAngle, config.enableQuadrants))
        continue;

      const difference = Math.abs(
        normalizeAngle(currentAngle) - normalizeAngle(specialAngle)
      );
      const wrappedDifference = Math.min(difference, 360 - difference); // Xử lý trường hợp qua 360°

      if (wrappedDifference < minDifference) {
        minDifference = wrappedDifference;
        closestSpecialAngle = specialAngle;
      }
    }

    return closestSpecialAngle;
  };

  const handleAngleChange = useCallback(
    (newAngle: number) => {
      const normalizedAngle = normalizeAngle(newAngle);
      if (!isAngleInAllowedQuadrants(normalizedAngle, config.enableQuadrants)) {
        return;
      }

      // Snap vào góc đặc biệt nếu gần
      const snappedAngle = snapToSpecialAngle(normalizedAngle);
      setAngle(snappedAngle);
      setTrigValues(calculateTrigonometricValues(snappedAngle, config));
    },
    [config]
  );

  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !canvas.getContext('2d')) return;
    const ctx = canvas.getContext('2d')!;
    const { circleRadius } = config;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (config.displayOptions.showGrid) {
      ctx.strokeStyle = '#e9e9e9';
      ctx.lineWidth = 1;
      for (
        let y = centerY - circleRadius;
        y <= centerY + circleRadius;
        y += 30
      ) {
        ctx.beginPath();
        ctx.moveTo(centerX - circleRadius, y);
        ctx.lineTo(centerX + circleRadius, y);
        ctx.stroke();
      }
      for (
        let x = centerX - circleRadius;
        x <= centerX + circleRadius;
        x += 30
      ) {
        ctx.beginPath();
        ctx.moveTo(x, centerY - circleRadius);
        ctx.lineTo(x, centerY + circleRadius);
        ctx.stroke();
      }
    }

    ctx.strokeStyle = '#666';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(centerX - circleRadius - 30, centerY);
    ctx.lineTo(centerX + circleRadius + 30, centerY);
    ctx.moveTo(centerX, centerY - circleRadius - 30);
    ctx.lineTo(centerX, centerY + circleRadius + 30);
    ctx.stroke();

    ctx.fillStyle = '#666';
    ctx.beginPath();
    ctx.moveTo(centerX + circleRadius + 30, centerY);
    ctx.lineTo(centerX + circleRadius + 20, centerY - 5);
    ctx.lineTo(centerX + circleRadius + 20, centerY + 5);
    ctx.fill();
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - circleRadius - 30);
    ctx.lineTo(centerX - 5, centerY - circleRadius - 20);
    ctx.lineTo(centerX + 5, centerY - circleRadius - 20);
    ctx.fill();

    if (config.displayOptions.showLabels) {
      ctx.fillStyle = '#000';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('x', centerX + circleRadius + 30, centerY + 20);
      ctx.fillText('y', centerX + 20, centerY - circleRadius - 30);
      ctx.fillText('1', centerX + circleRadius, centerY + 20);
      ctx.fillText('-1', centerX - circleRadius, centerY + 20);
      ctx.fillText('1', centerX - 20, centerY - circleRadius);
      ctx.fillText('-1', centerX - 20, centerY + circleRadius);
    }

    if (config.displayOptions.showUnitCircle) {
      const drawQuadrant = (
        startAngle: number,
        endAngle: number,
        enabled: boolean
      ) => {
        ctx.beginPath();
        ctx.arc(
          centerX,
          centerY,
          circleRadius,
          degreesToRadians(startAngle),
          degreesToRadians(endAngle)
        );
        ctx.strokeStyle = enabled ? '#3366cc' : '#cccccc';
        ctx.lineWidth = 2;
        ctx.stroke();
      };
      drawQuadrant(0, 90, config.enableQuadrants.first);
      drawQuadrant(90, 180, config.enableQuadrants.second);
      drawQuadrant(180, 270, config.enableQuadrants.third);
      drawQuadrant(270, 360, config.enableQuadrants.fourth);
    }

    Object.keys(specialAngleFractions).forEach((specialAngleStr) => {
      const specialAngle = parseFloat(specialAngleStr);
      if (isAngleInAllowedQuadrants(specialAngle, config.enableQuadrants)) {
        const point = getPointOnCircle(specialAngle, circleRadius);
        const markerX = centerX + point.x;
        const markerY = centerY - point.y;

        ctx.beginPath();
        ctx.arc(markerX, markerY, 4, 0, 2 * Math.PI);
        ctx.fillStyle = specialAngle === angle ? '#ff0000' : '#666666';
        ctx.fill();

        if (config.displayOptions.showLabels) {
          ctx.fillStyle = '#666666';
          ctx.font = '12px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(`${specialAngle}°`, markerX, markerY - 10);
        }
      }
    });

    const angleRadians = degreesToRadians(angle);
    const pointOnCircle = getPointOnCircle(angle, circleRadius);
    const pointX = centerX + pointOnCircle.x;
    const pointY = centerY - pointOnCircle.y;

    ctx.beginPath();
    ctx.strokeStyle = '#009900';
    ctx.lineWidth = 2;
    ctx.arc(centerX, centerY, 40, -angleRadians, 0);
    ctx.stroke();

    ctx.fillStyle = '#009900';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    const labelX = centerX + 50 * Math.cos(angleRadians / 2);
    const labelY = centerY - 50 * Math.sin(angleRadians / 2);
    ctx.fillText(`${angle.toFixed(1)}°`, labelX, labelY);

    ctx.beginPath();
    ctx.strokeStyle = '#ff0000';
    ctx.lineWidth = 2;
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(pointX, pointY);
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(pointX, pointY, 5, 0, 2 * Math.PI);
    ctx.fillStyle = '#ff0000';
    ctx.fill();

    if (config.displayOptions.showLabels) {
      ctx.fillStyle = '#ff0000';
      ctx.fillText('M', pointX + 10, pointY - 10);
    }

    if (config.displayOptions.showSinCosLines) {
      ctx.strokeStyle = '#0066cc';
      ctx.lineWidth = 1;
      ctx.setLineDash([3, 3]);
      ctx.beginPath();
      ctx.moveTo(pointX, pointY);
      ctx.lineTo(centerX, pointY);
      ctx.moveTo(pointX, pointY);
      ctx.lineTo(pointX, centerY);
      ctx.stroke();
      ctx.setLineDash([]);

      ctx.fillStyle = '#0066cc';
      ctx.beginPath();
      ctx.arc(centerX, pointY, 4, 0, 2 * Math.PI);
      ctx.fill();
      ctx.textAlign = 'right';
      ctx.fillText(
        `${translations.sin} α = ${trigValues.sin}`,
        centerX - 10,
        pointY - 10
      );

      ctx.fillStyle = '#cc6600';
      ctx.beginPath();
      ctx.arc(pointX, centerY, 4, 0, 2 * Math.PI);
      ctx.fill();
      ctx.textAlign = 'left';
      ctx.fillText(
        `${translations.cos} α = ${trigValues.cos}`,
        pointX + 10,
        centerY + 20
      );
    }

    if (
      config.displayOptions.showTangentLine &&
      trigValues.tan !== 'undefined'
    ) {
      const tangentX = centerX + circleRadius;
      const tangentY = centerY - trigValues.tanValue * circleRadius;
      ctx.beginPath();
      ctx.strokeStyle = '#9900cc';
      ctx.lineWidth = 2;
      ctx.moveTo(tangentX, centerY);
      ctx.lineTo(tangentX, tangentY);
      ctx.stroke();

      ctx.beginPath();
      ctx.arc(tangentX, tangentY, 4, 0, 2 * Math.PI);
      ctx.fillStyle = '#9900cc';
      ctx.fill();
      ctx.fillText(
        `${translations.tan} α = ${trigValues.tan}`,
        tangentX + 10,
        tangentY
      );
    }

    if (
      config.displayOptions.showCotangentLine &&
      trigValues.cot !== 'undefined'
    ) {
      const cotangentY = centerY - circleRadius;
      const cotangentX = centerX + trigValues.cotValue * circleRadius;
      ctx.beginPath();
      ctx.strokeStyle = '#cc0066';
      ctx.lineWidth = 2;
      ctx.moveTo(centerX, cotangentY);
      ctx.lineTo(cotangentX, cotangentY);
      ctx.stroke();

      ctx.beginPath();
      ctx.arc(cotangentX, cotangentY, 4, 0, 2 * Math.PI);
      ctx.fillStyle = '#cc0066';
      ctx.fill();
      ctx.fillText(
        `${translations.cot} α = ${trigValues.cot}`,
        cotangentX,
        cotangentY - 10
      );
    }

    if (config.displayOptions.showCoordinates) {
      ctx.fillStyle = '#000000';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      const coordText = isSpecialAngle(angle)
        ? `${translations.point} M(${trigValues.cos}, ${trigValues.sin})`
        : `${translations.point} M(${trigValues.cosValue.toFixed(
            config.precision
          )}, ${trigValues.sinValue.toFixed(config.precision)})`;
      ctx.fillText(coordText, centerX, canvas.height - 20);
    }
  }, [angle, config, trigValues, translations]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      setIsDragging(true);
      const canvas = canvasRef.current;
      if (!canvas) return;
      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left - canvas.width / 2;
      const mouseY = canvas.height / 2 - (e.clientY - rect.top);

      let selectedAngle: number | null = null;
      for (const specialAngleStr of Object.keys(specialAngleFractions)) {
        const specialAngle = parseFloat(specialAngleStr);
        if (!isAngleInAllowedQuadrants(specialAngle, config.enableQuadrants))
          continue;

        const point = getPointOnCircle(specialAngle, config.circleRadius);
        const markerX = canvas.width / 2 + point.x;
        const markerY = canvas.height / 2 - point.y;
        const distance = Math.sqrt(
          (mouseX - markerX) ** 2 + (mouseY - markerY) ** 2
        );

        if (distance < 10) {
          selectedAngle = specialAngle;
          break;
        }
      }

      if (selectedAngle !== null) {
        handleAngleChange(selectedAngle);
      } else {
        const normalizedAngle = calculateAngleFromCoords(mouseX, mouseY);
        if (
          isAngleInAllowedQuadrants(normalizedAngle, config.enableQuadrants)
        ) {
          handleAngleChange(normalizedAngle);
        }
      }
    },
    [config.enableQuadrants, config.circleRadius, handleAngleChange]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left - canvas.width / 2;
      const mouseY = canvas.height / 2 - (e.clientY - rect.top);
      const distance = Math.sqrt(mouseX * mouseX + mouseY * mouseY);
      const isNearCircle = Math.abs(distance - config.circleRadius) < 20;

      if (!isDragging) {
        canvas.style.cursor = isNearCircle ? 'pointer' : 'default';
        return;
      }

      const normalizedAngle = calculateAngleFromCoords(mouseX, mouseY);
      if (isAngleInAllowedQuadrants(normalizedAngle, config.enableQuadrants)) {
        handleAngleChange(normalizedAngle);
      }
    },
    [isDragging, config.enableQuadrants, config.circleRadius, handleAngleChange]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    const canvas = canvasRef.current;
    if (canvas) canvas.style.cursor = 'default';
  }, []);

  const setTouchFeedback = useCallback((isAllowed: boolean) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    canvas.style.boxShadow = isAllowed
      ? '0 0 8px rgba(0, 128, 0, 0.5)'
      : '0 0 8px rgba(255, 0, 0, 0.5)';
    setTimeout(() => {
      if (canvas) canvas.style.boxShadow = '';
    }, 300);
  }, []);

  const handleTouchStart = useCallback(
    (e: React.TouchEvent<HTMLCanvasElement>) => {
      e.preventDefault();
      setIsDragging(true);
      const canvas = canvasRef.current;
      if (!canvas) return;
      const touch = e.touches[0];
      const rect = canvas.getBoundingClientRect();
      const touchX = touch.clientX - rect.left - canvas.width / 2;
      const touchY = canvas.height / 2 - (touch.clientY - rect.top);

      let selectedAngle: number | null = null;
      for (const specialAngleStr of Object.keys(specialAngleFractions)) {
        const specialAngle = parseFloat(specialAngleStr);
        if (!isAngleInAllowedQuadrants(specialAngle, config.enableQuadrants))
          continue;

        const point = getPointOnCircle(specialAngle, config.circleRadius);
        const markerX = canvas.width / 2 + point.x;
        const markerY = canvas.height / 2 - point.y;
        const distance = Math.sqrt(
          (touchX - markerX) ** 2 + (touchY - markerY) ** 2
        );

        if (distance < 10) {
          selectedAngle = specialAngle;
          break;
        }
      }

      if (selectedAngle !== null) {
        handleAngleChange(selectedAngle);
        setTouchFeedback(true);
      } else {
        const normalizedAngle = calculateAngleFromCoords(touchX, touchY);
        if (
          isAngleInAllowedQuadrants(normalizedAngle, config.enableQuadrants)
        ) {
          setTouchFeedback(true);
          handleAngleChange(normalizedAngle);
        } else {
          setTouchFeedback(false);
        }
      }
    },
    [
      config.enableQuadrants,
      config.circleRadius,
      handleAngleChange,
      setTouchFeedback,
    ]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent<HTMLCanvasElement>) => {
      e.preventDefault();
      if (!isDragging) return;
      const canvas = canvasRef.current;
      if (!canvas) return;
      const touch = e.touches[0];
      const rect = canvas.getBoundingClientRect();
      const touchX = touch.clientX - rect.left - canvas.width / 2;
      const touchY = canvas.height / 2 - (touch.clientY - rect.top);
      const normalizedAngle = calculateAngleFromCoords(touchX, touchY);
      if (isAngleInAllowedQuadrants(normalizedAngle, config.enableQuadrants)) {
        handleAngleChange(normalizedAngle);
      }
    },
    [isDragging, config.enableQuadrants, handleAngleChange]
  );

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
    const canvas = canvasRef.current;
    if (canvas) canvas.style.boxShadow = '';
  }, []);

  useEffect(() => {
    if (isInitialMount) {
      if (
        isAngleInAllowedQuadrants(config.initialAngle, config.enableQuadrants)
      ) {
        handleAngleChange(config.initialAngle);
      } else {
        const fallbackAngles = [45, 135, 225, 315];
        for (const fallback of fallbackAngles) {
          if (isAngleInAllowedQuadrants(fallback, config.enableQuadrants)) {
            handleAngleChange(fallback);
            break;
          }
        }
      }
      setIsInitialMount(false);
    }
  }, [
    config.initialAngle,
    config.enableQuadrants,
    handleAngleChange,
    isInitialMount,
  ]);

  useEffect(() => {
    drawCanvas();
  }, [angle, config, drawCanvas]);

  const getTableData = () => {
    if (!config.displayOptions.showTable) return [];
    return config.specialAngles
      .filter((sa) =>
        isAngleInAllowedQuadrants(sa.angle, config.enableQuadrants)
      )
      .map((sa) => ({
        key: `angle-${sa.angle}`,
        angle: sa.angle,
        sin: sa.sin,
        cos: sa.cos,
        tan: sa.tan,
        cot: sa.cot,
      }));
  };

  const columns: ColumnsType<DataPoint> = [
    {
      title: `${translations.angle} (°)`,
      dataIndex: 'angle',
      key: 'angle',
      render: (value) => <strong>{value}°</strong>,
    },
    { title: `${translations.sin}`, dataIndex: 'sin', key: 'sin' },
    { title: `${translations.cos}`, dataIndex: 'cos', key: 'cos' },
    { title: `${translations.tan}`, dataIndex: 'tan', key: 'tan' },
    { title: `${translations.cot}`, dataIndex: 'cot', key: 'cot' },
  ];

  // Toggle state for panels
  const [showProperties, setShowProperties] = useState<boolean>(
    config.displayOptions.showPropertiesPanel
  );
  const [showTheory, setShowTheory] = useState<boolean>(
    config.displayOptions.showTheoryPanel
  );

  // Update toggle state when config changes
  useEffect(() => {
    setShowProperties(config.displayOptions.showPropertiesPanel);
    setShowTheory(config.displayOptions.showTheoryPanel);
  }, [
    config.displayOptions.showPropertiesPanel,
    config.displayOptions.showTheoryPanel,
  ]);

  return (
    <div className="trigonometric-visualizer tailwind-p-4 tailwind-bg-white tailwind-rounded-lg tailwind-shadow-md">
      {/* Main layout with flex container */}
      <div className="tailwind-flex tailwind-flex-col md:tailwind-flex-row tailwind-gap-4">
        {/* Left side - Canvas and main visualization */}
        <div className="tailwind-flex-1 tailwind-flex tailwind-flex-col">
          {/* Angle display and toggle buttons */}
          <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
            <h3 className="tailwind-text-lg tailwind-font-semibold">
              {config.displayOptions.showAngleInRadians
                ? `${translations.angle}: ${angle.toFixed(
                    1
                  )}° = ${degreesToRadians(angle).toFixed(config.precision)} ${
                    translations.radians
                  }`
                : `${translations.angle}: ${angle.toFixed(1)}°`}
            </h3>
            <div className="tailwind-flex tailwind-gap-2">
              <Tooltip
                title={
                  showProperties ? 'Ẩn bảng thuộc tính' : 'Hiện bảng thuộc tính'
                }
              >
                <button
                  onClick={() => setShowProperties(!showProperties)}
                  className={`toggle-button tailwind-px-3 tailwind-py-1 tailwind-text-sm tailwind-rounded-md tailwind-transition-all tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-shadow-sm hover:tailwind-shadow tailwind-transform hover:tailwind-scale-105 ${
                    showProperties
                      ? 'tailwind-bg-gradient-to-r tailwind-from-purple-500 tailwind-to-pink-500 tailwind-text-white'
                      : 'tailwind-bg-gray-100 tailwind-text-gray-700 hover:tailwind-bg-gray-200'
                  }`}
                >
                  {showProperties ? (
                    <EyeInvisibleOutlined className="tailwind-text-white tailwind-animate-pulse" />
                  ) : (
                    <TableOutlined className="tailwind-text-purple-500" />
                  )}
                  <span>
                    {showProperties ? 'Ẩn thuộc tính' : 'Hiện thuộc tính'}
                  </span>
                </button>
              </Tooltip>
              <Tooltip title={showTheory ? 'Ẩn lý thuyết' : 'Hiện lý thuyết'}>
                <button
                  onClick={() => setShowTheory(!showTheory)}
                  className={`toggle-button tailwind-px-3 tailwind-py-1 tailwind-text-sm tailwind-rounded-md tailwind-transition-all tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-shadow-sm hover:tailwind-shadow tailwind-transform hover:tailwind-scale-105 ${
                    showTheory
                      ? 'tailwind-bg-gradient-to-r tailwind-from-blue-500 tailwind-to-teal-400 tailwind-text-white'
                      : 'tailwind-bg-gray-100 tailwind-text-gray-700 hover:tailwind-bg-gray-200'
                  }`}
                >
                  {showTheory ? (
                    <EyeInvisibleOutlined className="tailwind-text-white tailwind-animate-pulse" />
                  ) : (
                    <FunctionOutlined className="tailwind-text-blue-500" />
                  )}
                  <span>{showTheory ? 'Ẩn lý thuyết' : 'Hiện lý thuyết'}</span>
                </button>
              </Tooltip>
            </div>
          </div>

          {/* Canvas - Make it more prominent */}
          <div className="tailwind-flex tailwind-justify-center tailwind-mb-4">
            <canvas
              ref={canvasRef}
              width={600}
              height={600}
              className="tailwind-border tailwind-rounded-lg tailwind-shadow-lg tailwind-cursor-pointer tailwind-max-w-full"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              onTouchCancel={handleTouchEnd}
            />
          </div>

          {/* Trigonometric values display - Compact and colorful */}
          <div className="tailwind-flex tailwind-justify-center tailwind-gap-4 tailwind-mb-4">
            <div className="trig-value-card tailwind-text-center tailwind-px-3 tailwind-py-2 tailwind-bg-gradient-to-br tailwind-from-blue-50 tailwind-to-blue-100 tailwind-rounded-lg tailwind-shadow-sm tailwind-flex-1 tailwind-border tailwind-border-blue-200 hover:tailwind-shadow-md tailwind-transition-all">
              <div className="tailwind-text-sm tailwind-text-blue-800 tailwind-font-medium tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-1">
                <span className="tailwind-text-blue-500 tailwind-text-lg">
                  sin
                </span>
              </div>
              <div className="tailwind-text-lg tailwind-font-bold tailwind-text-blue-900">
                {trigValues.sin}
              </div>
            </div>
            <div className="trig-value-card tailwind-text-center tailwind-px-3 tailwind-py-2 tailwind-bg-gradient-to-br tailwind-from-orange-50 tailwind-to-orange-100 tailwind-rounded-lg tailwind-shadow-sm tailwind-flex-1 tailwind-border tailwind-border-orange-200 hover:tailwind-shadow-md tailwind-transition-all">
              <div className="tailwind-text-sm tailwind-text-orange-800 tailwind-font-medium tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-1">
                <span className="tailwind-text-orange-500 tailwind-text-lg">
                  cos
                </span>
              </div>
              <div className="tailwind-text-lg tailwind-font-bold tailwind-text-orange-900">
                {trigValues.cos}
              </div>
            </div>
            <div className="trig-value-card tailwind-text-center tailwind-px-3 tailwind-py-2 tailwind-bg-gradient-to-br tailwind-from-purple-50 tailwind-to-purple-100 tailwind-rounded-lg tailwind-shadow-sm tailwind-flex-1 tailwind-border tailwind-border-purple-200 hover:tailwind-shadow-md tailwind-transition-all">
              <div className="tailwind-text-sm tailwind-text-purple-800 tailwind-font-medium tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-1">
                <span className="tailwind-text-purple-500 tailwind-text-lg">
                  tan
                </span>
              </div>
              <div className="tailwind-text-lg tailwind-font-bold tailwind-text-purple-900">
                {trigValues.tan}
              </div>
            </div>
            <div className="trig-value-card tailwind-text-center tailwind-px-3 tailwind-py-2 tailwind-bg-gradient-to-br tailwind-from-pink-50 tailwind-to-pink-100 tailwind-rounded-lg tailwind-shadow-sm tailwind-flex-1 tailwind-border tailwind-border-pink-200 hover:tailwind-shadow-md tailwind-transition-all">
              <div className="tailwind-text-sm tailwind-text-pink-800 tailwind-font-medium tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-1">
                <span className="tailwind-text-pink-500 tailwind-text-lg">
                  cot
                </span>
              </div>
              <div className="tailwind-text-lg tailwind-font-bold tailwind-text-pink-900">
                {trigValues.cot}
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Properties panel (conditionally rendered) */}
        {showProperties && (
          <div className="md:tailwind-w-1/3 tailwind-border-l tailwind-pl-4 tailwind-animate-fadeIn">
            <div className="tailwind-mb-4">
              <h4 className="tailwind-font-medium tailwind-text-lg tailwind-mb-3 tailwind-text-blue-700 tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-bg-purple-50 tailwind-p-2 tailwind-rounded-md tailwind-border-l-4 tailwind-border-purple-500">
                <TableOutlined className="tailwind-text-purple-500 tailwind-text-xl" />
                <span>Bảng thuộc tính</span>
              </h4>
              {config.displayOptions.showTable && (
                <div className="tailwind-overflow-x-auto tailwind-mb-4 tailwind-bg-white tailwind-p-2 tailwind-rounded-lg tailwind-shadow-sm tailwind-border tailwind-border-gray-200">
                  <Table
                    columns={columns}
                    dataSource={getTableData()}
                    size="small"
                    pagination={false}
                    bordered
                    className="tailwind-animate-fadeIn"
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Theory section (conditionally rendered) */}
      {showTheory && config.displayOptions.showFormulas && (
        <div className="tailwind-mt-4 tailwind-p-4 tailwind-border tailwind-rounded-lg tailwind-bg-gradient-to-br tailwind-from-blue-50 tailwind-to-white tailwind-shadow-sm tailwind-animate-fadeIn">
          <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-3">
            <h4 className="tailwind-font-medium tailwind-text-lg tailwind-text-blue-700 tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-bg-blue-50 tailwind-p-2 tailwind-rounded-md tailwind-border-l-4 tailwind-border-blue-500 tailwind-w-full">
              <FunctionOutlined className="tailwind-text-blue-500 tailwind-text-xl" />
              <span>{translations.basicTheory}</span>
            </h4>
          </div>
          <div className="tailwind-mb-4">
            <h5 className="tailwind-font-medium">{translations.definition}</h5>
            <p className="tailwind-text-sm tailwind-mb-2">
              {translations.theoryText}
            </p>
            <ul className="tailwind-list-disc tailwind-pl-5 tailwind-text-sm">
              <li className="tailwind-mb-1">{translations.sinDef}</li>
              <li className="tailwind-mb-1">{translations.cosDef}</li>
              <li className="tailwind-mb-1">{translations.tanDef}</li>
              <li className="tailwind-mb-1">{translations.cotDef}</li>
            </ul>
          </div>
          <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-2 tailwind-gap-4">
            <div className="tailwind-border tailwind-rounded-lg tailwind-p-3 tailwind-bg-white">
              <div className="tailwind-bg-blue-100 tailwind-p-2 tailwind-rounded tailwind-text-blue-800 tailwind-font-medium">
                {translations.complementaryAngles}
              </div>
              <ul className="tailwind-list-disc tailwind-pl-5 tailwind-mt-2 tailwind-text-sm">
                <li>
                  {translations.sin} α = {translations.cos}(90° - α)
                </li>
                <li>
                  {translations.cos} α = {translations.sin}(90° - α)
                </li>
                <li>
                  {translations.tan} α = {translations.cot}(90° - α)
                </li>
              </ul>
            </div>
            <div className="tailwind-border tailwind-rounded-lg tailwind-p-3 tailwind-bg-white">
              <div className="tailwind-bg-blue-100 tailwind-p-2 tailwind-rounded tailwind-text-blue-800 tailwind-font-medium">
                {translations.supplementaryAngles}
              </div>
              <ul className="tailwind-list-disc tailwind-pl-5 tailwind-mt-2 tailwind-text-sm">
                <li>
                  {translations.sin} α = {translations.sin}(180° - α)
                </li>
                <li>
                  {translations.cos} α = -{translations.cos}(180° - α)
                </li>
                <li>
                  {translations.tan} α = -{translations.tan}(180° - α)
                </li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrigonometricVisualizerSimulatorComponent;

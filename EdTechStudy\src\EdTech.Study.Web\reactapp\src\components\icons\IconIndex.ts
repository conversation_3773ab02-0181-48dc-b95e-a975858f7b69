/**
 * IconIndex.ts
 *
 * This is the main entry point for all icon-related imports.
 * It exports all icon components, utility functions, and hooks.
 */

// Export all icon components from IconRegister via IconExports
export * from './IconExports';

// Export utility functions and types from IconUtils
export {
  Icon
} from './IconUtils';

// Export types
export type { IconProps } from './IconUtils';
export { IconType, IconCategory } from './IconUtils';

// Export registry functions and hooks
export {
  IconProvider,
  useIconContext,
  useIcon,
  getIconByName,
  getIconsByCategory,
  getAllIcons,
  default as IconRegistry
} from './IconRegistry';

// Export the demo component
export { default as IconStoreDemo } from './IconStoreDemo';

import { Question, QuestionAnswer } from './QuestionBankConfig';

/**
 * <PERSON>ểm tra đáp án dựa vào loại câu hỏi
 * @param question Câu hỏi cần kiểm tra
 * @param answer Đáp án của người dùng
 * @returns boolean Kết quả đúng/sai
 */
export const checkAnswer = (question: Question, answer: QuestionAnswer): boolean => {
  if (answer === null) return false;

  switch (question.type) {
    case 'truefalse':
      return answer === question.isCorrect;
      
    case 'multiplechoice':
      return answer === question.correctOptionIndex;
      
    case 'fillin':
      if (typeof answer === 'string') {
        if (question.caseSensitive) {
          return answer === question.correctAnswer;
        } else {
          return answer.toLowerCase() === question.correctAnswer.toLowerCase();
        }
      }
      return false;
      
    case 'ordering':
      if (Array.isArray(answer)) {
        return JSON.stringify(answer) === JSON.stringify(question.correctOrder);
      }
      return false;
      
    case 'matching':
      if (Array.isArray(answer) && answer.length > 0 && Array.isArray(answer[0] as unknown[])) {
        // Sort the pairs to ensure consistent comparison
        const sortedAnswer = [...(answer as [number, number][])].sort((a, b) => a[0] - b[0] || a[1] - b[1]);
        const sortedCorrectLeftToRight = [...question.correctLeftToRight].sort((a, b) => a[0] - b[0] || a[1] - b[1]);
        return JSON.stringify(sortedAnswer) === JSON.stringify(sortedCorrectLeftToRight);
      }
      return false;
      
    default:
      return false;
  }
};

/**
 * Format đáp án dưới dạng text phù hợp với loại câu hỏi
 * @param question Câu hỏi
 * @param answer Đáp án cần format
 * @returns string Đáp án đã format
 */
export const formatAnswer = (question: Question, answer: QuestionAnswer): string => {
  if (answer === null) return '';

  switch (question.type) {
    case 'truefalse':
      return answer === true ? 'Đúng' : 'Sai';
      
    case 'multiplechoice':
      if (typeof answer === 'number' && question.options) {
        return question.options[answer] || '';
      }
      return '';
      
    case 'fillin':
      return String(answer);
      
    case 'ordering':
      if (Array.isArray(answer)) {
        return answer.map(index => question.items[index as number]).join(' → ');
      }
      return '';
      
    case 'matching':
      if (Array.isArray(answer) && answer.length > 0 && Array.isArray(answer[0])) {
        return (answer as Array<[number, number]>)
          .map(pair => `${question.leftItems[pair[0]]} → ${question.rightItems[pair[1]]}`)
          .join(', ');
      }
      return '';
      
    default:
      return String(answer);
  }
};

/**
 * Format đáp án đúng dưới dạng text
 * @param question Câu hỏi
 * @returns string Đáp án đúng đã format
 */
export const formatCorrectAnswer = (question: Question): string => {
  switch (question.type) {
    case 'truefalse':
      return question.isCorrect ? 'Đúng' : 'Sai';
      
    case 'multiplechoice':
      return question.options[question.correctOptionIndex] || '';
      
    case 'fillin':
      return question.correctAnswer;
      
    case 'ordering':
      return question.correctOrder
        .map(index => question.items[index])
        .join(' → ');
      
    case 'matching':
      return question.correctLeftToRight
        .map(pair => `${question.leftItems[pair[0]]} → ${question.rightItems[pair[1]]}`)
        .join(', ');
      
    default:
      return '';
  }
}; 
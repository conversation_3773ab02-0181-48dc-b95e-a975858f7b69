import type { UploadFile } from 'antd/es/upload/interface';

// Định nghĩa các kiểu dữ liệu
export type MediaType = 'image' | 'video' | 'audio' | 'model3d';

export interface UploadedFile {
  url: string;
  fileName: string;
  fileSize?: number;
  mimeType?: string;
  [key: string]: any; // Cho phép thêm các thuộc tính khác
}

export interface UploadResponse {
  successful: UploadedFile[];
  failed?: string[];
}

// Props cho component MediaFileUploader
export interface MediaFileUploaderProps {
  // Loại media (image, video, audio)
  mediaType: MediaType;

  // Cho phép upload nhiều file
  multiple?: boolean;

  // Hiển thị danh sách file đang upload
  showUploadList?: boolean;

  // Danh sách file hiện tại
  fileList?: UploadFile[];

  // Cho phép kéo thả
  allowDrop?: boolean;

  // Kích thước container kéo thả
  dropAreaHeight?: number | string;
  dropAreaWidth?: number | string;

  // Callback khi upload thành công
  onUploadSuccess: (files: UploadedFile[]) => void;

  // Callback khi có lỗi
  onUploadError?: (error: any) => void;

  // Callback khi bắt đầu upload
  onUploadStart?: () => void;

  // Callback khi kết thúc upload (thành công hoặc thất bại)
  onUploadComplete?: () => void;

  // Chế độ chỉnh sửa
  isEditing?: boolean;

  // Nội dung tùy chỉnh cho khu vực kéo thả
  dropAreaContent?: React.ReactNode;

  // Nội dung tùy chỉnh cho nút upload
  uploadButtonContent?: React.ReactNode;

  // Các thuộc tính CSS tùy chỉnh
  className?: string;
  style?: React.CSSProperties;

  // Các thuộc tính khác cho Upload component
  uploadProps?: any;
}

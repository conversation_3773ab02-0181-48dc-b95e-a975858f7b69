import LessonColumnComponent from '../../components/layouts/lessonColumn/LessonColumnComponent';
import LessonRowComponent, {
  lessonRowSchema,
} from '../../components/layouts/lessonRow/LessonRowComponent';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const LAYOUT_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'LessonRowComponent',
    title: 'Hàng bài học',
    components: [
      {
        version: '1.0.0',
        component: LessonRowComponent,
        schema: lessonRowSchema,
      },
    ],
    type: ETypeEdTechComponent.LAYOUT,
    tags: undefined,
    description:
      'Component hiển thị nội dung bài học trong một hàng, gi<PERSON><PERSON> tổ chức và trình bày nội dung một cách rõ ràng và có cấu trúc.',
    schema: lessonRowSchema,
  },
  {
    name: 'LessonColumnComponent',
    title: 'Cột bài học',
    components: [
      {
        version: '1.0.0',
        component: LessonColumnComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.LAYOUT,
    tags: undefined,
    description:
      'Component hiển thị nội dung bài học trong một cột, giúp tổ chức và trình bày nội dung một cách rõ ràng và có cấu trúc.',
    schema: undefined,
  },
];

import { ITextProps } from '../../../../../core/title/CoreTitle';
import { MediaComponentProps, MediaItem } from '../../shared/type';

/**
 * Interface for a single audio item
 */
export interface AudioItem extends MediaItem {
  /** Artist of the audio */
  artist?: string;
}

export interface AudioComponentProps extends MediaComponentProps {
  /** Title properties */
  titleProps?: ITextProps;

  /** Array of audios to display */
  medias?: AudioItem[];

  /** Artist of the audio (legacy support) */
  artist?: string;

  /** Playback rate (0.5, 0.75, 1, 1.25, 1.5, 2) */
  playbackRate?: number;

  /** Volume level (0 to 1) */
  volume?: number;


}

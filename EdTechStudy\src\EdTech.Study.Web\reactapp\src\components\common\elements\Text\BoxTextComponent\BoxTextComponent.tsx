import React, { useState, useRef, useEffect } from 'react';
import { DragOutlined } from '@ant-design/icons';
import './BoxTextComponentStyles.css';
import { IEdTechRenderProps } from '../../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { EngineContainer } from '../../../engines';
import { BoxTextComponentProps, defaultProps } from './BoxTextComponentConfig';
import { getBoxStyle, getTextStyle } from './BoxTextComponentStyles';
import BoxTextComponentControls from './BoxTextComponentControls';

const BoxTextComponent: React.FC<IEdTechRenderProps<BoxTextComponentProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Merge with default props
  const config: BoxTextComponentProps = { ...defaultProps, ...params };

  // Refs for container elements
  const parentContainerRef = useRef<HTMLDivElement | null>(null);
  // Không set giá trị mặc định cho engineWidth và engineHeight
  // Chỉ cập nhật khi đã có parentContainerRef.current
  const [engineWidth, setEngineWidth] = useState<number>(0);
  const [engineHeight, setEngineHeight] = useState<number>(0);
  // Local state for editing
  const [text, setText] = useState(config.text || defaultProps.text);

  // Local state for styling properties to ensure immediate UI updates
  const [localStyles, setLocalStyles] = useState({
    backgroundColor: config.backgroundColor,
    borderColor: config.borderColor,
    textColor: config.textColor,
    fontSize: config.fontSize,
    fontWeight: config.fontWeight,
    fontStyle: config.fontStyle,
    textAlign: config.textAlign,
  });

  // Local state for box dimensions to ensure smooth resizing
  const [localDimensions, setLocalDimensions] = useState({
    width: 0, // Will be calculated based on scaleFactor and engine width
    height: typeof config.height === 'number' ? Number(config.height) : 150,
    scaleFactor: config.scaleFactor || defaultProps.scaleFactor,
  });

  // Ref để lưu trữ scaleFactor ban đầu từ params
  const initialScaleFactorRef = useRef<number>(
    config.scaleFactor || defaultProps.scaleFactor || 0.3
  );

  // Refs
  const boxRef = useRef<HTMLDivElement>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  // Effect to sync text and styles with params
  useEffect(() => {
    if (params?.text !== undefined && params.text !== text) {
      setText(params.text);
    }

    // Sync styling properties
    setLocalStyles({
      backgroundColor: params?.backgroundColor || defaultProps.backgroundColor,
      borderColor: params?.borderColor || defaultProps.borderColor,
      textColor: params?.textColor || defaultProps.textColor,
      fontSize: params?.fontSize || defaultProps.fontSize,
      fontWeight: params?.fontWeight || defaultProps.fontWeight,
      fontStyle: params?.fontStyle || defaultProps.fontStyle,
      textAlign: params?.textAlign || defaultProps.textAlign,
    });

    // IMPORTANT FIX 1: Update initialScaleFactorRef when params.scaleFactor changes
    if (
      params?.scaleFactor !== undefined &&
      params.scaleFactor !== initialScaleFactorRef.current
    ) {
      initialScaleFactorRef.current = params.scaleFactor;
      currentScaleFactorRef.current = params.scaleFactor;
    }

    // Sync dimension properties
    if (parentContainerRef.current) {
      setLocalDimensions((prev) => {
        // If width hasn't been calculated yet (first load)
        if (prev.width === 0) {
          const newHeight =
            typeof params?.height === 'number'
              ? Number(params.height)
              : typeof defaultProps.height === 'number'
              ? Number(defaultProps.height)
              : 150;

          return {
            width:
              parentContainerRef.current!.clientWidth *
              initialScaleFactorRef.current,
            height: newHeight,
            scaleFactor: initialScaleFactorRef.current,
          };
        }
        // If only height changed, update height without changing scaleFactor
        else if (
          params?.height &&
          typeof params.height === 'number' &&
          Math.abs(Number(params.height) - prev.height) > 1
        ) {
          return {
            ...prev,
            height: Number(params.height),
            width: prev.width, // Ensure width is preserved
          };
        }

        // No significant changes, keep current dimensions
        return prev;
      });
    }
  }, [
    params?.text,
    params?.backgroundColor,
    params?.borderColor,
    params?.textColor,
    params?.fontSize,
    params?.fontWeight,
    params?.fontStyle,
    params?.textAlign,
    params?.height,
    params?.scaleFactor,
  ]);

  // Update configuration
  const updateConfig = (updates: Partial<BoxTextComponentProps>) => {
    // Update local state immediately for responsive UI
    if (updates.backgroundColor !== undefined) {
      setLocalStyles((prev) => ({
        ...prev,
        backgroundColor: updates.backgroundColor,
      }));
    }
    if (updates.borderColor !== undefined) {
      setLocalStyles((prev) => ({ ...prev, borderColor: updates.borderColor }));
    }
    if (updates.textColor !== undefined) {
      setLocalStyles((prev) => ({ ...prev, textColor: updates.textColor }));
    }
    if (updates.fontSize !== undefined) {
      setLocalStyles((prev) => ({ ...prev, fontSize: updates.fontSize }));
    }
    if (updates.fontWeight !== undefined) {
      setLocalStyles((prev) => ({ ...prev, fontWeight: updates.fontWeight }));
    }
    if (updates.fontStyle !== undefined) {
      setLocalStyles((prev) => ({ ...prev, fontStyle: updates.fontStyle }));
    }
    if (updates.textAlign !== undefined) {
      setLocalStyles((prev) => ({ ...prev, textAlign: updates.textAlign }));
    }

    // Update local dimensions if height or scaleFactor changes
    if (updates.height !== undefined || updates.scaleFactor !== undefined) {
      setLocalDimensions((prev) => {
        const newDimensions = { ...prev };

        if (updates.height !== undefined) {
          newDimensions.height =
            typeof updates.height === 'number'
              ? Number(updates.height)
              : prev.height;
        }

        if (updates.scaleFactor !== undefined && parentContainerRef.current) {
          newDimensions.scaleFactor = updates.scaleFactor;
          // IMPORTANT FIX 2: Calculate width directly from scaleFactor without any adjustments
          newDimensions.width =
            parentContainerRef.current.clientWidth * updates.scaleFactor;

          // Update the refs to keep track of the change
          initialScaleFactorRef.current = updates.scaleFactor;
          currentScaleFactorRef.current = updates.scaleFactor;
        }

        return newDimensions;
      });
    }

    // Update component parameters
    addOrUpdateParamComponent({
      ...config,
      ...updates,
    });
  };

  // Handle text change
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);
  };

  // Save text on blur
  const handleTextBlur = () => {
    updateConfig({ text });
  };

  // Update engine dimensions when window resizes or when switching modes
  useEffect(() => {
    const updateDimensions = () => {
      if (parentContainerRef.current) {
        const newEngineWidth = parentContainerRef.current.clientWidth;
        const newEngineHeight = parentContainerRef.current.clientHeight;
        setEngineWidth(newEngineWidth);
        setEngineHeight(newEngineHeight);

        // Cập nhật localDimensions khi kích thước container thay đổi
        // Để đảm bảo độ rộng của box luôn được cập nhật theo tỉ lệ scaleFactor
        const newWidth = newEngineWidth * initialScaleFactorRef.current;

        setLocalDimensions((prev) => {
          const updatedDimensions = {
            ...prev,
            width: newWidth,
            // Giữ nguyên scaleFactor và height
          };
          return updatedDimensions;
        });

        // Force re-render when dimensions change in normal mode
        if (!isEditing) {
          const boxRef = document.querySelector('.box-container');
          if (boxRef) {
            boxRef.setAttribute('data-updated', Date.now().toString());
          }
        }
      }
    };

    // Initial update
    updateDimensions();

    // Add event listener for window resize
    window.addEventListener('resize', updateDimensions);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, [isEditing]); // Re-run when switching between edit and normal modes

  // Refs for high-performance resize
  const initialWidthRef = useRef<number>(0);
  const initialHeightRef = useRef<number>(0);
  const initialMouseXRef = useRef<number>(0);
  const initialMouseYRef = useRef<number>(0);
  const isResizingRef = useRef<boolean>(false);
  const resizeModeRef = useRef<'horizontal' | 'vertical' | 'both' | null>(null);
  const scaleXRef = useRef<number>(1);
  const scaleYRef = useRef<number>(1);
  const transformOriginRef = useRef<string>('top left');
  const rafIdRef = useRef<number | null>(null);

  // Tracking state for UI updates only
  const [isResizing, setIsResizing] = useState(false);

  // High-performance resize handlers using CSS transform
  const startResize = (
    e: React.MouseEvent,
    mode: 'horizontal' | 'vertical' | 'both'
  ) => {
    if (!isEditing || !boxRef.current) return;

    e.preventDefault();
    e.stopPropagation();

    // Store initial values
    const box = boxRef.current;
    const rect = box.getBoundingClientRect();

    initialWidthRef.current = rect.width;
    initialHeightRef.current = rect.height;
    initialMouseXRef.current = e.clientX;
    initialMouseYRef.current = e.clientY;
    isResizingRef.current = true;
    resizeModeRef.current = mode;
    scaleXRef.current = 1;
    scaleYRef.current = 1;

    // Set transform origin based on resize mode
    if (mode === 'horizontal') {
      transformOriginRef.current = 'left center';
    } else if (mode === 'vertical') {
      transformOriginRef.current = 'center top';
    } else {
      transformOriginRef.current = 'top left';
    }

    box.style.transformOrigin = transformOriginRef.current;

    // Set UI state - minimal React state update
    setIsResizing(true);

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Set cursor based on resize mode
    if (mode === 'horizontal') {
      document.body.style.cursor = 'ew-resize';
    } else if (mode === 'vertical') {
      document.body.style.cursor = 'ns-resize';
    } else {
      document.body.style.cursor = 'nwse-resize';
    }

    // Prevent text selection during resize
    document.body.style.userSelect = 'none';
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (
      !isResizingRef.current ||
      !boxRef.current ||
      !parentContainerRef.current
    )
      return;

    // Cancel any pending animation frame
    if (rafIdRef.current !== null) {
      cancelAnimationFrame(rafIdRef.current);
    }

    // Use requestAnimationFrame for smooth animation
    rafIdRef.current = requestAnimationFrame(() => {
      if (!boxRef.current || !parentContainerRef.current) return;

      // Calculate deltas
      const deltaX = e.clientX - initialMouseXRef.current;
      const deltaY = e.clientY - initialMouseYRef.current;

      // Get current box and container dimensions
      const box = boxRef.current;
      const container = parentContainerRef.current;
      const containerWidth = container.clientWidth;

      // Calculate new dimensions
      let newWidth = initialWidthRef.current;
      let newHeight = initialHeightRef.current;

      if (
        resizeModeRef.current === 'horizontal' ||
        resizeModeRef.current === 'both'
      ) {
        // IMPORTANT FIX 4: Remove Math.min restriction to allow width up to containerWidth
        // This ensures user can resize to make boxRef width equal to containerWidth
        newWidth = Math.max(50, initialWidthRef.current + deltaX);

        // If scaleFactor would exceed 1, cap it at exactly 1
        if (newWidth > containerWidth) {
          newWidth = containerWidth;
        }
      }

      if (
        resizeModeRef.current === 'vertical' ||
        resizeModeRef.current === 'both'
      ) {
        newHeight = Math.max(50, initialHeightRef.current + deltaY);
      }

      // Apply new dimensions directly
      box.style.width = `${newWidth}px`;
      box.style.height = `${newHeight}px`;

      // Calculate new scaleFactor - always based on width ratio
      const newScaleFactor = newWidth / containerWidth;

      // Update local dimensions
      setLocalDimensions({
        width: newWidth,
        height: newHeight,
        scaleFactor: newScaleFactor,
      });

      // Store dimensions in refs
      currentWidthRef.current = newWidth;
      currentHeightRef.current = newHeight;
      currentScaleFactorRef.current = newScaleFactor;

      // Store current scale factors for reference
      scaleXRef.current = newWidth / initialWidthRef.current;
      scaleYRef.current = newHeight / initialHeightRef.current;
    });
  };

  const handleMouseUp = () => {
    if (
      !isResizingRef.current ||
      !boxRef.current ||
      !parentContainerRef.current
    )
      return;

    // Cancel any pending animation frame
    if (rafIdRef.current !== null) {
      cancelAnimationFrame(rafIdRef.current);
      rafIdRef.current = null;
    }

    // Reset state
    isResizingRef.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // Get final dimensions from refs
    const finalHeight = currentHeightRef.current;
    const newScaleFactor = currentScaleFactorRef.current;

    // Update initialScaleFactor with the new value from user resizing
    initialScaleFactorRef.current = newScaleFactor;

    // Update configuration with final dimensions
    updateConfig({
      scaleFactor: newScaleFactor,
      height: finalHeight,
    });

    // Reset UI state
    setIsResizing(false);
  };

  // Clean up event listeners and animation frames on unmount
  useEffect(() => {
    return () => {
      if (rafIdRef.current !== null) {
        cancelAnimationFrame(rafIdRef.current);
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  // Resize handlers using direct DOM manipulation
  const handleResizeRight = (e: React.MouseEvent) => {
    if (!isEditing) return;
    startResize(e, 'horizontal');
  };

  const handleResizeBottom = (e: React.MouseEvent) => {
    if (!isEditing) return;
    startResize(e, 'vertical');
  };

  const handleResizeCorner = (e: React.MouseEvent) => {
    if (!isEditing) return;
    startResize(e, 'both');
  };

  // Store current dimensions in refs
  const currentWidthRef = useRef<number>(0);
  const currentHeightRef = useRef<number>(0);
  const currentScaleFactorRef = useRef<number>(0.3);

  // Calculate box dimensions based on mode
  const calculateBoxDimensions = () => {
    const configHeight =
      typeof config.height === 'number' ? Number(config.height) : 150;

    let finalWidth: number = 0;
    let finalHeight: number = 0;

    // Always use scaleFactor from ref
    const scaleFactor = initialScaleFactorRef.current;

    // Kiểm tra xem container đã sẵn sàng chưa
    if (
      !parentContainerRef.current ||
      parentContainerRef.current.clientWidth <= 0
    ) {
      // Trả về kích thước 0 để đảm bảo không hiển thị box
      return { finalWidth: 0, finalHeight: configHeight };
    }

    // Sử dụng kích thước thực tế của container
    const actualContainerWidth = parentContainerRef.current.clientWidth;

    // Nếu container có kích thước quá nhỏ, không hiển thị box
    if (actualContainerWidth < 50) {
      return { finalWidth: 0, finalHeight: configHeight };
    }

    if (isEditing) {
      if (isResizing) {
        // During resize, use dimensions from localDimensions
        finalWidth = localDimensions.width;
        finalHeight = localDimensions.height;
        // Store current dimensions in refs
        currentWidthRef.current = finalWidth;
        currentHeightRef.current = finalHeight;
        currentScaleFactorRef.current = localDimensions.scaleFactor || 0.3;
      } else if (currentWidthRef.current > 0) {
        // After resizing, use dimensions from refs
        finalWidth = currentWidthRef.current;
        finalHeight = currentHeightRef.current;
      } else if (localDimensions.width > 0) {
        // Use localDimensions if available (e.g., after window resize)
        finalWidth = localDimensions.width;
        finalHeight = localDimensions.height;
      } else {
        // First render or never resized - use ACTUAL container width
        finalWidth = actualContainerWidth * scaleFactor;
        finalHeight = configHeight;

        // Update local dimensions but don't change scaleFactor
        setLocalDimensions((prev) => ({
          width: finalWidth,
          height: finalHeight,
          scaleFactor: prev.scaleFactor,
        }));

        currentWidthRef.current = finalWidth;
        currentHeightRef.current = finalHeight;
      }

      // Apply a minimum width and height
      finalWidth = Math.max(50, finalWidth);
      finalHeight = Math.max(50, finalHeight);
    } else {
      // In normal mode, calculate width directly from scaleFactor and ACTUAL container width
      finalWidth = actualContainerWidth * initialScaleFactorRef.current;
      finalHeight = configHeight;
      // Apply a minimum width and height
      finalWidth = Math.max(50, finalWidth);
      finalHeight = Math.max(50, finalHeight);

      // Reset resize-related refs but keep initialScaleFactor
      currentWidthRef.current = 0;
      currentHeightRef.current = 0;
    }

    return { finalWidth, finalHeight };
  };

  // Thêm state để kiểm soát việc hiển thị component
  const [isInitialized, setIsInitialized] = useState(false);

  // Thêm state để theo dõi việc tải component
  const [isLoading, setIsLoading] = useState(true);

  // Sử dụng useEffect để đánh dấu kết thúc quá trình tải sau một khoảng thời gian ngắn
  useEffect(() => {
    if (isInitialized) {
      // Đặt timeout ngắn để đảm bảo các giá trị đã được cập nhật
      const loadingTimeout = setTimeout(() => {
        setIsLoading(false);
      }, 50);

      return () => clearTimeout(loadingTimeout);
    }
  }, [isInitialized]);

  // Hàm debounce để giảm số lần cập nhật
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ) => {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        if (timeout) clearTimeout(timeout);
        func(...args);
      };
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // Sử dụng useEffect để đo kích thước container sau khi component đã mount
  useEffect(() => {
    // Đặt timeout để đảm bảo component được render trước khi đo kích thước
    const initTimeout = setTimeout(() => {
      // Sử dụng ResizeObserver để theo dõi thay đổi kích thước của container
      if (parentContainerRef.current) {
        // Tạo hàm xử lý có debounce để giảm số lần cập nhật
        const handleResize = debounce((entries) => {
          for (let entry of entries) {
            const containerWidth = entry.contentRect.width;
            if (containerWidth > 0) {
              // Cập nhật engineWidth khi kích thước container thay đổi
              setEngineWidth(containerWidth);

              // Cập nhật localDimensions để đảm bảo box được vẽ lại với kích thước mới
              setLocalDimensions((prev) => ({
                ...prev,
                width: containerWidth * initialScaleFactorRef.current,
              }));

              // Đánh dấu đã khởi tạo xong
              if (!isInitialized) {
                console.log(
                  'BoxTextComponent initialized with width:',
                  containerWidth
                );
                setIsInitialized(true);
              }
            }
          }
        }, 30); // Giảm xuống 30ms để phản hồi nhanh hơn

        const resizeObserver = new ResizeObserver((entries) =>
          handleResize(entries)
        );

        // Bắt đầu theo dõi container
        resizeObserver.observe(parentContainerRef.current);

        // Đo kích thước ngay lập tức nếu container đã có kích thước
        if (parentContainerRef.current.clientWidth > 0) {
          handleResize([
            {
              contentRect: {
                width: parentContainerRef.current.clientWidth,
              },
            },
          ]);
        }

        // Dọn dẹp khi component unmount
        return () => {
          resizeObserver.disconnect();
          clearTimeout(initTimeout);
        };
      }
    }, 0); // Timeout 0ms để đảm bảo chạy sau khi render

    return () => clearTimeout(initTimeout);
  }, []); // Chỉ chạy một lần sau khi component mount

  // Main component content - memoized to prevent unnecessary re-renders
  const MainComponent = React.useMemo(() => {
    const { finalWidth, finalHeight } = calculateBoxDimensions();
    const boxStyleProps = getBoxStyle(
      finalWidth,
      finalHeight,
      config,
      localStyles
    );
    const textStyleProps = getTextStyle(config, localStyles);
    return (
      <div
        className={`boxtext-container tailwind-flex tailwind-flex-col box-align-${
          config.boxAlign || 'left'
        }`}
        ref={parentContainerRef}
      >
        {isEditing && (
          <BoxTextComponentControls
            config={config}
            updateConfig={updateConfig}
          />
        )}
        {/* Chỉ hiển thị box khi đã khởi tạo xong và có kích thước */}
        {((isInitialized && !isLoading) || isEditing) && finalWidth > 0 && (
          <div
            ref={boxRef}
            className="box-container tailwind-relative"
            style={boxStyleProps}
          >
            {isEditing ? (
              <>
                <textarea
                  ref={textAreaRef}
                  className="box-text-textarea"
                  style={textStyleProps}
                  value={text}
                  onChange={handleTextChange}
                  onBlur={handleTextBlur}
                  placeholder="Nhập văn bản của bạn tại đây..."
                />
              </>
            ) : (
              <div className="box-text-content" style={textStyleProps}>
                {config.text || ''}
              </div>
            )}

            {/* Resize handles (only in edit mode) */}
            {isEditing && (
              <>
                {/* Right resize handle */}
                <div
                  className={`resize-handle resize-right`}
                  onMouseDown={handleResizeRight}
                  title="Kéo để thay đổi chiều rộng"
                />

                {/* Bottom resize handle */}
                <div
                  className={`resize-handle resize-bottom `}
                  onMouseDown={handleResizeBottom}
                  title="Kéo để thay đổi chiều cao"
                />

                {/* Corner resize handle */}
                <div
                  className={`resize-handle resize-corner`}
                  onMouseDown={handleResizeCorner}
                  title="Kéo để thay đổi kích thước"
                />

                {/* Drag handle */}
                <div
                  className="drag-handle tailwind-absolute tailwind-top-2 tailwind-left-2 tailwind-cursor-move tailwind-text-gray-400"
                  title="Kéo để di chuyển"
                >
                  <DragOutlined />
                </div>
              </>
            )}
          </div>
        )}
        {/* Hiển thị placeholder khi đang tải */}
        {(!isInitialized || isLoading) && !isEditing && (
          <div className="tailwind-h-32 tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-center">
            <div className="tailwind-animate-pulse tailwind-bg-gray-200 tailwind-rounded tailwind-h-24 tailwind-w-1/3"></div>
          </div>
        )}
      </div>
    );
  }, [
    isEditing,
    text,
    config.boxAlign,
    config.text,
    engineWidth, // Đảm bảo re-render khi kích thước container thay đổi
    engineHeight,
    isResizing,
    config.scaleFactor,
    config.height,
    localStyles,
    config.lineHeight,
    config.borderWidth,
    config.borderRadius,
    config.padding,
    currentWidthRef.current,
    currentHeightRef.current,
    currentScaleFactorRef.current,
    updateConfig,
    localDimensions.width,
    isInitialized, // Thêm isInitialized vào dependencies
    isLoading, // Thêm isLoading vào dependencies
  ]);

  return (
    <EngineContainer
      node={props}
      mainComponent={MainComponent}
      allowConfiguration={false}
      showFullscreenButton={false}
      id={props.path}
    />
  );
};

export default withEdComponentParams(BoxTextComponent);

import {
  QuestionDraft,
  QuestionOptionDraft,
  QuestionType,
} from '../../api/questionDraftApi';
import practiceLocalization from '../../components/quizs/localization';
import {
  MultiSelectAnswer,
  MultiSelectQuestion,
} from '../../interfaces/quizs/multiSelectQuiz.interface';
import {
  BaseAnswer,
  BaseQuestion,
  MatchingItemType,
  MatchingQuestion,
  QuestionStatus,
  QuizAnswer,
  QuizQuestion,
} from '../../interfaces/quizs/questionBase';

// // Server-side entity structure from C# backend
// export interface QuestionDraftEntity {
//   id: string;
//   content: string;
//   contentFormat: string;
//   type: QuestionTypeEnum;
//   difficultyLevel: number;
//   description?: string;
//   topics?: string;
//   tags?: string;
//   status: QuestionStatusEnum;
//   comment?: string;
//   idempotentKey?: string;
//   source?: string;
//   subjectId?: string;
//   subject?: Subject;
//   options?: QuestionOptionDraftEntity[];
//   creationTime?: string;
//   creatorId?: string;
//   lastModificationTime?: string;
//   lastModifierId?: string;
// }

// Option entity from server
// export interface QuestionOptionDraftEntity {
//   id: string;
//   questionDraftId: string;
//   content: string;
//   contentFormat: string;
//   isCorrect: boolean;
//   score: number;
//   order: number;
//   feedback?: string;
//   matchingContent?: string;
// }

// Enums matching C# backend
export enum QuestionTypeEnum {
  SingleChoice = 0,
  MultipleChoice = 1,
  Essay = 2,
  Matching = 3,
  Fillblank = 4,
}

export enum QuestionStatusEnum {
  Draft = 0,
  Submitted = 1,
  Approved = 2,
  Published = 3,
  Rejected = 4,
}

// Map server-side question types to client-side types
const mapQuestionType = (serverType: QuestionType): string => {
  switch (serverType) {
    case QuestionType.SingleChoice:
      return practiceLocalization.quiz;
    case QuestionType.MultipleChoice:
      return practiceLocalization.multiselect;
    case QuestionType.Essay:
      return practiceLocalization.shortanswer;
    case QuestionType.Matching:
      return practiceLocalization.matching;
    case QuestionType.Fillblank:
      return practiceLocalization.fillblanks;
    default:
      return '';
  }
};

// Map server-side status to client-side status
const mapQuestionStatus = (
  serverStatus: QuestionStatusEnum
): QuestionStatus => {
  switch (serverStatus) {
    case QuestionStatusEnum.Published:
      return QuestionStatus.Published;
    case QuestionStatusEnum.Draft:
    case QuestionStatusEnum.Submitted:
    case QuestionStatusEnum.Approved:
    case QuestionStatusEnum.Rejected:
    default:
      return QuestionStatus.Draft;
  }
};

// Helper function to process tags from a string or array
const processTags = (tags: string | string[] | undefined): string[] => {
  if (!tags) return [];
  if (Array.isArray(tags)) return tags;
  return tags
    .split(',')
    .map((tag) => tag.trim())
    .filter((tag) => tag !== '');
};

// Helper function to process topics from a string or array
const processTopics = (topics: string | string[] | undefined): string[] => {
  if (!topics) return [];
  if (Array.isArray(topics)) return topics;
  return topics
    .split(',')
    .map((topic) => topic.trim())
    .filter((topic) => topic !== '');
};

class QuestionDraftAdapter {
  /**
   * Convert server entity to React BaseQuestion
   */
  public toBaseQuestion(entity: QuestionDraft): BaseQuestion {
    const options = entity.options?.map(
      (option) =>
        ({
          id: option.id,
          content: option.content,
          text: option.content,
          description: option.content,
          isCorrect: option.isCorrect,
        } as BaseAnswer)
    );
    // Process tags and topics for the question
    const tagsArray = processTags(entity.tags);
    const topicsArray = processTopics(entity.topics);

    const baseQuestion: BaseQuestion = {
      id: entity.id || '',
      title: entity.description || '',
      description: entity.content || '',
      type: mapQuestionType(entity.type),
      points: entity.difficultyLevel,
      statusEntity: entity.status,
      tags: tagsArray,
      topics: topicsArray,
      subjectId: entity.subjectId,
      subject: entity.subject,
      answers: options,
      metadata: {
        source: entity.source,
        createdAt: entity.creationTime,
        updatedAt: entity.lastModificationTime,
        topics: topicsArray,
        contentFormat: entity.contentFormat,
        subjectId: entity.subjectId,
      },
    };

    // Handle specific question types
    switch (entity.type) {
      case QuestionType.SingleChoice:
      case QuestionType.MultipleChoice:
        return this.toQuizQuestion(entity, baseQuestion);
      case QuestionType.Matching:
        return this.toMatchingQuestion(entity, baseQuestion);
      case QuestionType.Fillblank:
        return this.toFillblankQuestion(entity, baseQuestion);
      case QuestionType.Essay:
        return this.toEssayQuestion(entity, baseQuestion);
      default:
        return baseQuestion;
    }
  }

  /**
   * Convert React BaseQuestion to server entity
   */
  public toQuestionDraftEntity(question: BaseQuestion): QuestionDraft {
    const entity: QuestionDraft = {
      id: question.id,
      content: question.description || '',
      contentFormat: question.metadata?.contentFormat || 'html',
      type: this.mapClientTypeToServerType(question.type),
      difficultyLevel: question.points || 1,
      description: question.title || '',
      topics: question.metadata?.topics?.join(','),
      tags: Array.isArray(question.tags)
        ? question.tags?.join(',')
        : question.tags,
      status: this.mapClientStatusToServerStatus(question.statusEntity),
      source: question.metadata?.source,
      subjectId: question.subjectId,
      options: [],
    };

    // Handle specific question types
    if (question.type === practiceLocalization.quiz) {
      entity.options = question.answers
        ? this.quizQuestionToOptions(
            (question as QuizQuestion).answers ?? [],
            question.id
          )
        : [];
    } else if (question.type === practiceLocalization.multiselect) {
      entity.options = this.multiselectQuestionToOptions(
        (question as MultiSelectQuestion).answers,
        question.id
      );
    }
    return entity;
  }

  /**
   * Convert a collection of server entities to React BaseQuestion[]
   */
  public toBaseQuestions(entities: QuestionDraft[]): BaseQuestion[] {
    return entities.map((entity) => this.toBaseQuestion(entity));
  }

  /**
   * Convert a collection of React BaseQuestion to server entities
   */
  public toQuestionDraftEntities(questions: BaseQuestion[]): QuestionDraft[] {
    return questions.map((question) => this.toQuestionDraftEntity(question));
  }

  // Private helper methods for specific question types
  private toQuizQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): QuizQuestion {
    const quizQuestion = baseQuestion as QuizQuestion;
    quizQuestion.question = entity.content;
    quizQuestion.options =
      entity.options?.map((option) => ({
        id: option.id,
        text: option.content,
        isCorrect: option.isCorrect,
        feedback: '',
        score: 1,
      })) || [];
    quizQuestion.allowMultiple = entity.type === QuestionType.MultipleChoice;

    return quizQuestion;
  }

  private toMatchingQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): MatchingQuestion {
    const matchingQuestion = baseQuestion as MatchingQuestion;

    // Process options to create left/right items for matching
    if (entity.options && entity.options.length > 0) {
      const leftItems: MatchingItemType[] = [];
      const rightItems: MatchingItemType[] = [];
      const matches: { left: string; right: string }[] = [];

      entity.options.forEach((option) => {
        const leftItem = {
          id: option.id,
          text: option.content,
          content: option.content,
          type: 'text',
          matchId: option.id,
        } as MatchingItemType;

        const rightItem = {
          id: option.id + '-match',
          text: '',
          content: '',
          type: 'text',
          matchId: option.id,
        } as MatchingItemType;

        leftItems.push(leftItem);
        rightItems.push(rightItem);

        matches.push({
          left: leftItem.id,
          right: rightItem.id,
        });
      });

      matchingQuestion.leftItems = leftItems;
      matchingQuestion.rightItems = rightItems;
      matchingQuestion.matches = matches;
    }

    return matchingQuestion;
  }

  private toFillblankQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): BaseQuestion {
    // Implement fill-in-the-blank specific logic here
    // This depends on how your fill-in-the-blank questions are structured
    return {
      ...baseQuestion,
      blanks: entity.options?.map((option) => ({
        id: option.id,
        answer: option.content,
        score: 1,
      })),
    };
  }

  private toEssayQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): BaseQuestion {
    // Implement essay specific logic here
    return {
      ...baseQuestion,
      modelAnswer: entity.options?.find((o) => o.isCorrect)?.content || '',
    };
  }

  private mapClientTypeToServerType(clientType: string): QuestionType {
    switch (clientType) {
      case practiceLocalization.quiz:
        return QuestionType.SingleChoice; // Default to single choice
      case practiceLocalization.shortanswer:
        return QuestionType.Essay;
      case practiceLocalization.matching:
        return QuestionType.Matching;
      case practiceLocalization.fillblanks:
        return QuestionType.Fillblank;
      case practiceLocalization.multiselect:
        return QuestionType.MultipleChoice;
      default:
        throw new Error(`Unsupported question type: ${clientType}`);
    }
  }

  private mapClientStatusToServerStatus(
    clientStatus?: QuestionStatus
  ): QuestionStatus {
    switch (clientStatus) {
      case QuestionStatus.Published:
        return QuestionStatus.Published;
      case QuestionStatus.Draft:
      default:
        return QuestionStatus.Draft;
    }
  }

  private quizQuestionToOptions(
    answers: QuizAnswer[],
    questionId?: string
  ): QuestionOptionDraft[] {
    return answers.map((answer, index) => ({
      id: answer.id,
      content: answer.text,
      isCorrect: answer.isCorrect,
      score: 1,
      feedback: '',
      questionDraftId: questionId || '',
      contentFormat: 'html',
      order: index,
      matchingContent: '',
    }));
  }

  private multiselectQuestionToOptions(
    answers: MultiSelectAnswer[],
    questionId?: string
  ): QuestionOptionDraft[] {
    return answers.map((answer, index) => ({
      id: answer.id,
      content: answer.text,
      isCorrect: answer.isCorrect,
      score: 1,
      feedback: '',
      questionDraftId: questionId || '',
      contentFormat: 'html',
      order: index,
      matchingContent: '',
    }));
  }
}

export default new QuestionDraftAdapter();

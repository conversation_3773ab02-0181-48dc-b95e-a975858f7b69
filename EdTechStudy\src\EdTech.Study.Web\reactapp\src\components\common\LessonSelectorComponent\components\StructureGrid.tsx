import React from 'react';
import { Empty, Typography, Collapse, theme } from 'antd';
import { IEdTechComponent } from '../../../../interfaces/AppComponents';
import ComponentCard from './ComponentCard';
import { ETypeEdTechComponent } from '../../../../enums/AppEnums';
import {
  CaretRightOutlined,
  BlockOutlined,
  LayoutOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface StructureGridProps {
  components: IEdTechComponent[];
  selectedComponents: IEdTechComponent[];
  onSelectComponent: (component: IEdTechComponent) => void;
}

/**
 * StructureGrid component displays structure elements divided into two categories:
 * 1. Structures: Components with type STRUCTURE that have a structure property
 * 2. Structure Components: Components with type STRUCTURE without structure property
 */
const StructureGrid: React.FC<StructureGridProps> = ({
  components,
  selectedComponents,
  onSelectComponent,
}) => {
  const { token } = theme.useToken();

  if (components.length === 0) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={
          <div className="tailwind-text-center tailwind-py-6">
            <Title
              level={5}
              className="tailwind-mt-2 tailwind-font-medium tailwind-text-gray-900"
            >
              Không tìm thấy cấu trúc phù hợp.
            </Title>
            <Text className="tailwind-text-gray-500">
              Thử tìm kiếm với từ khóa khác hoặc chọn danh mục khác
            </Text>
          </div>
        }
      />
    );
  }

  // Filter components into two categories
  const structureComponents = components.filter(
    (component) =>
      component.type === ETypeEdTechComponent.STRUCTURE && component.structure
  );

  const componentStructures = components.filter(
    (component) =>
      component.type === ETypeEdTechComponent.STRUCTURE && component.components
  );

  return (
    <div
      className="tailwind-mt-4 tailwind-overflow-y-auto"
      style={{
        maxHeight: 'calc(90vh - 350px)',
        scrollbarWidth: 'none',
        msOverflowStyle: 'none',
        WebkitOverflowScrolling: 'touch',
      }}
    >
      <style>
        {`
        .component-grid::-webkit-scrollbar {
          display: none;
        }
        .ant-collapse-header {
          align-items: center !important;
        }
        `}
      </style>

      <Collapse
        defaultActiveKey={['1', '2']}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined
            rotate={isActive ? 90 : 0}
            style={{ fontSize: '16px', color: token.colorPrimary }}
          />
        )}
        ghost
        className="tailwind-mb-4"
        items={[
          {
            key: '1',
            label: (
              <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                <BlockOutlined
                  className="tailwind-text-indigo-500"
                  style={{ fontSize: '18px' }}
                />
                <Title
                  level={5}
                  className="tailwind-text-gray-800 tailwind-mb-0"
                  style={{ margin: 0 }}
                >
                  Cấu trúc{' '}
                  <span className="tailwind-font-normal tailwind-text-sm tailwind-text-gray-500">
                    ({structureComponents.length})
                  </span>
                </Title>
              </div>
            ),
            children:
              structureComponents.length > 0 ? (
                <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-2 lg:tailwind-grid-cols-3 tailwind-gap-4 sm:tailwind-gap-6 component-grid tailwind-mt-4">
                  {structureComponents.map((component) => (
                    <div key={component.name} className="tailwind-relative">
                      <ComponentCard
                        component={component}
                        isSelected={selectedComponents.some(
                          (c) => c.name === component.name
                        )}
                        onSelect={onSelectComponent}
                      />
                    </div>
                  ))}
                </div>
              ) : null,
            extra: (
              <Text className="tailwind-text-gray-500 tailwind-text-sm tailwind-mr-3">
                Các cấu trúc bao gồm nhiều thành phần được định nghĩa sẵn
              </Text>
            ),
          },
          {
            key: '2',
            label: (
              <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                <LayoutOutlined
                  className="tailwind-text-blue-500"
                  style={{ fontSize: '18px' }}
                />
                <Title
                  level={5}
                  className="tailwind-text-gray-800 tailwind-mb-0"
                  style={{ margin: 0 }}
                >
                  Engine cấu trúc{' '}
                  <span className="tailwind-font-normal tailwind-text-sm tailwind-text-gray-500">
                    ({componentStructures.length})
                  </span>
                </Title>
              </div>
            ),
            children:
              componentStructures.length > 0 ? (
                <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-2 lg:tailwind-grid-cols-3 tailwind-gap-4 sm:tailwind-gap-6 component-grid tailwind-mt-4">
                  {componentStructures.map((component) => (
                    <div key={component.name} className="tailwind-relative">
                      <ComponentCard
                        component={component}
                        isSelected={selectedComponents.some(
                          (c) => c.name === component.name
                        )}
                        onSelect={onSelectComponent}
                      />
                    </div>
                  ))}
                </div>
              ) : null,
            extra: (
              <Text className="tailwind-text-gray-500 tailwind-text-sm tailwind-mr-3">
                Các engine có thể sử dụng để tạo cấu trúc riêng
              </Text>
            ),
          },
        ]}
      />
    </div>
  );
};

export default StructureGrid;

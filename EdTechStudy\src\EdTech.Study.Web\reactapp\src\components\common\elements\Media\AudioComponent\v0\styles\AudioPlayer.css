/* ================================
   AUDIO PLAYER COMPONENT STYLES
   ================================ */

/* Main container */
.edtech-audio-player-container {
  width: 100%;
  max-width: 100%;
}

.edtech-audio-player-card {
  width: 100%;
  border: 1px solid var(--edtt-color-border-default);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--edtt-shadow-sm);
  background-color: var(--edtt-color-bg-default);
}

.edtech-audio-player-wrapper {
  padding: 16px;
}

.edtech-audio-player {
  margin-top: 8px;
}

/* ================================
   H5 AUDIO PLAYER CUSTOMIZATION
   ================================ */

.pink-audio-player {
  box-shadow: none !important;
  background-color: transparent !important;
}

.pink-audio-player .rhap_progress-filled {
  background-color: var(--edtt-color-primary) !important;
  transition: all var(--edtt-transition-normal) !important;
}

.pink-audio-player .rhap_progress-indicator {
  background-color: var(--edtt-color-primary) !important;
  box-shadow: var(--edtt-shadow-focus) !important;
  transition: all var(--edtt-transition-fast) !important;
}

.pink-audio-player .rhap_volume-indicator {
  background-color: var(--edtt-color-primary) !important;
  transition: all var(--edtt-transition-fast) !important;
}

.pink-audio-player .rhap_button-clear {
  color: var(--edtt-color-text-default) !important;
  transition: all var(--edtt-transition-fast) !important;
}

.pink-audio-player .rhap_button-clear:hover {
  color: var(--edtt-color-primary) !important;
  transform: scale(1.05);
}

.pink-audio-player .rhap_time {
  font-size: 14px !important;
  color: var(--edtt-color-text-secondary) !important;
}

/* ================================
   AUDIO TRACK LIST
   ================================ */

.edtech-audio-tracklist {
  border-top: 1px solid var(--edtt-color-border-light);
  background-color: var(--edtt-color-bg-default);
}

.edtech-audio-track-item {
  position: relative;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px dashed var(--edtt-color-border-light);
  cursor: pointer;

  /* Smooth transitions for all properties */
  transition: all var(--edtt-transition-normal);

  /* Border-left animation preparation */
  border-left: 3px solid transparent;
}

/* Remove border from last item */
.edtech-audio-track-item:last-child {
  border-bottom: none;
}

/* Hover state */
.edtech-audio-track-item:hover {
  transform: translateX(2px);
}

/* Track info section */
.edtech-audio-track-info {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

/* Track icon */
.edtech-audio-track-icon {
  color: var(--edtt-color-primary);
  margin-right: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--edtt-transition-fast);
}

.edtech-audio-track-item .icon-play {
  display: none !important;
  transition: transform 1s ease-in-out;
}
.edtech-audio-track-item.active .icon-play,
.edtech-audio-track-item:hover .icon-play {
  display: block !important;
  transition: transform 1s ease-in-out;
}

/* Track name with smooth text color transition */
.edtech-audio-track-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
  transition: color var(--edtt-transition-normal);
}

.edtech-audio-track-item:hover .edtech-audio-track-name {
  color: var(--edtt-color-primary);
}

.edtech-audio-track-item.active .edtech-audio-track-name {
  color: var(--edtt-color-primary);
  font-weight: 600;
}

/* Track actions - Always visible */
.edtech-audio-track-actions {
  display: flex;
  gap: 8px;
  opacity: 1;
  transition: transform var(--edtt-transition-fast);
}

.edtech-audio-track-item:hover .edtech-audio-track-actions {
  transform: scale(1.02);
}

/* ================================
   AUDIO HEADER
   ================================ */

.edtech-audio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 15px;
}

.edtech-audio-filename {
  display: flex;
  align-items: center;
  gap: 8px;
}

.edtech-audio-filename-edit-button {
  color: var(--edtt-color-text-secondary);
  cursor: pointer;
  transition: all var(--edtt-transition-fast);
}

.edtech-audio-filename-edit-button:hover {
  color: var(--edtt-color-primary);
  transform: scale(1.1);
}

.edtech-audio-actions {
  display: flex;
  gap: 8px;
}

/* ================================
   PLAYBACK RATE SELECTOR
   ================================ */

.edtech-playback-rate-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--edtt-color-border-default);
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 14px;
  cursor: pointer;
  margin: 0 12px;
  color: var(--edtt-color-text-default);
  background-color: var(--edtt-color-bg-default);
  transition: all var(--edtt-transition-fast);
}

.edtech-playback-rate-selector:hover {
  border-color: var(--edtt-color-primary);
  color: var(--edtt-color-primary);
  box-shadow: var(--edtt-shadow-focus);
}

/* ================================
   RESPONSIVE DESIGN
   ================================ */

@media (max-width: 768px) {
  .edtech-audio-track-name {
    max-width: 200px;
  }

  .edtech-audio-track-item {
    padding: 10px 12px;
  }

  .edtech-audio-player-wrapper {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .edtech-audio-track-name {
    max-width: 150px;
  }
}

/* ================================
   ACCESSIBILITY & FOCUS STATES
   ================================ */

.edtech-audio-track-item:focus {
  outline: 2px solid var(--edtt-color-primary);
  outline-offset: 2px;
}

/* ================================
   LOADING & DISABLED STATES
   ================================ */

.edtech-audio-track-item.loading {
  opacity: 0.6;
  pointer-events: none;
}

.edtech-audio-track-item.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.edtech-audio-track-item.disabled:hover {
  transform: none;
  border-left-color: transparent;
  background-color: transparent;
}

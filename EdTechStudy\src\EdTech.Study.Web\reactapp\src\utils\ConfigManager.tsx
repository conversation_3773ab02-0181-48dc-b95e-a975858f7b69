import { storeEdTech } from '../store/store';
import { AppFunctions } from './AppFunctions';
import { message } from 'antd';

export const ConfigManager = {
  //hàm lấy id config từ params
  getIdConfigFromParams: () => {
    const urlParams = new URLSearchParams(window.location.search);
    const grade = urlParams.get('grade') || '0';
    const subject = urlParams.get('subject') || '0';
    const lesson = urlParams.get('lesson') || '0';
    return window.LessonConfigDB.createId(grade, subject, lesson);
  },

  // Hàm khởi tạo trang cấu hình bài học
  initConfigLessonPage: () => {
    // Tay id bài học từ params
    const idConfig = ConfigManager.getIdConfigFromParams();
    // Lấy cấu hình bài học từ database
    window.LessonConfigDB.getConfig(idConfig).then((response) => {
      let data = null;
      let isDefaultData = false;
      if (response) {
        data = response.config;
      } else {
        // Nếu không có cấu hình bài học thì dùng dữ liệu mặc định
        // TODO: Cần đọc từ db, api hoặc file json
        data = {
          ...window.LessonInitModule.getDefaultLessonData(),
          id: idConfig,
        };
        isDefaultData = true;
        // ConfigManager.saveConfigLessonPage(idConfig, data);
      }

      const result = AppFunctions.importDataToStore({
        jsonData: data as any,
        dispatch: storeEdTech.dispatch,
        showMessage: false,
        isDefaultData,
      });
      if (!result.isValid) {
        message.error('Dữ liệu cấu hình bài học không hợp lệ');
      }
    });
  },

  removeConfigLessonPage: () => {
    const idConfig = ConfigManager.getIdConfigFromParams();
    window.LessonConfigDB.deleteConfig(idConfig).then((response) => {
      if (response) {
        console.log('Xóa cấu hình bài học thành công', 'success');
      }
    });
  },

  // Hàm lưu cấu hình bài học vào database
  saveConfigLessonPage: async (id: string, data: any) => {
    await window.LessonConfigDB.saveConfig(id, data).then((response) => {
      if (response) {
        console.log('Lưu cấu hình bài học thành công', 'success');
      } else {
        console.log('Lưu cấu hình bài học thất bại', 'error');
      }
    });
  },
  // Hàm lưu edtech render tree data vào database
  saveEdTechRenderTreeData: async (id: string, data: any) => {
    await window.LessonConfigDB.updateEdTechRenderTreeData(id, data).then(
      (response) => {
        if (response) {
          console.log('Lưu cấu hình bài học thành công', 'success');
        } else {
          console.log('Lưu cấu hình bài học thất bại', 'error');
        }
      }
    );
  },
  // Hàm lưu ed component params vào database
  saveEdComponentParams: async (id: string, data: any) => {
    console.log('saveEdComponentParams', {
      id: id,
      data: JSON.stringify(data),
    });
    await window.LessonConfigDB.updateEdComponentParams(id, data).then(
      (response) => {
        if (response) {
          console.log('Lưu cấu hình bài học thành công', 'success');
        } else {
          console.log('Lưu cấu hình bài học thất bại', 'error');
        }
      }
    );
  },
};

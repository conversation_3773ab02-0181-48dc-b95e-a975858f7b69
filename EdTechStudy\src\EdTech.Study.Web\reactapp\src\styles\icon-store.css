.icon-store {
  max-width: 1200px;
  margin: 0 auto;
}

.icon-store__header {
  text-align: center;
}

.icon-store__header h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.icon-store__header p {
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.65);
}

.icon-demo .ant-card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-demo .ant-card {
  transition: all 0.3s;
}

.icon-demo .ant-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon-demo .ant-typography {
  margin-bottom: 0;
}

.icon-example .ant-card {
  height: 100%;
}

.icon-example pre {
  margin: 0;
}

/* Animation for spin effect */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

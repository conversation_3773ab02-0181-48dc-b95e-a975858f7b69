import ReactDOM from 'react-dom/client';
import IconStoreDemo from '../components/icons/IconStoreDemo';

export const IconStoreApp = () => {
  return (
    <div className="container">
      <IconStoreDemo />
    </div>
  );
};

// This is for standalone usage
const rootIconStore = document.getElementById('rootIconStore');
if (rootIconStore) {
  const root = ReactDOM.createRoot(rootIconStore);
  root.render(<IconStoreApp />);
}

export default IconStoreApp;

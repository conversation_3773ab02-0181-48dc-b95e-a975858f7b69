import React from 'react';
import { Input, Button } from 'antd';
import { useAudio } from '../context/AudioContext';
import { LinkOutlinedIcon } from '../../../../../../icons/IconRegister';

export const URLInputSection: React.FC = () => {
  const {
    tempUrl,
    setTempUrl,
    handleUrlSubmit,
    showDeleteAllConfirmation,
    config,
  } = useAudio();

  const hasAudios = config.medias && config.medias.length > 0;

  return (
    <div className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-between tailwind-gap-2 tailwind-mb-4">
      <div
        className="tailwind-flex tailwind-items-center tailwind-gap-2"
        style={{ width: '50%' }}
      >
        <Input
          placeholder="Nhập URL âm thanh (https://...)"
          value={tempUrl}
          onChange={(e) => setTempUrl(e.target.value)}
          prefix={<LinkOutlinedIcon />}
          style={{ width: '100%' }}
          onPressEnter={handleUrlSubmit}
        />
        <Button
          onClick={handleUrlSubmit}
          className="tailwind-flex-shrink-0"
          style={{
            backgroundColor: 'var(--edtt-color-primary)',
            borderColor: 'var(--edtt-color-primary)',
            color: 'var(--edtt-color-white)',
            borderRadius: '4px',
          }}
        >
          Thêm âm thanh
        </Button>
      </div>
      {hasAudios && (
        <Button
          danger
          onClick={showDeleteAllConfirmation}
          size="middle"
          style={{
            backgroundColor: 'var(--edtt-color-status-error)',
            color: 'var(--edtt-color-white)',
            border: 'none',
            borderRadius: '4px',
          }}
        >
          Xoá tất cả âm thanh
        </Button>
      )}
    </div>
  );
};

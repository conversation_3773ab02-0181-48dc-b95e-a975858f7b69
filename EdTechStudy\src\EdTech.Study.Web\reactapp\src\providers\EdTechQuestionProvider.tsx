import { Suspense } from 'react';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { IEdTechQuestionProviderProps } from './EdTechQuestionProvider.interfaces';
import QuestionRenderComponent from '../components/common/QuestionRenderComponent/QuestionRenderComponent';
import usePracticesLogic from '../components/quizs/practiceEngines/hooks/usePracticesLogic';

const EdTechQuestionProvider = (_: IEdTechQuestionProviderProps) => {
  usePracticesLogic();
  return (
    <Suspense fallback={<LoadingScreen />}>
      <QuestionRenderComponent />
    </Suspense>
  );
};

export default EdTechQuestionProvider;

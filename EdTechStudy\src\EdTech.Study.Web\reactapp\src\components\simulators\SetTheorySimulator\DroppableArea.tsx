import React from 'react';
import { useDrop } from 'react-dnd';
import { ItemTypes } from './DragTypes';
import { SetElement } from './SetTheoryConfig';

interface DroppableAreaProps {
  onDrop: (item: SetElement) => void;
  borderColor: string;
  children: React.ReactNode;
  className?: string;
}

const DroppableArea: React.FC<DroppableAreaProps> = ({
  onDrop,
  borderColor,
  children,
  className = '',
}) => {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: ItemTypes.ELEMENT,
    drop: (item: SetElement) => {
      onDrop(item);
      return { name: 'DroppableArea' };
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  }));

  const isActive = isOver && canDrop;
  
  return (
    <div
      ref={drop}
      className={`tailwind-min-h-24 tailwind-border-2 tailwind-border-dashed tailwind-rounded-md tailwind-p-2 tailwind-mb-2 tailwind-flex tailwind-flex-wrap tailwind-gap-2 ${className}`}
      style={{
        borderColor: isActive ? '#1890ff' : borderColor,
        backgroundColor: isActive ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
        transition: 'all 0.2s',
      }}
    >
      {children}
    </div>
  );
};

export default DroppableArea;

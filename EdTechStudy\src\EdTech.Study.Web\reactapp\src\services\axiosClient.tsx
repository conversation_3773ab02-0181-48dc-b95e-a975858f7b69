import axios from 'axios';
import { appConfig } from '../constants/AppContants';

// Hàm để lấy token từ localStorage hoặc nơi lưu trữ an toàn
const getAuthToken = () => {
  return localStorage.getItem('auth_token') || '';
};
// Kiểu token
export const TOKEN_TYPE = 'Bearer';

const axiosClient = axios.create(
  import.meta.env.DEV
    ? {
        baseURL: appConfig.baseURL,
        headers: {
          'Content-Type': 'application/json',
          // Sử dụng hàm để lấy token động
          Authorization: `${TOKEN_TYPE} ${getAuthToken()}`,
        },
        // Tăng giới hạn kích thước tệp lên 200MB
        maxContentLength: 209715200, // 200MB
        maxBodyLength: 209715200, // 200MB
      }
    : {
        headers: {
          'Content-Type': 'application/json',
        },
        // Tăng giới hạn kích thước tệp lên 200MB
        maxContentLength: 209715200, // 200MB
        maxBodyLength: 209715200, // 200MB
      }
);

axiosClient.interceptors.request.use(function (config) {
  config.headers['Content-Type'] =
    config.data instanceof FormData
      ? 'multipart/form-data'
      : 'application/json';
  return config;
});

axiosClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response.status >= 400) {
      return Promise.reject(error);
    }
  }
);
export { axiosClient };

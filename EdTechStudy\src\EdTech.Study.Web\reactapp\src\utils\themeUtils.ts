import { EThemeType } from '../enums/AppEnums';
import { ED_TECH_THEME_COLORS } from '../constants/themeConstants';
import { IAppThemeColors } from '../interfaces/ThemeAppInterface';

/**
 * Gets CSS variable value from the current theme
 * @param variableName - Name of the CSS variable (without --), e.g. 'primary-color'
 * @returns CSS variable value or empty string if not found
 */
export const getThemeVariable = (variableName: string): string => {
  return getComputedStyle(document.documentElement)
    .getPropertyValue(`--${variableName}`)
    .trim();
};

export const getDefaultThemeColors = (theme: EThemeType): IAppThemeColors => {
  return ED_TECH_THEME_COLORS[theme];
};

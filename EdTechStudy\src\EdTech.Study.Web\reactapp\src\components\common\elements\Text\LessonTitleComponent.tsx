import React, { useEffect } from 'react';
import { z } from 'zod';
import { IEdTechRenderProps } from '../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../hocs/withEdComponentParams/withEdComponentParams';
import CoreTitle, { ITextProps } from '../../../core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { EdTechRootState } from '../../../../store/store';
import { updateTitleAndSync } from '../../../../utils/titleSyncUtils';
import { createComponentSchema } from '../../../../utils/schema/createComponentSchema';
import { findNodeById } from '../../../../utils/treeUtils';
import EngineContainer from '../../engines/EngineContainer';

interface LessonTitleProps {
  titleProps?: ITextProps;
}

// Zod schema for component validation
export const lessonTitleSchema = createComponentSchema({
  paramsSchema: {
    text: z.string().optional(),
    fontSize: z.number().optional(),
    color: z.string().optional(),
    align: z.enum(['left', 'center', 'right']).optional(),
    bold: z.boolean().optional(),
  },
});

const LessonTitleComponent: React.FC<IEdTechRenderProps<LessonTitleProps>> = (
  props
) => {
  const { isEditing, addOrUpdateParamComponent, params } = props;

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Tìm node tương ứng trong tree data để theo dõi thay đổi title
  const currentNode = findNodeById(renderTreeData.subItems || [], props.id);

  // Theo dõi thay đổi title từ menu và cập nhật vào component
  useEffect(() => {
    if (currentNode && currentNode.title) {
      // Nếu params chưa tồn tại hoặc params.titleProps chưa tồn tại
      if (
        !params ||
        !params.titleProps ||
        params.titleProps.text !== currentNode.title
      ) {
        // Tạo mới hoặc cập nhật params
        const updatedParams = params || {};
        const titleProps = updatedParams.titleProps || {};

        addOrUpdateParamComponent({
          ...updatedParams,
          titleProps: {
            ...titleProps,
            text: currentNode.title,
          },
        });
      }
    }
  }, [currentNode?.title, params, addOrUpdateParamComponent, props.id]);

  const onUpdateText = (updates: Partial<ITextProps>) => {
    // Sử dụng hàm utility để cập nhật title và đồng bộ với menu
    updateTitleAndSync(
      props.id,
      updates,
      params,
      renderTreeData,
      dispatch,
      addOrUpdateParamComponent
    );
  };

  // Tạo default titleProps nếu params hoặc params.titleProps không tồn tại
  const defaultTitleProps = {
    text: currentNode?.title || 'Tiêu đề',
    fontSize: 24,
    align: 'left' as 'left' | 'center' | 'right',
  };

  // Create the CoreTitle component to use as mainComponent in EngineContainer
  const titleComponent = (
    <CoreTitle
      titleProps={params?.titleProps || defaultTitleProps}
      isEditing={isEditing}
      onUpdate={onUpdateText}
    />
  );

  return (
    <EngineContainer
      node={props}
      id={props.id}
      mode="NONE_WRAPPER"
      titleProps={undefined}
      mainComponent={titleComponent}
      showFullscreenButton={false}
      allowConfiguration={false}
      onTitleUpdate={onUpdateText}
    />
  );
};

export default withEdComponentParams(LessonTitleComponent);

.matching-description {
  margin-bottom: 20px;
  font-style: italic;
}

.matching-container {
  margin-bottom: 20px;
  position: relative; /* Important for connection lines */
  overflow: visible; /* Ensure connection lines aren't clipped */
  min-height: 200px; /* Provide enough space for connections */
}

.matching-column {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  min-height: 300px;
  position: relative; /* Needed for connection lines */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.matching-column h4 {
  margin-bottom: 15px;
  text-align: center;
  font-weight: bold;
  padding: 8px 0;
  background-color: #f0f0f0;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.matching-item {
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  z-index: 2; /* Ensure items appear above connection lines */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.matching-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.matching-text {
  flex: 1;
  word-break: break-word;
}

.matching-image {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.matching-image img {
  max-width: 100%;
  max-height: 120px;
  object-fit: contain;
}

/* New styles for click-to-match */
.matching-item.selected {
  border-width: 2px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
}

.matching-item.matched {
  border-color: #52c41a;
}

.matching-item.incorrect {
  border-color: #ff4d4f;
  background-color: #fff1f0;
}

.match-status-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 16px;
}

.match-correct {
  color: #52c41a;
  animation: correctIcon 0.5s ease;
}

.match-incorrect {
  color: #ff4d4f;
  animation: incorrectIcon 0.5s ease;
}

.matching-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.matching-feedback {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  animation: feedbackAnimation 0.5s ease;
}

.matching-feedback.success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.matching-feedback.warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  color: #faad14;
}

.matching-feedback.error {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
  color: #ff4d4f;
}

/* Connection lines container */
.connection-lines-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* Allow clicks to pass through to items */
  z-index: 1;
  overflow: visible; /* Ensure connections aren't clipped */
}

/* Individual connection line */
.connection-line {
  position: absolute;
  height: 3px;
  transform-origin: 0 0;
  z-index: 1;
  transition: all 0.3s ease;
  border-radius: 1.5px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

/* Animation for paired items */
@keyframes pairPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.matching-item.paired {
  animation: pairPulse 0.5s ease;
}

/* Success pulse animation */
.matching-success-pulse {
  animation: success-pulse 2s infinite;
}

@keyframes success-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* Animations */
@keyframes correctIcon {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes incorrectIcon {
  0% {
    transform: scale(0) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(-15deg);
  }
  50% {
    transform: scale(1.2) rotate(10deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

@keyframes feedbackAnimation {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Line animation */
@keyframes drawLine {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.connection-line.animate {
  animation: drawLine 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Configuration mode styles */
.matching-pairs-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.matching-pair-row {
  padding: 8px;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.matching-pair-row:hover {
  background-color: #f0f7ff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.matching-pair-inputs {
  align-items: center;
}

/* Enhanced connection indicator */
.connection-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 4px;
}

/* Add a subtle pulse animation when a match is made */
@keyframes matchPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 5px rgba(24, 144, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.matching-item.paired {
  animation: matchPulse 0.6s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .matching-container {
    overflow: visible;
    padding-bottom: 30px;
  }

  .matching-pair-inputs {
    flex-direction: column;
    gap: 8px;
  }

  .matching-pairs-config .ant-form-item {
    margin-bottom: 12px;
  }
}

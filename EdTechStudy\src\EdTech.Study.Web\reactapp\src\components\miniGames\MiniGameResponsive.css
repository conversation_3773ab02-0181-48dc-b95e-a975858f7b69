/* Common responsive styles for mini games */

/* Ensure mini game containers take full width of parent */
.mini-game-container {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

/* Responsive map heights */
.mini-game-map {
  height: 60vh !important;
  min-height: 400px !important;
  max-height: 600px !important;
  width: 100% !important;
}

/* Fullscreen mode map height */
.fullscreen .mini-game-map {
  height: calc(100vh - 200px) !important;
  max-height: none !important;
}

/* Normal mode map height adjustments */
@media (max-width: 1200px) {
  .mini-game-map:not(.fullscreen) {
    height: 50vh !important;
  }
}

@media (max-width: 768px) {
  .mini-game-map:not(.fullscreen) {
    height: 40vh !important;
    min-height: 300px !important;
  }
}

/* Card grid responsive adjustments */
.mini-game-card-grid {
  display: grid !important;
  gap: 12px !important;
  width: 100% !important;
}

/* Adjust grid columns based on container width */
@media (min-width: 1400px) {
  .mini-game-card-grid {
    grid-template-columns: repeat(5, 1fr) !important;
  }
}

@media (min-width: 1000px) and (max-width: 1399px) {
  .mini-game-card-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (min-width: 768px) and (max-width: 999px) {
  .mini-game-card-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (max-width: 767px) {
  .mini-game-card-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Responsive text sizes */
.mini-game-title {
  font-size: 1.5rem !important;
}

.mini-game-subtitle {
  font-size: 1.25rem !important;
}

.mini-game-text {
  font-size: 1rem !important;
}

@media (max-width: 768px) {
  .mini-game-title {
    font-size: 1.25rem !important;
  }

  .mini-game-subtitle {
    font-size: 1rem !important;
  }

  .mini-game-text {
    font-size: 0.875rem !important;
  }
}

/* Responsive controls */
.mini-game-controls {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
  justify-content: center !important;
  margin-top: 12px !important;
  margin-bottom: 12px !important;
}

/* Responsive prize ladder */
.mini-game-prize-ladder {
  max-height: 80vh !important;
  overflow-y: auto !important;
}

/* Responsive question modal */
.mini-game-question-modal .ant-modal-content {
  max-width: 100% !important;
  width: 100% !important;
}

@media (min-width: 768px) {
  .mini-game-question-modal .ant-modal-content {
    max-width: 90% !important;
    width: 90% !important;
    margin: 0 auto !important;
  }
}

/* Ensure splitter panels are responsive */
.mini-game-splitter .ant-splitter-panel {
  min-width: 200px !important;
}

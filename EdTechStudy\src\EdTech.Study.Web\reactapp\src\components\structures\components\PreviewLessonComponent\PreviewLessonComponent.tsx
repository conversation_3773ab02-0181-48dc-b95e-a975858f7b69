import React, { useRef, useState, useEffect } from 'react';
import { IEdTechRenderTreeData } from '../../../../interfaces/AppComponents';
import LessonRenderComponent from '../../../common/LessonRenderComponent/LessonRenderComponent';
import CarouselNavButton from './components/CarouselNavButton/CarouselNavButton';
import { ETypeMode } from '../../../../enums/AppEnums';
import { FullscreenContainer } from '../../../common/Fullscreen';
import { FullscreenContainerHandle } from '../../../common/Fullscreen/FullscreenContainer';
import { AppFunctions } from '../../../../utils/AppFunctions';
import { z } from 'zod';
import { createComponentSchema } from '../../../../utils/schema/createComponentSchema';
import FixedButtonsContainer from './components/FixedButtonsContainer';
import Carousel, { CarouselRef } from 'antd/es/carousel';
import { findParentContentItem } from '../LessonCommonComponent/utils/LessonCommonUtils';

export const previewLessonSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    content: z.string().optional(),
    showNavigation: z.boolean().optional(),
    showProgress: z.boolean().optional(),
  },
});

export const PreviewLessonComponent: React.FC<IEdTechRenderTreeData> = (
  props
) => {
  // Create ref for carousel
  const carouselRef = useRef<CarouselRef>(null);
  const fullscreenRef = useRef<FullscreenContainerHandle>(null);

  // Current slide index
  const [currentSlide, setCurrentSlide] = useState<number>(0);

  // Functions to navigate carousel
  const handlePrev = () => {
    if (carouselRef.current) {
      carouselRef.current.prev();
      setCurrentSlide(Math.max(0, currentSlide - 1));
    }
  };

  const handleNext = () => {
    if (carouselRef.current) {
      carouselRef.current.next();
      setCurrentSlide(
        Math.min((props.subItems?.length || 1) - 1, currentSlide + 1)
      );
    }
  };

  // Handle exit fullscreen/preview mode
  const handleExitPreview = () => {
    AppFunctions.redirectToModeApp({ mode: ETypeMode.CONFIGURATION });
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    fullscreenRef.current?.toggleFullscreen();
  };
  // Recursive function to find an item by ID in the nested structure

  // Handle menu item selection
  const handleMenuSelect = (key: string) => {
    const parentItem = findParentContentItem(key, props.subItems || []);

    // Tìm và chuyển đến slide của item cha (CONTENT)
    const parentIndex =
      props.subItems?.findIndex((item) => item.id === parentItem?.id) || 0;
    if (parentIndex >= 0 && carouselRef.current) {
      // Lưu trữ key để sử dụng sau khi carousel đã chuyển slide
      const targetKey = key;

      // Chuyển đến slide của item cha
      carouselRef.current.goTo(parentIndex);
      setCurrentSlide(parentIndex);

      // Sử dụng một sự kiện transitionend để phát hiện khi carousel đã hoàn thành việc chuyển slide
      const handleTransitionEnd = () => {
        // Tìm phần tử cần scroll đến
        const childElement = document.getElementById(targetKey);
        if (childElement) {
          // Scroll đến phần tử
          childElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }

        // Xóa event listener sau khi đã xử lý
        document.removeEventListener('transitionend', handleTransitionEnd);
      };

      // Thêm event listener để lắng nghe sự kiện transitionend
      document.addEventListener('transitionend', handleTransitionEnd);

      // Đặt một timeout dự phòng trong trường hợp sự kiện transitionend không được kích hoạt
      setTimeout(() => {
        // Xóa event listener nếu nó vẫn còn
        document.removeEventListener('transitionend', handleTransitionEnd);

        // Thực hiện scroll nếu chưa được thực hiện
        const childElement = document.getElementById(targetKey);
        if (childElement) {
          childElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 600); // Timeout dự phòng sau 600ms
    }
  };

  // Xử lý phím mũi tên để chuyển slide
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Chỉ xử lý khi không có input nào đang focus
      if (
        document.activeElement?.tagName.toLowerCase() !== 'input' &&
        document.activeElement?.tagName.toLowerCase() !== 'textarea'
      ) {
        if (event.key === 'ArrowLeft') {
          handlePrev();
        } else if (event.key === 'ArrowRight') {
          handleNext();
        }
      }
    };

    // Thêm event listener
    window.addEventListener('keydown', handleKeyDown);

    // Cleanup khi component unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSlide, handlePrev, handleNext]); // Thêm các hàm vào dependency array
  return (
    <FullscreenContainer ref={fullscreenRef}>
      {/* Main Content */}
      <div className="tailwind-relative">
        {/* Navigation buttons centered vertically */}
        <div className="tailwind-fixed tailwind-top-1/2 tailwind-left-0 tailwind-right-0 tailwind-transform -tailwind-translate-y-1/2 tailwind-flex tailwind-justify-between tailwind-px-5 tailwind-pointer-events-none tailwind-z-50">
          {/* Ẩn nút prev ở slide đầu tiên */}
          {currentSlide > 0 && (
            <CarouselNavButton type="prev" onClick={handlePrev} />
          )}
          {/* Ẩn nút next ở slide cuối cùng */}
          {currentSlide < (props.subItems?.length || 1) - 1 && (
            <CarouselNavButton type="next" onClick={handleNext} />
          )}
        </div>
        <Carousel
          ref={carouselRef}
          lazyLoad="ondemand"
          dots={false}
          autoplay={false}
          // arrows={false}
          adaptiveHeight={true}
          infinite={false}
          className="tailwind-rounded-lg tailwind-shadow-sm tailwind-bg-default"
        >
          {props.subItems?.map((item, index) => (
            <div
              key={`lesson-container-${item.id || index}`}
              className="tailwind-relative"
            >
              <LessonRenderComponent data={item} />
            </div>
          ))}
        </Carousel>
      </div>

      {/* Fixed buttons container component with sidebar */}
      <FixedButtonsContainer
        handleExitPreview={handleExitPreview}
        toggleFullscreen={toggleFullscreen}
        subItems={props.subItems}
        currentSlideItemId={props.subItems?.[currentSlide]?.id}
        onMenuSelect={handleMenuSelect}
      />
    </FullscreenContainer>
  );
};

export default PreviewLessonComponent;

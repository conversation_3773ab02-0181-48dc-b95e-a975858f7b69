import React from 'react';
import { useDrag } from 'react-dnd';
import { Button } from 'antd';
import { ItemTypes } from './DragTypes';
import { SetElement } from './SetTheoryConfig';
import { DeleteIcon } from '../../icons/IconIndex';

interface DraggableElementProps {
  element: SetElement;
  color: string;
  textColor?: string;
  onRemove?: (element: SetElement) => void;
}

const DraggableElement: React.FC<DraggableElementProps> = ({
  element,
  color,
  textColor = 'white',
  onRemove,
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.ELEMENT,
    item: element,
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className="tailwind-py-1 tailwind-px-3 tailwind-rounded-full tailwind-cursor-move tailwind-relative tailwind-flex tailwind-items-center"
      style={{
        backgroundColor: color,
        color: textColor,
        opacity: isDragging ? 0.5 : 1,
      }}
    >
      {element.value}
      {onRemove && (
        <Button
          type="text"
          size="small"
          className="tailwind-ml-1 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-text-white hover:tailwind-text-red-200"
          icon={<DeleteIcon />}
          onClick={() => onRemove(element)}
        />
      )}
    </div>
  );
};

export default DraggableElement;

/**
 * IconExports.ts
 *
 * This file exports all icons from IconRegister.tsx with proper typing.
 * It provides a consistent way to import icons throughout the application.
 */

import { SVGProps } from 'react';
import * as RegisteredIcons from './IconRegister';

// Re-export all icons from IconRegister
export * from './IconRegister';

// Define the IconComponent type with SVG props
export type IconComponent = (props: SVGProps<SVGSVGElement>) => JSX.Element;

// Create a map of all available icons for reference
export const IconMap: Record<string, IconComponent> = { ...RegisteredIcons };

// Helper function to get an icon by name
export const getIconByName = (name: string): IconComponent | undefined => {
  return IconMap[name];
};

// Helper function to get all icon names
export const getAllIconNames = (): string[] => {
  return Object.keys(IconMap);
};

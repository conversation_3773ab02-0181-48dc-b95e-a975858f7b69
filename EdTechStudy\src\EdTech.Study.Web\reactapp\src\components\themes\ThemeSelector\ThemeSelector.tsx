import React from 'react';
import { <PERSON><PERSON>, <PERSON>ltip, Dropdown, Space } from 'antd';
import type { MenuProps } from 'antd';
import { useTheme } from '../../../providers/theme';
import { EThemeType } from '../../../enums/AppEnums';
import {
  BulbOutlined,
  BulbFilled,
  BookOutlined,
  BookFilled,
  MoonOutlined,
  MoonFilled,
} from '@ant-design/icons';

interface ThemeOptionConfig {
  label: string;
  icon: React.ReactNode;
  activeIcon: React.ReactNode;
  description: string;
}

interface ThemeSelectorProps {
  showCustomizer?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  // showCustomizer = true,
  className,
  style,
}) => {
  const { theme, setTheme, availableThemes } = useTheme();

  // Cấu hình cho mỗi theme với các icon của Ant Design
  const themeConfig: Record<string, ThemeOptionConfig> = {
    [EThemeType.DEFAULT]: {
      label: 'Mặc định',
      icon: <BulbOutlined className="tailwind-text-lg" />,
      activeIcon: <BulbFilled className="tailwind-text-lg" />,
      description: 'Giao diện mặc định',
    },
    [EThemeType.DARK]: {
      label: 'Tối',
      icon: <MoonOutlined className="tailwind-text-lg" />,
      activeIcon: <MoonFilled className="tailwind-text-lg" />,
      description: 'Giao diện nền tối',
    },
    [EThemeType.LEARNING]: {
      label: 'Học tập',
      icon: <BookOutlined className="tailwind-text-lg" />,
      activeIcon: <BookFilled className="tailwind-text-lg" />,
      description: 'Giao diện học tập',
    },
  };

  // Xử lý khi thay đổi theme
  const handleThemeChange = (value: string) => {
    setTheme(value as EThemeType);
  };

  // Create menu items for dropdown using MenuProps
  const menuItems: MenuProps['items'] = availableThemes.map((themeName) => ({
    key: themeName,
    label: (
      <Space>
        <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-rounded-full">
          {themeName === theme
            ? themeConfig[themeName].activeIcon
            : themeConfig[themeName].icon}
        </span>
        <span className="tailwind-ml-2 tailwind-font-medium">
          {themeConfig[themeName].label}
        </span>
      </Space>
    ),
    className: 'tailwind-hover:tailwind-bg-gray-100 tailwind-transition-colors',
  }));

  // Get current theme active icon
  const currentThemeIcon =
    themeConfig[theme]?.activeIcon ||
    themeConfig[EThemeType.DEFAULT].activeIcon;

  // Get tooltip text showing current theme name
  const tooltipText = `Giao diện: ${themeConfig[theme]?.label || 'Mặc định'}`;

  return (
    <div className={className} style={style}>
      <Space>
        <Dropdown
          menu={{
            items: menuItems,
            selectedKeys: [theme],
            onClick: ({ key }) => handleThemeChange(key as string),
          }}
          trigger={['click']}
          overlayClassName="tailwind-z-50"
        >
          <Tooltip title={tooltipText} placement="left">
            <Button
              className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-transition-all tailwind-duration-300 tailwind-ease-in-out hover:tailwind-scale-110 active:tailwind-scale-95 tailwind-shadow-md hover:tailwind-shadow-lg focus:tailwind-outline-none"
              type="primary"
              shape="circle"
              icon={currentThemeIcon}
            />
          </Tooltip>
        </Dropdown>
        {/* {showCustomizer && <ThemeCustomizer position="right" />} */}
      </Space>
    </div>
  );
};

export default ThemeSelector;

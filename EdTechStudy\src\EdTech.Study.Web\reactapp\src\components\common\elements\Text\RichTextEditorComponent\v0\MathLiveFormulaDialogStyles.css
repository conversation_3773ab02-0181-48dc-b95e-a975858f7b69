/* MathLiveFormulaDialogStyles.css */
/* Ant Design Modal Customization */
.mathlive-formula-dialog .ant-modal-content {
  border-radius: 8px;
  overflow: visible; /* Changed from hidden to visible to allow virtual keyboard to show */
  position: relative;
  z-index: 1000; /* Ensure modal is above other elements */
  isolation: isolate; /* Create a new stacking context */
  transition: all 0.3s ease; /* Smooth transition for position changes */
}

/* Ensure the modal adjusts properly when keyboard is shown */
.mathlive-formula-dialog .ant-modal {
  transition: top 0.3s ease, max-height 0.3s ease;
}

/* Ensure the modal mask prevents focus events from reaching elements behind it */
.mathlive-formula-dialog .ant-modal-mask {
  pointer-events: all !important;
}

/* Prevent focus from leaving the modal */
.mathlive-formula-dialog .ant-modal-wrap {
  outline: none !important;
}

/* Ensure the modal content captures all events */
.mathlive-formula-dialog .ant-modal-content * {
  pointer-events: auto !important;
}

.mathlive-formula-dialog .ant-modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 24px;
}

.mathlive-formula-dialog .ant-modal-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.mathlive-formula-dialog .ant-modal-body {
  padding: 20px 24px;
  overflow: visible; /* Allow content to overflow for virtual keyboard */
  min-height: 400px; /* Ensure enough space for the content */
  transition: padding-bottom 0.3s ease; /* Smooth transition for padding changes */
}

/* Style for the keyboard toggle switch */
.mathlive-formula-dialog .ant-switch {
  margin-left: 8px;
  min-width: 120px;
}

.mathlive-formula-dialog .ant-switch-checked {
  background-color: #1890ff;
}

.mathlive-formula-dialog .ant-modal-footer {
  border-top: 1px solid #e0e0e0;
  padding: 12px 24px;
}

/* MathLive Field Container */
.math-field-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background-color: #fff;
  min-height: 120px;
  transition: all 0.3s;
  position: relative; /* Added for proper positioning */
  display: flex; /* Added to center the math-field */
  align-items: center; /* Center vertically */
  justify-content: flex-start; /* Align to the left */
  margin-bottom: 20px; /* Add space below for virtual keyboard */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05); /* Subtle inset shadow */
}

.math-field-container:hover {
  border-color: #40a9ff;
}

.math-field-container:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* MathLive Field Styling */
math-field {
  width: 100%;
  min-height: 100px;
  font-size: 18px;
  padding: 8px;
  --mathfield-font-size: 18px;
  --mathfield-text-font-family: 'Arial', sans-serif;
  --mathfield-keyboard-toggle-size: 24px;
  --mathfield-keyboard-toggle-color: #1890ff;
  --mathfield-keyboard-toggle-active-color: #096dd9;
  --mathfield-caret-color: #1890ff;
  --mathfield-selection-color: rgba(24, 144, 255, 0.2);
  --mathfield-selection-background-color: rgba(24, 144, 255, 0.1);
  outline: none; /* Remove default outline */
}

/* Formula Buttons Container */
.formula-buttons-container {
  padding: 8px 4px 8px 0;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Formula Tabs */
.formula-tabs .ant-tabs-nav {
  margin-bottom: 12px;
}

.formula-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-weight: 500;
}

.formula-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff;
  font-weight: 600;
}

.formula-tabs .ant-tabs-ink-bar {
  background-color: #1890ff;
}

/* Formula Button Styles */
/* Wrapper to ensure clicks are captured */
.formula-button-wrapper {
  width: 100%;
  height: 90px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.formula-button-wrapper:hover {
  transform: translateY(-2px);
}

.formula-button-wrapper:active {
  transform: translateY(0);
}

.formula-button {
  width: 100% !important;
  height: 100% !important;
  padding: 8px !important;
  text-align: center !important;
  border: 1px solid #e0e0e0 !important;
  background-color: #f9f9f9 !important;
  transition: all 0.3s !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  overflow: hidden !important;
  margin: 0 !important;
}

.formula-button-wrapper:hover .formula-button {
  border-color: #1890ff !important;
  background-color: #e6f7ff !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15) !important;
}

.formula-button-wrapper:active .formula-button {
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.1) !important;
}

.formula-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.formula-button-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 4px;
  font-size: 18px;
  overflow: hidden;
  max-width: 100%;
  position: relative; /* For positioning the overlay */
}

/* Transparent overlay to capture clicks */
.formula-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 20; /* Above the math-field */
  cursor: pointer;
  background-color: transparent;
}

.formula-button-preview math-field {
  min-height: 40px;
  height: 40px;
  font-size: 16px;
  --mathfield-font-size: 16px;
  cursor: pointer;
  position: relative;
  z-index: 10; /* Below the overlay (z-index: 20) */
  user-select: none; /* Prevent text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: none; /* Disable pointer events so clicks go through to the overlay */
}

.formula-button-name {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  font-weight: 500;
}

/* MathJax formula styles in editor and view mode */
.e-content .mathjax-formula,
.rich-text-content .mathjax-formula {
  display: inline-block !important;
  margin: 0 4px !important;
  padding: 2px 4px !important;
  background-color: transparent !important;
  border-radius: 2px !important;
  vertical-align: middle !important;
  text-align: center !important;
  box-sizing: border-box !important;
  max-width: 100% !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
  line-height: normal !important;
  font-size: inherit !important;
  border: 1px solid transparent !important;
}

/* Hover effect for better visibility when editing */
.e-content .mathjax-formula:hover {
  background-color: rgba(24, 144, 255, 0.1) !important;
  border: 1px dashed rgba(24, 144, 255, 0.7) !important;
  border-radius: 4px !important;
  cursor: pointer !important;
}

/* Make formulas selectable as a single unit */
.e-content .mathjax-formula {
  user-select: all !important;
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  -ms-user-select: all !important;
  cursor: pointer !important;
}

/* MathLive Virtual Keyboard Styling */
.ML__keyboard {
  z-index: 2000 !important; /* Ensure keyboard appears above other elements */
  max-height: 280px !important; /* Reduced height to prevent obscuring content */
  height: 35vh !important; /* Set height relative to viewport height */
  overflow-y: auto !important; /* Allow scrolling if needed */
  border: 1px solid #d9d9d9 !important; /* Add border */
  border-radius: 4px 4px 0 0 !important; /* Rounded corners only at top */
  position: fixed !important; /* Fix position on screen */
  top: auto !important;
  bottom: 0 !important; /* Position at bottom of screen */
  left: 0 !important; /* Start from left edge */
  width: 100% !important; /* Full width */
  background-color: #f5f5f5 !important; /* Light background */
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15) !important; /* Add shadow at top */
  transition: height 0.3s ease !important; /* Smooth height transition */
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .mathlive-formula-dialog .ant-modal-body {
    padding: 16px;
  }

  .formula-button-wrapper {
    height: 80px;
  }

  .formula-button {
    height: 100% !important;
  }

  .formula-button-preview math-field {
    min-height: 30px;
    height: 30px;
    font-size: 14px;
    --mathfield-font-size: 14px;
    pointer-events: none; /* Ensure this is applied in responsive mode too */
  }

  /* Ensure overlay works in responsive mode */
  .formula-button-overlay {
    z-index: 20;
  }

  .formula-button-name {
    font-size: 11px;
  }

  /* Adjust keyboard for mobile */
  .ML__keyboard {
    max-height: 180px !important; /* Smaller height on mobile */
    height: 30vh !important; /* Smaller relative height on mobile */
    width: 100% !important;
    bottom: 0 !important;
    left: 0 !important;
    font-size: 14px !important; /* Smaller font size on mobile */
  }

  /* Add a handle for better visibility on mobile */
  .ML__keyboard:before {
    content: '';
    display: block !important;
    width: 40px !important;
    height: 4px !important;
    background-color: #ccc !important;
    border-radius: 2px !important;
    margin: 4px auto !important;
  }
}

import React from 'react';
import { Input, Button, Space } from 'antd';
import { Edit, Trash2, Download } from './IconAdapter';
import { getFileNameWithoutExtension } from '../../../shared/utils';
import { useAudio } from '../context/AudioContext';

export const AudioHeader: React.FC = () => {
  const {
    currentAudio,
    isEditing,
    editingAudioName,
    selectedIndex,
    editingAudioNameValue,
    setEditingAudioNameValue,
    handleSaveAudioName,
    handleEditAudioName,
    handleDownloadAudio,
    showDeleteConfirmation,
  } = useAudio();

  if (!currentAudio) return null;

  return (
    <div className="edtech-audio-header">
      <div className="edtech-audio-filename">
        {editingAudioName === selectedIndex ? (
          <Space>
            <Input
              value={editingAudioNameValue}
              onChange={(e) => setEditingAudioNameValue(e.target.value)}
              size="small"
              autoFocus
              onPressEnter={handleSaveAudioName}
              style={{ maxWidth: 300 }}
            />
            <Button
              type="text"
              size="small"
              icon={<Edit size={16} />}
              onClick={handleSaveAudioName}
            />
          </Space>
        ) : (
          <>
            <span className="edtech-audio-filename-text">
              {getFileNameWithoutExtension(
                currentAudio.name || 'Tên âm thanh.mp3'
              )}
            </span>
            {isEditing && (
              <Edit
                size={16}
                className="edtech-audio-filename-edit-button"
                onClick={() => handleEditAudioName(selectedIndex)}
                style={{ color: 'var(--edtt-color-text-secondary)' }}
              />
            )}
          </>
        )}
      </div>

      <Space className="edtech-audio-actions">
        <Button
          type="text"
          icon={<Download size={18} />}
          onClick={handleDownloadAudio}
          className="edtech-audio-download-button tailwind-border tailwind-border-default"
          style={{ color: 'var(--edtt-color-text-secondary)' }}
        />
        {isEditing && (
          <Button
            type="text"
            danger
            icon={<Trash2 size={18} />}
            onClick={() => showDeleteConfirmation(selectedIndex)}
            className="edtech-audio-delete-button tailwind-border tailwind-border-red-600"
            style={{ color: 'var(--edtt-color-status-error)' }}
          />
        )}
      </Space>
    </div>
  );
};

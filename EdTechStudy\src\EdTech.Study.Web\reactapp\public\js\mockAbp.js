if (!window.abp) {
  window.abp = {};
}

window.abp.localization = {
  localize: function (key, sourceName, args) {
    // Nếu không có key hoặc sourceName, trả về key
    if (!key || !sourceName) {
      return key;
    }

    // Lấy resource từ source
    const resource = this.resources[sourceName];
    if (!resource) {
      console.warn(`Resource source not found: ${sourceName}`);
      return key;
    }

    // Lấy text từ resource
    const text = resource.texts[key];
    if (!text) {
      console.warn(`Translation not found for key: ${key}`);
      return key;
    }

    // Nếu có args, thay thế các placeholder
    if (args) {
      return text.replace(/\{(\d+)\}/g, (match, index) => {
        return args[index] !== undefined ? args[index] : match;
      });
    }

    return text;
  },
  resources: {
    EdTechStudy: {
      texts: {
        MyAccount: 'Tài khoản của tôi',
        SamplePageMessage: 'Trang mẫu cho mô-đun Study',
        'Menu:Root Menu': 'Danh mục',
        'Menu:ClassSix': 'Lớp 6',
        'Menu:ClassTen': 'Lớp 10',
        'Menu:NaturalScience': 'Môn Khoa học tự nhiên',
        'Menu:ClassSix_NaturalScience_SolarSystem': 'Hệ mặt trời',
        'Menu:Math': 'Môn toán',
        'Menu:Physics': 'Môn vật lý',
        'Menu:Geography': 'Môn Địa lý',
        'Menu:ClassSix_Math_LogicalProposition': 'Bài 1: Mệnh đề',
        'Menu:ClassTen_Math_Sets': 'Bài 2: Tập hợp',
        'Menu:ClassTen_Math_LinearInequality':
          'Bài 3: Phương trình bậc nhất hai ẩn',
        'Menu:ClassTen_Math_LinearInequalitySystem':
          'Bài 4: Hệ phương trình bậc nhất hai ẩn',
        'Menu:ClassTen_Math_Trigonometric':
          'Bài 5: Giá trị lượng giác của một góc từ 0° đến 180° ',
        'Menu:ClassTen_Physics_PhysicsIntroduction':
          'Bài 1: Làm quen với vật lý',
        'Menu:ClassTen_Geography_MapSymbols': 'Biểu diễn bản đồ',
        'Menu:ClassTen_Geography_MapReading': 'Đọc và sử dụng bản đồ số',
        'Menu:ClassSix_Geography_GeographicCoordinateSystem':
          'Hệ thống kinh, vĩ tuyến, toạ độ địa lý',
        'Menu:ClassSix_Geography_GeographicGrid':
          'Bản đồ. Một số lưới kinh, vĩ tuyến. Phương hướng trên bản đồ',
        'Menu:ClassSix_Geography_MapScale':
          'Tỉ lệ bản đồ. Tính khoảng cách thực tế dựa vào tỉ lệ bản đồ',
        'Menu:ClassSix_Math_Collection': 'Bài 1: Lý thuyết tập hợp',
        'Menu:ClassSix_Math_SetNaturalNumbers':
          'Bài 2: Tập hợp số tự nhiên. Ghi số tự nhiên',
        'Permission:ClassTen_Math': 'Toán lớp 10',
        'Permission:ClassSix_Geography': 'Địa lý lớp 6',
        'Permission:ClassTen_Geography': 'Địa lý lớp 10',
        'Category:GRADE_6': 'Lớp 6',
        'Category:GRADE_7': 'Lớp 7',
        'Category:GRADE_8': 'Lớp 8',
        'Category:GRADE_9': 'Lớp 9',
        'Category:GRADE_10': 'Lớp 10',
        'Category:GRADE_11': 'Lớp 11',
        'Category:GRADE_12': 'Lớp 12',
        'Category:MATHEMATICS': 'Toán học',
        'Category:PHYSICS': 'Vật lý',
        'Category:CHEMISTRY': 'Hóa học',
        'Category:BIOLOGY': 'Sinh học',
        'Category:GEOGRAPHY': 'Địa lý',
        'Category:HISTORY': 'Lịch sử',
        'Category:LITERATURE': 'Văn học',
        'Category:ENGLISH': 'Tiếng Anh',
        'Category:DEMO': 'Demo',
        'Category:LAYOUT': 'Bố cục',
        'Category:SIMULATORS': 'Mô phỏng',
        'Category:MINI_GAMES': 'Trò chơi mini',
        'Category:QUIZ': 'Câu đố',
        'Category:INTERACTIVE': 'Tương tác',
        'Category:THEORY': 'Lý thuyết',
        'Category:PRACTICE': 'Thực hành',
        'Category:ASSESSMENT': 'Đánh giá',
        'Category:VISUALIZATION': 'Trực quan hóa',
        'Category:EXPERIMENT': 'Thí nghiệm',
        'Category:PROJECT': 'Dự án',
        'Category:GROUP_WORK': 'Làm việc nhóm',
        'Category:INDIVIDUAL': 'Cá nhân',
        'Category:BEGINNER': 'Sơ cấp',
        'Category:INTERMEDIATE': 'Trung cấp',
        'Category:ADVANCED': 'Nâng cao',
        'Category:PROBLEM_SOLVING': 'Giải quyết vấn đề',
        'Category:CRITICAL_THINKING': 'Tư duy phản biện',
        'Category:CREATIVE_THINKING': 'Tư duy sáng tạo',
        'Category:MEMORIZATION': 'Ghi nhớ',
        'Category:ANALYSIS': 'Phân tích',
        'Category:SYNTHESIS': 'Tổng hợp',
        'Category:EVALUATION': 'Đánh giá',
        'Category:SPATIAL_REASONING': 'Tư duy không gian',
        'Category:DRAG_AND_DROP': 'Kéo và thả',
        'Category:MULTIPLE_CHOICE': 'Nhiều lựa chọn',
        'Category:FILL_IN_BLANK': 'Điền vào chỗ trống',
        'Category:MATCHING': 'Nối',
        'Category:SIMULATION': 'Mô phỏng',
        'Category:GAMIFICATION': 'Trò chơi hóa',
        'Category:CONCEPT_LEARNING': 'Học khái niệm',
        'Category:SKILL_DEVELOPMENT': 'Phát triển kỹ năng',
        'Category:KNOWLEDGE_REVIEW': 'Ôn tập kiến thức',
        'Category:PERFORMANCE_ASSESSMENT': 'Đánh giá hiệu suất',
        'Category:ENGAGEMENT': 'Tham gia',
        'Category:MOTIVATION': 'Động lực',
        'Category:VISUAL': 'Trực quan',
        'Category:AUDIO': 'Âm thanh',
        'Category:TEXT_BASED': 'Dựa trên văn bản',
        'Category:HANDS_ON': 'Thực hành',
        'Category:REAL_WORLD': 'Thực tế',
        'Category:ABSTRACT': 'Trừu tượng',
        'Type:DEMO': 'Demo',
        'Type:COMMON': 'Thường',
        'Type:LAYOUT': 'Bố cục',
        'Type:SIMULATORS': 'Mô phỏng',
        'Type:MINI_GAMES': 'Trò chơi mini',
        'Type:QUIZ': 'Câu đố',
        'Type:CONTENT': 'Nội dung',
        'Type:STRUCTURE': 'Cấu trúc mặc định',
      },
      baseResources: ['AbpValidation', 'AbpUi'],
    },
  },
};
var LessonInitModule = new (function () {
  let self = this;
  this.defaultLessonData = {};
  this.setDefaultLessonData = (data) => {
    self.defaultLessonData = data;
    const interval = setInterval(() => {
      if (window.initReactApp) {
        clearInterval(interval);
        window.initReactApp();
      }
    }, 100);
  };
  this.getDefaultLessonData = () => {
    return self.defaultLessonData;
  };
})();

LessonInitModule.setDefaultLessonData({
  edComponentParams: [],
  edTechRenderTreeData: {
    id: 'ZvmKFQu7',
    name: 'LessonCommonComponent',
    title: 'Bài học',
    type: 'STRUCTURE',
    order: 0,
    version: '1.0.0',
    path: 'ZvmKFQu7',
    subItems: [
      {
        id: '5XoExsMt',
        name: 'LessonCardComponent',
        title: 'Mục Tiêu Bài Học',
        type: 'CONTENT',
        order: 0,
        path: 'ZvmKFQu7/5XoExsMt',
        version: '1.0.0',
      },
      {
        id: 'TgEAhlLz',
        name: 'LessonCardComponent',
        title: 'Nội Dung Bài Học',
        type: 'CONTENT',
        order: 1,
        path: 'ZvmKFQu7/TgEAhlLz',
        subItems: [],
        version: '1.0.0',
      },
      {
        id: 'ADKTJf8x',
        name: 'LessonCardComponent',
        title: 'Trò Chơi Bài Học',
        type: 'CONTENT',
        order: 2,
        path: 'ZvmKFQu7/ADKTJf8x',
        subItems: [],
        version: '1.0.0',
      },
      {
        id: 'QQiNaJC2',
        name: 'LessonCardComponent',
        title: 'Bài Kiểm Tra Nhỏ',
        type: 'CONTENT',
        order: 3,
        path: 'ZvmKFQu7/QQiNaJC2',
        version: '1.0.0',
      },
    ],
  },
});

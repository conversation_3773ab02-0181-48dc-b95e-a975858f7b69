import { axiosClient } from '../services/axiosClient';

class FileApi {
  public async Upload(formData: FormData, config?: any) {
    return axiosClient({
      method: 'post',
      url: `/api/edtech-files/`,
      data: formData,
      ...config
    });
  }

  public async UploadMultiple(formData: FormData, config?: any) {
    return axiosClient({
      method: 'post',
      url: `/api/edtech-files/multiple`,
      data: formData,
      ...config
    });
  }

  public async Remove(imageUrl: string) {
    return axiosClient({
      method: 'delete',
      url: '/api/edtech-files?fileName=' + imageUrl,
    });
  }

  public async GetFile(imageUrl: string) {
    return axiosClient({
      method: 'get',
      url: '/api/edtech-files/' + imageUrl,
    });
  }
}

export default new FileApi();

import React, { useState, useEffect } from 'react';
import LoadingScreen from './Loading/LoadingScreen';

interface DynamicImportWrapperProps {
  importFn: () => Promise<any>;
  componentProps: any;
  fallback?: React.ReactNode;
}

/**
 * Wrapper component để xử lý dynamic imports một cách an toàn
 * thay vì sử dụng React.lazy trực tiếp
 */
const DynamicImportWrapper: React.FC<DynamicImportWrapperProps> = ({
  importFn,
  componentProps,
  fallback = <LoadingScreen />,
}) => {
  const [Component, setComponent] = useState<React.ComponentType<any> | null>(
    null
  );
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const loadComponent = async () => {
      try {
        const module = await importFn();

        // Hỗ trợ cả hai loại: default export và named exports
        const ImportedComponent = module.default || module;

        if (isMounted) {
          setComponent(() => ImportedComponent);
        }
      } catch (err) {
        console.error('Failed to load dynamic component:', err);
        if (isMounted) {
          setError(err as Error);
        }
      }
    };

    loadComponent();

    return () => {
      isMounted = false;
    };
  }, [importFn]);

  if (error) {
    return (
      <div
        className="error-container"
        style={{
          padding: '16px',
          margin: '8px',
          background: '#fff1f0',
          border: '1px solid #ffa39e',
          borderRadius: '2px',
        }}
      >
        <h4 style={{ color: '#cf1322', margin: '0 0 8px' }}>
          Lỗi Tải Component
        </h4>
        <p style={{ margin: 0 }}>{error.message}</p>
      </div>
    );
  }

  if (!Component) {
    return <>{fallback}</>;
  }

  // Render component an toàn
  return <Component {...componentProps} />;
};

export default DynamicImportWrapper;

// MapSimulatorConfig.ts
import { ITextProps } from '../../../components/core/title/CoreTitle';

export interface MapMarker {
  id: string;
  name: string;
  description: string;
  latitude: number;
  longitude: number;
  iconUrl?: string;
  visible: boolean;
}

export interface MapPolygon {
  id: string;
  name: string;
  description: string;
  coordinates: Array<[number, number]>;
  fillColor: string;
  strokeColor: string;
  fillOpacity: number;
  visible: boolean;
}

export interface MapSimulatorConfig {
  titleProps: ITextProps;
  description: string;
  initialCenter: { lat: number; lng: number };
  initialZoom: number;
  minZoom: number;
  maxZoom: number;

  interactiveOptions: {
    scrollWheelZoom: boolean;
    dragging: boolean;
    touchZoom: boolean;
    doubleClickZoom: boolean;
    boxZoom: boolean;
    keyboard: boolean;
    inertia: boolean;
  };

  // Thay layers bằng viewModes
  viewModes: {
    standard: boolean;
    satellite: boolean;
    terrain: boolean;
    hybrid: boolean;
  };

  markers: MapMarker[];
  polygons: MapPolygon[];

  controls: {
    showZoomControl: boolean;
    showScaleControl: boolean;
    showLayerControl: boolean;
    showMeasurementTools: boolean;
    showSearchBox: boolean;
    showCompassControl: boolean;
    showRoutingControl: boolean;
    showMeasureControl: boolean;
    showSearchControl: boolean;
    searchControlPosition?:
      | 'topleft'
      | 'topright'
      | 'bottomleft'
      | 'bottomright';
    compassControlPosition?:
      | 'topleft'
      | 'topright'
      | 'bottomleft'
      | 'bottomright';
    measureControlPosition?:
      | 'topleft'
      | 'topright'
      | 'bottomleft'
      | 'bottomright';
    routingControlPosition?:
      | 'topleft'
      | 'topright'
      | 'bottomleft'
      | 'bottomright';
    layerControlPosition?:
      | 'topleft'
      | 'topright'
      | 'bottomleft'
      | 'bottomright';
  };

  specialFeatures: {
    enableTimeline: boolean;
    enableComparison: boolean;
    enable3DTerrain: boolean;
    enableHeatmap: boolean;
  };
}

export const defaultMapSimulatorConfig: MapSimulatorConfig = {
  titleProps: {
    text: 'Bản đồ nền Việt Nam',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  description:
    'Bản đồ mô phỏng địa lý Việt Nam với các điểm đánh dấu quan trọng',
  initialCenter: { lat: 16.0, lng: 106.0 },
  initialZoom: 5,
  minZoom: 3,
  maxZoom: 18,

  interactiveOptions: {
    scrollWheelZoom: true,
    dragging: true,
    touchZoom: true,
    doubleClickZoom: true,
    boxZoom: true,
    keyboard: true,
    inertia: true,
  },

  // Luôn bật tất cả các chế độ xem
  viewModes: {
    standard: true,
    satellite: true,
    terrain: true,
    hybrid: true,
  },

  markers: [
    {
      id: 'marker-hanoi',
      name: 'Hà Nội',
      description: 'Thủ đô của Việt Nam',
      latitude: 21.0285,
      longitude: 105.8542,
      visible: true,
    },
    {
      id: 'marker-hcmc',
      name: 'Thành phố Hồ Chí Minh',
      description: 'Thành phố lớn nhất Việt Nam',
      latitude: 10.8231,
      longitude: 106.6297,
      visible: true,
    },
    {
      id: 'marker-danang',
      name: 'Đà Nẵng',
      description: 'Thành phố lớn ở miền Trung Việt Nam',
      latitude: 16.0544,
      longitude: 108.2022,
      visible: true,
    },
  ],

  polygons: [],

  controls: {
    showZoomControl: true,
    showScaleControl: true,
    showLayerControl: true,
    showMeasurementTools: false,
    showSearchBox: true,
    showCompassControl: true,
    showRoutingControl: false,
    showMeasureControl: false,
    showSearchControl: true,
    searchControlPosition: 'topleft',
    compassControlPosition: 'topright',
    measureControlPosition: 'topleft',
    routingControlPosition: 'topleft',
    layerControlPosition: 'topright',
  },

  specialFeatures: {
    enableTimeline: false,
    enableComparison: false,
    enable3DTerrain: false,
    enableHeatmap: false,
  },
};

/* Resizable divider styles */
.resizable-divider {
  width: 8px;
  background-color: #f0f0f0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%; /* Default position at center */
  transform: translateX(-50%); /* Center the divider on its position */
  cursor: col-resize;
  transition: background-color 0.2s ease;
  z-index: 10;
}

.resizable-divider:hover,
.resizable-divider.active {
  background-color: #1890ff;
}

.resizable-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 30px;
  background-color: #fff;
  border-radius: 1px;
}

/* Responsive styles for resizable divider */
@media (max-width: 768px) {
  .resizable-divider {
    display: none;
  }
}

import { Guid } from 'guid-typescript';
import {
  QuizQuestion,
  MatchingQuestion,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';
import { FillBlanksQuestion } from '../../../interfaces/quizs/fillblanks.interfaces';
import { createMultiSelectQuestionTemplate } from '../multiselect';
import { createShortAnswerQuestionTemplate } from '../shortanswer';
export { createMultiSelectQuestionTemplate };

export const createQuizQuestionTemplate = (
  title: string = 'Câu hỏi trắc nghiệm'
): QuizQuestion => {
  const id = Guid.create().toString();
  return {
    id,
    type: 'quiz',
    title,
    question: 'Nhập nội dung câu hỏi ở đây',
    answers: [
      {
        id: `${id}A`,
        text: 'Phương án A',
        isCorrect: true,
      },
      {
        id: `${id}B`,
        text: 'Phương án B',
        isCorrect: false,
      },
      {
        id: `${id}C`,
        text: 'Phương án C',
        isCorrect: false,
      },
      {
        id: `${id}D`,
        text: 'Phương án D',
        isCorrect: false,
      },
    ],
    explanation: 'Thêm giải thích đáp án',
    points: 1,
  };
};

export const createMatchingQuestionTemplate = (
  title: string = 'Câu hỏi nối đáp án'
): MatchingQuestion => {
  const id = Guid.create().toString();
  return {
    id,
    type: 'matching',
    title,
    description: 'Nối các mục từ cột A với các mục tương ứng trong cột B',
    leftItems: [
      {
        id: `${id}L1`,
        content: 'Mục 1',
        type: 'text',
        matchId: `${id}R1`,
      },
      {
        id: `${id}L2`,
        content: 'Mục 2',
        type: 'text',
        matchId: `${id}R2`,
      },
      {
        id: `${id}L3`,
        content: 'Mục 3',
        type: 'text',
        matchId: `${id}R3`,
      },
    ],
    rightItems: [
      {
        id: `${id}R1`,
        content: 'Mục 4',
        type: 'text',
        matchId: `${id}L1`,
      },
      {
        id: `${id}R2`,
        content: 'Mục 5',
        type: 'text',
        matchId: `${id}L2`,
      },
      {
        id: `${id}R3`,
        content: 'Mục 6',
        type: 'text',
        matchId: `${id}L3`,
      },
    ],
    shuffleItems: true,
    answers: [
      {
        id: Guid.create().toString(),
        left: `${id}L1`,
        right: `${id}R1`,
        isCorrect: true,
      },
      {
        id: Guid.create().toString(),
        left: `${id}L2`,
        right: `${id}R2`,
        isCorrect: true,
      },
      {
        id: Guid.create().toString(),
        left: `${id}L3`,
        right: `${id}R3`,
        isCorrect: true,
      },
    ],
  };
};

export const createFillBlanksQuestionTemplate = (
  title: string = 'Câu hỏi điền từ'
): FillBlanksQuestion => {
  const id = Guid.create().toString();
  return {
    id,
    type: 'fillblanks',
    title,
    question: 'Hà Nội là ____ của Việt Nam.',
    blanks: [
      {
        id: `${id}B1`,
        correctAnswer: 'thủ đô',
        alternativeAnswers: ['Thủ đô'],
        hint: 'Trung tâm chính trị của đất nước',
      },
    ],
    explanation: 'Hà Nội là thủ đô của Việt Nam.',
    caseSensitive: false,
    showHints: true,
    points: 1,
  };
};

export { createShortAnswerQuestionTemplate };

export class QuestionTemplateFactory {
  private static instance: QuestionTemplateFactory;
  private templateMaps: Record<string, () => BaseQuestion>;

  private constructor() {
    this.templateMaps = {};
  }

  static getInstance(): QuestionTemplateFactory {
    if (!QuestionTemplateFactory.instance) {
      QuestionTemplateFactory.instance = new QuestionTemplateFactory();
    }
    return QuestionTemplateFactory.instance;
  }

  register(type: string, callback: () => BaseQuestion) {
    this.templateMaps[type] = callback;
  }

  create(type: string): BaseQuestion | null {
    const creation = this.templateMaps[type];
    return creation ? creation() : null;
  }
}

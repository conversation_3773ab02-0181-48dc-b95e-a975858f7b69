import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Space, Typography } from 'antd';
import LessonImportExport from '../../structures/components/LessonCommonComponent/components/LessonImportExport/LessonImportExport';
import { useSelector } from 'react-redux';
import { EdTechRootState } from '../../../store/store';
import { ETypeMode } from '../../../enums/AppEnums';

interface ILessonConfigWrapperProps {
  children: React.ReactNode;
}

const LessonConfigWrapper = (props: ILessonConfigWrapperProps) => {
  const { children } = props;
  const mode = useSelector((state: EdTechRootState) => state.appConfig.mode);
  return (
    <div className="tailwind-flex tailwind-flex-col tailwind-w-full tailwind-h-screen tailwind-bg-default tailwind-overflow-hidden">
      {/* Header navigation - fixed at top */}
      <div className="tailwind-flex tailwind-items-center tailwind-justify-between tailwind-p-4 tailwind-border-b tailwind-border-gray-200 tailwind-z-10">
        <div className="tailwind-flex tailwind-items-center">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            className="tailwind-mr-2 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-text-default"
            onClick={window.LessonInitModule.backToPreviousPage}
          />
          <Typography.Title
            level={3}
            className="tailwind-m-0 tailwind-flex tailwind-items-center !tailwind-my-0 !tailwind-text-default"
            style={{ lineHeight: 1 }}
          >
            {mode === ETypeMode.CONFIGURATION
              ? 'Cấu hình bài giảng:' + window.LessonInitModule.title || ''
              : window.LessonInitModule.title || ''}
          </Typography.Title>
        </div>
        {mode === ETypeMode.CONFIGURATION && (
          <Space size="small">
            <LessonImportExport />
          </Space>
        )}
      </div>

      {/* Main content - scrollable */}
      <div className="tailwind-flex-1 tailwind-overflow-y-auto tailwind-p-4">
        {children}
      </div>
    </div>
  );
};

export default LessonConfigWrapper;

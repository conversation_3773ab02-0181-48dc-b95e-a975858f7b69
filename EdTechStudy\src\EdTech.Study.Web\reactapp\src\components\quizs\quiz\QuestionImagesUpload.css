.question-images-preview {
  margin-top: 16px;
}

.image-preview-item {
  position: relative;
  margin: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
}

.image-preview-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.delete-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.3s;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px;
}

.image-preview-item:hover .delete-image-btn {
  opacity: 1;
}

.ant-upload-list-picture-card-container {
  width: 100px;
  height: 100px;
}

.ant-image-preview-mask {
  background-color: rgba(0, 0, 0, 0.45);
}

.ant-image-preview-img {
  max-width: 90vw;
  max-height: 90vh;
}

.question-images-display {
  margin: 16px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
}

.question-images-display img {
  cursor: pointer;
}

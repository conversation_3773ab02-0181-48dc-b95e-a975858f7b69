import { Guid } from 'guid-typescript';
import { MultiSelectQuestion } from '../../../interfaces/quizs/multiSelectQuiz.interface';

export const createMultiSelectQuestionTemplate = (
  title: string = 'Câu hỏi nhiều lựa chọn'
): MultiSelectQuestion => {
  const id = Guid.create().toString();
  return {
    id,
    type: 'multiselect',
    title,
    question: 'Chọn tất cả các đáp án đúng',
    answers: [
      {
        id: `${id}-A`,
        text: 'Đáp án A',
        isCorrect: true,
      },
      {
        id: `${id}-B`,
        text: 'Đáp án B',
        isCorrect: true,
      },
      {
        id: `${id}-C`,
        text: 'Đáp án C',
        isCorrect: false,
      },
      {
        id: `${id}-D`,
        text: 'Đáp án D',
        isCorrect: false,
      },
    ],
    explanation: 'Đáp án đúng là A và B vì...',
    points: 1,
  };
};

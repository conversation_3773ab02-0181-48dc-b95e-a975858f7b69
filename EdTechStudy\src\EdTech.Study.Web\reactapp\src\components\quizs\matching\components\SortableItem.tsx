// import React from 'react';
// import { useSortable } from '@dnd-kit/sortable';
// import { CSS } from '@dnd-kit/utilities';
// import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
// import { SortableItemProps } from '../../../../interfaces/quizs/mapping.interface';

// // Optimized sortable item component
// const SortableItem: React.FC<SortableItemProps> = ({
//   id,
//   item,
//   isMatched,
//   isIncorrect,
// }) => {
//   const {
//     attributes,
//     listeners,
//     setNodeRef,
//     transform,
//     transition,
//     isDragging,
//   } = useSortable({
//     id,
//     // Add data for easy access during drag
//     data: { item },
//   });

//   const style = {
//     transform: CSS.Transform.toString(transform),
//     transition,
//     opacity: isDragging ? 0.5 : 1,
//     zIndex: isDragging ? 100 : 1,
//   };

//   return (
//     <div
//       ref={setNodeRef}
//       style={style}
//       {...attributes}
//       {...listeners}
//       className={`matching-item ${isMatched ? 'matched' : ''} ${
//         isIncorrect ? 'incorrect' : ''
//       } ${isDragging ? 'dragging' : ''} ${
//         isMatched ? 'matching-success-pulse' : ''
//       }`}
//     >
//       {item.type === 'text' ? (
//         <div className="matching-text">{item.content}</div>
//       ) : (
//         <div className="matching-image">
//           {typeof item.content === 'string' ? (
//             <img src={item.content} alt="Matching item" loading="lazy" />
//           ) : (
//             item.content
//           )}
//         </div>
//       )}
//       {isMatched && (
//         <CheckOutlined className="match-status-icon match-correct" />
//       )}
//       {isIncorrect && (
//         <CloseOutlined className="match-status-icon match-incorrect" />
//       )}
//     </div>
//   );
// };

// export default SortableItem;

export default () => {
  return <></>;
};

import { ED_TECH_COMPONENT } from '../constants/edTechComponents';
import {
  IEdTechRenderParam,
  IEdTechRenderTreeData,
} from '../interfaces/AppComponents';
import { message } from 'antd';
import { DEFAULT_LOCALIZATION_SOURCE } from '../constants/AppContants';

import { EdTechRenderTreeDataSlice } from '../store/slices/AppSlices/EdTechRenderDataSlice';
import { Dispatch } from '@reduxjs/toolkit';
import { EdComponentParamsSlice } from '../store/slices/AppSlices/EdComponentParamsSlice';
import { setId } from '../store/slices/utils/createTreeDataSlice';
import { ConfigManager } from './ConfigManager';
import { ETypeMode } from '../enums/AppEnums';
import { validateData, ValidationResult } from './validation/validateSchema';
import { isEmpty } from '@tsp/utils';
import { AppConfigSlice } from '../store/slices/AppConfig/AppConfigSlice';

/**
 * Hàm đọc file thành chuỗi text
 * @param file - File cần đọc
 * @returns Promise<string | ArrayBuffer> - Nội dung file
 */
const readFile = (file: File): Promise<string | ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result || '');
    reader.onerror = () => reject(new Error('Không thể đọc file'));
    reader.readAsText(file);
  });
};

export const AppFunctions = {
  /**
   * Lấy component render tương ứng với tên và trả về fallback nếu không tìm thấy
   * @param param0 Đối tượng chứa thông tin component cần render
   * @returns Component React tương ứng hoặc fallback
   */
  getComponentRender: ({
    name,
    version,
  }: {
    name: string;
    version?: string;
  }) => {
    const result = ED_TECH_COMPONENT.find((x) => x.name === name);
    if (result) {
      if (version) {
        return result.components?.find((x) => x.version === version);
      }
      if (!result.components || result.components.length === 0) {
        return undefined;
      }
      return result.components[result.components.length - 1];
    }
    return undefined;
  },

  /**
   * Lấy ra danh sách version của component hiện tại
   * @param param0 Đối tượng chứa thông tin component cần lấy danh sách version
   * @returns Danh sách version của component hoặc undefined
   */
  getComponentVersions: ({
    name,
    version,
  }: {
    name: string;
    version?: string;
  }) => {
    const result = ED_TECH_COMPONENT.find((x) => x.name === name);
    if (result) {
      if (version) {
        // Return the specific version if found, wrapped in an array to maintain consistent return type
        const specificVersion = result.components?.find(
          (x) => x.version === version
        );
        return specificVersion ? [specificVersion] : [];
      }
      // Return all versions
      return result.components;
    }
    return undefined;
  },

  /**
   * Lấy bản dịch cho một khóa từ tài nguyên cụ thể
   * @param key - Khóa cần dịch
   * @param sourceName - Tên tài nguyên (mặc định là DEFAULT_LOCALIZATION_SOURCE)
   * @returns Bản dịch hoặc khóa gốc nếu không tìm thấy
   */
  getLocalizedText: (
    key: string,
    sourceName: string = DEFAULT_LOCALIZATION_SOURCE
  ) => {
    if ((window as any).abp?.localization) {
      return (window as any).abp?.localization.localize(key, sourceName);
    }
    return key;
  },

  /**
   * Hàm import dữ liệu vào Redux store
   * @param jsonData - Dữ liệu JSON cần import
   * @param dispatch - Dispatch function của Redux
   * @param showMessage - Có hiển thị thông báo hay không
   * @param messageContent - Nội dung thông báo tùy chỉnh
   * @param isDefaultData - Dữ liệu mặc định hay không
   * @returns ValidationResult - Kết quả validation và danh sách lỗi
   */
  importDataToStore: ({
    jsonData,
    dispatch,
    showMessage = true,
    isDefaultData = false,
    messageContent,
  }: {
    jsonData: any;
    dispatch: Dispatch<any>;
    showMessage?: boolean;
    isDefaultData?: boolean;
    messageContent?: string;
  }): ValidationResult => {
    const { setLoading, setError, setParams } = EdComponentParamsSlice.actions;

    try {
      dispatch(setLoading(true));

      // Validate dữ liệu
      const validationResult = validateData(jsonData);
      // nếu không có jsonData.edTechRenderTreeData thì có logic lấy rootlesson rồi
      if (
        !isEmpty(jsonData.edTechRenderTreeData) &&
        !validationResult.isValid
      ) {
        return validationResult;
      }

      // Cập nhật dữ liệu vào Redux store
      dispatch(setParams(jsonData.edComponentParams));
      dispatch(
        EdTechRenderTreeDataSlice.actions.initData(
          jsonData.edTechRenderTreeData
        )
      );

      if (jsonData.id) {
        if (
          typeof jsonData.id !== 'string' &&
          typeof jsonData.id !== 'number'
        ) {
          throw new Error('ID phải là chuỗi hoặc số');
        }
        // Update ID using the setId action
        dispatch(setId(jsonData.id));
      }
      if (showMessage) {
        if (messageContent) {
          message.success(`${messageContent}`);
        } else {
          message.success('Dữ liệu đã được import thành công');
        }
      }

      dispatch(AppConfigSlice.actions.setIsDefaultData(isDefaultData));

      return validationResult;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Lỗi không xác định';
      dispatch(setError(`Lỗi import: ${errorMessage}`));

      return {
        isValid: false,
        errors: [
          {
            componentName: 'System',
            componentId: 'Unknown',
            path: 'Unknown',
            errorMessage: `Lỗi import: ${errorMessage}`,
            errorCode: 'IMPORT_ERROR',
          },
        ],
      };
    } finally {
      dispatch(setLoading(false));
    }
  },

  /**
   * Hàm import dữ liệu từ file JSON vào Redux store
   * @param file - File JSON cần import
   * @param dispatch - Dispatch function của Redux
   * @returns Promise<ValidationResult> - Kết quả validation và danh sách lỗi
   */
  importFromEdTechRenderFile: async (
    file: File,
    dispatch: Dispatch
  ): Promise<ValidationResult> => {
    const { setLoading, setError } = EdComponentParamsSlice.actions;
    try {
      dispatch(setLoading(true));

      // Đọc và parse file JSON
      const fileData = await readFile(file);
      const jsonData = JSON.parse(fileData as string);

      return AppFunctions.importDataToStore({
        jsonData,
        dispatch,
        messageContent: `Import dữ liệu từ file ${file.name} thành công`,
      });
    } catch (error) {
      console.error('Import error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Lỗi không xác định';
      dispatch(setError(`Lỗi import: ${errorMessage}`));
      return {
        isValid: false,
        errors: [
          {
            componentName: 'System',
            componentId: 'Unknown',
            path: 'Unknown',
            errorMessage: `Lỗi import: ${errorMessage}`,
            errorCode: 'IMPORT_ERROR',
          },
        ],
      };
    } finally {
      dispatch(setLoading(false));
    }
  },

  /**
   * Hàm xuất dữ liệu từ Redux store thành file JSON
   * @param params - Dữ liệu từ EdComponentParamsSlice
   * @param renderTreeData - Dữ liệu từ EdTechRenderDataSlice
   * @param fileName - Tên file xuất (mặc định là edtech_data_export_<ngày>.json)
   * @returns boolean - true nếu xuất thành công, false nếu thất bại
   */
  exportToFileEdTechRenderData: async (fileName?: string): Promise<boolean> => {
    try {
      // Kết hợp dữ liệu từ hai slice
      const exportData = await AppFunctions.getCurrentJsonData();

      // Chuyển đổi dữ liệu thành chuỗi JSON
      const jsonString = JSON.stringify(exportData, null, 2);

      // Tạo Blob và tạo URL tải xuống
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Tạo phần tử a để tải xuống
      const link = document.createElement('a');
      link.href = url;
      link.download =
        fileName ||
        `edtech_data_export_${new Date().toISOString().split('T')[0]}.json`;

      // Thêm vào DOM, click và xóa
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success('Xuất dữ liệu thành công!');
      return true;
    } catch (error) {
      console.error('Export error:', error);
      message.error(`Xuất dữ liệu thất bại: ${(error as Error).message}`);
      return false;
    }
  },

  /**
   * Hàm lấy dữ liệu mặc định từ file JSON
   * @param dispatch - Dispatch function của Redux
   */
  /**
   * Hàm lấy dữ liệu mặc định từ file JSON
   * @param dispatch - Dispatch function của Redux
   */
  resetDefaultDataFromJsonFile: async (dispatch: Dispatch) => {
    try {
      const idConfig = ConfigManager.getIdConfigFromParams();
      const dataDefault = window.LessonInitModule.getDefaultLessonData();
      window.LessonConfigDB.deleteConfig(idConfig);

      // Cập nhật dữ liệu mặc định vào store
      AppFunctions.importDataToStore({
        jsonData: {
          ...dataDefault,
          id: idConfig,
        } as any,
        dispatch,
        messageContent: 'Đặt lại dữ liệu mặc định thành công',
      });

      // Cập nhật trạng thái isDefaultData là true
      dispatch(AppConfigSlice.actions.setIsDefaultData(true));

      return true; // Trả về kết quả thành công
    } catch (error) {
      console.error('Error resetting default data:', error);
      message.error(
        `Đặt lại dữ liệu mặc định thất bại: ${(error as Error).message}`
      );
      return false; // Trả về kết quả thất bại
    }
  },

  /**
   * Chuyển hướng đến chế độ ứng dụng tương ứng
   * @param mode - Chế độ cần chuyển đến
   */
  redirectToModeApp: ({ mode }: { mode: ETypeMode }) => {
    switch (mode) {
      case ETypeMode.PRESENT:
        window.location.href = window.location.href.replace(
          'demolessonpage',
          'previewlessonpage'
        );
        break;
      case ETypeMode.CONFIGURATION:
        window.location.href = window.location.href.replace(
          'previewlessonpage',
          'demolessonpage'
        );
        break;
      case ETypeMode.DEFAULT:
        window.location.href = '/';
        break;
      default:
        break;
    }
  },
  /**
   * Hàm lấy dữ liệu mặc định từ file JSON
   * @param dispatch - Dispatch function của Redux
   */
  getCurrentJsonData: async () => {
    //to do: kiểm tra trong indexed db xem có cấu hình nào được lưu không, nếu có thì trả về cấu hình đã được lưu
    const idConfig = ConfigManager.getIdConfigFromParams();
    const data = await window.LessonConfigDB.getConfig(idConfig);
    if (data) {
      return data.config;
    }

    // Nếu không có thì lấy cấu hình mặc định
    return window.LessonInitModule.getDefaultLessonData();
  },
};

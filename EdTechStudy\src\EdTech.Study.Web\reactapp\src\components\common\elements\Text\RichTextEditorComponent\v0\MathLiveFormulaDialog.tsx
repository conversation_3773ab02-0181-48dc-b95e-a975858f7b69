import React, { useState, useEffect, memo, useRef } from 'react';
import {
  Modal,
  Button,
  Alert,
  Space,
  Typography,
  Tooltip,
  Row,
  Col,
  Tabs,
} from 'antd';

import 'mathlive';
import './MathLiveFormulaDialogStyles.css';

import { commonFormulas } from './commonFormulas.ts';
import { ModalAntdCustom } from '../../../../../customs/antd/ModalAntdCustom.tsx';

const { Text } = Typography;
const { TabPane } = Tabs;

// Define the MathField element for TypeScript
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'math-field': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          value?: string;
          'virtual-keyboard-mode'?: string;
          'virtual-keyboard-theme'?: string;
          'letter-shape-style'?: string;
          'math-mode-space'?: string;
        },
        HTMLElement
      >;
    }
  }
}

interface MathLiveFormulaDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (formula: string) => void;
  initialFormula?: string;
}

const MathLiveFormulaDialog: React.FC<MathLiveFormulaDialogProps> = ({
  isOpen,
  onClose,
  onInsert,
  initialFormula = '',
}) => {
  // State for the formula input
  const [formula, setFormula] = useState<string>(initialFormula);
  // State for error message
  const [errorMessage, setErrorMessage] = useState<string>('');
  // State for virtual keyboard toggle
  const [virtualKeyboardEnabled, setVirtualKeyboardEnabled] =
    useState<boolean>(false);
  // Reference to the math-field element
  const mathFieldRef = useRef<HTMLElement>(null);

  // Effect to initialize the formula and focus the input when the dialog opens
  useEffect(() => {
    if (isOpen) {
      console.log('Dialog opened, initializing with formula:', initialFormula);
      // Initialize formula state
      setFormula(initialFormula || '');

      // Prevent any outside elements from getting focus
      const preventFocusOut = (e: FocusEvent) => {
        if (isOpen && mathFieldRef.current && !e.defaultPrevented) {
          // If focus is moving outside the modal, prevent it
          const modalElement = document.querySelector(
            '.mathlive-formula-dialog'
          );
          if (modalElement && !modalElement.contains(e.target as Node)) {
            e.preventDefault();
            e.stopPropagation();
            mathFieldRef.current.focus();
          }
        }
      };

      // Add event listener to capture focus events
      document.addEventListener('focusin', preventFocusOut as any);

      // Wait for the component to fully render before focusing
      const focusTimer = setTimeout(() => {
        if (mathFieldRef.current) {
          (mathFieldRef.current as any).value = initialFormula || '';

          // Focus the math field and ensure it's ready for input
          mathFieldRef.current.focus();

          // Manually trigger an input event to ensure the formula state is updated
          const inputEvent = new Event('input', { bubbles: true });
          mathFieldRef.current.dispatchEvent(inputEvent);

          // Handle virtual keyboard based on toggle state
          if (
            typeof (mathFieldRef.current as any).executeCommand === 'function'
          ) {
            if (virtualKeyboardEnabled) {
              (mathFieldRef.current as any).executeCommand(
                'showVirtualKeyboard'
              );
            } else {
              (mathFieldRef.current as any).executeCommand(
                'hideVirtualKeyboard'
              );
            }
          }

          // Force a re-render of the math field to ensure it's properly initialized
          if (typeof (mathFieldRef.current as any).render === 'function') {
            (mathFieldRef.current as any).render();
          }
        }
      }, 500); // Increased timeout to ensure modal is fully rendered

      // Cleanup function
      return () => {
        document.removeEventListener('focusin', preventFocusOut as any);
        clearTimeout(focusTimer);
      };
    }
  }, [isOpen, initialFormula, virtualKeyboardEnabled]);

  // Handle virtual keyboard toggle
  useEffect(() => {
    if (
      mathFieldRef.current &&
      typeof (mathFieldRef.current as any).executeCommand === 'function'
    ) {
      if (virtualKeyboardEnabled) {
        (mathFieldRef.current as any).executeCommand('showVirtualKeyboard');

        // Add padding to the bottom of the modal body when keyboard is shown
        const modalBody = document.querySelector(
          '.mathlive-formula-dialog .ant-modal-body'
        );
        if (modalBody) {
          // Calculate appropriate padding based on viewport height
          const viewportHeight = window.innerHeight;
          const keyboardHeight = Math.min(280, viewportHeight * 0.35); // Match CSS values
          const paddingBottom = `${keyboardHeight + 40}px`; // Add extra padding
          (modalBody as HTMLElement).style.paddingBottom = paddingBottom;

          // Also adjust the modal position to ensure it's visible
          const modalContent = document.querySelector(
            '.mathlive-formula-dialog .ant-modal'
          );
          if (modalContent) {
            (modalContent as HTMLElement).style.top = '5%';
            (
              modalContent as HTMLElement
            ).style.maxHeight = `calc(95vh - ${keyboardHeight}px)`;
          }
        }
      } else {
        (mathFieldRef.current as any).executeCommand('hideVirtualKeyboard');

        // Reset padding when keyboard is hidden
        const modalBody = document.querySelector(
          '.mathlive-formula-dialog .ant-modal-body'
        );
        if (modalBody) {
          (modalBody as HTMLElement).style.paddingBottom = '20px';
        }

        // Reset modal position
        const modalContent = document.querySelector(
          '.mathlive-formula-dialog .ant-modal'
        );
        if (modalContent) {
          (modalContent as HTMLElement).style.top = '20px';
          (modalContent as HTMLElement).style.maxHeight = '80vh';
        }
      }
    }
  }, [virtualKeyboardEnabled]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormula(value);
  };

  // Handle insert button click
  const handleInsert = () => {
    try {
      // Get the current value directly from the math field to ensure it's up-to-date
      const currentFormula = mathFieldRef.current
        ? (mathFieldRef.current as any).value
        : formula;

      console.log('Inserting formula:', currentFormula);

      if (currentFormula && currentFormula.trim() !== '') {
        // Validate the formula before insertion
        try {
          // Basic validation - check for unbalanced braces
          let openBraces = 0;
          let openBrackets = 0;

          for (let i = 0; i < currentFormula.length; i++) {
            if (currentFormula[i] === '{') openBraces++;
            else if (currentFormula[i] === '}') openBraces--;
            else if (currentFormula[i] === '[') openBrackets++;
            else if (currentFormula[i] === ']') openBrackets--;

            // If at any point we have negative count, braces are unbalanced
            if (openBraces < 0 || openBrackets < 0) {
              throw new Error('Công thức có dấu ngoặc không cân đối');
            }
          }

          // Check if all braces are closed
          if (openBraces !== 0 || openBrackets !== 0) {
            throw new Error('Công thức có dấu ngoặc không cân đối');
          }

          // Make sure formula state is up-to-date before inserting
          if (formula !== currentFormula) {
            console.log(
              'Updating formula state before insertion from:',
              formula,
              'to:',
              currentFormula
            );
            setFormula(currentFormula);
          }

          // If validation passes, insert the formula
          onInsert(currentFormula);
          onClose();
        } catch (validationError: any) {
          // Show validation error
          setErrorMessage(validationError.message || 'Công thức không hợp lệ');

          // Try to focus the math field again
          if (mathFieldRef.current) {
            setTimeout(() => {
              mathFieldRef.current?.focus();
            }, 100);
          }
        }
      } else {
        // Show error message using Ant Design Modal
        Modal.error({
          title: 'Lỗi',
          content: 'Vui lòng nhập công thức trước khi chèn',
        });

        // Try to focus the math field again
        if (mathFieldRef.current) {
          setTimeout(() => {
            mathFieldRef.current?.focus();
          }, 100);
        }
      }
    } catch (error: any) {
      console.error('Error in handleInsert:', error);
      setErrorMessage(error.message || 'Đã xảy ra lỗi khi xử lý công thức');

      // Try to focus the math field again
      if (mathFieldRef.current) {
        setTimeout(() => {
          mathFieldRef.current?.focus();
        }, 100);
      }
    }
  };

  // Footer buttons for the modal
  const modalFooter = (
    <>
      <Button onClick={onClose}>Hủy</Button>
      <Button type="primary" onClick={handleInsert} disabled={!formula}>
        Chèn
      </Button>
    </>
  );

  // Handle formula selection from predefined formulas
  const handleFormulaSelect = (latex: string) => {
    setErrorMessage(''); // Clear any previous error messages

    if (mathFieldRef.current) {
      try {
        // Focus the math field first to ensure it's active
        mathFieldRef.current.focus();

        // Check if we should replace or insert
        const currentValue = (mathFieldRef.current as any).value || '';
        const hasSelection =
          (mathFieldRef.current as any).selectionIsCollapsed === false;

        if (currentValue.trim() === '' || hasSelection) {
          // If empty or has selection, replace with the new formula
          (mathFieldRef.current as any).value = latex;
        } else {
          // Otherwise, insert at cursor position
          (mathFieldRef.current as any).insert(latex);
        }

        // Get the updated value after insertion
        const updatedValue = (mathFieldRef.current as any).value;
        // Update the formula state
        setFormula(updatedValue);

        // Manually trigger an input event to ensure consistency
        const inputEvent = new Event('input', { bubbles: true });
        mathFieldRef.current.dispatchEvent(inputEvent);

        // Force focus back to the math field after a short delay
        setTimeout(() => {
          if (mathFieldRef.current) {
            mathFieldRef.current.focus();

            // Handle virtual keyboard based on toggle state
            if (
              typeof (mathFieldRef.current as any).executeCommand === 'function'
            ) {
              if (virtualKeyboardEnabled) {
                (mathFieldRef.current as any).executeCommand(
                  'showVirtualKeyboard'
                );
              } else {
                (mathFieldRef.current as any).executeCommand(
                  'hideVirtualKeyboard'
                );
              }
            }
          }
        }, 50);
      } catch (error) {
        console.error('Error inserting formula:', error);

        try {
          (mathFieldRef.current as any).value = latex;
          setFormula(latex);

          // Force focus back to the math field
          setTimeout(() => {
            if (mathFieldRef.current) {
              mathFieldRef.current.focus();
            }
          }, 50);
        } catch (e) {
          console.error('Fallback also failed:', e);
          setErrorMessage('Không thể chèn công thức. Vui lòng thử lại.');
        }
      }
    } else {
      console.warn('Math field reference is not available');
      // Set the formula state directly if the ref is not available
      setFormula(latex);
      setErrorMessage(
        'Trình soạn thảo công thức không sẵn sàng. Vui lòng thử lại.'
      );
    }
  };

  // Component to display a formula button
  const FormulaButton = ({
    formula,
  }: {
    formula: { name: string; latex: string; description: string };
  }) => {
    // Create a ref for the math-field element
    const formulaMathFieldRef = useRef<HTMLElement>(null);

    // Function to handle formula selection
    const selectFormula = (e: React.MouseEvent) => {
      console.log('Button clicked', e);
      // Stop propagation to prevent any event bubbling issues
      e.stopPropagation();
      // Call the formula selection handler
      handleFormulaSelect(formula.latex);
    };

    // Effect to add a direct click event listener to the math-field element
    useEffect(() => {
      const mathFieldElement = formulaMathFieldRef.current;

      if (mathFieldElement) {
        // Add a direct click event listener to the math-field
        const handleMathFieldClick = (e: Event) => {
          console.log('Math field clicked directly');
          e.preventDefault();
          e.stopPropagation();
          // Call the formula selection handler
          handleFormulaSelect(formula.latex);
        };

        // Add the event listener
        mathFieldElement.addEventListener('click', handleMathFieldClick, true);
        mathFieldElement.addEventListener(
          'mousedown',
          handleMathFieldClick,
          true
        );

        // Clean up the event listener when the component unmounts
        return () => {
          mathFieldElement.removeEventListener(
            'click',
            handleMathFieldClick,
            true
          );
          mathFieldElement.removeEventListener(
            'mousedown',
            handleMathFieldClick,
            true
          );
        };
      }
    }, [formula.latex]); // Re-run when the formula changes

    return (
      <Tooltip title={formula.description} placement="bottom">
        <div className="formula-button-wrapper" onClick={selectFormula}>
          <Button
            className="formula-button"
            type="text" // Use text type to remove button styling that might interfere
          >
            <div className="formula-button-content">
              <div className="formula-button-preview">
                {/* Add a transparent overlay to capture clicks */}
                <div
                  className="formula-button-overlay"
                  onClick={(e) => {
                    console.log('Overlay clicked');
                    e.stopPropagation();
                    handleFormulaSelect(formula.latex);
                  }}
                ></div>
                <math-field
                  ref={formulaMathFieldRef}
                  read-only="true"
                  value={formula.latex}
                  virtual-keyboard-mode="off"
                ></math-field>
              </div>
              <div className="formula-button-name">{formula.name}</div>
            </div>
          </Button>
        </div>
      </Tooltip>
    );
  };

  return (
    <ModalAntdCustom
      title="Chèn công thức toán học"
      open={isOpen}
      onCancel={onClose}
      footer={modalFooter}
      width={900}
      className="mathlive-formula-dialog"
      maskClosable={false} // Prevent closing when clicking outside
      keyboard={false} // Disable keyboard shortcuts to close modal
      style={{
        zIndex: 1000, // Ensure high z-index
        top: 20, // Position higher in the viewport
        maxHeight: '80vh', // Set max height
        overflow: 'visible', // Allow keyboard to overflow
      }}
    >
      <Row gutter={[24, 0]}>
        {/* Cột bên trái: Nhập công thức và xem trước */}
        <Col span={12}>
          <math-field
            ref={mathFieldRef}
            virtual-keyboard-mode={virtualKeyboardEnabled ? 'onfocus' : 'off'}
            virtual-keyboard-theme="material"
            letter-shape-style="tex"
            math-mode-space="\,"
            smart-fence="true"
            smart-superscript="true"
            smart-mode="true"
            placeholder-text="Nhập công thức toán học"
            onInput={handleInputChange}
          ></math-field>
          {errorMessage && (
            <Alert
              message="Lỗi cú pháp"
              description={errorMessage}
              type="error"
              showIcon
              style={{ marginTop: 16, marginBottom: 16 }}
            />
          )}

          <div style={{ marginTop: 16 }}>
            <Text strong>Hướng dẫn:</Text>
            <Space
              direction="vertical"
              style={{ marginTop: 8, paddingLeft: 8 }}
            >
              <Text>
                - Sử dụng các phím tắt như <code>/</code> cho phân số,{' '}
                <code>^</code> cho lũy thừa
              </Text>
              <Text>- Chọn từ các công thức có sẵn ở bên phải</Text>
            </Space>
          </div>
        </Col>

        {/* Cột bên phải: Các công thức phổ biến */}
        <Col span={12}>
          <Tabs defaultActiveKey="basic" className="formula-tabs">
            <TabPane tab="Cơ bản" key="basic">
              <div className="formula-buttons-container">
                <Row gutter={[8, 8]}>
                  {commonFormulas.basic.map((formula, index) => (
                    <Col span={6} key={`basic-${index}`}>
                      <FormulaButton formula={formula} />
                    </Col>
                  ))}
                </Row>
              </div>
            </TabPane>
            <TabPane tab="Đại số" key="algebra">
              <div className="formula-buttons-container">
                <Row gutter={[8, 8]}>
                  {commonFormulas.algebra.map((formula, index) => (
                    <Col span={6} key={`algebra-${index}`}>
                      <FormulaButton formula={formula} />
                    </Col>
                  ))}
                </Row>
              </div>
            </TabPane>
            <TabPane tab="Hình học" key="geometry">
              <div className="formula-buttons-container">
                <Row gutter={[8, 8]}>
                  {commonFormulas.geometry.map((formula, index) => (
                    <Col span={6} key={`geometry-${index}`}>
                      <FormulaButton formula={formula} />
                    </Col>
                  ))}
                </Row>
              </div>
            </TabPane>
            <TabPane tab="Ký hiệu" key="symbols">
              <div className="formula-buttons-container">
                <Row gutter={[8, 8]}>
                  {commonFormulas.symbols.map((formula, index) => (
                    <Col span={6} key={`symbols-${index}`}>
                      <FormulaButton formula={formula} />
                    </Col>
                  ))}
                </Row>
              </div>
            </TabPane>
          </Tabs>
        </Col>
      </Row>
    </ModalAntdCustom>
  );
};

export default memo(MathLiveFormulaDialog);

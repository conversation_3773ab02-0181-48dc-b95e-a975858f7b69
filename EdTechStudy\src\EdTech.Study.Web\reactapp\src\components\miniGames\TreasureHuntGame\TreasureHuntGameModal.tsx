import React, { useState, useEffect } from 'react';
import {
  TreasureClue,
  TreasureHuntGameConfig,
  defaultTreasureHuntGameConfig,
  sampleTreasureHuntGameConfigs,
} from './TreasureHuntGameConfig';
import {
  Tabs,
  Button,
  Form,
  Input,
  Select,
  InputNumber,
  Checkbox,
  Typography,
  Table,
  Space,
  Alert,
  Row,
  Col,
  message,
  Tag,
  Card,
  Tooltip,
} from 'antd';
import {
  InfoCircleOutlined,
  DatabaseOutlined,
  SettingOutlined,
  PlusOutlined,
  CloseOutlined,
  UpOutlined,
  DownOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';

const { Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface TreasureHuntGameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: TreasureHuntGameConfig) => void;
  defaultConfig?: Partial<TreasureHuntGameConfig>;
  path?: string;
  order?: number;
  addOrUpdateParamComponent?: (data: any) => void;
}

// Define tabs
type TabType = 'basic' | 'clues' | 'advanced';

// Augment TreasureHuntGameConfig with additional title and description fields
interface ExtendedTreasureHuntGameConfig extends TreasureHuntGameConfig {
  title?: string;
  description?: string;
  correctPoints?: number;
  incorrectPoints?: number;
  maxAttempts?: number;
  penaltyPerSecond?: number;
  showDistance?: boolean;
  showCompass?: boolean;
  showCoordinates?: boolean;
  mapType?: string;
  initialMapZoom?: number;
  allowMapDragging?: boolean;
}

const TreasureHuntGameModal: React.FC<TreasureHuntGameModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig = {},
  path,
  order,
  addOrUpdateParamComponent,
}) => {
  // State for tab switching
  const [activeTab, setActiveTab] = useState<TabType>('basic');

  // State for form configuration
  const [config, setConfig] = useState<ExtendedTreasureHuntGameConfig>({
    ...defaultTreasureHuntGameConfig,
    ...defaultConfig,
    titleProps: {
      text: defaultConfig.titleProps?.text || 'Truy Tìm Kho Báu',
      fontSize: defaultConfig.titleProps?.fontSize || 24,
      color: defaultConfig.titleProps?.color || '#000000',
      align: defaultConfig.titleProps?.align || 'left',
      bold: defaultConfig.titleProps?.bold || true,
    },
    description:
      defaultConfig.description ||
      'Hãy làm theo các hướng dẫn để tìm ra vị trí của kho báu bí ẩn trên bản đồ.',
  });

  // State for selected template
  const [selectedTemplate, setSelectedTemplate] = useState<string>('custom');

  // State for new clue
  const [newClue, setNewClue] = useState<Partial<TreasureClue>>({
    type: 'direction',
    value: '',
    direction: 'N',
    distance: 100,
    hint: '',
  });

  // State for hint modal
  const [isHintModalOpen, setIsHintModalOpen] = useState<boolean>(false);
  const [editingClueIndex, setEditingClueIndex] = useState<number | null>(null);

  // Re-initialize config when defaultConfig changes
  useEffect(() => {
    setConfig({
      ...defaultTreasureHuntGameConfig,
      ...defaultConfig,
      titleProps: {
        text: defaultConfig.titleProps?.text || 'Truy Tìm Kho Báu',
        fontSize: defaultConfig.titleProps?.fontSize || 24,
        color: defaultConfig.titleProps?.color || '#000000',
        align: defaultConfig.titleProps?.align || 'left',
        bold: defaultConfig.titleProps?.bold || true,
      },
      description:
        defaultConfig.description ||
        'Hãy làm theo các hướng dẫn để tìm ra vị trí của kho báu bí ẩn trên bản đồ.',
    });
  }, [defaultConfig]);

  // Handle new clue changes
  const handleNewClueChange = (name: string, value: any) => {
    let updatedClue = { ...newClue, [name]: value };

    // Auto-update value field based on type and other parameters
    if (name === 'type' || name === 'direction' || name === 'distance') {
      let autoValue = '';

      if (
        (name === 'type' && value === 'direction') ||
        (name === 'direction' && updatedClue.type === 'direction') ||
        (name === 'distance' && updatedClue.type === 'direction')
      ) {
        const direction = name === 'direction' ? value : updatedClue.direction;
        const distance = name === 'distance' ? value : updatedClue.distance;

        const directionText =
          {
            N: 'Bắc',
            NE: 'Đông Bắc',
            E: 'Đông',
            SE: 'Đông Nam',
            S: 'Nam',
            SW: 'Tây Nam',
            W: 'Tây',
            NW: 'Tây Bắc',
          }[
            (direction || 'N') as
              | 'N'
              | 'NE'
              | 'E'
              | 'SE'
              | 'S'
              | 'SW'
              | 'W'
              | 'NW'
          ] || '';

        autoValue = `Đi về phía ${directionText} ${distance} km`;
      } else if (
        (name === 'type' && value === 'latitudinal') ||
        (name === 'distance' && updatedClue.type === 'latitudinal')
      ) {
        const distance =
          name === 'distance' ? value : updatedClue.distance || 0;
        autoValue = `Di chuyển ${distance > 0 ? 'lên' : 'xuống'} ${Math.abs(
          distance
        )} độ vĩ độ`;
      } else if (
        (name === 'type' && value === 'longitudinal') ||
        (name === 'distance' && updatedClue.type === 'longitudinal')
      ) {
        const distance =
          name === 'distance' ? value : updatedClue.distance || 0;
        autoValue = `Di chuyển ${
          distance > 0 ? 'sang phải' : 'sang trái'
        } ${Math.abs(distance)} độ kinh độ`;
      }

      if (autoValue) {
        updatedClue = {
          ...updatedClue,
          value: autoValue,
        };
      }
    }

    setNewClue(updatedClue);
  };

  // Add new clue to list
  const handleAddClue = () => {
    if (!newClue.value) {
      message.error('Vui lòng nhập nội dung hướng dẫn.');
      return;
    }

    const clueToAdd: TreasureClue = {
      type: newClue.type as
        | 'direction'
        | 'coordinates'
        | 'latitudinal'
        | 'longitudinal',
      value: newClue.value,
      direction:
        newClue.type === 'direction' ? (newClue.direction as any) : undefined,
      distance: ['direction', 'latitudinal', 'longitudinal'].includes(
        newClue.type || ''
      )
        ? newClue.distance
        : undefined,
      hint: newClue.hint || undefined,
    };

    if (editingClueIndex !== null) {
      // Update existing clue
      const updatedClues = [...(config.clues || [])];
      updatedClues[editingClueIndex] = clueToAdd;

      setConfig({
        ...config,
        clues: updatedClues,
      });
      setEditingClueIndex(null);
    } else {
      // Add new clue
      setConfig({
        ...config,
        clues: [...(config.clues || []), clueToAdd],
      });
    }

    // Reset form
    setNewClue({
      type: 'direction',
      value: '',
      direction: 'N',
      distance: 100,
      hint: '',
    });

    // Close hint modal
    setIsHintModalOpen(false);
  };

  // Edit existing clue
  const handleEditClue = (index: number) => {
    if (!config.clues || index >= config.clues.length) return;

    const clue = config.clues[index];
    setNewClue(clue);
    setEditingClueIndex(index);
    setIsHintModalOpen(true);
  };

  // Remove clue from list
  const handleRemoveClue = (index: number) => {
    setConfig({
      ...config,
      clues: (config.clues || []).filter(
        (_: TreasureClue, i: number) => i !== index
      ),
    });
  };

  // Move clue up
  const handleMoveClueUp = (index: number) => {
    if (index <= 0 || !config.clues) return;

    const newClues = [...config.clues];
    const temp = newClues[index];
    newClues[index] = newClues[index - 1];
    newClues[index - 1] = temp;

    setConfig({
      ...config,
      clues: newClues,
    });
  };

  // Move clue down
  const handleMoveClueDown = (index: number) => {
    if (!config.clues || index >= config.clues.length - 1) return;

    const newClues = [...config.clues];
    const temp = newClues[index];
    newClues[index] = newClues[index + 1];
    newClues[index + 1] = temp;

    setConfig({
      ...config,
      clues: newClues,
    });
  };

  // Submit form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !config.treasurePosition ||
      !config.clues ||
      config.clues.length === 0 ||
      config.proximityThreshold === undefined
    ) {
      message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
      return;
    }

    // Sử dụng updateParam để cập nhật cấu hình vào state nếu có
    if (
      addOrUpdateParamComponent &&
      path !== undefined &&
      order !== undefined
    ) {
      addOrUpdateParamComponent({
        ...defaultConfig, // Giữ lại các trường khác trong params
        ...config,
      });
    }
    onSubmit(config);
    onClose();
  };

  // Tab Content - Basic Configuration
  const renderBasicTab = () => (
    <div className="tailwind-space-y-4">
      {/* Template Selection */}
      <div>
        <div className="tailwind-font-medium tailwind-mb-2">
          Mẫu cấu hình có sẵn
          <Tooltip title="Chọn một mẫu có sẵn hoặc tùy chỉnh cấu hình riêng của bạn">
            <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
          </Tooltip>
        </div>
        <Select
          value={selectedTemplate}
          onChange={(value) => {
            if (value === 'custom') {
              // Keep current configuration
              return;
            }

            // Apply selected template
            const templateConfig = sampleTreasureHuntGameConfigs[value];
            if (templateConfig) {
              setConfig({
                ...templateConfig,
                title: config.title,
                description: config.description,
              });
              setSelectedTemplate(value);
            }
          }}
          className="tailwind-w-full"
        >
          <Option value="custom">Cấu hình tùy chỉnh</Option>
          <Option value="hanoi-landmarks">Khám Phá Hà Nội</Option>
          <Option value="hanoi-museums">Bảo Tàng Hà Nội</Option>
          <Option value="old-quarter">Khám Phá Phố Cổ</Option>
          <Option value="vietnam-cities">Các Thành Phố Việt Nam</Option>
          <Option value="world-capitals">Thủ Đô Thế Giới</Option>
        </Select>
      </div>

      {/* Game Title */}
      <div>
        <div className="tailwind-font-medium tailwind-mb-2">
          Tiêu đề trò chơi <span className="tailwind-text-red-500">*</span>
        </div>
        <Input
          value={config.title || 'Truy Tìm Kho Báu'}
          onChange={(e) => setConfig({ ...config, title: e.target.value })}
          placeholder="Nhập tiêu đề trò chơi"
        />
      </div>

      {/* Game Description */}
      <div>
        <div className="tailwind-font-medium tailwind-mb-2">Mô tả trò chơi</div>
        <TextArea
          rows={2}
          value={
            config.description ||
            'Hãy làm theo các hướng dẫn để tìm ra vị trí của kho báu bí ẩn trên bản đồ.'
          }
          onChange={(e) =>
            setConfig({ ...config, description: e.target.value })
          }
          placeholder="Nhập mô tả trò chơi"
        />
      </div>

      {/* Positions */}
      <div className="tailwind-mt-2">
        <div className="tailwind-font-medium tailwind-mb-2">Vị trí bản đồ</div>
        <Card className="tailwind-shadow-sm">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Vĩ độ ban đầu <span className="tailwind-text-red-500">*</span>
                  <Tooltip title="Vị trí bắt đầu của người chơi (vĩ độ)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.initialPosition?.lat || 0}
                  onChange={(value) =>
                    setConfig({
                      ...config,
                      initialPosition: {
                        lat: value as number,
                        lng: config.initialPosition?.lng || 0,
                      },
                    })
                  }
                  step={0.0001}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Kinh độ ban đầu{' '}
                  <span className="tailwind-text-red-500">*</span>
                  <Tooltip title="Vị trí bắt đầu của người chơi (kinh độ)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.initialPosition?.lng || 0}
                  onChange={(value) =>
                    setConfig({
                      ...config,
                      initialPosition: {
                        lat: config.initialPosition?.lat || 0,
                        lng: value as number,
                      },
                    })
                  }
                  step={0.0001}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Vĩ độ kho báu <span className="tailwind-text-red-500">*</span>
                  <Tooltip title="Vị trí đích đến của kho báu (vĩ độ)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.treasurePosition?.lat || 0}
                  onChange={(value) =>
                    setConfig({
                      ...config,
                      treasurePosition: {
                        lat: value as number,
                        lng: config.treasurePosition?.lng || 0,
                      },
                    })
                  }
                  step={0.0001}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Kinh độ kho báu{' '}
                  <span className="tailwind-text-red-500">*</span>
                  <Tooltip title="Vị trí đích đến của kho báu (kinh độ)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.treasurePosition?.lng || 0}
                  onChange={(value) =>
                    setConfig({
                      ...config,
                      treasurePosition: {
                        lat: config.treasurePosition?.lat || 0,
                        lng: value as number,
                      },
                    })
                  }
                  step={0.0001}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
          </Row>
        </Card>
      </div>

      {/* Game Settings */}
      <div className="tailwind-mt-2">
        <div className="tailwind-font-medium tailwind-mb-2">
          Cài đặt trò chơi
        </div>
        <Card className="tailwind-shadow-sm">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Mức zoom ban đầu{' '}
                  <span className="tailwind-text-red-500">*</span>
                  <Tooltip title="Mức độ phóng to/thu nhỏ ban đầu của bản đồ (1-20)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  min={1}
                  max={20}
                  value={config.initialZoom || 6}
                  onChange={(value) =>
                    setConfig({ ...config, initialZoom: value as number })
                  }
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Ngưỡng khoảng cách (km){' '}
                  <span className="tailwind-text-red-500">*</span>
                  <Tooltip title="Khoảng cách tối đa (km) để coi là đã đến đúng vị trí">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  min={0.01}
                  max={100}
                  step={0.01}
                  value={config.proximityThreshold || 0.1}
                  onChange={(value) =>
                    setConfig({
                      ...config,
                      proximityThreshold: value as number,
                    })
                  }
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Độ khó
                  <Tooltip title="Mức độ khó của trò chơi, ảnh hưởng đến độ chi tiết của gợi ý">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <Select
                  value={config.difficultyLevel}
                  onChange={(value) =>
                    setConfig({ ...config, difficultyLevel: value })
                  }
                  className="tailwind-w-full"
                >
                  <Option value="easy">Dễ</Option>
                  <Option value="medium">Trung bình</Option>
                  <Option value="hard">Khó</Option>
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Thời gian giới hạn (giây)
                  <Tooltip title="Thời gian hoàn thành trò chơi (0 = không giới hạn)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  min={0}
                  max={3600}
                  value={config.timeLimit || 0}
                  onChange={(value) =>
                    setConfig({ ...config, timeLimit: value as number })
                  }
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={24}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Điểm trừ khi dùng gợi ý
                  <Tooltip title="Số điểm bị trừ mỗi khi người chơi sử dụng gợi ý">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  min={0}
                  max={100}
                  value={config.hintPenalty || 5}
                  onChange={(value) =>
                    setConfig({ ...config, hintPenalty: value as number })
                  }
                  className="tailwind-w-full"
                />
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  );

  // Tab Content - Clues Management
  const renderCluesTab = () => (
    <div className="tailwind-space-y-4">
      <Alert
        type="info"
        message="Quản lý gợi ý tìm kho báu"
        description="Thêm các gợi ý để hướng dẫn người chơi tìm kiếm kho báu. Các gợi ý sẽ hiển thị theo thứ tự từ trên xuống dưới."
        showIcon
        className="tailwind-mb-3"
      />

      <div className="tailwind-mb-3 tailwind-flex tailwind-justify-end">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setNewClue({
              type: 'direction',
              value: '',
              direction: 'N',
              distance: 100,
              hint: '',
            });
            setEditingClueIndex(null);
            setIsHintModalOpen(true);
          }}
        >
          Thêm gợi ý mới
        </Button>
      </div>

      {config.clues && config.clues.length > 0 ? (
        <Table
          dataSource={config.clues.map((clue, index) => ({
            ...clue,
            key: index,
          }))}
          pagination={false}
          className="tailwind-mb-4"
          scroll={{ x: undefined }}
          columns={[
            {
              title: 'STT',
              dataIndex: 'key',
              key: 'key',
              width: 60,
              render: (_, __, index) => index + 1,
            },
            {
              title: 'Loại',
              dataIndex: 'type',
              key: 'type',
              width: 120,
              render: (type) => (
                <Tag
                  color={
                    type === 'direction'
                      ? 'blue'
                      : type === 'coordinates'
                      ? 'purple'
                      : type === 'latitudinal'
                      ? 'green'
                      : type === 'longitudinal'
                      ? 'orange'
                      : 'default'
                  }
                >
                  {type === 'direction'
                    ? 'Hướng dẫn'
                    : type === 'coordinates'
                    ? 'Toạ độ'
                    : type === 'latitudinal'
                    ? 'Vĩ độ'
                    : type === 'longitudinal'
                    ? 'Kinh độ'
                    : type}
                </Tag>
              ),
            },
            {
              title: 'Nội dung',
              dataIndex: 'value',
              key: 'value',
              render: (value) => <Text>{value}</Text>,
            },
            {
              title: 'Gợi ý bổ sung',
              dataIndex: 'hint',
              key: 'hint',
              render: (hint) =>
                hint ? (
                  <Text italic>{hint}</Text>
                ) : (
                  <Text type="secondary">Không có</Text>
                ),
            },
            {
              title: 'Thao tác',
              key: 'action',
              width: 180,
              render: (_, __, index) => (
                <Space>
                  <Tooltip title="Chỉnh sửa">
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditClue(index)}
                    />
                  </Tooltip>
                  <Tooltip title="Di chuyển lên">
                    <Button
                      type="text"
                      icon={<UpOutlined />}
                      disabled={index === 0}
                      onClick={() => handleMoveClueUp(index)}
                    />
                  </Tooltip>
                  <Tooltip title="Di chuyển xuống">
                    <Button
                      type="text"
                      icon={<DownOutlined />}
                      disabled={index === (config.clues?.length || 0) - 1}
                      onClick={() => handleMoveClueDown(index)}
                    />
                  </Tooltip>
                  <Tooltip title="Xóa">
                    <Button
                      type="text"
                      danger
                      icon={<CloseOutlined />}
                      onClick={() => handleRemoveClue(index)}
                    />
                  </Tooltip>
                </Space>
              ),
            },
          ]}
        />
      ) : (
        <Alert
          type="warning"
          message="Chưa có gợi ý nào được thêm vào"
          description="Hãy thêm ít nhất một gợi ý để hướng dẫn người chơi tìm kiếm kho báu."
          showIcon
          className="tailwind-mb-4"
        />
      )}

      {/* Hint Modal */}
      <ModalAntdCustom
        title={editingClueIndex !== null ? 'Chỉnh sửa gợi ý' : 'Thêm gợi ý mới'}
        open={isHintModalOpen}
        onCancel={() => setIsHintModalOpen(false)}
        onOk={handleAddClue}
        width={700}
        okText={editingClueIndex !== null ? 'Cập nhật' : 'Thêm'}
        cancelText="Hủy"
      >
        <Form layout="vertical">
          <Form.Item
            label="Loại gợi ý"
            required
            tooltip="Chọn loại gợi ý phù hợp với hướng dẫn của bạn"
          >
            <Select
              value={newClue.type}
              onChange={(value) => handleNewClueChange('type', value)}
              className="tailwind-w-full"
            >
              <Option value="direction">Hướng dẫn theo hướng</Option>
              <Option value="coordinates">Toạ độ cụ thể</Option>
              <Option value="latitudinal">Thay đổi vĩ độ</Option>
              <Option value="longitudinal">Thay đổi kinh độ</Option>
            </Select>
          </Form.Item>

          {newClue.type === 'direction' && (
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Hướng"
                  required
                  tooltip="Hướng di chuyển cần thực hiện"
                >
                  <Select
                    value={newClue.direction}
                    onChange={(value) =>
                      handleNewClueChange('direction', value)
                    }
                    className="tailwind-w-full"
                  >
                    <Option value="N">Bắc (N)</Option>
                    <Option value="NE">Đông Bắc (NE)</Option>
                    <Option value="E">Đông (E)</Option>
                    <Option value="SE">Đông Nam (SE)</Option>
                    <Option value="S">Nam (S)</Option>
                    <Option value="SW">Tây Nam (SW)</Option>
                    <Option value="W">Tây (W)</Option>
                    <Option value="NW">Tây Bắc (NW)</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Khoảng cách (km)"
                  required
                  tooltip="Khoảng cách cần di chuyển theo hướng đã chọn"
                >
                  <InputNumber
                    value={newClue.distance}
                    onChange={(value) => handleNewClueChange('distance', value)}
                    min={0.1}
                    max={1000}
                    step={0.1}
                    className="tailwind-w-full"
                  />
                </Form.Item>
              </Col>
            </Row>
          )}

          {(newClue.type === 'latitudinal' ||
            newClue.type === 'longitudinal') && (
            <Form.Item
              label={
                newClue.type === 'latitudinal'
                  ? 'Thay đổi vĩ độ'
                  : 'Thay đổi kinh độ'
              }
              required
              tooltip={
                newClue.type === 'latitudinal'
                  ? 'Giá trị dương di chuyển lên trên, âm di chuyển xuống dưới'
                  : 'Giá trị dương di chuyển sang phải, âm di chuyển sang trái'
              }
            >
              <InputNumber
                value={newClue.distance}
                onChange={(value) => handleNewClueChange('distance', value)}
                min={newClue.type === 'latitudinal' ? -90 : -180}
                max={newClue.type === 'latitudinal' ? 90 : 180}
                step={0.1}
                className="tailwind-w-full"
              />
            </Form.Item>
          )}

          {newClue.type === 'coordinates' && (
            <Form.Item
              label="Tọa độ (vĩ độ, kinh độ)"
              required
              tooltip="Nhập tọa độ dạng 'lat,lng' (vd: 21.0285,105.8542)"
            >
              <Input
                value={newClue.value}
                onChange={(e) => handleNewClueChange('value', e.target.value)}
                placeholder="Vd: 21.0285,105.8542"
              />
            </Form.Item>
          )}

          <Form.Item
            label="Nội dung gợi ý"
            required
            tooltip="Văn bản mô tả hướng dẫn cho người chơi"
          >
            <Input
              value={newClue.value}
              onChange={(e) => handleNewClueChange('value', e.target.value)}
              placeholder="Nhập nội dung gợi ý tìm kho báu"
            />
          </Form.Item>

          <Form.Item
            label="Gợi ý bổ sung"
            tooltip="Gợi ý bổ sung khi người chơi gặp khó khăn (không bắt buộc)"
          >
            <Input
              value={newClue.hint}
              onChange={(e) => handleNewClueChange('hint', e.target.value)}
              placeholder="Nhập gợi ý bổ sung nếu người chơi gặp khó khăn"
            />
          </Form.Item>
        </Form>
      </ModalAntdCustom>
    </div>
  );

  // Tab Content - Advanced Settings
  const renderAdvancedTab = () => (
    <div className="tailwind-space-y-4">
      <Alert
        type="info"
        message="Cài đặt nâng cao"
        description="Điều chỉnh các thông số chi tiết của trò chơi để tạo trải nghiệm phù hợp với nhu cầu của bạn."
        showIcon
        className="tailwind-mb-3"
      />

      <div className="tailwind-mt-2">
        <div className="tailwind-font-medium tailwind-mb-2">Hệ thống điểm</div>
        <Card className="tailwind-shadow-sm">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Điểm thưởng cho bước đúng
                  <Tooltip title="Số điểm nhận được khi tìm thấy kho báu">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.correctPoints}
                  onChange={(value) => {
                    setConfig({
                      ...config,
                      correctPoints: value as number,
                    });
                  }}
                  min={1}
                  max={1000}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Điểm trừ cho bước sai
                  <Tooltip title="Số điểm bị trừ khi đoán sai vị trí kho báu">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.incorrectPoints}
                  onChange={(value) => {
                    setConfig({
                      ...config,
                      incorrectPoints: value as number,
                    });
                  }}
                  min={0}
                  max={1000}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
          </Row>
        </Card>
      </div>

      <div className="tailwind-mt-2">
        <div className="tailwind-font-medium tailwind-mb-2">
          Cấu hình trò chơi
        </div>
        <Card className="tailwind-shadow-sm">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Số lần thử tối đa
                  <Tooltip title="Số lần người chơi được phép đoán vị trí kho báu (0 = không giới hạn)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.maxAttempts}
                  onChange={(value) => {
                    setConfig({
                      ...config,
                      maxAttempts: value as number,
                    });
                  }}
                  min={0}
                  max={100}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Giảm điểm theo thời gian
                  <Tooltip title="Điểm bị trừ mỗi giây (0 = không trừ điểm theo thời gian)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.penaltyPerSecond}
                  onChange={(value) => {
                    setConfig({
                      ...config,
                      penaltyPerSecond: value as number,
                    });
                  }}
                  min={0}
                  max={10}
                  step={0.1}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
          </Row>
        </Card>
      </div>

      <div className="tailwind-mt-2">
        <div className="tailwind-font-medium tailwind-mb-2">
          Tùy chọn hiển thị
        </div>
        <Card className="tailwind-shadow-sm">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Hiển thị khoảng cách
                  <Tooltip title="Hiển thị khoảng cách từ vị trí hiện tại đến kho báu">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <Checkbox
                  checked={config.showDistance}
                  onChange={(e) => {
                    setConfig({
                      ...config,
                      showDistance: e.target.checked,
                    });
                  }}
                >
                  Hiển thị khoảng cách tới kho báu
                </Checkbox>
              </div>
            </Col>
            <Col span={8}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Hiển thị la bàn
                  <Tooltip title="Hiển thị la bàn để người chơi định hướng">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <Checkbox
                  checked={config.showCompass}
                  onChange={(e) => {
                    setConfig({
                      ...config,
                      showCompass: e.target.checked,
                    });
                  }}
                >
                  Hiển thị la bàn chỉ hướng
                </Checkbox>
              </div>
            </Col>
            <Col span={8}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Hiển thị tọa độ
                  <Tooltip title="Hiển thị tọa độ hiện tại của người chơi">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <Checkbox
                  checked={config.showCoordinates}
                  onChange={(e) => {
                    setConfig({
                      ...config,
                      showCoordinates: e.target.checked,
                    });
                  }}
                >
                  Hiển thị tọa độ hiện tại
                </Checkbox>
              </div>
            </Col>
          </Row>
        </Card>
      </div>

      <div className="tailwind-mt-2">
        <div className="tailwind-font-medium tailwind-mb-2">Cài đặt bản đồ</div>
        <Card className="tailwind-shadow-sm">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Loại bản đồ
                  <Tooltip title="Chọn loại bản đồ hiển thị">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <Select
                  value={config.mapType}
                  onChange={(value) => {
                    setConfig({
                      ...config,
                      mapType: value,
                    });
                  }}
                  className="tailwind-w-full"
                >
                  <Option value="roadmap">Bản đồ đường phố</Option>
                  <Option value="satellite">Bản đồ vệ tinh</Option>
                  <Option value="hybrid">Bản đồ kết hợp</Option>
                  <Option value="terrain">Bản đồ địa hình</Option>
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Mức zoom ban đầu
                  <Tooltip title="Mức độ phóng to ban đầu của bản đồ (1-20)">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <InputNumber
                  value={config.initialMapZoom}
                  onChange={(value) => {
                    setConfig({
                      ...config,
                      initialMapZoom: value as number,
                    });
                  }}
                  min={1}
                  max={20}
                  className="tailwind-w-full"
                />
              </div>
            </Col>
            <Col span={24}>
              <div>
                <div className="tailwind-font-medium tailwind-mb-2">
                  Cho phép kéo bản đồ
                  <Tooltip title="Cho phép người chơi kéo và di chuyển bản đồ">
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </Tooltip>
                </div>
                <Checkbox
                  checked={config.allowMapDragging}
                  onChange={(e) => {
                    setConfig({
                      ...config,
                      allowMapDragging: e.target.checked,
                    });
                  }}
                >
                  Cho phép kéo bản đồ
                </Checkbox>
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  );

  // Tab items
  const tabItems = [
    {
      key: 'basic',
      label: (
        <span>
          <InfoCircleOutlined /> Cấu hình cơ bản
        </span>
      ),
      children: renderBasicTab(),
    },
    {
      key: 'clues',
      label: (
        <span>
          <DatabaseOutlined /> Quản lý gợi ý
        </span>
      ),
      children: renderCluesTab(),
    },
    {
      key: 'advanced',
      label: (
        <span>
          <SettingOutlined /> Nâng cao
        </span>
      ),
      children: renderAdvancedTab(),
    },
  ];

  return (
    <ModalAntdCustom
      open={isOpen}
      title={
        <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
          <SettingOutlined />
          <span>Cấu hình trò chơi</span>
        </div>
      }
      onCancel={onClose}
      width={1000}
      styles={{
        body: {
          maxHeight: 'calc(90vh - 180px)',
          overflowY: 'auto',
          paddingTop: '16px',
        },
      }}
      className="tailwind-treasure-hunt-modal"
      footer={[
        <Button key="cancel" onClick={onClose}>
          Huỷ
        </Button>,
        <Button
          key="submit"
          className="tailwind-bg-blue-500"
          type="primary"
          onClick={handleSubmit}
        >
          Lưu cấu hình
        </Button>,
      ]}
    >
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabType)}
        items={tabItems}
        className="tailwind-mb-4"
        destroyInactiveTabPane
      />
    </ModalAntdCustom>
  );
};

export default TreasureHuntGameModal;

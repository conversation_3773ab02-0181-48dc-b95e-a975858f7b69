// pubsub.ts - A simple pub/sub implementation with TypeScript

// Define types for the subscription callback
type Callback<T = any> = (data: T) => void;

// Define type for subscribers storage
interface SubscriberMap {
  [event: string]: {
    [id: number]: Callback;
  };
}

class PubSub {
  private subscribers: SubscriberMap;
  private lastId: number;

  constructor() {
    this.subscribers = {};
    this.lastId = 0;
  }

  /**
   * Subscribe to an event
   * @param event The event name to subscribe to
   * @param callback The callback function to execute when the event is published
   * @returns A subscription ID that can be used to unsubscribe
   */
  public subscribe<T>(event: string, callback: Callback<T>): number {
    if (!this.subscribers[event]) {
      this.subscribers[event] = {};
    }

    const id = this.lastId++;
    this.subscribers[event][id] = callback as Callback;

    return id;
  }

  /**
   * Unsubscribe from a subscription using its ID
   * @param subscriptionId The ID returned from the subscribe method
   * @returns true if unsubscribed successfully, false otherwise
   */
  public unsubscribe(subscriptionId: number): boolean {
    for (const event in this.subscribers) {
      if (this.subscribers[event][subscriptionId]) {
        delete this.subscribers[event][subscriptionId];
        return true;
      }
    }
    return false;
  }

  /**
   * Publish an event with data
   * @param event The event name to publish
   * @param data The data to pass to subscriber callbacks
   */
  public publish<T>(event: string, data: T): void {
    if (!this.subscribers[event]) {
      return;
    }

    Object.values(this.subscribers[event]).forEach((callback) => {
      callback(data);
    });
  }

  /**
   * Check if an event has subscribers
   * @param event The event name to check
   * @returns true if the event has subscribers, false otherwise
   */
  public hasSubscribers(event: string): boolean {
    return (
      !!this.subscribers[event] &&
      Object.keys(this.subscribers[event]).length > 0
    );
  }

  /**
   * Get the number of subscribers for an event
   * @param event The event name
   * @returns The number of subscribers
   */
  public getSubscriberCount(event: string): number {
    if (!this.subscribers[event]) {
      return 0;
    }
    return Object.keys(this.subscribers[event]).length;
  }
}

// Create a singleton instance
const pubsub = new PubSub();
export default pubsub;

/* Usage example with TypeScript:

// Define a type for the event data
interface UserData {
  id: number;
  name: string;
  email: string;
}

// Subscribe with type safety
const subscriptionId = pubsub.subscribe<UserData>('userLoggedIn', (user) => {
  // TypeScript knows user is of type UserData
  console.log(`User logged in: ${user.name} (${user.email})`);
});

// Publishing an event with type checking
pubsub.publish<UserData>('userLoggedIn', { 
  id: 1, 
  name: 'John', 
  email: '<EMAIL>' 
});

// Unsubscribe using the ID
pubsub.unsubscribe(subscriptionId);
*/

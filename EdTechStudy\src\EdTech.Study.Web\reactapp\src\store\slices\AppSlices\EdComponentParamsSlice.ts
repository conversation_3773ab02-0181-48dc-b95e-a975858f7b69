import {
  createParamsSlice,
  IEdComponentParamsState,
} from '../utils/createParamsSlice';

const ED_COMPONENT_PARAMS_SLICE = 'ED_COMPONENT_PARAMS_SLICE';

// Create the slice using the factory function
export const EdComponentParamsSlice = createParamsSlice(
  ED_COMPONENT_PARAMS_SLICE
);

// Re-export the state interface
export type { IEdComponentParamsState };

// Export actions
export const {
  setParams,
  addParam,
  updateParam,
  addOrUpdateParam,
  removeParam,
  clearParams,
  setLoading,
  setError,
} = EdComponentParamsSlice.actions;

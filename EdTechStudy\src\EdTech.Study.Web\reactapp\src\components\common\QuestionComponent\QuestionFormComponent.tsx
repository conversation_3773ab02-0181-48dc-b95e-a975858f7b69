import React, {
  useState,
  useEffect,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  forwardRef,
} from 'react';
import {
  Question,
  QuestionType,
  BaseQuestion,
  TrueFalseQuestion,
  MultipleChoiceQuestion,
  FillInQuestion,
  OrderingQuestion,
  MatchingQuestion,
} from './QuestionBankConfig';
import {
  Form,
  Input,
  Select,
  Radio,
  Checkbox,
  Button,
  Upload,
  InputNumber,
  Space,
  Tag,
  Card,
  Typography,
  message,
} from 'antd';
import MultipleChoiceQuestionComponent from './question/MultipleChoiceQuestionComponent';
import {
  AddOulinedIcon,
  ArrowRightOutlinedIcon,
  ArrowUploadIcon,
  DeleteIcon,
} from '../../icons/IconIndex';

const { TextArea } = Input;
const { Text } = Typography;

export interface QuestionFormRef {
  submitForm: () => void;
}

interface QuestionFormComponentProps {
  question?: Question;
  onSave?: (question: Question) => void;
  onCancel?: () => void;
  initialQuestionType?: QuestionType;
  restrictQuestionType?: QuestionType;
}

const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

const QuestionFormComponent = forwardRef<
  QuestionFormRef,
  QuestionFormComponentProps
>(({ question, onSave, initialQuestionType, restrictQuestionType }, ref) => {
  const [form] = Form.useForm();

  // Expose the submitForm method to parent components
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      form.submit();
    },
  }));

  const [questionType, setQuestionType] = useState<QuestionType>(
    question?.type || initialQuestionType || 'multiplechoice'
  );
  const [tags, setTags] = useState<string[]>(question?.tags || []);
  const [tagInput, setTagInput] = useState<string>('');

  // New states for image and hint
  const [imageUrl, setImageUrl] = useState<string>(question?.imageUrl || '');
  const [previewUrl, setPreviewUrl] = useState<string>('');
  // Multiple Choice specific states
  const [options, setOptions] = useState<string[]>(
    question?.type === 'multiplechoice' ? question.options : ['', '', '', '']
  );
  // Ordering specific states
  const [items, setItems] = useState<string[]>(
    question?.type === 'ordering' ? question.items : ['', '', '', '']
  );
  const [correctOrder, setCorrectOrder] = useState<number[]>(
    question?.type === 'ordering' ? question.correctOrder : [0, 1, 2, 3]
  );

  // Matching specific states
  const [leftItems, setLeftItems] = useState<string[]>(
    question?.type === 'matching' ? question.leftItems : ['', '']
  );
  const [rightItems, setRightItems] = useState<string[]>(
    question?.type === 'matching' ? question.rightItems : ['', '']
  );
  const [correctPairs, setCorrectPairs] = useState<number[][]>(
    question?.type === 'matching'
      ? question.correctLeftToRight
      : [
          [0, 0],
          [1, 1],
        ]
  );

  // Reset form fields when question changes or question type changes
  useEffect(() => {
    if (question) {
      form.setFieldsValue({
        questionType: question.type,
        questionText: question.questionText,
        explanation: question.explanation,
        difficulty: question.difficulty,
        points: question.points,
        hint: question.hint || '',

        // Type-specific fields
        correctOptionIndex:
          question.type === 'multiplechoice' ? question.correctOptionIndex : 0,
        isCorrect: question.type === 'truefalse' ? question.isCorrect : true,
        correctAnswer: question.type === 'fillin' ? question.correctAnswer : '',
        caseSensitive:
          question.type === 'fillin' ? question.caseSensitive : false,
      });

      // Set states that aren't directly mapped to form fields
      setQuestionType(question.type);
      setTags(question.tags || []);
      setImageUrl(question.imageUrl || '');
      setPreviewUrl(question.imageUrl || '');

      if (question.type === 'multiplechoice') {
        setOptions(question.options);
      } else if (question.type === 'ordering') {
        setItems(question.items);
        setCorrectOrder(question.correctOrder);
      } else if (question.type === 'matching') {
        setLeftItems(question.leftItems);
        setRightItems(question.rightItems);
        setCorrectPairs(question.correctLeftToRight);
      }
    } else {
      // New question - set defaults
      form.resetFields();
      form.setFieldsValue({
        questionType: initialQuestionType || 'multiplechoice',
        difficulty: 'medium',
        points: 10,
      });

      setQuestionType(initialQuestionType || 'multiplechoice');
      setTags([]);
      setTagInput('');
      setImageUrl('');
      setPreviewUrl('');

      // Initialize type-specific fields
      if (initialQuestionType === 'multiplechoice' || !initialQuestionType) {
        setOptions(['', '']);
      } else if (initialQuestionType === 'ordering') {
        setItems(['', '']);
        setCorrectOrder([0, 1]);
      } else if (initialQuestionType === 'matching') {
        setLeftItems(['', '']);
        setRightItems(['', '']);
        setCorrectPairs([
          [0, 0],
          [1, 1],
        ]);
      }
    }
  }, [question, initialQuestionType, form]);

  // Update component to use our new restrictQuestionType
  useEffect(() => {
    // If restrictQuestionType is set, force the question type
    if (restrictQuestionType) {
      setQuestionType(restrictQuestionType);
      form.setFieldsValue({ questionType: restrictQuestionType });

      // Initialize type-specific fields for the restricted type
      handleTypeChange(restrictQuestionType);
    }
  }, [restrictQuestionType, form]);

  // Handle question type change
  const handleTypeChange = (value: QuestionType) => {
    setQuestionType(value);

    // Reset type-specific fields when changing type
    if (!question || question.type !== value) {
      if (value === 'multiplechoice') {
        setOptions(['', '']);
        form.setFieldsValue({ correctOptionIndex: 0 });
      } else if (value === 'truefalse') {
        form.setFieldsValue({ isCorrect: true });
      } else if (value === 'fillin') {
        form.setFieldsValue({
          correctAnswer: 'XXX',
          caseSensitive: false,
        });
      } else if (value === 'ordering') {
        setItems(['', '']);
        setCorrectOrder([0, 1]);
      } else if (value === 'matching') {
        setLeftItems(['', '']);
        setRightItems(['', '']);
        setCorrectPairs([
          [0, 0],
          [1, 1],
        ]);
      }
    }
  };

  // Image handling
  const handleImageChange = (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }

    if (info.file.status === 'done') {
      // Handle file
      const file = info.file.originFileObj;

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setPreviewUrl(base64String);
        setImageUrl(base64String);
      };
      reader.readAsDataURL(file);
    }
  };

  const clearImageSelection = () => {
    setPreviewUrl('');
    setImageUrl('');
    form.setFieldsValue({ image: undefined });
  };

  // Multiple choice options
  const handleAddOption = () => {
    setOptions([...options, '']);
  };

  const handleRemoveOption = (index: number) => {
    if (options.length > 2) {
      const newOptions = [...options];
      newOptions.splice(index, 1);
      setOptions(newOptions);

      // Update correct option index if needed
      const correctOptionIndex = form.getFieldValue('correctOptionIndex');

      if (correctOptionIndex === index) {
        // Nếu xóa đúng đáp án đúng, đặt đáp án đúng là tùy chọn đầu tiên
        form.setFieldsValue({ correctOptionIndex: 0 });
      } else if (correctOptionIndex > index) {
        // Nếu xóa tùy chọn trước đáp án đúng, giảm chỉ số đáp án đúng đi 1
        form.setFieldsValue({ correctOptionIndex: correctOptionIndex - 1 });
      }
    } else {
      message.warning('Câu hỏi trắc nghiệm cần ít nhất 2 lựa chọn');
    }
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  // Ordering items
  const handleAddItem = () => {
    setItems([...items, '']);
    setCorrectOrder([...correctOrder, correctOrder.length]);
  };

  const handleRemoveItem = (index: number) => {
    if (items.length > 2) {
      const newItems = [...items];
      newItems.splice(index, 1);
      setItems(newItems);

      const newOrder = correctOrder
        .filter((i) => i !== index)
        .map((i) => (i > index ? i - 1 : i));
      setCorrectOrder(newOrder);
    } else {
      message.warning('Câu hỏi sắp xếp cần ít nhất 2 mục');
    }
  };

  const handleItemChange = (index: number, value: string) => {
    const newItems = [...items];
    newItems[index] = value;
    setItems(newItems);
  };

  // New function to handle multiple connections
  const handleConnectItems = (leftIndex: number, rightIndex: number) => {
    const pairExists = correctPairs.some(
      (pair) => pair[0] === leftIndex && pair[1] === rightIndex
    );
    if (!pairExists) {
      setCorrectPairs([...correctPairs, [leftIndex, rightIndex]]);
    }
  };

  const handleRemoveConnection = (leftIndex: number, rightIndex: number) => {
    setCorrectPairs(
      correctPairs.filter(
        (pair) => !(pair[0] === leftIndex && pair[1] === rightIndex)
      )
    );
  };

  // Left item change
  const handleLeftItemChange = (index: number, value: string) => {
    const newItems = [...leftItems];
    newItems[index] = value;
    setLeftItems(newItems);
  };

  // Right item change
  const handleRightItemChange = (index: number, value: string) => {
    const newItems = [...rightItems];
    newItems[index] = value;
    setRightItems(newItems);
  };

  // Tag handling
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (removedTag: string) => {
    const newTags = tags.filter((tag) => tag !== removedTag);
    setTags(newTags);
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(e.target.value);
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Form submission
  const onFinish = (values: any) => {
    const baseQuestion: BaseQuestion = {
      id: question?.id || generateUUID(),
      type: questionType,
      questionText: values.questionText,
      explanation: values.explanation?.trim() || undefined,
      difficulty: values.difficulty,
      points: values.points,
      tags: tags.length > 0 ? tags : undefined,
      imageUrl: imageUrl || undefined,
      hint: values.hint?.trim() || undefined,
    };

    let finalQuestion: Question;

    switch (questionType) {
      case 'multiplechoice':
        finalQuestion = {
          ...baseQuestion,
          type: 'multiplechoice',
          options,
          correctOptionIndex: values.correctOptionIndex,
        } as MultipleChoiceQuestion;
        break;
      case 'truefalse':
        finalQuestion = {
          ...baseQuestion,
          type: 'truefalse',
          isCorrect: values.isCorrect,
        } as TrueFalseQuestion;
        break;
      case 'fillin':
        finalQuestion = {
          ...baseQuestion,
          type: 'fillin',
          correctAnswer: values.correctAnswer,
          caseSensitive: values.caseSensitive,
        } as FillInQuestion;
        break;
      case 'ordering':
        finalQuestion = {
          ...baseQuestion,
          type: 'ordering',
          items,
          correctOrder,
        } as OrderingQuestion;
        break;
      case 'matching':
        finalQuestion = {
          ...baseQuestion,
          type: 'matching',
          leftItems,
          rightItems,
          correctLeftToRight: correctPairs,
          correctRightToLeft: correctPairs.map((pair) => [pair[1], pair[0]]),
        } as MatchingQuestion;
        break;
      default:
        throw new Error(`Unsupported question type: ${questionType}`);
    }

    if (onSave) {
      onSave(finalQuestion);
    }
  };

  // Function to show how a fill-in question will look
  const getFormattedFillInQuestion = () => {
    const questionText = form.getFieldValue('questionText') || '';
    if (!questionText) return 'Nhập nội dung câu hỏi';

    const correctAnswer = form.getFieldValue('correctAnswer') || '';
    const parts = questionText.split('__________');

    if (parts.length === 1) {
      return (
        <>
          {questionText}{' '}
          <span className="tailwind-text-blue-500 tailwind-font-semibold tailwind-italic">
            [{correctAnswer}]
          </span>
        </>
      );
    }

    return parts.map((part: string, index: number) => (
      <React.Fragment key={index}>
        {part}
        {index < parts.length - 1 && (
          <span className="tailwind-text-blue-500 tailwind-font-semibold tailwind-underline">
            {correctAnswer}
          </span>
        )}
      </React.Fragment>
    ));
  };

  // Add blank placeholder for fill-in questions
  const addBlankPlaceholder = () => {
    const questionText = form.getFieldValue('questionText') || '';
    // Get cursor position - not possible with antd directly, so we'll append to the end
    form.setFieldsValue({
      questionText: questionText + '__________ ',
    });
  };

  // Question Type Selector
  const renderQuestionTypeSelector = () => {
    return (
      <Form.Item
        name="questionType"
        label="Loại câu hỏi"
        className="tailwind-mb-4"
      >
        <Select
          onChange={(value) => handleTypeChange(value as QuestionType)}
          disabled={!!restrictQuestionType}
        >
          <Select.Option
            value="multiplechoice"
            disabled={
              restrictQuestionType && restrictQuestionType !== 'multiplechoice'
            }
          >
            Trắc nghiệm
          </Select.Option>
          <Select.Option
            value="truefalse"
            disabled={
              restrictQuestionType && restrictQuestionType !== 'truefalse'
            }
          >
            Đúng/Sai
          </Select.Option>
          <Select.Option
            value="fillin"
            disabled={restrictQuestionType && restrictQuestionType !== 'fillin'}
          >
            Điền vào chỗ trống
          </Select.Option>
          <Select.Option
            value="ordering"
            disabled={
              restrictQuestionType && restrictQuestionType !== 'ordering'
            }
          >
            Sắp xếp thứ tự
          </Select.Option>
          <Select.Option
            value="matching"
            disabled={
              restrictQuestionType && restrictQuestionType !== 'matching'
            }
          >
            Ghép cặp
          </Select.Option>
        </Select>
      </Form.Item>
    );
  };

  // Phương thức để thêm mục bên trái và phải riêng biệt
  const handleAddLeftItem = () => {
    setLeftItems([...leftItems, '']);
  };

  const handleAddRightItem = () => {
    setRightItems([...rightItems, '']);
  };

  return (
    <div className="tailwind-bg-white">
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          questionType: restrictQuestionType || questionType,
          difficulty: 'medium',
          points: 10,
          isCorrect: true,
        }}
      >
        {/* Question Type Selector */}
        {renderQuestionTypeSelector()}

        <Space direction="vertical" className="tailwind-w-full">
          {/* Question Text */}
          <div className="tailwind-mb-4">
            <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-1">
              <Form.Item
                name="questionText"
                label="Nội dung câu hỏi"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập nội dung câu hỏi',
                  },
                ]}
                className="tailwind-w-full tailwind-mb-0"
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập nội dung câu hỏi, ví dụ: Thủ đô của Việt Nam là gì?"
                />
              </Form.Item>

              {questionType === 'fillin' && (
                <Button
                  type="primary"
                  icon={<AddOulinedIcon />}
                  onClick={addBlankPlaceholder}
                  className="tailwind-ml-2 tailwind-mt-7"
                >
                  Thêm ô trống
                </Button>
              )}
            </div>

            <Text type="secondary" className="tailwind-text-xs">
              Hãy nhập câu hỏi rõ ràng và dễ hiểu
              {questionType === 'fillin' &&
                ". Sử dụng nút 'Thêm ô trống' để thêm dấu gạch ngang đại diện cho chỗ trống"}
            </Text>

            {/* Preview for fill-in-the-blank questions */}
            {questionType === 'fillin' && (
              <Card className="tailwind-mt-2" size="small" title="Xem trước">
                <p>{getFormattedFillInQuestion()}</p>
              </Card>
            )}
          </div>

          {/* Question Image */}
          <Form.Item label="Hình ảnh" className="tailwind-mb-4">
            <Upload
              listType="picture-card"
              showUploadList={false}
              beforeUpload={() => false}
              onChange={handleImageChange}
            >
              {previewUrl ? (
                <div className="tailwind-relative">
                  <img
                    src={previewUrl}
                    alt="Question"
                    className="tailwind-w-full"
                  />
                  <Button
                    type="text"
                    className="tailwind-absolute tailwind-top-0 tailwind-right-0"
                    icon={<DeleteIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      clearImageSelection();
                    }}
                  />
                </div>
              ) : (
                <div>
                  <ArrowUploadIcon />
                  <div className="tailwind-mt-2">Tải ảnh lên</div>
                </div>
              )}
            </Upload>
            <Text type="secondary" className="tailwind-text-xs">
              Thêm hình ảnh minh họa cho câu hỏi (nếu có)
            </Text>
          </Form.Item>

          {/* Question Type Specific Answer Fields */}
          <Card className="tailwind-mb-4" title="Thiết lập đáp án" size="small">
            {/* Multiple Choice */}
            {questionType === 'multiplechoice' && (
              <div>
                <p className="tailwind-mb-2 tailwind-text-xs tailwind-text-gray-500">
                  Chọn một đáp án đúng và thêm ít nhất 2-4 đáp án nhiễu. Đáp án
                  được chọn sẽ là đáp án đúng.
                </p>

                <Form.Item
                  name="correctOptionIndex"
                  className="tailwind-hidden"
                  initialValue={0}
                >
                  <InputNumber />
                </Form.Item>

                <MultipleChoiceQuestionComponent
                  question={{
                    id: question?.id || 'temp-id',
                    type: 'multiplechoice',
                    questionText: form.getFieldValue('questionText') || '',
                    options: options,
                    correctOptionIndex:
                      form.getFieldValue('correctOptionIndex') || 0,
                    difficulty: form.getFieldValue('difficulty') || 'medium',
                    points: form.getFieldValue('points') || 10,
                  }}
                  onAnswer={() => {}}
                  configMode={true}
                  onOptionTextChange={(index, text) =>
                    handleOptionChange(index, text)
                  }
                  onCorrectAnswerChange={(index) => {
                    // Cập nhật giá trị correctOptionIndex trong form
                    form.setFieldsValue({ correctOptionIndex: index });
                  }}
                  onAddOption={handleAddOption}
                  onRemoveOption={handleRemoveOption}
                />
              </div>
            )}

            {/* True/False */}
            {questionType === 'truefalse' && (
              <div>
                <p className="tailwind-mb-2 tailwind-text-xs tailwind-text-gray-500">
                  Chọn "Đúng" nếu phát biểu trong câu hỏi là đúng, chọn "Sai"
                  nếu phát biểu là sai.
                </p>
                <Form.Item name="isCorrect" className="tailwind-mb-0">
                  <Radio.Group>
                    <Radio value={true}>Đúng</Radio>
                    <Radio value={false}>Sai</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
            )}

            {/* Fill In */}
            {questionType === 'fillin' && (
              <div>
                <Form.Item
                  name="correctAnswer"
                  label="Đáp án đúng"
                  rules={[
                    { required: true, message: 'Vui lòng nhập đáp án đúng' },
                  ]}
                >
                  <Input placeholder="Nhập đáp án đúng, ví dụ: Hà Nội" />
                </Form.Item>

                <Form.Item name="caseSensitive" valuePropName="checked">
                  <Checkbox>Phân biệt chữ hoa/thường</Checkbox>
                </Form.Item>
                <Text type="secondary" className="tailwind-text-xs">
                  Khi bật tùy chọn này, người dùng phải nhập chính xác cả chữ
                  hoa và chữ thường.
                </Text>
              </div>
            )}

            {/* Ordering */}
            {questionType === 'ordering' && (
              <div>
                <p className="tailwind-mb-2 tailwind-text-xs tailwind-text-gray-500">
                  Thêm các mục theo đúng thứ tự. Khi hiển thị cho người dùng,
                  các mục sẽ được xáo trộn.
                </p>
                <Space className="tailwind-w-full" direction="vertical">
                  {items.map((item, index) => (
                    <div
                      key={index}
                      className="tailwind-flex tailwind-items-center"
                    >
                      <span className="tailwind-mr-2 tailwind-font-medium">
                        {index + 1}.
                      </span>
                      <Input
                        value={item}
                        onChange={(e) =>
                          handleItemChange(index, e.target.value)
                        }
                        placeholder={`Mục ${index + 1}`}
                        className="tailwind-flex-grow"
                      />
                      <Button
                        type="text"
                        danger
                        icon={<DeleteIcon />}
                        onClick={() => handleRemoveItem(index)}
                        disabled={items.length <= 2}
                        className="tailwind-ml-2"
                      />
                    </div>
                  ))}
                </Space>

                <Button
                  type="dashed"
                  onClick={handleAddItem}
                  icon={<AddOulinedIcon />}
                  className="tailwind-mt-2 tailwind-w-full"
                >
                  Thêm mục
                </Button>

                <Text type="secondary" className="tailwind-block tailwind-mt-2">
                  Lưu ý: Các mục sẽ được hiển thị ngẫu nhiên cho người dùng
                </Text>
              </div>
            )}

            {/* Matching */}
            {questionType === 'matching' && (
              <div>
                <p className="tailwind-mb-2 tailwind-text-xs tailwind-text-gray-500">
                  Thêm các mục cần ghép và thiết lập kết nối. Hai cột có thể có
                  số lượng mục khác nhau và một mục có thể được ghép với nhiều
                  mục khác.
                </p>

                {/* Phần thêm các mục */}
                <div className="tailwind-p-3 tailwind-bg-gray-50 tailwind-rounded-lg tailwind-mb-4">
                  <div className="tailwind-flex tailwind-justify-between tailwind-mb-2">
                    <div className="tailwind-font-medium">Các mục cần ghép</div>
                    <div className="tailwind-text-xs tailwind-text-gray-500">
                      Hai bên có thể có số lượng khác nhau
                    </div>
                  </div>

                  <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4 tailwind-mb-2">
                    <div className="tailwind-border tailwind-rounded-lg tailwind-p-3">
                      <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
                        <div className="tailwind-font-medium tailwind-text-indigo-800">
                          Mục bên trái (A)
                        </div>
                        <Button
                          type="primary"
                          size="small"
                          icon={<AddOulinedIcon />}
                          onClick={handleAddLeftItem}
                        >
                          Thêm
                        </Button>
                      </div>

                      {leftItems.map((item, index) => (
                        <div
                          key={index}
                          className="tailwind-flex tailwind-items-center tailwind-mb-2"
                        >
                          <span className="tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-w-7 tailwind-h-7 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mr-2">
                            {index + 1}
                          </span>
                          <Input
                            value={item}
                            onChange={(e) =>
                              handleLeftItemChange(index, e.target.value)
                            }
                            placeholder={`Mục trái ${index + 1}, ví dụ: Hà Nội`}
                            className="tailwind-flex-grow"
                          />
                          <Button
                            type="text"
                            danger
                            icon={<DeleteIcon />}
                            onClick={() => {
                              if (leftItems.length > 1) {
                                // Xóa mục bên trái
                                const newLeftItems = [...leftItems];
                                newLeftItems.splice(index, 1);
                                setLeftItems(newLeftItems);

                                // Xóa tất cả kết nối liên quan đến mục này
                                const newPairs = correctPairs
                                  .filter((pair) => pair[0] !== index)
                                  .map((pair) => [
                                    pair[0] > index ? pair[0] - 1 : pair[0],
                                    pair[1],
                                  ]);
                                setCorrectPairs(newPairs);
                              } else {
                                message.warning(
                                  'Phải có ít nhất 1 mục bên trái'
                                );
                              }
                            }}
                            disabled={leftItems.length <= 1}
                            className="tailwind-ml-2"
                          />
                        </div>
                      ))}
                    </div>

                    <div className="tailwind-border tailwind-rounded-lg tailwind-p-3">
                      <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
                        <div className="tailwind-font-medium tailwind-text-green-800">
                          Mục bên phải (B)
                        </div>
                        <Button
                          type="primary"
                          size="small"
                          icon={<AddOulinedIcon />}
                          onClick={handleAddRightItem}
                        >
                          Thêm
                        </Button>
                      </div>

                      {rightItems.map((item, index) => (
                        <div
                          key={index}
                          className="tailwind-flex tailwind-items-center tailwind-mb-2"
                        >
                          <span className="tailwind-bg-green-100 tailwind-text-green-800 tailwind-w-7 tailwind-h-7 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mr-2">
                            {String.fromCharCode(65 + index)}
                          </span>
                          <Input
                            value={item}
                            onChange={(e) =>
                              handleRightItemChange(index, e.target.value)
                            }
                            placeholder={`Mục phải ${
                              index + 1
                            }, ví dụ: Việt Nam`}
                            className="tailwind-flex-grow"
                          />
                          <Button
                            type="text"
                            danger
                            icon={<DeleteIcon />}
                            onClick={() => {
                              if (rightItems.length > 1) {
                                // Xóa mục bên phải
                                const newRightItems = [...rightItems];
                                newRightItems.splice(index, 1);
                                setRightItems(newRightItems);

                                // Xóa tất cả kết nối liên quan đến mục này
                                const newPairs = correctPairs
                                  .filter((pair) => pair[1] !== index)
                                  .map((pair) => [
                                    pair[0],
                                    pair[1] > index ? pair[1] - 1 : pair[1],
                                  ]);
                                setCorrectPairs(newPairs);
                              } else {
                                message.warning(
                                  'Phải có ít nhất 1 mục bên phải'
                                );
                              }
                            }}
                            disabled={rightItems.length <= 1}
                            className="tailwind-ml-2"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Phần thiết lập kết nối */}
                <div className="tailwind-p-3 tailwind-bg-gray-50 tailwind-rounded-lg tailwind-mb-4">
                  <p className="tailwind-mb-3 tailwind-font-medium">
                    Thiết lập kết nối đúng:
                  </p>
                  <p className="tailwind-mb-2 tailwind-text-xs tailwind-text-gray-500">
                    Click vào các nút để thiết lập hoặc loại bỏ kết nối. Mỗi mục
                    có thể kết nối với nhiều mục khác.
                  </p>

                  <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4">
                    <div className="tailwind-border tailwind-rounded-lg tailwind-p-3">
                      <div className="tailwind-font-medium tailwind-mb-2 tailwind-text-indigo-800">
                        Mục bên trái (A)
                      </div>
                      {leftItems.map((leftItem, leftIndex) => (
                        <div
                          key={leftIndex}
                          className="tailwind-p-2 tailwind-border tailwind-rounded tailwind-mb-2 tailwind-bg-white"
                        >
                          <div className="tailwind-flex tailwind-items-center">
                            <span className="tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-w-7 tailwind-h-7 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mr-2">
                              {leftIndex + 1}
                            </span>
                            <span className="tailwind-font-medium">
                              {leftItem || '...'}
                            </span>
                          </div>

                          <div className="tailwind-mt-2 tailwind-pl-9">
                            <p className="tailwind-text-xs tailwind-text-gray-500 tailwind-mb-1">
                              Kết nối với:
                            </p>
                            <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1">
                              {rightItems.map((rightItem, rightIndex) => {
                                const isConnected = correctPairs.some(
                                  (pair) =>
                                    pair[0] === leftIndex &&
                                    pair[1] === rightIndex
                                );
                                return (
                                  <Button
                                    key={rightIndex}
                                    size="small"
                                    type={isConnected ? 'primary' : 'default'}
                                    onClick={() => {
                                      if (isConnected) {
                                        handleRemoveConnection(
                                          leftIndex,
                                          rightIndex
                                        );
                                      } else {
                                        handleConnectItems(
                                          leftIndex,
                                          rightIndex
                                        );
                                      }
                                    }}
                                    className="tailwind-mb-1 tailwind-flex tailwind-items-center"
                                  >
                                    <span
                                      className={`tailwind-w-5 tailwind-h-5 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-mr-1 ${
                                        isConnected
                                          ? 'tailwind-bg-white tailwind-text-primary'
                                          : 'tailwind-bg-green-100 tailwind-text-green-800'
                                      }`}
                                    >
                                      {String.fromCharCode(65 + rightIndex)}
                                    </span>
                                    {rightItem || '...'}
                                  </Button>
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="tailwind-border tailwind-rounded-lg tailwind-p-3">
                      <div className="tailwind-font-medium tailwind-mb-2 tailwind-text-green-800">
                        Mục bên phải (B)
                      </div>
                      {rightItems.map((rightItem, rightIndex) => (
                        <div
                          key={rightIndex}
                          className="tailwind-p-2 tailwind-border tailwind-rounded tailwind-mb-2 tailwind-bg-white"
                        >
                          <div className="tailwind-flex tailwind-items-center">
                            <span className="tailwind-bg-green-100 tailwind-text-green-800 tailwind-w-7 tailwind-h-7 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mr-2">
                              {String.fromCharCode(65 + rightIndex)}
                            </span>
                            <span className="tailwind-font-medium">
                              {rightItem || '...'}
                            </span>
                          </div>

                          <div className="tailwind-mt-2 tailwind-pl-9">
                            <p className="tailwind-text-xs tailwind-text-gray-500 tailwind-mb-1">
                              Kết nối với:
                            </p>
                            <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1">
                              {leftItems.map((leftItem, leftIndex) => {
                                const isConnected = correctPairs.some(
                                  (pair) =>
                                    pair[0] === leftIndex &&
                                    pair[1] === rightIndex
                                );
                                return (
                                  <Button
                                    key={leftIndex}
                                    size="small"
                                    type={isConnected ? 'primary' : 'default'}
                                    onClick={() => {
                                      if (isConnected) {
                                        handleRemoveConnection(
                                          leftIndex,
                                          rightIndex
                                        );
                                      } else {
                                        handleConnectItems(
                                          leftIndex,
                                          rightIndex
                                        );
                                      }
                                    }}
                                    className="tailwind-mb-1 tailwind-flex tailwind-items-center"
                                  >
                                    <span
                                      className={`tailwind-w-5 tailwind-h-5 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-mr-1 ${
                                        isConnected
                                          ? 'tailwind-bg-white tailwind-text-primary'
                                          : 'tailwind-bg-indigo-100 tailwind-text-indigo-800'
                                      }`}
                                    >
                                      {leftIndex + 1}
                                    </span>
                                    {leftItem || '...'}
                                  </Button>
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Kết nối hiện tại */}
                <div className="tailwind-p-3 tailwind-bg-gray-50 tailwind-rounded-lg tailwind-mb-4">
                  <p className="tailwind-mb-2 tailwind-font-medium">
                    Các kết nối hiện tại:
                  </p>
                  {correctPairs.length > 0 ? (
                    <div className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-2 tailwind-gap-2">
                      {correctPairs.map((pair, index) => (
                        <div
                          key={index}
                          className="tailwind-flex tailwind-items-center tailwind-p-2 tailwind-bg-white tailwind-rounded tailwind-border"
                        >
                          <div className="tailwind-flex-grow tailwind-flex tailwind-items-center">
                            <span className="tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-w-6 tailwind-h-6 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mr-1 tailwind-text-xs">
                              {pair[0] + 1}
                            </span>
                            <span className="tailwind-font-medium tailwind-mr-1">
                              {leftItems[pair[0]] || '...'}
                            </span>
                            <ArrowRightOutlinedIcon className="tailwind-mx-1 tailwind-text-gray-400" />
                            <span className="tailwind-bg-green-100 tailwind-text-green-800 tailwind-w-6 tailwind-h-6 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mx-1 tailwind-text-xs">
                              {String.fromCharCode(65 + pair[1])}
                            </span>
                            <span className="tailwind-font-medium">
                              {rightItems[pair[1]] || '...'}
                            </span>
                          </div>
                          <Button
                            type="text"
                            danger
                            icon={<DeleteIcon />}
                            onClick={() =>
                              handleRemoveConnection(pair[0], pair[1])
                            }
                            size="small"
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="tailwind-text-center tailwind-py-4 tailwind-text-gray-500">
                      Chưa có kết nối nào được thiết lập
                    </div>
                  )}
                </div>

                <Text type="secondary" className="tailwind-block tailwind-mt-2">
                  Lưu ý: Các mục sẽ được hiển thị ngẫu nhiên cho người dùng
                </Text>
              </div>
            )}
          </Card>

          {/* Explanation */}
          <Form.Item
            name="explanation"
            label="Giải thích"
            className="tailwind-mb-4"
          >
            <TextArea
              rows={2}
              placeholder="Nhập giải thích cho đáp án. Giải thích sẽ hiển thị sau khi người dùng trả lời."
            />
          </Form.Item>
          <Text
            type="secondary"
            className="tailwind-block tailwind-mb-4 tailwind-text-xs"
          >
            Giải thích giúp người học hiểu rõ hơn về đáp án đúng và lý do tại
            sao
          </Text>

          {/* Hint */}
          <Form.Item name="hint" label="Gợi ý" className="tailwind-mb-4">
            <Input placeholder="Nhập gợi ý cho người dùng" />
          </Form.Item>
          <Text
            type="secondary"
            className="tailwind-block tailwind-mb-4 tailwind-text-xs"
          >
            Gợi ý sẽ hiển thị khi người dùng cần trợ giúp
          </Text>

          {/* Difficulty and Points */}
          <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4 tailwind-mb-4">
            <Form.Item
              name="difficulty"
              label="Độ khó"
              className="tailwind-mb-0"
            >
              <Select>
                <Select.Option value="easy">Dễ</Select.Option>
                <Select.Option value="medium">Trung bình</Select.Option>
                <Select.Option value="hard">Khó</Select.Option>
                <Select.Option value="expert">Chuyên gia</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="points"
              label="Điểm"
              rules={[{ required: true, message: 'Vui lòng nhập điểm' }]}
              className="tailwind-mb-0"
            >
              <InputNumber min={1} className="tailwind-w-full" />
            </Form.Item>
          </div>
          <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4 tailwind-mb-4">
            <Text type="secondary" className="tailwind-text-xs">
              Độ khó ảnh hưởng đến cách hệ thống sắp xếp và trình bày câu hỏi
            </Text>
            <Text type="secondary" className="tailwind-text-xs">
              Điểm số người dùng sẽ nhận được khi trả lời đúng câu hỏi này
            </Text>
          </div>

          {/* Tags */}
          <Form.Item label="Thẻ gắn kèm" className="tailwind-mb-4">
            <div>
              <p className="tailwind-mb-2 tailwind-text-xs tailwind-text-gray-500">
                Thêm các thẻ để phân loại câu hỏi và dễ dàng tìm kiếm sau này
              </p>

              <div className="tailwind-mb-2">
                {tags.map((tag) => (
                  <Tag
                    key={tag}
                    closable
                    onClose={() => handleRemoveTag(tag)}
                    className="tailwind-mb-1"
                  >
                    {tag}
                  </Tag>
                ))}
              </div>

              <Input
                placeholder="Thêm thẻ và nhấn Enter"
                value={tagInput}
                onChange={handleTagInputChange}
                onKeyDown={handleTagInputKeyDown}
                suffix={
                  <Button
                    type="text"
                    onClick={handleAddTag}
                    icon={<AddOulinedIcon />}
                  />
                }
              />
            </div>
          </Form.Item>
        </Space>
      </Form>
    </div>
  );
});

export default QuestionFormComponent;

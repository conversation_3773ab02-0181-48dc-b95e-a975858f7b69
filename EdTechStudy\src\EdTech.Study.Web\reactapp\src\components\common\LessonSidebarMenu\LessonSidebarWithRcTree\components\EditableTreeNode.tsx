import React from 'react';
import { Input } from 'antd';
import { TreeMenuItem } from '../types';
import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';

interface EditableTreeNodeProps {
  nodeData: TreeMenuItem;
  editingItemValue: string;
  level: number;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSaveEdit: (key: string) => void;
  handleKeyDown: (e: React.KeyboardEvent, key: string) => void;
}

const EditableTreeNode: React.FC<EditableTreeNodeProps> = ({
  nodeData,
  editingItemValue,
  level,
  handleInputChange,
  handleSaveEdit,
  handleKeyDown,
}) => {
  const paddingStyle = {
    paddingLeft: `${level * 16}px`,
  };

  return (
    <div
      className={`rc-tree-node-content ${
        nodeData.type === ETypeEdTechComponent.LAYOUT
          ? `tailwind-font-roboto tailwind-font-semibold tailwind-text-[#908588]`
          : ''
      }`}
      style={paddingStyle}
      onClick={(e) => e.stopPropagation()}
    >
      <Input
        value={editingItemValue}
        onChange={handleInputChange}
        onBlur={() => handleSaveEdit(nodeData.key)}
        onKeyDown={(e) => handleKeyDown(e, nodeData.key)}
        autoFocus
        className="edit-item-input"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      />
    </div>
  );
};

export default EditableTreeNode;

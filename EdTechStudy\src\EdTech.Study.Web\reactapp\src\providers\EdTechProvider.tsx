import { createContext, useContext, useMemo } from 'react';
import {
  IEdTechContext,
  IEdTechProviderProps,
  IEdTechProviderPropsMapDispatchToProps,
  IEdTechProviderPropsMapStateToProps,
} from './EdTechProvider.interface';
import { connect } from 'react-redux';
import LessonRenderComponent from '../components/common/LessonRenderComponent/LessonRenderComponent';
import usePubSubListeners from '../hooks/apps/usePubSubListeners';
import { EdTechRootState } from '../store/store';
import { IEdTechRenderTreeData } from '../interfaces/AppComponents';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { ETypeMode } from '../enums/AppEnums';
import { Layout } from 'antd';
import LessonConfigWrapper from '../components/common/LessonConfigWrapper/LessonConfigWrapper';
import { Content } from 'antd/es/layout/layout';

const EdTechContext = createContext<IEdTechContext>({
  edTechRenderTreeData: undefined,
});
export const useEdTechContext = () => useContext(EdTechContext);
const EdTechProvider = (props: IEdTechProviderProps) => {
  const { edTechRenderTreeData, isInitialized, mode } = props;
  usePubSubListeners();

  const buildDataPreview = (data: IEdTechRenderTreeData) => {
    // kiểm tra và thay đổi, nếu data có name là "LessonCommonComponent" thì thay đổi thành "LESSON_COMMON_STRUCTURE"
    const stringifyData = JSON.stringify(data);
    const newData = stringifyData.replace(
      /"name":"LessonCommonComponent"/g,
      '"name":"PreviewLessonComponent"'
    );
    data = JSON.parse(newData);
    return data;
  };

  const value = useMemo(() => {
    if (mode === ETypeMode.PRESENT && edTechRenderTreeData) {
      const dataPreview = buildDataPreview(edTechRenderTreeData);
      return { edTechRenderTreeData: dataPreview };
    }
    return { edTechRenderTreeData };
  }, [edTechRenderTreeData, mode]);

  if (!value.edTechRenderTreeData) {
    return <></>;
  }
  // isInitialized là true thì hiển thị loading
  if (!isInitialized) {
    return <LoadingScreen fullscreen />;
  }
  return (
    <EdTechContext.Provider value={value}>
      <LessonConfigWrapper>
        <Layout className="tailwind-h-full">
          <Content className="tailwind-h-full">
            <LessonRenderComponent data={value.edTechRenderTreeData} />
          </Content>
        </Layout>
      </LessonConfigWrapper>
    </EdTechContext.Provider>
  );
};

const mapStateToProps = ({
  appConfig,
  edTechRenderTreeData,
}: EdTechRootState): IEdTechProviderPropsMapStateToProps => {
  return {
    mode: appConfig.mode,
    edTechRenderTreeData: edTechRenderTreeData.data,
    isInitialized: edTechRenderTreeData.isInitialized,
  };
};
const mapDispatchToProps = (): IEdTechProviderPropsMapDispatchToProps => {
  return {};
};

export const EdTechProviderContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(EdTechProvider);

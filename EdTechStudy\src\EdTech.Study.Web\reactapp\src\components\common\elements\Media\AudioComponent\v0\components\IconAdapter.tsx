import React from 'react';
import {
  PlayIcon,
  PauseCircleOutlinedIcon,
  SoundOutlinedIcon,
  RefreshIcon,
  EditIcon,
  DeleteIcon,
  ArrowDownloadIcon,
  BackwardIcon,
  FastForwardIcon,
  VolumeMuteOutLinedIcon,
  FastBackwardFilledIcon,
  FastForwardFilledIcon,
} from '../../../../../../icons/IconIndex';

// Interface for icon wrapper props
interface IconWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: number;
}

export const PlayCircle: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <PlayIcon width={size} height={size} />
  </div>
);

export const PauseCircle: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <PauseCircleOutlinedIcon width={size} height={size} />
  </div>
);

export const SkipBack: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <FastBackwardFilledIcon width={size} height={size} />
  </div>
);

export const SkipForward: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <FastForwardFilledIcon width={size} height={size} />
  </div>
);

export const Volume2: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <SoundOutlinedIcon width={size} height={size} />
  </div>
);

export const Repeat: React.FC<IconWrapperProps> = ({ size = 20, ...props }) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <RefreshIcon width={size} height={size} />
  </div>
);

export const Edit: React.FC<IconWrapperProps> = ({ size = 20, ...props }) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <EditIcon width={size} height={size} />
  </div>
);

export const Trash2: React.FC<IconWrapperProps> = ({ size = 20, ...props }) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <DeleteIcon width={size} height={size} />
  </div>
);

export const Download: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <ArrowDownloadIcon width={size} height={size} />
  </div>
);

export const Forward: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <FastForwardIcon width={size} height={size} />
  </div>
);

export const Rewind: React.FC<IconWrapperProps> = ({ size = 20, ...props }) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <BackwardIcon width={size} height={size} />
  </div>
);

export const VolumeMute: React.FC<IconWrapperProps> = ({
  size = 20,
  ...props
}) => (
  <div
    style={{
      width: size,
      height: size,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    {...props}
  >
    <VolumeMuteOutLinedIcon width={size} height={size} />
  </div>
);

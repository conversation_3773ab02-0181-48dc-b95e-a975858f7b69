import { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import '../styles/modal-fixes.css';
import { Provider } from 'react-redux';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { questionStore } from '../store/questionStore';
import EdTechQuestionProvider from '../providers/EdTechQuestionProvider';
import QuestionsManager from '../utils/QuestionsManager';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';

// Register Syncfusion license
registerLicenseSyncfusionBase();

QuestionsManager.initQuestionsData();
export const QuestionPage = () => {
  return (
    <Provider store={questionStore}>
      <Suspense fallback={<LoadingScreen />}>
        <EdTechQuestionProvider />
      </Suspense>
    </Provider>
  );
};

const rootQuestionPageElement = document.getElementById(
  'QuestionPage'
) as HTMLElement;

const rootQuestionPage = ReactDOM.createRoot(rootQuestionPageElement);
rootQuestionPage.render(<QuestionPage />);

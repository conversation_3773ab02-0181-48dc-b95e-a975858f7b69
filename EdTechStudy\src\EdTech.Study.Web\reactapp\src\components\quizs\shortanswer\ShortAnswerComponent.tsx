import React, { useContext, useEffect, useState } from 'react';
import {
  Card,
  Button,
  message,
  Typography,
  Input,
  Form,
  Tooltip,
  PopconfirmProps,
  InputNumber,
  Switch,
  Row,
  Col,
  Collapse,
  Space,
} from 'antd';
import {
  CheckOutlined,
  DeleteFilled,
  QuestionOutlined,
} from '@ant-design/icons';
import { PracticeEngineContext } from '../../../interfaces/quizs/questionBase';
import '../quiz/QuizAnimations.css';
import {
  ShortAnswerAnswer,
  ShortAnswerComponentProps,
} from '../../../interfaces/quizs/shortAnswer.interface';
import { Guid } from 'guid-typescript';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import DOMPurify from 'dompurify';
import practiceLocalization, { quizLocalization } from '../localization';

const { Title, Text } = Typography;
const { TextArea } = Input;

// Define modules for the rich text editor
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image'],
    ['clean'],
  ],
};

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
];

const ShortAnswerComponent: React.FC<ShortAnswerComponentProps> = ({
  question,
  onComplete,
  showFeedback = true,
  allowManyTimes = false,
  configMode = false,
  disabled = false,
  hideSaveButton = false,
}) => {
  const [userAnswer, setUserAnswer] = useState<ShortAnswerAnswer>({
    id: '',
    text: '',
    isCorrect: false,
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const {
    handleChangeQuestion,
    handleDeleteQuestion: externalHandleDeleteQuestion,
  } = useContext(PracticeEngineContext);

  // Config mode states
  const [editedQuestion, setEditedQuestion] = useState<any>({
    ...question,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    setEditedQuestion({ ...question });
    if (question.userAnswer) {
      setUserAnswer(question.userAnswer);
      setIsSubmitted(true);
      setIsCorrect(question.status === 'correct');
    }
  }, [question]);

  // Handle input change
  const handleInputChange = (value: string) => {
    if (!isSubmitted) {
      setUserAnswer((prev) => ({ ...prev, text: value }));
      // Store answer without auto-submitting
      if (onComplete) {
        onComplete(question.id, { text: value } as ShortAnswerAnswer);
      }
    }
  };

  // Reset the question
  const handleReset = () => {
    setUserAnswer({
      id: Guid.create().toString(),
      text: '',
      isCorrect: false,
    });
    setIsSubmitted(false);
    setIsCorrect(false);
  };

  useEffect(() => {
    // Reset state when question changes
    handleReset();
  }, [question.id]);

  // Config mode functions
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuestion = { ...editedQuestion, title: e.target.value };
    setEditedQuestion(newQuestion);
  };

  const handleQuestionTextChange = (content: string) => {
    const newQuestion = { ...editedQuestion, question: content };
    setEditedQuestion(newQuestion);
  };

  const handleCorrectAnswerChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newQuestion = { ...editedQuestion, correctAnswer: e.target.value };
    setEditedQuestion(newQuestion);
  };

  const handleExplanationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newQuestion = { ...editedQuestion, explanation: e.target.value };
    setEditedQuestion(newQuestion);
  };

  const handlePointsChange = (value: number | null) => {
    const points = value || 1;
    const newQuestion = { ...editedQuestion, points };
    setEditedQuestion(newQuestion);
  };

  const handleCaseSensitiveChange = (checked: boolean) => {
    const newQuestion = { ...editedQuestion, caseSensitive: checked };
    setEditedQuestion(newQuestion);
  };

  const handlePartialMatchChange = (checked: boolean) => {
    const newQuestion = { ...editedQuestion, allowPartialMatch: checked };
    setEditedQuestion(newQuestion);
  };

  const handleMaxLengthChange = (value: number | null) => {
    const maxLength = value || 0;
    const newQuestion = { ...editedQuestion, maxLength };
    setEditedQuestion(newQuestion);
  };

  // Handle save configuration
  const handleSaveConfig = () => {
    // Validate the question before saving
    const hasTitle = editedQuestion.title.trim() !== '';
    const hasQuestionText = editedQuestion.question.trim() !== '';
    const hasCorrectAnswer = editedQuestion.correctAnswer.trim() !== '';

    if (!hasTitle) {
      message.error('Tiêu đề không được để trống!');
      return;
    }

    if (!hasQuestionText) {
      message.error('Nội dung câu hỏi không được để trống!');
      return;
    }

    if (!hasCorrectAnswer) {
      message.error('Đáp án đúng không được để trống!');
      return;
    }

    handleChangeQuestion(editedQuestion);
    message.success('Lưu cấu hình thành công!');
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.id);
  };

  const deleteCancel: PopconfirmProps['onCancel'] = () => {
    // Handle cancel logic if needed
  };

  // Create a variable for advanced configuration items
  const collapseItems = [
    {
      key: '1',
      label: practiceLocalization['Advanced Options'],
      children: (
        <>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Điểm">
                <InputNumber
                  value={editedQuestion.points || 1}
                  onChange={handlePointsChange}
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Phân biệt chữ hoa/thường">
                <Switch
                  checked={editedQuestion.caseSensitive || false}
                  onChange={handleCaseSensitiveChange}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Chấp nhận đáp án gần đúng">
                <Switch
                  checked={editedQuestion.allowPartialMatch || false}
                  onChange={handlePartialMatchChange}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="Giới hạn ký tự">
            <InputNumber
              value={editedQuestion.maxLength || 0}
              onChange={handleMaxLengthChange}
              min={0}
              placeholder="0 = không giới hạn"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item label="Giải thích đáp án">
            <TextArea
              rows={3}
              value={editedQuestion.explanation || ''}
              onChange={handleExplanationChange}
              placeholder="Nhập giải thích cho đáp án đúng"
            />
          </Form.Item>
        </>
      ),
    },
  ];

  // Render config mode
  if (configMode) {
    return (
      <Card className="quiz-card">
        <Form form={form} layout="vertical">
          <Form.Item required>
            <Input
              value={editedQuestion.title}
              onChange={handleTitleChange}
              placeholder="Nhập tiêu đề câu hỏi"
              variant="underlined"
            />
          </Form.Item>

          <Form.Item required>
            <ReactQuill
              theme="snow"
              value={editedQuestion.question}
              onChange={handleQuestionTextChange}
              placeholder="Nhập nội dung câu hỏi"
              modules={modules}
              formats={formats}
            />
          </Form.Item>

          <Form.Item label="Đáp án đúng" required>
            <TextArea
              rows={2}
              value={editedQuestion.correctAnswer}
              onChange={handleCorrectAnswerChange}
              placeholder="Nhập đáp án đúng"
            />
          </Form.Item>

          <Form.Item>
            <Collapse defaultActiveKey={[]} ghost items={collapseItems} />
          </Form.Item>
          <div>
            <Space>
              {!hideSaveButton && (
                <Button type="primary" onClick={handleSaveConfig}>
                  {quizLocalization.buttons.saveChanges}
                </Button>
              )}
              <PopconfirmAntdCustom
                title={quizLocalization.buttons.deleteQuestion.confirmTitle}
                onConfirm={deleteConfirm}
                onCancel={() => {}}
                okText={quizLocalization.buttons.deleteQuestion.yes}
                cancelText={quizLocalization.buttons.deleteQuestion.no}
              >
                <Button danger icon={<DeleteFilled />}>
                  {quizLocalization.buttons.deleteQuestion.button}
                </Button>
              </PopconfirmAntdCustom>
            </Space>
          </div>
        </Form>
      </Card>
    );
  }

  // Render normal quiz mode
  return (
    <Card
      title={question.title}
      className="short-answer-card"
      extra={
        <>
          <Tooltip title="Hướng dẫn">
            <Button
              icon={<QuestionOutlined />}
              type="text"
              onClick={() =>
                message.info('Nhập câu trả lời ngắn gọn và chính xác.')
              }
            />
          </Tooltip>
        </>
      }
    >
      <div className="short-answer-question tailwind-mb-2">
        <div
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(question.question),
          }}
        />
      </div>

      <div className="short-answer-input tailwind-mb-2">
        <TextArea
          value={userAnswer.text}
          onChange={(e) => handleInputChange(e.target.value)}
          disabled={isSubmitted}
          placeholder="Nhập câu trả lời của bạn"
          autoSize={{ minRows: 2, maxRows: 6 }}
          className={isSubmitted ? (isCorrect ? 'correct' : 'incorrect') : ''}
        />
      </div>

      {isSubmitted && question.explanation && showFeedback && (
        <div
          className={`short-answer-explanation ${
            isCorrect ? 'correct' : 'incorrect'
          }`}
          style={{
            marginTop: '1rem',
          }}
        >
          <Text strong>{isCorrect ? 'Giải thích:' : 'Đáp án đúng:'} </Text>
          <Text>{question.explanation}</Text>
        </div>
      )}
    </Card>
  );
};

export default ShortAnswerComponent;

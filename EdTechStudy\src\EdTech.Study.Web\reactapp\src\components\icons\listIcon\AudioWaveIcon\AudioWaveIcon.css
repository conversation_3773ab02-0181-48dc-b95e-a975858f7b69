/* AudioWaveIcon.css */

@keyframes audiowave-bounce {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(1.4);
  }
}

@keyframes audiowave-bounce-soft {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(1.1);
  }
}

.audiowave-icon {
  display: inline-block;
  vertical-align: middle;
}

.audiowave-bar {
  transform-origin: center;
  animation: audiowave-bounce 1s infinite ease-in-out;
}

.audiowave-bar-1 {
  animation-delay: 0s;
}

.audiowave-bar-2 {
  animation-delay: 0.1s;
}

.audiowave-bar-3 {
  animation-delay: 0.2s;
  animation: audiowave-bounce-soft 1s infinite ease-in-out;
}

.audiowave-bar-4 {
  animation-delay: 0.3s;
}

.audiowave-bar-5 {
  animation-delay: 0.4s;
}

.audiowave-bar-6 {
  animation-delay: 0.5s;
}

.audiowave-bar-7 {
  animation-delay: 0.6s;
}

/* Modifier classes cho các trạng thái kh<PERSON>c nhau */
.audiowave-icon--paused .audiowave-bar {
  animation-play-state: paused;
}

.audiowave-icon--slow .audiowave-bar {
  animation-duration: 1.5s;
}

.audiowave-icon--fast .audiowave-bar {
  animation-duration: 0.7s;
}

.audiowave-icon--muted .audiowave-bar {
  opacity: 0.5;
}
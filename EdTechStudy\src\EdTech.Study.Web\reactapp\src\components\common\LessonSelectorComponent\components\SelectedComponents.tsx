import React from 'react';
import { Button, Typography } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { IEdTechComponent } from '../../../../interfaces/AppComponents';

const { Text } = Typography;

interface SelectedComponentsProps {
  selectedComponents: IEdTechComponent[];
  onRemoveComponent: (componentName: string) => void;
  onClearAll: () => void;
}

const SelectedComponents: React.FC<SelectedComponentsProps> = ({
  selectedComponents,
  onRemoveComponent,
  onClearAll,
}) => {
  if (selectedComponents.length === 0) return null;

  return (
    <div className="tailwind-mb-3">
      <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
        <div className="tailwind-flex tailwind-items-center">
          <Text className="tailwind-text-sm tailwind-font-medium">
            <PERSON><PERSON> chọn ({selectedComponents.length})
          </Text>
        </div>
        {selectedComponents.length > 0 && (
          <Button
            type="link"
            danger
            size="small"
            onClick={onClearAll}
            className="tailwind-text-xs tailwind-font-normal tailwind-ml-auto"
          >
            Xóa tất cả
          </Button>
        )}
      </div>
      <div className="tailwind-flex tailwind-flex-nowrap tailwind-gap-2 tailwind-overflow-x-auto tailwind-pb-2 tailwind-scrollbar-thin tailwind-scrollbar-thumb-gray-200 tailwind-scrollbar-track-transparent">
        {selectedComponents.length <= 4 ? (
          selectedComponents.map((comp) => (
            <div
              key={comp.name}
              className="tailwind-flex-shrink-0 tailwind-flex tailwind-items-center tailwind-bg-blue-50 tailwind-border tailwind-border-blue-200 tailwind-rounded-lg tailwind-px-2 tailwind-py-1 tailwind-text-sm hover:tailwind-bg-blue-100 tailwind-transition-colors"
            >
              <Text
                ellipsis={{ tooltip: comp.title }}
                style={{ maxWidth: '120px' }}
              >
                {comp.title}
              </Text>
              <Button
                type="text"
                danger
                size="small"
                icon={<CloseOutlined style={{ fontSize: '10px' }} />}
                onClick={() => onRemoveComponent(comp.name)}
                className="tailwind-ml-1 tailwind-min-w-0 tailwind-w-4 tailwind-h-4 tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center hover:tailwind-bg-red-50 tailwind-rounded-full"
              />
            </div>
          ))
        ) : (
          <>
            {selectedComponents.slice(0, 3).map((comp) => (
              <div
                key={comp.name}
                className="tailwind-flex-shrink-0 tailwind-flex tailwind-items-center tailwind-bg-blue-50 tailwind-border tailwind-border-blue-200 tailwind-rounded-lg tailwind-px-2 tailwind-py-1 tailwind-text-sm hover:tailwind-bg-blue-100 tailwind-transition-colors"
              >
                <Text
                  ellipsis={{ tooltip: comp.title }}
                  style={{ maxWidth: '120px' }}
                >
                  {comp.title}
                </Text>
                <Button
                  type="text"
                  danger
                  size="small"
                  icon={<CloseOutlined style={{ fontSize: '10px' }} />}
                  onClick={() => onRemoveComponent(comp.name)}
                  className="tailwind-ml-1 tailwind-min-w-0 tailwind-w-4 tailwind-h-4 tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center hover:tailwind-bg-red-50 tailwind-rounded-full"
                />
              </div>
            ))}
            <div className="tailwind-flex-shrink-0 tailwind-flex tailwind-items-center tailwind-bg-gray-100 tailwind-border tailwind-border-gray-200 tailwind-rounded-lg tailwind-px-3 tailwind-py-1 tailwind-text-sm hover:tailwind-bg-gray-200 tailwind-transition-colors tailwind-cursor-pointer">
              <Text
                className="tailwind-text-gray-500"
                ellipsis={{
                  tooltip: selectedComponents
                    .slice(3)
                    .map((c) => c.title)
                    .join(','),
                }}
              >
                +{selectedComponents.length - 3} khác
              </Text>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SelectedComponents;

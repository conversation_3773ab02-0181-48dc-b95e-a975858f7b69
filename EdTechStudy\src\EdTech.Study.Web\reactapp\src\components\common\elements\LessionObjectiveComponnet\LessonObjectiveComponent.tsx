// Enhanced LessonObjectiveComponent.tsx
import React, { useState, useCallback } from 'react';
import { Button, Input, Tooltip } from 'antd';
import { z } from 'zod';
import { createComponentSchema } from '../../../../utils/schema/createComponentSchema';
import { IEdTechRenderProps } from '../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../hocs/withEdComponentParams/withEdComponentParams';
import { EngineContainer } from '../../engines';
import {
  AddOulinedIcon,
  DeleteIcon,
  StarOutlinedIcon,
} from '../../../icons/IconIndex';

// Types
interface ObjectiveItem {
  id: string;
  content: string;
  children?: ObjectiveItem[];
  level?: number;
}

interface LessonObjectiveProps {
  objectives?: ObjectiveItem[];
  title?: string;
  showNumbers?: boolean;
  showIcons?: boolean;
}

// Zod schema for component validation
export const lessonObjectiveSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    objectives: z
      .array(
        z.object({
          id: z.string(),
          content: z.string(),
          children: z.array(z.any()).optional(),
          level: z.number().optional(),
        })
      )
      .optional(),
    showNumbers: z.boolean().optional(),
    showIcons: z.boolean().optional(),
  },
});

// New Objective Form Component
const NewObjectiveForm: React.FC<{
  id: string;
  content: string;
  onChange: (id: string, content: string) => void;
  onSave: (id: string, content: string) => void;
  onRemove: (id: string) => void;
}> = ({ id, content, onChange, onSave, onRemove }) => {
  const handleBlur = () => {
    if (content.trim()) {
      onSave(id, content);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (content.trim()) {
        onSave(id, content);
      }
    }
  };

  return (
    <div className="tailwind-mb-3">
      <div className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-gap-2">
        <div className="tailwind-flex-grow">
          <Input.TextArea
            autoFocus
            placeholder="Nhập mục tiêu"
            value={content}
            onChange={(e) => onChange(id, e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="tailwind-w-full tailwind-border tailwind-rounded tailwind-p-2 tailwind-text-sm tailwind-resize-none focus:tailwind-outline-none focus:tailwind-shadow-sm"
            autoSize={{ minRows: 1, maxRows: 3 }}
          />
        </div>
        <Button
          type="primary"
          className=" tailwind-text-red-500 tailwind-bg-white tailwind-border-red-500 !tailwind-w-[26px] !tailwind-h-[26px] tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-rounded-[4px]"
          onClick={() => onRemove(id)}
          aria-label="Delete objective"
          icon={<DeleteIcon />}
        />
      </div>
    </div>
  );
};

// Editable Objective Component
const EditableObjective: React.FC<{
  objective: ObjectiveItem;
  isEditing: boolean;
  onContentChange: (id: string, content: string) => void;
  onDelete: (id: string) => void;
}> = ({ objective, isEditing, onContentChange, onDelete }) => {
  const [content, setContent] = useState(objective.content);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  const handleBlur = () => {
    if (content.trim() !== objective.content.trim()) {
      onContentChange(objective.id, content);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleBlur();
    }
  };

  return (
    <div className="tailwind-relative tailwind-mb-3">
      {isEditing ? (
        <div className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-gap-2">
          <div className="tailwind-flex-grow">
            <Input.TextArea
              value={content}
              onChange={handleContentChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              className="tailwind-w-full tailwind-border tailwind-rounded tailwind-p-2 tailwind-text-sm tailwind-resize-none focus:tailwind-outline-none focus:tailwind-shadow-sm"
              autoSize={{ minRows: 1, maxRows: 3 }}
            />
          </div>
          <Tooltip title="Xóa mục tiêu">
            <Button
              type="text"
              className="tailwind-p-1 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-text-red-600
              tailwind-border-red-600  tailwind-border-1 tailwind-w-[26px] tailwind-h-[26px]"
              onClick={() => onDelete(objective.id)}
              aria-label="Delete objective"
            >
              <DeleteIcon />
            </Button>
          </Tooltip>
        </div>
      ) : (
        <div
          className="tailwind-flex tailwind-items-start tailwind-mb-3 tailwind-rounded tailwind-p-3 tailwind-shadow-sm"
          style={{ backgroundColor: 'var(--edtt-color-primary-100)' }}
        >
          <div className="tailwind-flex tailwind-items-center tailwind-w-full">
            <span className="tailwind-mr-2 tailwind-flex-shrink-0 tailwind-flex tailwind-items-center tailwind-text-primary">
              <StarOutlinedIcon />
            </span>
            <div className="tailwind-flex-grow">
              <p className="tailwind-m-0 tailwind-text-sm">
                {objective.content}
              </p>
            </div>
          </div>
        </div>
      )}

      {objective.children && objective.children.length > 0 && (
        <div className="tailwind-ml-8 tailwind-mt-2">
          {objective.children.map((child) => (
            <EditableObjective
              key={child.id}
              objective={child}
              isEditing={isEditing}
              onContentChange={onContentChange}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Main Component
const LessonObjectiveComponent: React.FC<
  IEdTechRenderProps<LessonObjectiveProps>
> = (props) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;
  const [newObjectives, setNewObjectives] = useState<
    { id: string; content: string }[]
  >([]);

  // Use params or default values
  const objectives = params?.objectives || [];

  // Objective handlers - Memoized with useCallback
  const handleObjectivesChange = useCallback(
    (newObjectives: ObjectiveItem[]) => {
      addOrUpdateParamComponent({
        ...params,
        objectives: newObjectives,
      });
    },
    [params, addOrUpdateParamComponent]
  );

  const addNewObjectiveField = useCallback(() => {
    const tempId = `temp-${Date.now()}`;
    setNewObjectives((prev) => [...prev, { id: tempId, content: '' }]);
  }, []);

  const handleNewObjectiveChange = useCallback(
    (id: string, content: string) => {
      setNewObjectives((prev) =>
        prev.map((obj) => (obj.id === id ? { ...obj, content } : obj))
      );
    },
    []
  );

  const saveNewObjective = useCallback(
    (id: string, content: string) => {
      if (content.trim() === '') return;

      const newObjective: ObjectiveItem = {
        id: `obj-${Date.now()}`,
        content: content.trim(),
        level: 0,
      };

      handleObjectivesChange([...objectives, newObjective]);

      // Remove this temp objective from the new objectives list
      setNewObjectives((prev) => prev.filter((obj) => obj.id !== id));
    },
    [objectives, handleObjectivesChange]
  );

  const removeNewObjective = useCallback((id: string) => {
    setNewObjectives((prev) => prev.filter((obj) => obj.id !== id));
  }, []);

  // Optimized recursive operations on objectives
  const updateObjective = useCallback(
    (
      items: ObjectiveItem[],
      id: string,
      updatedContent: string
    ): ObjectiveItem[] => {
      return items.map((item) => {
        if (item.id === id) {
          return { ...item, content: updatedContent };
        }
        if (item.children && item.children.length > 0) {
          return {
            ...item,
            children: updateObjective(item.children, id, updatedContent),
          };
        }
        return item;
      });
    },
    []
  );

  const removeObjective = useCallback(
    (items: ObjectiveItem[], id: string): ObjectiveItem[] => {
      return items.filter((item) => {
        if (item.id === id) {
          return false;
        }
        if (item.children && item.children.length > 0) {
          item.children = removeObjective(item.children, id);
        }
        return true;
      });
    },
    []
  );

  const handleObjectiveContentChange = useCallback(
    (id: string, newContent: string) => {
      const updatedObjectives = updateObjective(objectives, id, newContent);
      handleObjectivesChange(updatedObjectives);
    },
    [objectives, updateObjective, handleObjectivesChange]
  );

  const handleDelete = useCallback(
    (id: string) => {
      const updatedObjectives = removeObjective(objectives, id);
      handleObjectivesChange(updatedObjectives);
    },
    [objectives, removeObjective, handleObjectivesChange]
  );

  // Empty state message component
  const EmptyState = () => (
    <div className="tailwind-text-center tailwind-text-gray-400 tailwind-py-4">
      Chưa có mục tiêu bài học nào. {isEditing ? 'Hãy thêm mục tiêu.' : ''}
    </div>
  );

  return (
    <EngineContainer
      node={props}
      mode="NONE_WRAPPER"
      showFullscreenButton={false}
      mainComponent={
        <>
          {objectives.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="tailwind-p-2 tailwind-space-y-3 tailwind-bg-default">
              {objectives.map((objective) => (
                <EditableObjective
                  key={objective.id}
                  objective={objective}
                  isEditing={isEditing}
                  onContentChange={handleObjectiveContentChange}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          )}

          {/* New objectives being added */}
          {isEditing && newObjectives.length > 0 && (
            <div className="tailwind-p-2 tailwind-space-y-3">
              {newObjectives.map((newObj) => (
                <NewObjectiveForm
                  key={newObj.id}
                  id={newObj.id}
                  content={newObj.content}
                  onChange={handleNewObjectiveChange}
                  onSave={saveNewObjective}
                  onRemove={removeNewObjective}
                />
              ))}
            </div>
          )}

          {/* Add objective button */}
          {isEditing && (
            <div className="tailwind-mt-4 tailwind-flex tailwind-justify-center">
              <Button
                type="primary"
                icon={<AddOulinedIcon />}
                onClick={addNewObjectiveField}
              >
                Thêm mục tiêu
              </Button>
            </div>
          )}
        </>
      }
    />
  );
};

export default withEdComponentParams(LessonObjectiveComponent);

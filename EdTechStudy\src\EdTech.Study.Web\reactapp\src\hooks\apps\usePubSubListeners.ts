import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { EventChange } from '../../constants/eventChange';
import {
  IEdTechRenderTreeData,
  IEdTechRenderParam,
} from '../../interfaces/AppComponents';
import { ConfigManager } from '../../utils/ConfigManager';
import pubsub from '../../utils/pubsub';

export default function usePubSubListeners() {
  const dispatch = useDispatch();
  useEffect(() => {
    // Đăng ký sự kiện EdTechRenderTreeDataSlice
    let renderTreeSubscriberId = pubsub.subscribe(
      EventChange.EdTechRenderTreeDataChange,
      async (eventData: { id: string; data: IEdTechRenderTreeData }) => {
        await ConfigManager.saveEdTechRenderTreeData(
          eventData.id,
          eventData.data
        );
      }
    );
    // Đăng ký sự kiện EdComponentParamsSlice
    let paramSubscriberId = pubsub.subscribe(
      EventChange.EdTechParamsChange,
      async (eventData: { id: string; data: IEdTechRenderParam[] }) => {
        await ConfigManager.saveEdComponentParams(eventData.id, eventData.data);
      }
    );

    return () => {
      // Huỷ bỏ các sự kiện đã đăng ký khi component bị unmount
      pubsub.unsubscribe(renderTreeSubscriberId);
      pubsub.unsubscribe(paramSubscriberId);
    };
  }, [dispatch]);
}

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Select,
  Button,
  Tooltip,
  Tag,
  Alert,
  Table,
  Typography,
  Space,
} from 'antd';
import { QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  SetTheoryConfig,
  SetElement,
  defaultSetTheoryConfig,
} from './SetTheoryConfig';
import { DeleteIcon } from '../../icons/IconIndex';

const { Text, Paragraph } = Typography;
const { Option } = Select;

interface SetTheorySimulatorComponentProps {
  config?: Partial<SetTheoryConfig>;
  currentSetType?: string;
  onSetTypeChange?: (value: string) => void;
  onDataChange?: (data: any) => void;
}

const SetTheorySimulatorComponent: React.FC<
  SetTheorySimulatorComponentProps
> = ({
  config = {},
  currentSetType: propCurrentSetType,
  onSetTypeChange,
  onDataChange,
}) => {
  // Merge default config with provided config
  const mergedConfig = {
    ...defaultSetTheoryConfig,
    ...config,
    options: {
      ...defaultSetTheoryConfig.options,
      ...(config.options || {}),
    },
    theme: {
      ...defaultSetTheoryConfig.theme,
      ...(config.theme || {}),
    },
    titleProps: {
      ...defaultSetTheoryConfig.titleProps,
      ...(config.titleProps || {}),
    },
  };

  // State variables
  const [currentSetType, setCurrentSetType] = useState<string>(
    propCurrentSetType ||
      (config.elementSets?.letters && config.elementSets.letters.length > 0
        ? 'letters'
        : 'numbers')
  );
  const [setA, setSetA] = useState<SetElement[]>([]);
  const [availableElements, setAvailableElements] = useState<SetElement[]>([]);
  const [draggedElement, setDraggedElement] = useState<SetElement | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Membership testing
  const [testElement, setTestElement] = useState<string>('');
  const [testResult, setTestResult] = useState<string>('');
  const [isElementInSetA, setIsElementInSetA] = useState<boolean>(false);

  const handleSetTypeChange = (newType: string) => {
    setCurrentSetType(newType);

    // Thông báo thay đổi cho component cha nếu cần
    if (onSetTypeChange) {
      onSetTypeChange(newType);
    }

    // Khi thay đổi loại tập hợp, tự động khởi tạo lại dữ liệu ngay lập tức
    // Không sử dụng setTimeout để tránh lỗi không đồng bộ
    const allElements = mergedConfig.elementSets[newType] || [];
    setSetA([]);
    setAvailableElements([]);
    setTestElement('');
    setTestResult('');

    // Thêm phần tử mặc định
    if (allElements.length > 0) {
      const isDefaultSetType = newType === 'numbers' || newType === 'letters';
      if (isDefaultSetType) {
        const defaultElements = allElements.slice(
          0,
          Math.min(3, allElements.length)
        );
        setSetA(defaultElements);
        setAvailableElements(
          allElements.filter(
            (el) => !defaultElements.some((def) => def.id === el.id)
          )
        );
      } else {
        setSetA([]);
        setAvailableElements([...allElements]);
      }
    }
  };

  // Xử lý thêm phần tử vào tập hợp A (từ kéo thả hoặc nút thêm)
  const handleAddToSetA = (element: SetElement) => {
    // Kiểm tra xem phần tử đã có trong tập hợp A chưa
    if (setA.some((el) => el.id === element.id)) {
      return; // Nếu đã có thì không thêm nữa
    }

    // Kiểm tra xem phần tử có trong danh sách khả dụng không
    if (!availableElements.some((el) => el.id === element.id)) {
      return; // Nếu không có trong danh sách khả dụng thì không thêm
    }

    // Add to set A
    setSetA((prev) => [...prev, element]);
    // Remove from available
    setAvailableElements((prev) => prev.filter((el) => el.id !== element.id));

    // Notify data change
    if (onDataChange) {
      onDataChange({
        action: 'element_added',
        element: element,
        setA: [...setA, element],
      });
    }
  };

  // Xử lý khi kéo thả kết thúc
  const handleDragEnd = () => {
    // Reset draggedElement
    setDraggedElement(null);

    // Reset background color of drop zones if needed
    const dropZones = document.querySelectorAll('.tailwind-border-dashed');
    dropZones.forEach((zone: any) => {
      zone.style.backgroundColor = 'rgba(24, 144, 255, 0.05)';
    });
  };

  const handleRemoveFromSetA = (element: SetElement) => {
    // Remove from set A
    setSetA((prev) => prev.filter((el) => el.id !== element.id));
    // Add back to available
    setAvailableElements((prev) => [...prev, element]);

    // Notify data change
    if (onDataChange) {
      onDataChange({
        action: 'element_removed',
        element,
        setA: setA.filter((el) => el.id !== element.id),
      });
    }
  };

  // Test membership
  const handleTestMembership = () => {
    if (!testElement) {
      setTestResult('');
      return;
    }

    const element = [...setA, ...availableElements].find(
      (el) => el.value === testElement
    );

    if (!element) {
      setTestResult('Phần tử không tồn tại');
      setIsElementInSetA(false);
      return;
    }

    const elementInSetA = setA.some((el) => el.value === testElement);
    setIsElementInSetA(elementInSetA);
    setTestResult(elementInSetA ? `${testElement} ∈ A` : `${testElement} ∉ A`);

    // Notify data change
    if (onDataChange) {
      onDataChange({
        action: 'membership_tested',
        element: testElement,
        result: elementInSetA,
      });
    }
  };

  // Generate examples for display
  const getExampleElements = useCallback(() => {
    // Combine elements from both sets
    const allElements = [...setA, ...availableElements];

    // Nếu không có phần tử nào, trả về mảng rỗng
    if (allElements.length === 0) {
      return [];
    }

    // Take first 5 elements or less if fewer exist
    return allElements.slice(0, Math.min(5, allElements.length));
  }, [setA, availableElements]);

  // Render set notation
  const renderSetNotation = () => {
    return `A = {${setA.map((el) => el.value).join(', ')}}`;
  };

  // Cập nhật currentSetType khi prop thay đổi
  useEffect(() => {
    if (propCurrentSetType && propCurrentSetType !== currentSetType) {
      setCurrentSetType(propCurrentSetType);

      // Khi nhận giá trị mới từ prop, tự động khởi tạo lại dữ liệu
      const allElements = mergedConfig.elementSets[propCurrentSetType] || [];

      setSetA([]);
      setAvailableElements([]);
      setTestElement('');
      setTestResult('');

      // Thêm phần tử mặc định
      if (allElements.length > 0) {
        const isDefaultSetType =
          propCurrentSetType === 'numbers' || propCurrentSetType === 'letters';
        if (isDefaultSetType) {
          const defaultElements = allElements.slice(
            0,
            Math.min(3, allElements.length)
          );
          setSetA(defaultElements);
          setAvailableElements(
            allElements.filter(
              (el) => !defaultElements.some((def) => def.id === el.id)
            )
          );
        } else {
          // Đối với các loại tập hợp tùy chỉnh, hiển thị tất cả phần tử trong danh sách khả dụng
          setSetA([]);
          setAvailableElements([...allElements]);
        }
      }
    }
  }, [propCurrentSetType, mergedConfig.elementSets]);

  // Effect to update state when config changes
  useEffect(() => {
    if (!config.elementSets) return;

    const updateSetTypeAndElements = () => {
      if (!config.elementSets) return;
      // Check if currentSetType exists in config.elementSets
      if (!config.elementSets[currentSetType]) {
        // Find a valid set type from config.elementSets
        const validSetType = Object.keys(config.elementSets).find(
          (key) =>
            config.elementSets &&
            config.elementSets[key] &&
            Array.isArray(config.elementSets[key])
        );

        if (validSetType && validSetType !== currentSetType) {
          setCurrentSetType(validSetType);

          // Khởi tạo lại dữ liệu cho loại tập hợp mới
          const allElements = config.elementSets[validSetType] || [];

          if (allElements.length > 0) {
            const isDefaultSetType =
              validSetType === 'numbers' || validSetType === 'letters';
            if (isDefaultSetType) {
              const defaultElements = allElements.slice(
                0,
                Math.min(3, allElements.length)
              );
              setSetA(defaultElements);
              setAvailableElements(
                allElements.filter(
                  (el) => !defaultElements.some((def) => def.id === el.id)
                )
              );
            } else {
              setSetA([]);
              setAvailableElements([...allElements]);
            }
          }
        }
      }
      // Check if elements for current set type have changed
      else if (
        config.elementSets[currentSetType] &&
        mergedConfig.elementSets?.[currentSetType] &&
        JSON.stringify(config.elementSets[currentSetType]) !==
          JSON.stringify(mergedConfig.elementSets[currentSetType])
      ) {
        // Khởi tạo lại dữ liệu khi phần tử thay đổi
        const allElements = config.elementSets[currentSetType] || [];

        if (allElements.length > 0) {
          const isDefaultSetType =
            currentSetType === 'numbers' || currentSetType === 'letters';
          if (isDefaultSetType) {
            const defaultElements = allElements.slice(
              0,
              Math.min(3, allElements.length)
            );
            setSetA(defaultElements);
            setAvailableElements(
              allElements.filter(
                (el) => !defaultElements.some((def) => def.id === el.id)
              )
            );
          } else {
            // Đối với các loại tập hợp tùy chỉnh, hiển thị tất cả phần tử trong danh sách khả dụng
            setSetA([]);
            setAvailableElements([...allElements]);
          }
        }
      }
    };

    updateSetTypeAndElements();
  }, [config.elementSets, currentSetType, mergedConfig.elementSets]);

  // Initialize on component mount
  useEffect(() => {
    if (!isInitialized) {
      const allElements = mergedConfig.elementSets[currentSetType] || [];

      if (allElements.length > 0) {
        const isDefaultSetType =
          currentSetType === 'numbers' || currentSetType === 'letters';
        if (isDefaultSetType) {
          const defaultElements = allElements.slice(
            0,
            Math.min(3, allElements.length)
          );
          setSetA(defaultElements);
          setAvailableElements(
            allElements.filter(
              (el) => !defaultElements.some((def) => def.id === el.id)
            )
          );
        } else {
          setSetA([]);
          setAvailableElements([...allElements]);
        }

        setIsInitialized(true);
      }
    }
  }, [isInitialized, currentSetType, mergedConfig.elementSets]);

  return (
    <div className="set-theory-simulator">
      <div className="tailwind-mb-3">
        <Paragraph>{mergedConfig.description}</Paragraph>

        <div className="tailwind-mb-3 tailwind-flex tailwind-items-center">
          <label className="tailwind-mr-2 tailwind-font-medium">
            Chọn loại tập hợp:
          </label>
          <Select
            value={currentSetType}
            onChange={handleSetTypeChange}
            className="tailwind-w-40"
          >
            <Option value="numbers">Số</Option>
            <Option value="letters">Chữ cái</Option>
            {/* Hiển thị các loại tập hợp tùy chỉnh */}
            {Object.keys(mergedConfig.elementSets || {})
              .filter((type) => !['numbers', 'letters'].includes(type))
              .map((setType) => (
                <Option key={setType} value={setType}>
                  {setType.charAt(0).toUpperCase() +
                    setType.slice(1).replace(/_/g, ' ')}
                </Option>
              ))}
          </Select>
        </div>

        <div className="tailwind-bg-gray-50 tailwind-p-2 tailwind-rounded tailwind-mb-3">
          <Paragraph className="tailwind-mb-1">
            Trong lý thuyết tập hợp, quan hệ thuộc tính (∈) và không thuộc tính
            (∉) được sử dụng để biểu thị mối quan hệ giữa một phần tử và một tập
            hợp.
          </Paragraph>

          <ul className="tailwind-pl-6 tailwind-mb-0">
            <li className="tailwind-mb-1">
              <Text strong>x ∈ A</Text>: Phần tử x <Text strong>thuộc</Text> tập
              hợp A
            </li>
            <li>
              <Text strong>x ∉ A</Text>: Phần tử x{' '}
              <Text strong>không thuộc</Text> tập hợp A
            </li>
          </ul>
        </div>
      </div>

      {
        <>
          <div className="tailwind-flex tailwind-flex-col md:tailwind-flex-row tailwind-gap-3 tailwind-mb-3">
            {/* Set A Container */}
            <Card
              title="Tập hợp A"
              className="tailwind-flex-1"
              styles={{
                header: {
                  backgroundColor: mergedConfig.theme.setAColor,
                  color: 'white',
                },
                body: {
                  padding: '12px',
                },
              }}
            >
              <div
                className="tailwind-min-h-24 tailwind-border-2 tailwind-border-dashed tailwind-rounded-md tailwind-p-2 tailwind-mb-2 tailwind-flex tailwind-flex-wrap tailwind-gap-2"
                style={{
                  borderColor: mergedConfig.theme.setAColor,
                  backgroundColor: 'rgba(24, 144, 255, 0.05)',
                }}
                onDragOver={(e) => {
                  e.preventDefault();
                  e.currentTarget.style.backgroundColor =
                    'rgba(24, 144, 255, 0.1)';
                }}
                onDragLeave={(e) => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(24, 144, 255, 0.05)';
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.currentTarget.style.backgroundColor =
                    'rgba(24, 144, 255, 0.05)';
                  if (draggedElement) {
                    handleAddToSetA(draggedElement);
                  }
                }}
              >
                {setA.length === 0 ? (
                  <div className="tailwind-w-full tailwind-h-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-text-gray-400 tailwind-text-sm">
                    Kéo các phần tử vào đây để thêm vào tập hợp A
                  </div>
                ) : (
                  setA.map((element) => (
                    <div
                      key={element.id}
                      className="tailwind-py-1 tailwind-px-3 tailwind-rounded-full tailwind-cursor-move tailwind-relative tailwind-flex tailwind-items-center"
                      style={{
                        backgroundColor: mergedConfig.theme.setAColor,
                        color: 'white',
                      }}
                      draggable="true"
                      onDragStart={(e) => {
                        setDraggedElement(element);
                        if (e.dataTransfer) {
                          e.dataTransfer.setData('text/plain', element.id);
                          e.dataTransfer.effectAllowed = 'move';
                        }
                      }}
                      onDragEnd={handleDragEnd}
                    >
                      {element.value}
                      <Button
                        type="text"
                        size="small"
                        className="tailwind-ml-1 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-text-white hover:tailwind-text-red-200"
                        icon={<DeleteIcon />}
                        onClick={() => handleRemoveFromSetA(element)}
                      />
                    </div>
                  ))
                )}
              </div>

              {mergedConfig.options.showSetNotation && (
                <div className="tailwind-bg-gray-100 tailwind-p-2 tailwind-rounded">
                  <Text strong>{renderSetNotation()}</Text>
                </div>
              )}
            </Card>

            {/* Available Elements */}
            <Card
              title="Các phần tử"
              className="tailwind-flex-1"
              styles={{
                body: {
                  padding: '12px',
                },
              }}
            >
              <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-mb-2">
                {availableElements.length === 0 ? (
                  <div className="tailwind-w-full tailwind-h-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-text-gray-400 tailwind-text-sm tailwind-min-h-24">
                    Không có phần tử khả dụng
                  </div>
                ) : (
                  availableElements.map((element) => (
                    <div
                      key={element.id}
                      className="tailwind-py-1 tailwind-px-3 tailwind-border tailwind-rounded-full tailwind-cursor-move tailwind-flex tailwind-items-center tailwind-gap-1"
                      style={{
                        backgroundColor: mergedConfig.theme.elementColor,
                      }}
                      draggable="true"
                      onDragStart={(e) => {
                        setDraggedElement(element);
                        if (e.dataTransfer) {
                          e.dataTransfer.setData('text/plain', element.id);
                          e.dataTransfer.effectAllowed = 'move';
                        }
                      }}
                      onDragEnd={handleDragEnd}
                    >
                      {element.value}
                      <Button
                        type="primary"
                        size="small"
                        className="tailwind-ml-1 tailwind-flex tailwind-items-center tailwind-justify-center"
                        icon={<PlusOutlined />}
                        onClick={() => handleAddToSetA(element)}
                      />
                    </div>
                  ))
                )}
              </div>
            </Card>
          </div>

          {/* Membership Testing Section */}
          {mergedConfig.options.showMembershipTesting && (
            <Card
              title={
                <Space>
                  <span>Kiểm Tra Thuộc Tính</span>
                  <Tooltip title="Kiểm tra xem một phần tử có thuộc tập hợp A hay không">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              className="tailwind-mb-3"
              styles={{
                header: { backgroundColor: '#1890ff', color: 'white' },
                body: { padding: '12px' },
              }}
            >
              <div className="tailwind-flex tailwind-flex-col md:tailwind-flex-row tailwind-gap-3 tailwind-items-center">
                <div className="tailwind-flex-1">
                  <Space className="tailwind-w-full">
                    <span>Phần tử:</span>
                    <Select
                      placeholder="Chọn phần tử"
                      value={testElement}
                      onChange={setTestElement}
                      style={{ width: 120 }}
                    >
                      {[...setA, ...availableElements].map((element) => (
                        <Option key={element.id} value={element.value}>
                          {element.value}
                        </Option>
                      ))}
                    </Select>
                    <Button onClick={handleTestMembership} type="primary">
                      Kiểm tra
                    </Button>
                  </Space>
                </div>

                <div className="tailwind-flex-1">
                  {testResult ? (
                    <Alert
                      type={isElementInSetA ? 'success' : 'warning'}
                      message={testResult}
                      showIcon
                    />
                  ) : (
                    <div className="tailwind-text-gray-400 tailwind-text-sm">
                      Chọn một phần tử và nhấn kiểm tra để xem kết quả
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )}

          {/* Set Membership Examples */}
          {mergedConfig.options.showExamples && (
            <Card
              title="Ví dụ về quan hệ thuộc tính"
              className="tailwind-mb-3"
              styles={{
                header: { backgroundColor: '#52c41a', color: 'white' },
                body: { padding: '12px' },
              }}
            >
              <Table
                dataSource={getExampleElements().map((el) => ({
                  key: el.id,
                  element: el.value,
                  relationship: setA.some((a) => a.id === el.id),
                  explanation: setA.some((a) => a.id === el.id)
                    ? `Phần tử ${el.value} thuộc tập hợp A vì nó là một phần tử trong tập hợp A.`
                    : `Phần tử ${el.value} không thuộc tập hợp A vì nó không có trong tập hợp A.`,
                }))}
                columns={[
                  {
                    title: 'Phần tử',
                    dataIndex: 'element',
                    key: 'element',
                    align: 'center',
                    width: '15%',
                  },
                  {
                    title: 'Quan hệ với tập A',
                    dataIndex: 'relationship',
                    key: 'relationship',
                    align: 'center',
                    width: '25%',
                    render: (isInSet, record) => (
                      <Tag color={isInSet ? 'success' : 'warning'}>
                        {record.element} {isInSet ? '∈' : '∉'} A
                      </Tag>
                    ),
                  },
                  {
                    title: 'Giải thích',
                    dataIndex: 'explanation',
                    key: 'explanation',
                    width: '60%',
                  },
                ]}
                pagination={false}
                size="small"
              />
            </Card>
          )}
        </>
      }
    </div>
  );
};

export default SetTheorySimulatorComponent;

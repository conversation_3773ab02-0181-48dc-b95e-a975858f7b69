.add-panel-button {
  margin-bottom: 16px;
}

.lesson-row-container {
  width: 100%;
  position: relative;
  min-height: 50px;
  border-radius: 4px;
  /* overflow: hidden; */
}

.lesson-row-container.editing {
  /* border: 1px solid #d9d9d9; */
  background-color: #fafafa;
}

.panel {
  padding: 16px;
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  position: relative;
  transition: height 0.3s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.panel-size-info {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  display: inline-block;
}

.row-height-divider {
  width: 100%;
  height: 8px;
  background-color: transparent;
  cursor: row-resize;
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  user-select: none;
  touch-action: none;
  transition: background-color 0.2s;
  pointer-events: auto;
}

.row-height-divider:hover {
  background-color: #1890ff;
}

.row-height-divider-bar {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  margin: auto 0;
  background-color: #1890ff;
  opacity: 0.7;
  border-radius: 2px;
}

.splitter-container {
  display: flex;
  width: 100%;
  position: relative;
  height: 100%;
  gap: 4px
}

.splitter-panel {
  position: relative;
}

.splitter-divider {
  width: 4px;
  background-color: transparent;
  position: absolute;
  z-index: 10;
  box-sizing: border-box;
  user-select: none;
  touch-action: none;
  transition: opacity 0.2s;
  pointer-events: none;
  overflow: visible;
  margin: 0 2px;
}

.splitter-divider.editing {
  cursor: col-resize;
  opacity: 1;
  pointer-events: auto;
}

.splitter-divider:hover {
  background-color: #e8e8e8;
}

.splitter-divider-bar {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 2px;
  margin: 0 auto;
  background-color: #e8e8e8;
  opacity: 1;
  border-radius: 1px;
}
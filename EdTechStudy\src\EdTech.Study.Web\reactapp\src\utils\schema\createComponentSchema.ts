import { z } from 'zod';
import { ETypeEdTechComponent } from '../../enums/AppEnums';

//#region schemaComponent
export const baseComponentSchema = z.object({
  id: z.string(),
  name: z.string(),
  title: z.string(),
  order: z.number(),
  path: z.string(),
});

/**
 * Creates a component schema by extending the base schema with custom params
 * @param paramsSchema - The Zod schema for the component's params
 * @returns A new Zod schema that extends baseComponentSchema with the provided params
 */
export const createComponentSchema = ({
  paramsSchema,
}: {
  paramsSchema: z.ZodRawShape;
}) => {
  return baseComponentSchema.extend({
    params: z.object(paramsSchema).optional(),
  });
};
// Tạo schema chỉ quan tâm đến params từ schema đầy đủ
export const extractParamsSchema = (
  fullSchema: ReturnType<typeof createComponentSchema>
) => {
  // Lấy schema của params từ schema đầy đủ
  const shape = fullSchema.shape;

  // Tạo và trả về schema mới chỉ chứa phần params
  return z.object({
    params: shape.params,
  });
};
//#endregion
//#region schemaJsonDataApp
// First declare the schema without defining it
const schemaEdTechRenderTreeData: z.ZodType<any> = z.object({}) as any;
const schemaETypeEdTechComponent = z.enum(
  Object.values(ETypeEdTechComponent) as [string, ...string[]]
);
// Later update its definition
Object.assign(
  schemaEdTechRenderTreeData,
  z.object({
    name: z.string(),
    title: z.string(),
    type: schemaETypeEdTechComponent,
    id: z.string(),
    order: z.number(),
    path: z.string(),
    version: z.string(),
    subItems: z.array(schemaEdTechRenderTreeData).optional(),
    children: z.any().optional(),
    titleProps: z.any().optional(),
  })
);

// Schema for IEdTechRenderParam (using any since we don't have its full definition)
const schemaEdTechRenderParam = z.any();

// Main schema for IEdTechAppJsonData
export const schemaJsonDataApp = z.object({
  edComponentParams: z.array(schemaEdTechRenderParam).optional(),
  edTechRenderTreeData: schemaEdTechRenderTreeData.optional(),
});
//#endregion

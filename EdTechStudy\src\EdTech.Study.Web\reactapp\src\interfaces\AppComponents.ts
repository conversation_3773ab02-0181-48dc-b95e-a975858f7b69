import { ETypeEdTechComponent } from '../enums/AppEnums';
import {
  IEdComponentParamsMapDispatchToProps,
  IEdComponentParamsMapSateToProps,
} from '../hocs/withEdComponentParams/withEdComponentParams';
import { z } from 'zod';

export interface IEdTechAppJsonData {
  edComponentParams: IEdTechRenderParam[] | undefined;
  edTechRenderTreeData: IEdTechRenderTreeData | undefined;
}

//#region Interfaces Component App
interface IEdTechComponentBase {
  name: string;
  title: string;
  type: ETypeEdTechComponent;
}

interface IEdTechVersionComponent {
  version: string;
  component: any;
  schema: z.ZodType<any> | undefined;
  description?: string;
}

export interface IEdTechComponent extends IEdTechComponentBase {
  description?: string;
  tags: string[] | undefined;
  thumbnailUrl?: string;
  components: IEdTechVersionComponent[] | undefined;
  structure?: IEdTechRenderTreeStructureData;
  schema: z.ZodType<any> | undefined;
  convertVersion?: (fromVersion: string, toVersion: string, data: any) => any;
}
export interface IEdTechRenderTreeData extends IEdTechComponentBase {
  id: string;
  order: number;
  path: string;
  subItems?: IEdTechRenderTreeData[];
  version: string;
  children?: any;
}
export interface IEdTechRenderTreeStructureData<T = any>
  extends IEdTechRenderTreeData {
  subItems?: IEdTechRenderTreeStructureData[];
  params?: T;
}
export interface IEdTechRenderParam<T = any> {
  id: string;
  params: T | undefined;
}
export interface IEdTechRenderProps<T = any>
  extends IEdTechRenderTreeData,
    IEdComponentParamsMapSateToProps<T>,
    IEdComponentParamsMapDispatchToProps<T> {}
export interface IEdTechMedia {
  blobContext: string;
  blobKey: string;
}
//#endregion

import React, { useRef, useMemo } from 'react';
import { Question, QuestionType } from './QuestionBankConfig';
import QuestionFormComponent, {
  QuestionFormRef,
} from './QuestionFormComponent';
import { <PERSON><PERSON>, Button } from 'antd';

interface QuestionFormModalProps {
  isOpen: boolean;
  onClose: (e?: React.MouseEvent) => void;
  onSave: (question: Question) => void;
  question?: Question;
  title?: string;
  initialQuestionType?: QuestionType;
  restrictQuestionType?: QuestionType;
  getContainer?: () => HTMLElement;
}

// Utility function to determine modal width based on question type
const getModalWidthByQuestionType = (type?: QuestionType): number => {
  switch (type) {
    case 'matching':
      return 1200; // Wider for matching questions to accommodate connections
    case 'ordering':
      return 1100; // Slightly wider for ordering questions
    case 'multiplechoice':
    case 'truefalse':
    case 'fillin':
    default:
      return 1000; // Default width for other question types
  }
};

const QuestionFormModal: React.FC<QuestionFormModalProps> = ({
  isOpen,
  onClose,
  onSave,
  question,
  title = question ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới',
  initialQuestionType,
  restrictQuestionType,
  getContainer,
}) => {
  const formRef = useRef<QuestionFormRef>(null);

  // Determine the effective question type (from question, restrictQuestionType, or initialQuestionType)
  const effectiveQuestionType = useMemo(() => {
    return question?.type || restrictQuestionType || initialQuestionType;
  }, [question, restrictQuestionType, initialQuestionType]);

  // Calculate modal width based on question type
  const modalWidth = useMemo(() => {
    return getModalWidthByQuestionType(effectiveQuestionType);
  }, [effectiveQuestionType]);

  const handleSave = (updatedQuestion: Question) => {
    onSave(updatedQuestion);
    onClose();
  };

  const handleSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  // Calculate max height based on question type
  const maxBodyHeight = useMemo(() => {
    return effectiveQuestionType === 'matching'
      ? 'calc(90vh - 100px)'
      : 'calc(90vh - 130px)';
  }, [effectiveQuestionType]);

  return (
    <Modal
      open={isOpen}
      title={title}
      onCancel={onClose}
      width={modalWidth}
      className="tailwind-max-h-[90vh]"
      styles={{
        body: {
          maxHeight: maxBodyHeight,
          overflowY: 'auto',
        },
      }}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Hủy
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {question ? 'Cập nhật' : 'Thêm mới'}
        </Button>,
      ]}
      destroyOnHidden
      getContainer={getContainer}
    >
      <div className="tailwind-w-full">
        <QuestionFormComponent
          ref={formRef}
          question={question}
          onSave={handleSave}
          onCancel={onClose}
          initialQuestionType={initialQuestionType}
          restrictQuestionType={restrictQuestionType}
        />
      </div>
    </Modal>
  );
};

export default QuestionFormModal;

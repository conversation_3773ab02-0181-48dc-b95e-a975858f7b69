import { ITextProps } from '../../../../../core/title/CoreTitle';
import { MediaComponentProps, MediaItem } from '../../shared/type';

/**
 * Interface for a single image item
 */
export interface ImageItem extends MediaItem {
}
export interface ImageComponentProps extends MediaComponentProps {
  /** Title properties for the component */
  titleProps?: ITextProps;
  /** Array of images to display */
  medias?: ImageItem[];
  /** Width of the image container */
  width?: number | string;

  /** Height of the image container */
  height?: number | string;

  /** How the image should fit within its container */
  objectFit?: 'contain' | 'cover' | 'fill';

  /** Border radius of the image container in pixels */
  borderRadius?: number;

  /** Whether to apply a shadow effect to the image */
  shadow?: boolean;

  /** Display mode for multiple images: horizontal (slider) or vertical (stacked) */
  displayMode?: 'horizontal' | 'vertical';
}

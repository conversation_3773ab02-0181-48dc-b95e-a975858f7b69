import { TreeMenuItem } from '../types';
import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';
import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';

/**
 * Convert items to rc-tree format
 */
export const convertToRcTreeFormat = (
  items: TreeMenuItem[],
  isEditing: boolean = false
): TreeMenuItem[] => {
  return items
    .map((item) => {
      if (!item.key || !item.title) {
        console.warn('Invalid item structure:', item);
        return null;
      }

      const hasChildren =
        item.children &&
        Array.isArray(item.children) &&
        item.children.length > 0;
      const canAddChildren =
        item.type &&
        [
          ETypeEdTechComponent.STRUCTURE,
          ETypeEdTechComponent.CONTENT,
          ETypeEdTechComponent.LAYOUT,
        ].includes(item.type);

      const treeNode: TreeMenuItem = {
        key: item.key,
        title: item.title,
        type: item.type,
        name: item.name,
        path: item.path,
        isLeaf: !hasChildren && !canAddChildren,
        disableCheckbox: true,
      };

      if (hasChildren) {
        const childNodes = convertToRcTreeFormat(
          item.children || [],
          isEditing
        ).filter((node) => node !== null) as TreeMenuItem[];
        if (childNodes.length > 0) {
          treeNode.children = childNodes;
          treeNode.isLeaf = false;
        }
      }

      return treeNode;
    })
    .filter((node) => node !== null) as TreeMenuItem[];
};

/**
 * Find a node by key in a tree structure
 */
export const findNodeByKey = (
  menuItems: TreeMenuItem[],
  targetKey: string
): TreeMenuItem | null => {
  for (const item of menuItems) {
    if (item.key === targetKey) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const found = findNodeByKey(item.children, targetKey);
      if (found) return found;
    }
  }
  return null;
};

/**
 * Find parent keys for a given node key
 */
export const findParentKeys = (
  menuItems: TreeMenuItem[],
  targetKey: string,
  parentKeys: string[] = []
): string[] => {
  for (const item of menuItems) {
    if (item.key === targetKey) {
      return parentKeys;
    }

    if (item.children && item.children.length > 0) {
      const foundKeys = findParentKeys(item.children, targetKey, [
        ...parentKeys,
        item.key,
      ]);
      if (foundKeys.length > 0) {
        return foundKeys;
      }
    }
  }
  return [];
};

/**
 * Find parent item for a given node key
 */
export const findParentItem = (
  menuItems: TreeMenuItem[],
  targetKey: string,
  parentItem: TreeMenuItem | null = null
): TreeMenuItem | null => {
  for (const item of menuItems) {
    if (item.key === targetKey) {
      return parentItem;
    }
    if (item.children && item.children.length > 0) {
      const found = findParentItem(item.children, targetKey, item);
      if (found) return found;
    }
  }
  return null;
};

/**
 * Deep copy an object
 */
export const deepCopy = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if a node can have children
 */
export const canNodeHaveChildren = (node: IEdTechRenderTreeData): boolean => {
  return !!(
    node.type &&
    [
      ETypeEdTechComponent.STRUCTURE,
      ETypeEdTechComponent.CONTENT,
      ETypeEdTechComponent.LAYOUT,
    ].includes(node.type)
  );
};

/**
 * Get screen size category
 */
export const getScreenSizeCategory = (): string => {
  const width = window.innerWidth;
  if (width <= 320) return 'xs';
  if (width <= 480) return 'sm';
  if (width <= 768) return 'md';
  return 'lg';
};

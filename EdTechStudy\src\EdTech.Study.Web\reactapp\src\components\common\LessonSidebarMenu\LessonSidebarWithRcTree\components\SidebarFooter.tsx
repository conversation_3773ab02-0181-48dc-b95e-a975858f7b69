import React from 'react';
import { Button, Tooltip } from 'antd';
import { SaveOutlined } from '@ant-design/icons';

interface SidebarFooterProps {
  onSave: () => void;
}

const SidebarFooter: React.FC<SidebarFooterProps> = ({ onSave }) => {
  return (
    <div className="lesson-sidebar-footer">
      <Tooltip title="Lưu">
        <Button
          icon={<SaveOutlined />}
          onClick={onSave}
          className="save-button"
        >
          Lưu
        </Button>
      </Tooltip>
    </div>
  );
};

export default SidebarFooter;

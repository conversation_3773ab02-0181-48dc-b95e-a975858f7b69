import { defineConfig, Plugin, UserConfig } from 'vite';
import { resolve } from 'path';
import path from 'path';
import chalk from 'chalk';

// Tắt các log không cần thiết nhưng giữ nguyên console.error
const originalLog = console.log;
const originalWarn = console.warn;

// Chỉ tắt log không phải error khi không phải mode test
if (process.env.NODE_ENV !== 'test') {
  console.debug = () => {};
  console.info = () => {};
  console.warn = (...args) => {
    // Giữ lại warning về TypeScript và lỗi nghiêm trọng
    const logString = String(args[0] || '');
    if (
      logString.includes('TypeScript') ||
      logString.includes('Error') ||
      logString.includes('error')
    ) {
      originalWarn(...args);
    }
  };

  // Cần giữ lại log quan trọng
  console.log = (...args) => {
    const logString = String(args[0] || '');

    // Giữ lại log HMR (hot reload) nhưng lọc log vite không cần thiết
    if (
      logString.includes('[vite] hmr update') ||
      logString.includes('page reloaded') ||
      logString.includes('hot updated') ||
      logString.includes('File changed')
    ) {
      originalLog(...args);
    } else if (
      !logString.startsWith('vite:') &&
      !logString.includes('vite:resolve') &&
      !logString.includes('vite:cache') &&
      !logString.includes('vite:time')
    ) {
      // Vẫn hiển thị các log không liên quan đến vite
      originalLog(...args);
    }
  };

  // KHÔNG ghi đè console.error, giữ nguyên để hiển thị tất cả lỗi
}

// Plugin tùy chỉnh hiển thị lỗi và bỏ qua log thường
const silentNonErrorPlugin = (): Plugin => {
  return {
    name: 'vite-plugin-silent-non-error',
    enforce: 'pre',
    configResolved(config) {
      // Thiết lập mức độ log ở mức thấp nhất có thể, nhưng giữ lại error
      if (config.logger) {
        // Giữ nguyên error handler gốc
        const originalError = config.logger.error;
        config.logger.clearScreen = () => {};
        config.logger.info = () => {};
        config.logger.warn = () => {};
        config.logger.warnOnce = () => {};
        // KHÔNG ghi đè error handler
        config.logger.error = originalError;
      }
    },
  };
};

// Plugin để kiểm tra lỗi sau mỗi lần save file - CẢI TIẾN VỚI DEBOUNCE VÀ CANCEL THÔNG MINH
const errorCheckOnSavePlugin = (): Plugin => {
  // Biến để theo dõi timer
  let debounceTimer: NodeJS.Timeout | null = null;
  // Biến để theo dõi các file đã thay đổi
  let changedFiles = new Set<string>();
  // Biến để kiểm soát trạng thái đang xử lý
  let isProcessing = false;
  // Biến để lưu các quy trình đang chạy
  let activeProcesses: { process: any; killed: boolean; sessionId: number }[] =
    [];
  // Biến để theo dõi phiên làm việc hiện tại
  let currentSessionId = 0;
  // Biến để theo dõi lần kiểm tra TypeScript theo từng session
  let checkSessionMap = new Map<
    number,
    { lastCheckTime: number; hasChecked: boolean }
  >();
  // Thời gian tối thiểu giữa các lần kiểm tra TypeScript (ms)
  const typescriptCheckMinInterval = 5000; // 5 giây
  // Ngưỡng số lượng file để quyết định có chạy TypeScript check ngay lập tức hay không
  const typeScriptCheckFileThreshold = 10;

  return {
    name: 'vite-plugin-error-check-on-save',
    configureServer(server) {
      // Hàm hủy bỏ tất cả các quy trình đang chạy
      const killAllProcesses = () => {
        // Kiểm tra xem có bất kỳ process nào cần kill không
        const processesToKill = activeProcesses.filter((p) => !p.killed);

        if (processesToKill.length > 0) {
          console.log(
            chalk.yellow(
              `Canceling ${processesToKill.length} active processes...`
            )
          );

          // Đánh dấu tất cả các process đã bị kill trước để tránh chặn trễ
          processesToKill.forEach((processObj) => {
            processObj.killed = true;
          });

          // Sau đó mới thực hiện kill để tránh blocking
          processesToKill.forEach((processObj) => {
            if (processObj.process && processObj.process.kill) {
              try {
                processObj.process.kill();
              } catch (error) {
                // Bỏ qua lỗi nếu không thể kill process
              }
            }
          });

          // Xóa các process đã bị kill
          activeProcesses = activeProcesses.filter((p) => !p.killed);
        }
      };

      // Hàm xử lý kiểm tra lỗi
      const processErrors = async (sessionId: number) => {
        // Nếu session đã hết hạn, thoát
        if (sessionId !== currentSessionId) return;

        // Đánh dấu đang xử lý để tránh xung đột
        isProcessing = true;

        // Lấy danh sách các file đã thay đổi
        const filesToCheck = Array.from(changedFiles);
        if (filesToCheck.length === 0) {
          isProcessing = false;
          return;
        }

        // Reset danh sách file đã thay đổi
        changedFiles.clear();

        // Log thông tin về các file đã thay đổi
        console.log(
          chalk.cyan(
            `\nProcessing ${filesToCheck.length} changed files (session: ${sessionId})`
          )
        );

        // Chỉ log chi tiết nếu số lượng file ít
        if (filesToCheck.length < 10) {
          filesToCheck.forEach((file) => {
            const relativePath = path.relative(process.cwd(), file);
            console.log(chalk.blue(`  - ${relativePath}`));
          });
        } else {
          console.log(
            chalk.blue(
              `  (${filesToCheck.length} files changed, not showing individual files)`
            )
          );
        }

        try {
          // Kiểm tra xem session có còn hợp lệ không
          if (sessionId !== currentSessionId) {
            console.log(
              chalk.yellow(
                `Session ${sessionId} has been canceled. New session: ${currentSessionId}`
              )
            );
            return;
          }

          // Lấy thông tin kiểm tra của session hiện tại hoặc tạo mới nếu chưa có
          if (!checkSessionMap.has(sessionId)) {
            checkSessionMap.set(sessionId, {
              lastCheckTime: 0,
              hasChecked: false,
            });
          }
          const sessionCheckInfo = checkSessionMap.get(sessionId)!;

          // Quyết định có chạy TypeScript check không
          const currentTime = Date.now();
          const timeSinceLastCheck =
            currentTime - sessionCheckInfo.lastCheckTime;

          // Chạy TypeScript check nếu:
          // 1. Chưa từng check trong session này, HOẶC
          // 2. Đã quá thời gian tối thiểu từ lần check cuối, HOẶC
          // 3. Số lượng file thay đổi lớn hơn ngưỡng (có thể là đổi branch)
          const shouldRunTypeScriptCheck =
            !sessionCheckInfo.hasChecked ||
            timeSinceLastCheck >= typescriptCheckMinInterval ||
            filesToCheck.length >= typeScriptCheckFileThreshold;

          if (shouldRunTypeScriptCheck) {
            // Đánh dấu là đã check và cập nhật thời gian
            sessionCheckInfo.hasChecked = true;
            sessionCheckInfo.lastCheckTime = currentTime;

            // Chạy TypeScript checker cho toàn dự án - chỉ một lần cho tất cả các file
            console.log(
              chalk.cyan(
                `Running TypeScript checker for session ${sessionId}...`
              )
            );

            const { spawn } = require('child_process');

            // Sử dụng spawn thay vì exec để có thể hủy quá trình dễ dàng hơn
            const tscProcess = spawn('npx', ['tsc', '--noEmit'], {
              shell: true,
              stdio: 'pipe',
            });

            // Thêm vào danh sách các quy trình đang chạy với ID phiên hiện tại
            const processObj = {
              process: tscProcess,
              killed: false,
              sessionId: sessionId,
            };
            activeProcesses.push(processObj);

            let stdout = '';
            let stderr = '';

            tscProcess.stdout.on('data', (data) => {
              stdout += data.toString();
            });

            tscProcess.stderr.on('data', (data) => {
              stderr += data.toString();
            });

            await new Promise<void>((resolve) => {
              tscProcess.on('close', (code) => {
                // Đánh dấu process đã kết thúc (không cần gì vì đã được đánh dấu khi kill)

                // Kiểm tra xem session có còn hợp lệ không
                if (sessionId !== currentSessionId) {
                  // Chỉ log nếu cần thiết
                  console.log(
                    chalk.yellow(
                      `TypeScript check from session ${sessionId} completed, but session was superseded by ${currentSessionId}`
                    )
                  );
                  resolve();
                  return;
                }

                if (code !== 0 || stdout || stderr) {
                  if (stdout.trim()) {
                    console.error(chalk.red('TypeScript errors:'));
                    console.error(chalk.red(stdout));
                  }
                  if (stderr.trim()) {
                    console.error(chalk.red(stderr));
                  }
                } else {
                  console.log(chalk.green('✓ No TypeScript errors in project'));
                }
                resolve();
              });
            });
          } else {
            console.log(
              chalk.cyan(
                `Skipping TypeScript check for session ${sessionId} (checked ${
                  sessionCheckInfo.hasChecked
                    ? Math.round(timeSinceLastCheck / 1000) + ' seconds ago'
                    : 'never'
                })`
              )
            );
          }

          // Kiểm tra xem session có còn hợp lệ không
          if (sessionId !== currentSessionId) {
            console.log(
              chalk.yellow(
                `Current session ${sessionId} has been superseded by ${currentSessionId}. Skipping further checks.`
              )
            );
            return;
          }

          // Force full reload để cập nhật lại lỗi trên UI
          server.ws.send({
            type: 'full-reload',
          });

          // Kiểm tra lỗi ESLint - chỉ kiểm tra các file JavaScript/TypeScript đã thay đổi
          const jsFiles = filesToCheck.filter(
            (file) =>
              file.endsWith('.ts') ||
              file.endsWith('.tsx') ||
              file.endsWith('.js') ||
              file.endsWith('.jsx')
          );

          if (jsFiles.length > 0 && sessionId === currentSessionId) {
            try {
              // Chỉ kiểm tra ESLint nếu số lượng file không quá lớn
              if (jsFiles.length <= 20) {
                console.log(
                  chalk.cyan(
                    `Running ESLint checker for ${jsFiles.length} files...`
                  )
                );
                const { ESLint } = require('eslint');
                const eslint = new ESLint({
                  cache: true, // Bật cache để cải thiện hiệu suất
                });
                const results = await eslint.lintFiles(jsFiles);

                // Kiểm tra lại session sau khi ESLint hoàn thành
                if (sessionId !== currentSessionId) {
                  console.log(
                    chalk.yellow(
                      `ESLint check from session ${sessionId} was canceled.`
                    )
                  );
                  return;
                }

                let hasErrors = false;
                results.forEach((result) => {
                  if (result.errorCount > 0) {
                    hasErrors = true;
                    const relativePath = path.relative(
                      process.cwd(),
                      result.filePath
                    );
                    console.error(
                      chalk.yellow(`ESLint errors in ${relativePath}:`)
                    );
                    result.messages.forEach((msg) => {
                      if (msg.severity === 2) {
                        // error
                        console.error(
                          chalk.red(`  Line ${msg.line}: ${msg.message}`)
                        );
                      }
                    });
                  }
                });

                if (!hasErrors) {
                  console.log(
                    chalk.green('✓ No ESLint errors in changed files')
                  );
                }
              } else {
                console.log(
                  chalk.yellow(
                    `Skipping ESLint check (too many files: ${jsFiles.length})`
                  )
                );
              }
            } catch (err) {
              // ESLint có thể không được cài đặt, bỏ qua lỗi này
            }
          }

          if (sessionId === currentSessionId) {
            console.log(
              chalk.green(
                `✓ Finished checking ${filesToCheck.length} files for errors (session: ${sessionId})`
              )
            );
          } else {
            console.log(
              chalk.yellow(
                `Session ${sessionId} was canceled during final steps. New session: ${currentSessionId}`
              )
            );
          }
        } catch (error) {
          console.error(chalk.red('Error during processing:'), error);
        } finally {
          // Đảm bảo đặt lại trạng thái xử lý
          isProcessing = false;

          // Trong trường hợp có nhiều file thay đổi, chờ lâu hơn để đảm bảo tất cả các file được thay đổi được gọi
          const debounceTime = filesToCheck.length > 20 ? 2000 : 1000;
          if (changedFiles.size > 0 && sessionId === currentSessionId) {
            debounceTimer = setTimeout(
              () => processErrors(currentSessionId),
              debounceTime
            );
          }
        }
      };

      // Theo dõi file thay đổi
      server.watcher.on('change', async (file) => {
        if (
          file.endsWith('.ts') ||
          file.endsWith('.tsx') ||
          file.endsWith('.js') ||
          file.endsWith('.jsx')
        ) {
          // Thêm file vào danh sách file đã thay đổi
          changedFiles.add(file);

          // Tạo session mới và hủy session cũ
          const newSessionId = currentSessionId + 1; // Tạo ID mới nhưng chưa gán

          // Chỉ hủy các quy trình của các session cũ
          const oldProcesses = activeProcesses.filter(
            (p) => !p.killed && p.sessionId < newSessionId
          );
          if (oldProcesses.length > 0) {
            console.log(
              chalk.yellow(
                `Canceling ${oldProcesses.length} processes from older sessions...`
              )
            );

            // Đánh dấu các quy trình cũ là đã bị hủy
            oldProcesses.forEach((p) => {
              p.killed = true;
            });

            // Hủy các quy trình cũ
            oldProcesses.forEach((p) => {
              if (p.process && p.process.kill) {
                try {
                  p.process.kill();
                } catch (error) {
                  // Bỏ qua lỗi nếu không thể kill process
                }
              }
            });

            // Xóa các process đã bị kill
            activeProcesses = activeProcesses.filter((p) => !p.killed);
          }

          // Cập nhật ID session hiện tại
          currentSessionId = newSessionId;

          // Hủy timer hiện tại nếu có
          if (debounceTimer) {
            clearTimeout(debounceTimer);
          }

          // Thiết lập timer mới - chờ 1 giây sau thay đổi cuối cùng
          debounceTimer = setTimeout(
            () => processErrors(currentSessionId),
            1000
          );
        }
      });

      // Bắt lỗi từ HMR
      server.ws.on('connection', (socket) => {
        socket.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            if (message.type === 'error') {
              console.error(chalk.red('Client runtime error:'), message.err);
            }
          } catch (e) {
            // Ignore parsing errors
          }
        });
      });
    },
  };
};

// Plugin TypeScript checker - chạy khi khởi động
const typeCheckPlugin = (): Plugin => {
  return {
    name: 'vite-plugin-typescript-checker',
    buildStart() {
      // Thực hiện kiểm tra TypeScript
      const { exec } = require('child_process');
      exec('npx tsc --noEmit', (error, stdout, stderr) => {
        if (error) {
          console.error(chalk.red.bold('TypeScript Error:'));
          console.error(chalk.red(stdout || stderr));
        }
      });
    },
  };
};

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';
  const outputDir = isProduction ? '../wwwroot/EdTech/reactapp' : 'dist';

  const config: UserConfig = {
    plugins: [
      silentNonErrorPlugin(),
      errorCheckOnSavePlugin(),
      typeCheckPlugin(),
    ],

    // Thêm base trống để đảm bảo đường dẫn tương đối được giữ nguyên
    base: '',

    // Cấu hình CSS
    css: {
      // Đảm bảo CSS được xử lý và xuất ra đúng
      devSourcemap: !isProduction,
      // Thêm module CSS nếu cần
      modules: {
        scopeBehaviour: 'local',
      },
    },

    // Cấu hình báo lỗi
    esbuild: {
      logOverride: {
        'this-is-undefined-in-esm': 'silent',
        'unused-import': 'silent',
        'unused-variable': 'silent',
      },
      legalComments: 'inline',
      treeShaking: true,
    },

    // Cấu hình server
    server: {
      port: 8080,
      open: '/demo.html',
      fs: {
        strict: false,
        allow: ['..'],
      },
      origin: 'http://localhost:8080', // Thêm origin từ cấu hình mới
      watch: {
        usePolling: true,
        interval: 1000, // Đã đổi từ 300ms sang 1000ms theo cấu hình mới
        ignored: ['node_modules/**', 'dist/**'],
      },
      hmr: {
        overlay: true,
        timeout: 1000,
      },
    },

    // Cấu hình cho TypeScript
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },

    // Cấu hình cho build
    build: {
      outDir: outputDir,
      emptyOutDir: true,
      sourcemap: isProduction ? false : true, // Tắt sourcemap trong production
      minify: isProduction ? 'terser' : false,
      reportCompressedSize: false,
      chunkSizeWarningLimit: 1600, // Thêm theo cấu hình mới
      copyPublicDir: true, // Thêm theo cấu hình mới
      // Đảm bảo CSS được tạo cho mỗi entry
      cssCodeSplit: true,
      rollupOptions: {
        external: [/\/public\/js\/.*/],
        input: {
          DemoLessonPage: resolve(__dirname, 'public/demo.html'),
          PreviewLessonPage: resolve(__dirname, 'public/preview.html'),
          QuestionPage: resolve(__dirname, 'public/question.html'),
          IconStorePage: resolve(__dirname, 'public/iconStore.html'),
        },
        output: {
          format: 'es',
          entryFileNames: '[name].js',
          chunkFileNames: isProduction
            ? 'chunks/[name]-[hash].js'
            : 'chunks/[name].js',
          // Chỉ định định dạng cho file CSS
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name || '';
            if (info.endsWith('.css')) {
              return 'assets/css/[name][extname]';
            }
            if (/\.(jpe?g|png|gif|svg)$/i.test(info)) {
              return 'assets/images/[name][extname]';
            }
            if (/\.(mp3)$/i.test(info)) {
              return 'assets/[name][extname]';
            }
            return 'assets/[name][extname]';
          },
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react';
              }
              // Loại bỏ antd bundle riêng theo cấu hình mới
              return 'vendor-deps';
            }

            if (id.includes('components/common/LessonConfigComponent')) {
              return 'lesson-config-components';
            }

            // Gộp các file CSS lại thành một chunk
            if (
              id.endsWith('.css') ||
              id.endsWith('.scss') ||
              id.endsWith('.less')
            ) {
              return 'styles';
            }
          },
        },
      },
      terserOptions: {
        compress: {
          drop_console: isProduction, // Chỉ drop console trong production
          drop_debugger: isProduction,
        },
        format: {
          comments: !isProduction,
        },
      },
    },

    // Giảm log trong quá trình tối ưu dependencies
    optimizeDeps: {
      force: true,
      esbuildOptions: {
        legalComments: 'inline',
        treeShaking: true,
        logLevel: 'error', // Chỉ hiển thị lỗi
      },
    },

    // Thiết lập cho việc xử lý preview
    preview: {
      port: 8080,
      open: true,
    },

    // Tắt tất cả debug logs với customLogger nhưng giữ nguyên error
    customLogger: {
      info: () => {},
      warn: () => {},
      warnOnce: () => {},
      error: console.error, // Giữ nguyên console.error
      clearScreen: (_type) => {},
      hasWarned: true,
      hasErrorLogged: (_error) => false, // Luôn hiển thị lỗi, không bỏ qua
    },
  };

  return config;
});

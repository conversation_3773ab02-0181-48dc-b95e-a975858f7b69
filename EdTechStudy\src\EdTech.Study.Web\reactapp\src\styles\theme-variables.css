@import url(./theme-antd-custom.css);

:root {
  /* Base Colors */
  --edtt-color-white: #ffffff;
  --edtt-color-black: #000000;
  --edtt-color-gray-100: #f0f2f5;
  --edtt-color-gray-200: #d9d9d9;
  --edtt-color-gray-700: #434343;
  --edtt-color-gray-800: #1f1f1f;
  --edtt-color-gray-900: #141414;

  /* Color Palette System */
  /* Primary Color (Pink) */
  --edtt-color-primary-base: #ea4c89;
  --edtt-color-primary-100: #fcedf1;
  --edtt-color-primary-200: #f6c9d5;
  --edtt-color-primary-300: #ef92ae;
  --edtt-color-primary-400: #ea4c89;
  --edtt-color-primary-500: #b33065;
  --edtt-color-primary-600: #7a1e43;
  --edtt-color-primary-700: #450d23;

  /* Secondary Color (Blue) */
  --edtt-color-secondary-base: #4cd8ea;
  --edtt-color-secondary-100: #cef5fd;
  --edtt-color-secondary-200: #4cd8ea;
  --edtt-color-secondary-300: #3caebd;
  --edtt-color-secondary-400: #2c8691;
  --edtt-color-secondary-500: #1d6068;
  --edtt-color-secondary-600: #0f3c42;
  --edtt-color-secondary-700: #041c1f;

  /* Tertiary Color (Green) */
  --edtt-color-tertiary-base: #4ceaad;
  --edtt-color-tertiary-100: #c4fde1;
  --edtt-color-tertiary-200: #4ceaad;
  --edtt-color-tertiary-300: #3cbe8c;
  --edtt-color-tertiary-400: #2d956d;
  --edtt-color-tertiary-500: #1f6d4f;
  --edtt-color-tertiary-600: #114732;
  --edtt-color-tertiary-700: #052519;

  /* Quaternary Color (Purple) */
  --edtt-color-quaternary-base: #5e4cea;
  --edtt-color-quaternary-100: #d7d4fb;
  --edtt-color-quaternary-200: #aea8f6;
  --edtt-color-quaternary-300: #857cf1;
  --edtt-color-quaternary-400: #5e4cea;
  --edtt-color-quaternary-500: #3b21c2;
  --edtt-color-quaternary-600: #1f0f74;
  --edtt-color-quaternary-700: #0d0540;

  /* Grey Color */
  --edtt-color-grey-base: #908588;
  --edtt-color-grey-100: #f1f0f1;
  --edtt-color-grey-200: #d7d4d5;
  --edtt-color-grey-300: #b2acae;
  --edtt-color-grey-400: #908588;
  --edtt-color-grey-500: #6b6264;
  --edtt-color-grey-600: #474142;
  --edtt-color-grey-700: #262223;

  /* Status Colors */
  --edtt-color-status-success: #52c41a;
  --edtt-color-status-warning: #faad14;
  --edtt-color-status-error: #f5222d;
  --edtt-color-status-info: #1890ff;

  /* Default Theme Variables - Optimized */
  /* Màu sắc cơ bản theo ngữ cảnh */
  --edtt-color-primary: var(--edtt-color-primary-base);
  --edtt-color-secondary: var(--edtt-color-secondary-base);
  --edtt-color-tertiary: var(--edtt-color-tertiary-base);

  /* Màu nền */
  --edtt-color-bg-default: var(--edtt-color-white);
  /* Nền chính */
  --edtt-color-bg-secondary: var(--edtt-color-gray-100);
  /* Nền phụ */
  --edtt-color-bg-tertiary: var(--edtt-color-gray-200);
  /* Nền mức 3 (vd: vùng nhấn mạnh) */
  --edtt-color-bg-inverse: var(--edtt-color-black);
  /* Nền đảo ngược màu (vd: tooltip) */
  --edtt-color-bg-card: var(--edtt-color-white);
  /* Nền thẻ */
  --edtt-color-bg-overlay: rgba(0,
      0,
      0,
      0.45);
  /* Nền lớp phủ (modal, drawer) */
  --edtt-color-bg-mask: rgba(0, 0, 0, 0.15);
  /* Lớp mờ (vd: disabled) */
  --edtt-color-bg-spotlight: var(--edtt-color-primary-100);
  /* Nền vùng nhấn mạnh */
  --edtt-color-disabled-bg: rgba(0, 0, 0, 0.04);
  /* Nền phần tử bị vô hiệu */
  --edtt-color-selected: var(--edtt-color-primary-100);
  /* Nền phần tử được chọn */
  --edtt-color-hover: var(--edtt-color-primary-50);
  /* Nền khi hover */

  /* Màu chữ */
  --edtt-color-text-default: var(--edtt-color-black);
  /* Chữ chính */
  --edtt-color-text-primary: var(--edtt-color-primary-base);
  /* Chữ nhấn mạnh */
  --edtt-color-text-secondary: var(--edtt-color-grey-500);
  /* Chữ ít quan trọng hơn */
  --edtt-color-text-tertiary: var(--edtt-color-grey-400);
  /* Chữ ít quan trọng nhất (phụ đề) */
  --edtt-color-text-inverse: var(--edtt-color-white);
  /* Chữ trên nền tối */
  --edtt-color-text-disabled: rgba(0, 0, 0, 0.25);
  /* Chữ bị vô hiệu */
  --edtt-color-link: var(--edtt-color-primary-base);
  /* Màu đường dẫn */
  --edtt-color-link-hover: var(--edtt-color-primary-500);
  /* Màu đường dẫn khi hover */
  --edtt-color-link-active: var(--edtt-color-primary-600);
  /* Màu đường dẫn khi active */
  --edtt-color-placeholder: var(--edtt-color-grey-400);
  /* Màu placeholder trong input */

  /* Màu viền */
  --edtt-color-border-default: var(--edtt-color-gray-200);
  /* Viền mặc định */
  --edtt-color-border-strong: var(--edtt-color-gray-400);
  /* Viền đậm hơn */
  --edtt-color-border-light: var(--edtt-color-gray-100);
  /* Viền nhẹ */
  --edtt-color-border-primary: var(--edtt-color-primary-base);
  /* Viền nhấn mạnh */
  --edtt-color-border-focus: var(--edtt-color-primary-400);
  /* Viền khi focus */
  --edtt-color-border-disabled: var(--edtt-color-gray-200);
  /* Viền bị vô hiệu */

  /* Độ sâu - Đổ bóng */
  --edtt-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  /* Đổ bóng mặc định */
  --edtt-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  /* Đổ bóng nhỏ */
  --edtt-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  /* Đổ bóng vừa */
  --edtt-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  /* Đổ bóng lớn */
  --edtt-shadow-card: 0 1px 3px rgba(0, 0, 0, 0.12);
  /* Đổ bóng thẻ */
  --edtt-shadow-popup: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
  /* Đổ bóng popup */
  --edtt-shadow-dropdown: 0 3px 6px -4px rgba(0, 0, 0, 0.12);
  /* Đổ bóng dropdown */
  --edtt-shadow-focus: 0 0 0 2px rgba(234, 76, 137, 0.2);
  /* Đổ bóng khi focus */

  /* Hiệu ứng chuyển đổi */
  --edtt-transition-slow: 0.3s ease-in-out;
  /* Chuyển đổi chậm */
  --edtt-transition-normal: 0.2s ease-in-out;
  /* Chuyển đổi thông thường */
  --edtt-transition-fast: 0.1s ease-in-out;
  /* Chuyển đổi nhanh */
}

/* Learning Theme */
[data-theme='learning'] {
  /* Color Palette System */

  /* Primary Color (Sky Blue - truyền cảm hứng) */
  --edtt-color-primary-base: #00aeef;
  --edtt-color-primary-100: #e0f7ff;
  --edtt-color-primary-200: #b3e9ff;
  --edtt-color-primary-300: #80daff;
  --edtt-color-primary-400: #4dcaff;
  --edtt-color-primary-500: #1abafd;
  --edtt-color-primary-600: #0099d6;
  --edtt-color-primary-700: #0077a8;

  /* Secondary Color (Warm Yellow - tạo năng lượng) */
  --edtt-color-secondary-base: #ffc107;
  --edtt-color-secondary-100: #fff8e1;
  --edtt-color-secondary-200: #ffecb3;
  --edtt-color-secondary-300: #ffe082;
  --edtt-color-secondary-400: #ffd54f;
  --edtt-color-secondary-500: #ffc107;
  --edtt-color-secondary-600: #ffb300;
  --edtt-color-secondary-700: #ffa000;

  /* Tertiary Color (Fresh Green - sự phát triển) */
  --edtt-color-tertiary-base: #4caf50;
  --edtt-color-tertiary-100: #e8f5e9;
  --edtt-color-tertiary-200: #c8e6c9;
  --edtt-color-tertiary-300: #a5d6a7;
  --edtt-color-tertiary-400: #81c784;
  --edtt-color-tertiary-500: #4caf50;
  --edtt-color-tertiary-600: #43a047;
  --edtt-color-tertiary-700: #388e3c;

  /* Quaternary Color (Soft Purple - sáng tạo) */
  --edtt-color-quaternary-base: #9c27b0;
  --edtt-color-quaternary-100: #f3e5f5;
  --edtt-color-quaternary-200: #e1bee7;
  --edtt-color-quaternary-300: #ce93d8;
  --edtt-color-quaternary-400: #ba68c8;
  --edtt-color-quaternary-500: #9c27b0;
  --edtt-color-quaternary-600: #8e24aa;
  --edtt-color-quaternary-700: #7b1fa2;

  /* Quinary Color (Warm Orange - sự vui tươi) */
  --edtt-color-quinary-base: #ff5722;
  --edtt-color-quinary-100: #fbe9e7;
  --edtt-color-quinary-200: #ffccbc;
  --edtt-color-quinary-300: #ffab91;
  --edtt-color-quinary-400: #ff8a65;
  --edtt-color-quinary-500: #ff5722;
  --edtt-color-quinary-600: #f4511e;
  --edtt-color-quinary-700: #e64a19;
}

/* Dark Mode Support */
/* Learning Dark Theme */
[data-theme='dark'] {
  /* Color Palette System */

  /* Primary Color (Sky Blue - ánh sáng trong bóng tối) */
  --color-primary-base: #4dcfff;
  --color-primary-100: #e0f8ff;
  --color-primary-200: #a3e7ff;
  --color-primary-300: #75d9ff;
  --color-primary-400: #4dcfff;
  --color-primary-500: #29b7e6;
  --color-primary-600: #188fb3;
  --color-primary-700: #0e6c8f;

  /* Secondary Color (Vàng ấm - điểm nhấn nổi bật) */
  --color-secondary-base: #ffd54f;
  --color-secondary-100: #fff8e1;
  --color-secondary-200: #ffe082;
  --color-secondary-300: #ffd54f;
  --color-secondary-400: #ffc107;
  --color-secondary-500: #ffb300;
  --color-secondary-600: #ff9800;
  --color-secondary-700: #f57c00;

  /* Tertiary Color (Xanh Lá Đậm - sự sống, rõ nét) */
  --color-tertiary-base: #81c784;
  --color-tertiary-100: #e8f5e9;
  --color-tertiary-200: #a5d6a7;
  --color-tertiary-300: #81c784;
  --color-tertiary-400: #66bb6a;
  --color-tertiary-500: #4caf50;
  --color-tertiary-600: #388e3c;
  --color-tertiary-700: #2e7d32;

  /* Quaternary Color (Tím mềm - dịu và sáng tạo) */
  --color-quaternary-base: #ce93d8;
  --color-quaternary-100: #f3e5f5;
  --color-quaternary-200: #e1bee7;
  --color-quaternary-300: #ce93d8;
  --color-quaternary-400: #ba68c8;
  --color-quaternary-500: #ab47bc;
  --color-quaternary-600: #9c27b0;
  --color-quaternary-700: #7b1fa2;

  /* Quinary Color (Cam sáng - năng lượng trong bóng tối) */
  --color-quinary-base: #ff8a65;
  --color-quinary-100: #fbe9e7;
  --color-quinary-200: #ffccbc;
  --color-quinary-300: #ffab91;
  --color-quinary-400: #ff8a65;
  --color-quinary-500: #ff7043;
  --color-quinary-600: #f4511e;
  --color-quinary-700: #d84315;

  /* Senary Color (Xám than - nền chủ đạo dark mode) */
  --color-senary-base: #263238;
  --color-senary-100: #eceff1;
  --color-senary-200: #cfd8dc;
  --color-senary-300: #b0bec5;
  --color-senary-400: #78909c;
  --color-senary-500: #546e7a;
  --color-senary-600: #37474f;
  --color-senary-700: #263238;

  /* Default Theme Variables - Optimized */
  --edtt-color-primary: var(--color-primary-base);
  --edtt-color-bg-default: #121212;
  --edtt-color-bg-secondary: #1e1e1e;
  --edtt-color-disabled-bg: rgba(255, 255, 255, 0.05);
  --edtt-color-text-default: #e0e0e0;
  --edtt-color-text-primary: var(--color-primary-base);
  --edtt-color-disabled-text: rgba(255, 255, 255, 0.3);
  --edtt-color-selected: var(--color-primary-700);
  --edtt-shadow: 0 1px 2px 0 rgba(255, 255, 255, 0.06);
}

/* Enable smooth transitions between themes */
.theme-transitions-enabled {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease, box-shadow 0.3s ease;
}
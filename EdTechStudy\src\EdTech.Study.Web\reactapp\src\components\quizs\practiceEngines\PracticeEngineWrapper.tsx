import { useMemo, useRef, useState } from 'react';
import PracticeEngines from './PracticeEngines';
import {
  BaseQuestion,
  PracticeEngineContext,
  Question,
} from '../../../interfaces/quizs/questionBase';
import {
  PracticeEnginesRef,
  PracticeEnginesProps,
} from '../../../interfaces/quizs/practiceEngines.interface';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import usePracticesLogic from './hooks/usePracticesLogic';
import PracticeEngineSidebar from './PracticeEngineSidebar';
import { Guid } from 'guid-typescript';
import {
  FullscreenContainer,
  useFullscreenAdapter,
} from '../../common/Fullscreen';
import React from 'react';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

export interface IPracticeEngineWrapper {
  id: string;
  questions: BaseQuestion[];
}

// Zod schema for component validation
export const practiceEngineSchema = createComponentSchema({
  paramsSchema: {
    id: z.string(),
    showConfig: z.boolean().optional(),
    questions: z
      .array(
        z.object({
          id: z.string(),
          type: z.string(),
          title: z.string(),
          description: z.string().optional(),
          points: z.number().optional(),
          isCompleted: z.boolean().optional(),
          status: z.string().optional(),
          images: z
            .array(
              z.object({
                url: z.string(),
                alt: z.string().optional(),
              })
            )
            .optional(),
          answers: z
            .array(
              z.object({
                id: z.string(),
                isCorrect: z.boolean(),
                content: z.string().optional(),
                explanation: z.string().optional(),
              })
            )
            .optional(),
          userSelect: z
            .union([
              z.object({
                id: z.string(),
                isCorrect: z.boolean(),
                content: z.string().optional(),
                explanation: z.string().optional(),
              }),
              z.array(
                z.object({
                  id: z.string(),
                  isCorrect: z.boolean(),
                  content: z.string().optional(),
                  explanation: z.string().optional(),
                })
              ),
            ])
            .optional(),
          timeLimit: z.number().optional(),
          difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
          tags: z.array(z.string()).optional(),
          createdAt: z.string().optional(),
          updatedAt: z.string().optional(),
        })
      )
      .min(1),
    showProgressBar: z.boolean().optional(),
    onComplete: z.function().optional(),
    onRefresh: z.function().optional(),
    onShowMenu: z.function().optional(),
    onCreateQuestion: z.function().optional(),
  },
});

const PracticeEngineWrapper: React.FC<
  IEdTechRenderProps<PracticeEnginesProps>
> = (props) => {
  const { params, isEditing } = props;
  const id = useMemo(() => params?.id ?? Guid.create().toString(),[props.params]); // prettier-ignore
  const allQuestions = useMemo(() => {
    return props.params?.questions ?? [];
  }, [props.params?.questions]);

  const [currentQuestionId, setCurrentQuestionId] = useState<string | null>(
    props.params?.questions ? props.params?.questions[0]?.id : null
  );

  const practiceEngineRef = useRef<PracticeEnginesRef>(null);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const { isFullscreen, fullscreenRef, handleToggleFullscreen } =
    useFullscreenAdapter();
  // register base
  usePracticesLogic();

  // Xử lý sự kiện khi hoàn thành tất cả bài tập
  const handlePracticeComplete = (_: any) => {
    props.addOrUpdateParamComponent({
      id: id ?? '',
      questions: allQuestions.map((question) => ({
        ...question,
        isCompleted: false,
        userSelect: undefined,
        status: 'default',
      })),
    });
  };

  const handleChangeConfigQuestion = (update: Question) => {
    let res = allQuestions.map((p) => {
      if (p.id === update.id) {
        return {
          ...p,
          ...update,
          isCompleted: false,
          userSelect: undefined,
        } as Question;
      }
      return p;
    });

    props.addOrUpdateParamComponent({
      id: id,
      questions: res,
    });
  };

  const handleSaveAllQuestions = (id: string, questions: BaseQuestion[]) => {
    let updateIds = questions.map((p) => p.id);
    let res = allQuestions.map((p) => {
      if (updateIds.includes(p.id)) {
        return {
          ...p,
          ...questions.find((q) => q.id === p.id),
          isCompleted: false,
          userSelect: undefined,
        } as Question;
      }
      return p;
    });
    props.addOrUpdateParamComponent({
      id: id ?? '',
      questions: res,
    });
  };

  function handleShowMenu(_: React.MouseEvent<HTMLElement, MouseEvent>): void {
    // Show menu as a Sidebar
    setSidebarVisible(true);
  }

  const handleGoToQuestion = (questionId: string) => {
    if (practiceEngineRef.current) {
      practiceEngineRef.current.goToQuestion(questionId);
    }
  };

  const handleQuestionChange = (id: string) => {
    setCurrentQuestionId(id);
  };

  function handleDeleteQuestion(id: string) {
    props.addOrUpdateParamComponent({
      id: id,
      questions: allQuestions.filter((que) => que.id !== id),
    });
  }

  const handleCreateQuestion = (question: Question, index?: number) => {
    index ??= 0;
    if (index === undefined || index < 0 || index > allQuestions.length) {
      return [...allQuestions, question];
    }
    const res = [
      ...allQuestions.slice(0, index),
      question,
      ...allQuestions.slice(index),
    ];
    props.addOrUpdateParamComponent({
      id: id,
      questions: res,
    });
  };

  function refreshConfirm() {
    practiceEngineRef.current?.forceReset();
  }

  return (
    <FullscreenContainer ref={fullscreenRef}>
      <PracticeEngineContext.Provider
        value={{
          isFullscreen: isFullscreen,
          handleToggleFullscreen: handleToggleFullscreen,
          handleChangeQuestion: handleChangeConfigQuestion,
          handleDeleteQuestion: handleDeleteQuestion,
          handleChangePosition: handleQuestionChange,
          handleSaveAllQuestions: handleSaveAllQuestions,
        }}
      >
        <div
          id={'practice-engine-wrapper'}
          className="practice-engine-wrapper-usage"
          style={{
            flex: 1,
            overflow: 'auto',
          }}
        >
          <PracticeEngines
            id={'engine-core-' + id}
            ref={practiceEngineRef}
            questions={allQuestions ?? []}
            showConfig={isEditing}
            onComplete={handlePracticeComplete}
            onCreateQuestion={handleCreateQuestion}
            onRefresh={refreshConfirm}
            onShowMenu={handleShowMenu}
          />
        </div>
        <PracticeEngineSidebar
          visible={sidebarVisible}
          questions={allQuestions ?? []}
          onClose={() => setSidebarVisible(false)}
          onSelectQuestion={handleGoToQuestion}
          currentQuestionId={currentQuestionId ?? ''}
        />
      </PracticeEngineContext.Provider>
    </FullscreenContainer>
  );
};

export default withEdComponentParams(
  (props: IEdTechRenderProps<PracticeEnginesProps>) => {
    return <PracticeEngineWrapper {...props} />;
  }
);

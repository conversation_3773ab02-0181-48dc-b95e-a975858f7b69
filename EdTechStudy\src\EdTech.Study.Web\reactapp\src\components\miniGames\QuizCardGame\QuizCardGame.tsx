import React, { useState, useEffect, memo, useMemo } from 'react';
import '../MiniGameResponsive.css';
import {
  QuizCardGameConfig,
  defaultQuizCardGameConfig,
} from './QuizCardGameConfig';
import QuizCardGameComponent from './QuizCardGameComponent';
import QuizCardGameModal from './QuizCardGameModal';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { getQuestionsForGame } from '../../common/QuestionComponent/QuestionBankConfig';
import { EngineContainer } from '../../common/engines';
import { ITextProps } from '../../core/title/CoreTitle';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

interface QuizCardGameProps extends QuizCardGameConfig {}

export const quizCardSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    cards: z
      .array(
        z.object({
          question: z.string(),
          answer: z.string(),
          imageUrl: z.string().optional(),
        })
      )
      .optional(),
    difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  },
});

const QuizCardGame: React.FC<IEdTechRenderProps<QuizCardGameProps>> = (
  props
) => {
  const { params, addOrUpdateParamComponent, path } = props;
  // State for game configuration
  const [gameConfig, setGameConfig] = useState<QuizCardGameProps>({
    ...defaultQuizCardGameConfig,
    ...params,
  });

  // State for fullscreen and config modal
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);

  // Update configuration when params change (from Redux)
  useEffect(() => {
    let newConfig = {
      ...defaultQuizCardGameConfig,
    };

    if (params) {
      newConfig = { ...params };
    }
    // Kiểm tra xem có câu hỏi trong params không
    const hasQuestions = newConfig.questions && newConfig.questions.length > 0;
    // Đảm bảo danh sách câu hỏi được giữ nguyên từ params nếu có
    if (!hasQuestions) {
      // Nếu không có câu hỏi trong params, sử dụng câu hỏi mặc định
      newConfig.questions = getQuestionsForGame(15);
    }
    setGameConfig(newConfig);
  }, [params]);

  // Handle game config changes
  const handleConfigSubmit = (config: QuizCardGameConfig) => {
    if (addOrUpdateParamComponent) {
      // Preserve titleProps when updating config
      const updatedConfig = {
        ...config,
        titleProps: gameConfig.titleProps,
      };
      addOrUpdateParamComponent(updatedConfig);
      setGameConfig(updatedConfig);
    }

    // Close modal
    setIsConfigModalOpen(false);
  };

  // Handle title updates
  const handleTitleUpdate = (titleProps: Partial<ITextProps>) => {
    if (addOrUpdateParamComponent) {
      const updatedConfig = {
        ...gameConfig,
        titleProps: {
          ...gameConfig.titleProps,
          ...titleProps,
        },
      };
      addOrUpdateParamComponent(updatedConfig);
      setGameConfig(updatedConfig);
    }
  };

  // Handle fullscreen state changes
  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  // Create text props for the title
  const titleProps = gameConfig.titleProps || {
    text: 'Lật thẻ câu hỏi',
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Memoize the structured instruction props
  const instructionsContent = useMemo(
    () => ({
      objective:
        'Trả lời đúng các câu hỏi để lật các thẻ và kiếm điểm cao nhất có thể!',
      steps: [
        'Chọn một thẻ bất kỳ để xem câu hỏi',
        'Đọc câu hỏi và chọn đáp án bạn cho là đúng',
        'Trả lời đúng sẽ mở thẻ và cộng điểm thưởng',
        'Tiếp tục cho đến khi lật hết các thẻ',
      ],
      notes: `Điểm thưởng: +${gameConfig.correctAnswerScore} cho mỗi câu trả lời đúng. Điểm phạt: -${gameConfig.incorrectAnswerPenalty} cho mỗi câu trả lời sai. Càng trả lời đúng nhiều, điểm càng cao.`,
      isFullscreen: isFullscreen,
    }),
    [
      gameConfig.correctAnswerScore,
      gameConfig.incorrectAnswerPenalty,
      isFullscreen,
    ]
  );

  // Determine if configuration is allowed, default to true
  const allowConfiguration =
    params?.allowConfiguration !== undefined ? params.allowConfiguration : true;

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      allowConfiguration={allowConfiguration}
      onTitleUpdate={handleTitleUpdate}
      onFullscreenChange={handleFullscreenChange}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      id={path}
      mainComponent={
        <QuizCardGameComponent
          config={gameConfig}
          isFullscreen={isFullscreen}
        />
      }
      configModal={
        <QuizCardGameModal
          isOpen={isConfigModalOpen}
          onClose={() => setIsConfigModalOpen(false)}
          onSubmit={handleConfigSubmit}
          defaultConfig={gameConfig}
        />
      }
      instructionsContent={instructionsContent}
    />
  );
};

export default memo(withEdComponentParams(QuizCardGame));

import React, { useState, useEffect } from 'react';
import { Button, Input, Radio } from 'antd';
import { MultipleChoiceQuestion } from '../QuestionBankConfig';
import { DeleteIcon } from '../../../icons/IconIndex';

interface MultipleChoiceQuestionProps {
  question: MultipleChoiceQuestion;
  onAnswer: (index: number) => void;
  disabled?: boolean;
  configMode?: boolean;
  onOptionTextChange?: (index: number, text: string) => void;
  onCorrectAnswerChange?: (index: number) => void;
  onAddOption?: () => void;
  onRemoveOption?: (index: number) => void;
}

const MultipleChoiceQuestionComponent: React.FC<
  MultipleChoiceQuestionProps
> = ({
  question,
  onAnswer,
  disabled = false,
  configMode = false,
  onOptionTextChange,
  onCorrectAnswerChange,
  onAddOption,
  onRemoveOption,
}) => {
  // Sử dụng state nội bộ để theo dõi đáp án đúng
  const [correctIndex, setCorrectIndex] = useState<number>(
    question.correctOptionIndex
  );

  // Cập nhật state khi prop thay đổi
  useEffect(() => {
    setCorrectIndex(question.correctOptionIndex);
  }, [question.correctOptionIndex]);

  // Hàm xử lý khi người dùng thay đổi đáp án đúng
  const handleCorrectAnswerChange = (index: number) => {
    setCorrectIndex(index);
    if (onCorrectAnswerChange) {
      onCorrectAnswerChange(index);
    }
  };

  // If in config mode, render the editable version
  if (configMode) {
    return (
      <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-space-y-3 tailwind-mb-4">
        {question.options.map((option: string, index: number) => (
          <div
            key={index}
            className="tailwind-flex tailwind-items-center tailwind-space-x-2 tailwind-w-full"
          >
            <Radio
              checked={index === correctIndex}
              onChange={() => handleCorrectAnswerChange(index)}
            />
            <Input
              value={option}
              onChange={(e) =>
                onOptionTextChange && onOptionTextChange(index, e.target.value)
              }
              className="tailwind-flex-1"
              placeholder={`Tùy chọn ${String.fromCharCode(65 + index)}`}
            />
            {question.options.length > 2 && (
              <Button
                type="text"
                danger
                icon={<DeleteIcon />}
                onClick={() => onRemoveOption && onRemoveOption(index)}
              />
            )}
          </div>
        ))}
        <Button
          type="dashed"
          onClick={onAddOption}
          className="tailwind-w-full tailwind-mt-2"
        >
          Thêm tùy chọn
        </Button>
      </div>
    );
  }

  // Normal mode (non-config)
  return (
    <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-space-y-3 tailwind-mb-4">
      {question.options.map((option: string, index: number) => (
        <Button
          key={index}
          onClick={() => onAnswer(index)}
          type="default"
          disabled={disabled}
          className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-text-left tailwind-bg-white hover:tailwind-bg-indigo-50 tailwind-h-auto tailwind-py-3 tailwind-px-4 tailwind-min-w-0"
        >
          <div className="tailwind-w-8 tailwind-h-8 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-rounded-full tailwind-mr-4 tailwind-font-medium tailwind-flex-shrink-0">
            {String.fromCharCode(65 + index)}
          </div>
          <span className="tailwind-flex-1 tailwind-truncate">{option}</span>
        </Button>
      ))}
    </div>
  );
};

export default MultipleChoiceQuestionComponent;

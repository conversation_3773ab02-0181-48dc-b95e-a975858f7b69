.fill-blanks-container {
  margin-bottom: 20px;
}

.fill-blanks-question {
  margin-bottom: 1rem;
}

.fill-blanks-input {
  width: 150px;
  margin: 0 4px;
  display: inline-block;
}

.fill-blanks-input.correct {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.fill-blanks-input.incorrect {
  border-color: #ff4d4f;
  background-color: #fff2f0;
}

.fill-blanks-hint {
  color: #1890ff;
  margin-top: 4px;
  font-size: 12px;
}

.fill-blanks-controls {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.fill-blanks-explanation {
  margin-top: 1rem;
  padding: 8px;
  border-radius: 4px;
}

.fill-blanks-explanation.correct {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.fill-blanks-explanation.incorrect {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.blank-wrapper {
  display: inline-block;
  margin: 0 4px;
  position: relative;
}

.blank-feedback-icon {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
}

.blank-feedback-icon.correct {
  color: #52c41a;
}

.blank-feedback-icon.incorrect {
  color: #ff4d4f;
}

@keyframes iconAnimation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* For config mode */
.fill-blanks-config-section {
  margin-bottom: 20px;
}

.blank-config-item {
  border: 1px solid #f0f0f0;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
}

.blank-config-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.blank-alternatives {
  margin-top: 8px;
}

.blank-alternative-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

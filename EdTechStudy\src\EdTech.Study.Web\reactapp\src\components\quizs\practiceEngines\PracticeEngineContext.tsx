import React, { createContext, useState, useContext, ReactNode } from 'react';
import { Question } from '../../../interfaces/quizs/questionBase';
import { questionService } from './questionService';

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho context
interface PracticeEngineContextState {
  questions: Question[];
  originalQuestions: Question[];
  isModified: boolean;
  currentQuestionId: string | null;
  savingState: 'idle' | 'saving' | 'saved' | 'error';
  updateQuestion: (question: Question) => void;
  updateAllQuestions: (questions: Question[]) => void;
  deleteQuestion: (id: string) => void;
  resetToOriginal: () => void;
  setCurrentQuestion: (id: string) => void;
  setSavingState: (state: 'idle' | 'saving' | 'saved' | 'error') => void;
}

// Tạo context
const EngineContext = createContext<PracticeEngineContextState | undefined>(
  undefined
);

// Props cho Provider
interface PracticeEngineContextProviderProps {
  children: ReactNode;
  initialQuestions: Question[];
}

// Provider component
export const PracticeEngineContextProvider: React.FC<
  PracticeEngineContextProviderProps
> = ({ children, initialQuestions }) => {
  const [questions, setQuestions] = useState<Question[]>(initialQuestions);
  const [originalQuestions, setOriginalQuestions] =
    useState<Question[]>(initialQuestions);
  const [currentQuestionId, setCurrentQuestionId] = useState<string | null>(
    initialQuestions.length > 0 ? initialQuestions[0].id : null
  );
  const [savingState, setSavingState] = useState<
    'idle' | 'saving' | 'saved' | 'error'
  >('idle');

  // Kiểm tra xem có thay đổi nào không
  const isModified = questionService.hasQuestionsToSave(
    originalQuestions,
    questions
  );

  // Cập nhật một câu hỏi
  const updateQuestion = (updatedQuestion: Question) => {
    setQuestions((prevQuestions) =>
      prevQuestions.map((q) =>
        q.id === updatedQuestion.id ? updatedQuestion : q
      )
    );
  };

  // Cập nhật tất cả câu hỏi
  const updateAllQuestions = (newQuestions: Question[]) => {
    setQuestions(newQuestions);
  };

  // Xóa một câu hỏi
  const deleteQuestion = (id: string) => {
    setQuestions((prevQuestions) => prevQuestions.filter((q) => q.id !== id));
  };

  // Reset về trạng thái ban đầu
  const resetToOriginal = () => {
    setQuestions([...originalQuestions]);
  };

  // Đặt câu hỏi hiện tại
  const setCurrentQuestion = (id: string) => {
    setCurrentQuestionId(id);
  };

  // Tạo giá trị context
  const contextValue: PracticeEngineContextState = {
    questions,
    originalQuestions,
    isModified,
    currentQuestionId,
    savingState,
    updateQuestion,
    updateAllQuestions,
    deleteQuestion,
    resetToOriginal,
    setCurrentQuestion,
    setSavingState,
  };

  return (
    <EngineContext.Provider value={contextValue}>
      {children}
    </EngineContext.Provider>
  );
};

// Hook để sử dụng context
export const usePracticeEngineContext = () => {
  const context = useContext(EngineContext);
  if (context === undefined) {
    throw new Error(
      'usePracticeEngineContext must be used within a PracticeEngineContextProvider'
    );
  }
  return context;
};

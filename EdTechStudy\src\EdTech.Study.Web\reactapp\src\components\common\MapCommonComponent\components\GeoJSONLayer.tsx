import React, { useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';

interface GeoJSONLayerProps {
  data: any;
  pointToLayer?: (feature: any, latlng: L.LatLng) => L.Layer;
  style?: (feature: any) => L.PathOptions;
  onEachFeature?: (feature: any, layer: <PERSON><PERSON>Layer) => void;
  filter?: (feature: any) => boolean;
}

// Extended Layer interface to accommodate custom properties
interface ExtendedLayer extends L.Layer {
  getBounds?: () => L.LatLngBounds;
  _labels?: L.Marker[];
  feature?: any;
}

/**
 * Component hiển thị dữ liệu GeoJSON lên bản đồ
 */
const GeoJSONLayer: React.FC<GeoJSONLayerProps> = ({
  data,
  pointToLayer,
  style,
  onEachFeature,
  filter,
}) => {
  const map = useMap();
  // Use a ref to track which regions have been labeled to prevent duplicates
  const labeledRegionsRef = useRef<Set<string>>(new Set());
  // Store all created labels to manage zoom-based visibility
  const allLabelsRef = useRef<
    Array<{ label: L.<PERSON>; minZoom: number; maxZoom?: number }>
  >([]);
  // Add a spatial grid to track label positions and prevent overlaps
  const labelGridRef = useRef<Record<string, string>>({});

  // Function to check if a new label would overlap with existing ones
  const wouldLabelOverlap = (center: L.LatLng) => {
    // Convert to grid coordinates (divide map into cells)
    // Use larger cell size for lower zoom levels but smaller than before to allow more labels
    const currentZoom = map.getZoom();
    const gridSize = currentZoom < 6 ? 0.7 : currentZoom < 8 ? 0.35 : 0.2;

    // Calculate grid cell for this position
    const gridX = Math.floor(center.lng / gridSize);
    const gridY = Math.floor(center.lat / gridSize);

    // Check only immediate cell and cardinal directions (not diagonals) to allow more density
    const checkCells = [
      { x: gridX, y: gridY }, // current cell
      { x: gridX, y: gridY + 1 }, // north
      { x: gridX + 1, y: gridY }, // east
      { x: gridX, y: gridY - 1 }, // south
      { x: gridX - 1, y: gridY }, // west
    ];

    for (const cell of checkCells) {
      const cellKey = `${cell.x},${cell.y}`;
      if (labelGridRef.current[cellKey]) {
        return true; // Found a nearby label
      }
    }

    // Reserve this cell for the new label
    const cellKey = `${gridX},${gridY}`;
    labelGridRef.current[cellKey] = 'occupied';
    return false;
  };

  // Function to update label visibility based on current zoom level
  const updateLabelVisibility = () => {
    const currentZoom = map.getZoom();

    allLabelsRef.current.forEach((item) => {
      const { label, minZoom, maxZoom } = item;
      const isVisible =
        currentZoom >= minZoom &&
        (maxZoom === undefined || currentZoom <= maxZoom);

      // Apply visibility by toggling the opacity
      const iconElement = label.getElement();
      if (iconElement) {
        iconElement.style.opacity = isVisible ? '1' : '0';
      }
    });
  };

  useEffect(() => {
    if (!data) return;

    // Reset tracked states when data changes
    labeledRegionsRef.current = new Set();
    allLabelsRef.current = [];

    // Create a custom pane for labels to ensure they're always on top
    if (!map.getPane('labelsPane')) {
      map.createPane('labelsPane');
      map.getPane('labelsPane')!.style.zIndex = '650';
      map.getPane('labelsPane')!.style.pointerEvents = 'none';
    }

    // Add zoom handler to update label visibility
    map.on('zoomend', updateLabelVisibility);

    // Debug the first few features to check name properties

    // Mặc định onEachFeature để hiển thị popup với name và description
    const defaultOnEachFeature = (feature: any, layer: L.Layer) => {
      // Cast to extended layer type
      const extendedLayer = layer as ExtendedLayer;
      extendedLayer.feature = feature;

      // Extract region information from properties
      const regionName =
        feature.properties.Ten_Huyen || feature.properties.name || '';
      const provinceName = feature.properties.Ten_Tinh || '';
      const regionCode = feature.properties.Code_vung || '';

      // Check if this is a Hoàng Sa or Trường Sa region - use both Vietnamese and ASCII versions
      const isHoangSaTruongSa =
        regionName &&
        (regionName.includes('Trường Sa') ||
          regionName.includes('Truong Sa') ||
          regionName.includes('Hoàng Sa') ||
          regionName.includes('Hoang Sa'));
      const isHoangSa =
        regionName &&
        (regionName.includes('Hoàng Sa') || regionName.includes('Hoang Sa'));
      const isTruongSa =
        regionName &&
        (regionName.includes('Trường Sa') || regionName.includes('Truong Sa'));

      // Add specific name for unnamed features based on their position
      let enhancedRegionName = regionName;
      if (!regionName && feature.geometry && feature.geometry.type) {
        // Generate a name based on coordinates for unnamed features
        if (isHoangSa) {
          enhancedRegionName = 'Đảo Hoàng Sa';
        } else if (isTruongSa) {
          enhancedRegionName = 'Đảo Trường Sa';
        }
      }

      // Create popup content with available information - but only for non-Hoàng Sa, Trường Sa features
      if ((regionName || provinceName || regionCode) && !isHoangSaTruongSa) {
        const popupContent = `
          <div style="font-family: Arial, sans-serif; padding: 5px;">
            ${
              regionName
                ? `<h3 style="margin: 0; color: #0056b3; font-size: 16px; font-weight: bold;">${regionName}</h3>`
                : ''
            }
            ${
              provinceName
                ? `<p style="margin: 5px 0 0 0; font-weight: bold;">Tỉnh: ${provinceName}</p>`
                : ''
            }
            ${
              regionCode
                ? `<p style="margin: 5px 0 0 0; color: #666;">Mã vùng: ${regionCode}</p>`
                : ''
            }
            ${
              feature.properties.description
                ? `<p style="margin: 5px 0 0 0;">${feature.properties.description}</p>`
                : ''
            }
          </div>
        `;
        layer.bindPopup(popupContent);
      }

      // Create labels based on feature type and zoom level
      if (isHoangSaTruongSa) {
        const labelKey = `${regionName}-${
          feature.id || Math.random().toString(36).substring(2, 9)
        }`;

        // Skip if we already have this label (prevent duplicates)
        if (labeledRegionsRef.current.has(labelKey)) {
          return;
        }

        // Check if getBounds exists (for Polygon/MultiPolygon)
        const center = extendedLayer.getBounds
          ? extendedLayer.getBounds().getCenter()
          : null;
        if (center) {
          // Store labels in our layer for cleanup
          if (!extendedLayer._labels) {
            extendedLayer._labels = [];
          }

          // Determine the zoom level thresholds based on the feature size and importance
          let minZoom = 5; // Default minimum zoom
          let maxZoom; // No maximum by default

          // Determine if this is a main archipelago feature
          const isMainArchipelago =
            regionName === 'Quần đảo Hoàng Sa' ||
            regionName === 'Quần đảo Trường Sa' ||
            regionName === 'Quan dao Hoang Sa' ||
            regionName === 'Quan dao Truong Sa';

          // Main archipelago labels (highest priority - shown at low zoom levels)
          if (isMainArchipelago) {
            minZoom = 3; // Visible at very low zoom
            // Add to tracking to prevent duplicates
            labeledRegionsRef.current.add(labelKey);
          }
          // Labels for important island groups (shown at medium zoom)
          else if (
            (regionName.includes('Đảo') || regionName.includes('Cụm')) &&
            regionName.length > 5 && // Reduced from 6 to 5 to include more labels
            // Filtering conditions
            !regionName.includes('nhỏ') &&
            !regionName.includes('phụ') &&
            !regionName.toLowerCase().includes('vô danh') &&
            !regionName.toLowerCase().includes('khác')
          ) {
            minZoom = 6;
            // Check for overlaps before adding label
            if (wouldLabelOverlap(center)) {
              return; // Skip this label if it would overlap
            }
            // Add to tracking to prevent duplicates
            labeledRegionsRef.current.add(labelKey);
          }
          // Individual islands or features (only shown at higher zoom)
          else if (
            regionName.includes('Đá') &&
            regionName.length > 4 && // Reduced from 5 to 4 to include more labels
            !regionName.includes('nhỏ') &&
            !regionName.toLowerCase().includes('vô danh') &&
            !regionName.toLowerCase().includes('khác')
          ) {
            minZoom = 7;
            // Check for overlaps before adding label
            if (wouldLabelOverlap(center)) {
              return; // Skip this label if it would overlap
            }
            // Add to tracking to prevent duplicates
            labeledRegionsRef.current.add(labelKey);
          }
          // Very detailed features (only shown at high zoom)
          else if (
            regionName.length > 3 && // Reduced from 4 to 3 to include more labels
            !regionName.toLowerCase().includes('vô danh') &&
            !regionName.toLowerCase().includes('khác')
          ) {
            minZoom = 8; // Reduced from 9 to 8 to show more labels at lower zoom
            // Check for overlaps before adding label
            if (wouldLabelOverlap(center)) {
              return; // Skip this label if it would overlap
            }
            // Add to tracking to prevent duplicates
            labeledRegionsRef.current.add(labelKey);
          } else {
            // Skip less important labels completely
            return;
          }

          // Adjust label size based on name length and zoom level
          const iconSize: L.PointTuple =
            enhancedRegionName.length > 15
              ? [160, 22]
              : enhancedRegionName.length > 10
              ? [140, 22]
              : [120, 22];

          // Adjust icon anchor based on size
          const iconAnchor: L.PointTuple = [iconSize[0] / 2, 11];

          // Create OSM-style label with enhanced visibility
          const label = L.marker(center, {
            icon: L.divIcon({
              className: 'osm-style-label',
              html: `<div>${enhancedRegionName}</div>`,
              iconSize: iconSize,
              iconAnchor: iconAnchor,
            }),
            interactive: false, // Make the label non-interactive to prevent clicks
            zIndexOffset: 1000, // Ensure labels appear above other map elements
            pane: 'labelsPane', // Use our custom pane for labels
          });

          // Add to map and track it
          label.addTo(map);
          extendedLayer._labels.push(label);

          // Store in our zoom-based tracking
          allLabelsRef.current.push({
            label,
            minZoom,
            maxZoom,
          });

          // Apply initial visibility based on current zoom
          const currentZoom = map.getZoom();
          const isVisible =
            currentZoom >= minZoom &&
            (maxZoom === undefined || currentZoom <= maxZoom);
          const element = label.getElement();
          if (element) {
            element.style.opacity = isVisible ? '1' : '0';
          }
        }
      }
    };

    // Mặc định pointToLayer để tạo marker tùy chỉnh
    const defaultPointToLayer = (feature: any, latlng: L.LatLng) => {
      // Determine color based on region - use #9B9BB5 for Vietnamese territories
      const color =
        feature.properties?.Ten_Huyen?.includes('Trường Sa') ||
        feature.properties?.Ten_Huyen?.includes('Truong Sa') ||
        feature.properties?.Ten_Huyen?.includes('Hoàng Sa') ||
        feature.properties?.Ten_Huyen?.includes('Hoang Sa')
          ? '#9B9BB5'
          : '#9B9BB5';

      return L.circleMarker(latlng, {
        radius: 8,
        fillColor: color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8,
      });
    };

    // Mặc định style cho các polygon - enhanced for better visibility
    const defaultStyle = (feature: any) => {
      // Check if this is a Hoàng Sa or Trường Sa region
      const isVietnamTerritory =
        feature.properties?.Ten_Huyen?.includes('Trường Sa') ||
        feature.properties?.Ten_Huyen?.includes('Truong Sa') ||
        feature.properties?.Ten_Huyen?.includes('Hoàng Sa') ||
        feature.properties?.Ten_Huyen?.includes('Hoang Sa');

      // Use #9B9BB5 for Vietnamese maritime territories
      const territoryFillColor = '#9B9BB5'; // Light blue-gray
      const territoryBorderColor = '#9B9BB5'; // Matching border

      return {
        fillColor: isVietnamTerritory ? territoryFillColor : '#9B9BB5', // #9B9BB5 for territories
        color: isVietnamTerritory ? territoryBorderColor : '#9B9BB5',
        weight: isVietnamTerritory ? 2 : 2,
        opacity: isVietnamTerritory ? 0.9 : 0.8,
        fillOpacity: isVietnamTerritory ? 0.5 : 0.3,
        // Ensure this layer shows on top of other layers
        pane: 'overlayPane',
        // Override the z-index to ensure it's above base layers
        zIndex: 650,
      };
    };

    // Add custom CSS to ensure our GeoJSON layers display properly
    const styleEl = document.createElement('style');
    styleEl.innerHTML = `
      .leaflet-overlay-pane {
        z-index: 410 !important;
      }
      .osm-style-label {
        z-index: 1000 !important;
        pointer-events: none;
        font-family: "Noto Sans", Arial, sans-serif;
        font-size: 11px;
        font-weight: bold;
        color: #333;
        text-shadow: 
          -1px -1px 0 #fff,
          1px -1px 0 #fff,
          -1px 1px 0 #fff,
          1px 1px 0 #fff,
          0 0 5px #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        letter-spacing: 0.05em;
        transition: opacity 0.2s ease-in-out;
        white-space: nowrap;
        overflow: visible;
      }
      .osm-style-label div {
        padding: 2px 4px;
        white-space: nowrap;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    `;
    styleEl.id = 'geojson-layer-styles';
    document.head.appendChild(styleEl);

    // Tạo GeoJSON layer
    const geoJSONLayer = L.geoJSON(data, {
      pointToLayer: pointToLayer || defaultPointToLayer,
      style: style || defaultStyle,
      onEachFeature: onEachFeature || defaultOnEachFeature,
      filter: filter,
    });

    // Thêm layer vào bản đồ
    geoJSONLayer.addTo(map);

    // Initialize label visibility
    updateLabelVisibility();

    // Cleanup khi component unmount
    return () => {
      // Remove zoom event handler
      map.off('zoomend', updateLabelVisibility);

      // Remove the labels
      if (geoJSONLayer.getLayers) {
        geoJSONLayer.getLayers().forEach((layer) => {
          // Cast to extended layer type
          const extendedLayer = layer as ExtendedLayer;
          if (extendedLayer._labels) {
            extendedLayer._labels.forEach((label) => {
              map.removeLayer(label);
            });
          }
        });
      }

      map.removeLayer(geoJSONLayer);

      // Clear the label grid
      labelGridRef.current = {};

      // Remove custom styles
      const existingStyle = document.getElementById('geojson-layer-styles');
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, [data, map, pointToLayer, style, onEachFeature, filter]);

  return null;
};

export default GeoJSONLayer;

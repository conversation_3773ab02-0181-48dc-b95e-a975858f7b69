import React, { useState, useRef, useEffect, ReactElement } from 'react';
import { Tooltip } from 'antd';
import { getFullscreenElement } from '../../common/Fullscreen';
import { TooltipPropsWithTitle } from 'antd/es/tooltip';

export interface ITooltipAntdCustomProps extends TooltipPropsWithTitle {
  children: ReactElement;
}

const TooltipAntdCustom = (props: ITooltipAntdCustomProps) => {
  const {
    children,
    title,
    getTooltipContainer = getFullscreenElement as any,
    ...rest
  } = props;

  const triggerRef = useRef<HTMLElement>(null);
  const [showTooltip, setShowTooltip] = useState(false);

  // Handle different trigger types
  const triggerType = rest.trigger || 'hover';

  // Create a wrapper div to attach our ref and events
  const handleMouseEnter = (e: React.MouseEvent) => {
    if (triggerType === 'hover') {
      setShowTooltip(true);
    }
    // Call the original onMouseEnter if it exists
    if (children.props.onMouseEnter) {
      children.props.onMouseEnter(e);
    }
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    if (triggerType === 'hover') {
      setShowTooltip(false);
    }
    // Call the original onMouseLeave if it exists
    if (children.props.onMouseLeave) {
      children.props.onMouseLeave(e);
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    // For click trigger type
    if (triggerType === 'click') {
      setShowTooltip(!showTooltip);
    }
    // Call the original onClick if it exists
    if (children.props.onClick) {
      children.props.onClick(e);
    }
  };

  const handleFocus = (e: React.FocusEvent) => {
    if (triggerType === 'focus') {
      setShowTooltip(true);
    }
    // Call the original onFocus if it exists
    if (children.props.onFocus) {
      children.props.onFocus(e);
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    if (triggerType === 'focus') {
      setShowTooltip(false);
    }
    // Call the original onBlur if it exists
    if (children.props.onBlur) {
      children.props.onBlur(e);
    }
  };

  // Calculate position for the tooltip
  const [position, setPosition] = useState({ top: 0, left: 0 });

  // Update position when the tooltip is shown
  useEffect(() => {
    if (showTooltip && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
      });
    }
  }, [showTooltip]);

  // Use Ant Design's Tooltip but with our own show/hide logic
  return (
    <div
      ref={triggerRef as any}
      style={{
        display: 'inline-block',
        width: 'fit-content',
        margin: 0,
        padding: 0,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      onFocus={handleFocus}
      onBlur={handleBlur}
    >
      {children}
      {showTooltip && title && (
        <Tooltip
          {...rest}
          title={title}
          open={true}
          getTooltipContainer={getTooltipContainer}
        >
          <span
            style={{
              position: 'absolute',
              top: `${position.top}px`,
              left: `${position.left}px`,
              display: 'inline-block',
              width: '0',
              height: '0',
              overflow: 'hidden',
            }}
          />
        </Tooltip>
      )}
    </div>
  );
};

export default TooltipAntdCustom;

import React, { useState, memo } from 'react';
import { <PERSON><PERSON>, Tooltip, Layout } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  FullscreenOutlined,
} from '@ant-design/icons';
import ThemeSelector from '../../../../../themes/ThemeSelector';
import LessonSidebarMenu from '../../../../../common/LessonSidebarMenu/LessonSidebarMenu';
import { IEdTechRenderTreeData } from '../../../../../../interfaces/AppComponents';

const { Sider } = Layout;

interface FixedButtonsContainerProps {
  handleExitPreview: () => void;
  toggleFullscreen: () => void;
  subItems?: IEdTechRenderTreeData[];
  currentSlideItemId?: string;
  onMenuSelect: (key: string) => void;
}

const FixedButtonsContainer: React.FC<FixedButtonsContainerProps> = ({
  handleExitPreview,
  toggleFullscreen,
  subItems,
  onMenuSelect,
}) => {
  // State for sidebar visibility
  const [sidebarVisible, setSidebarVisible] = useState<boolean>(false);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  // No need to create menu items, we'll pass subItems directly

  // Common button class
  const buttonClass =
    'tailwind-flex tailwind-items-center tailwind-justify-center tailwind-transition-all tailwind-duration-300 tailwind-ease-in-out hover:tailwind-scale-110 active:tailwind-scale-95 tailwind-shadow-md hover:tailwind-shadow-lg focus:tailwind-outline-none';
  return (
    <>
      {/* Fixed position sidebar */}
      {
        <Sider
          width={300}
          className="tailwind-fixed tailwind-top-0 tailwind-right-0 tailwind-bottom-0 tailwind-h-screen tailwind-overflow-hidden tailwind-transition-all tailwind-duration-300 tailwind-ease-in-out tailwind-shadow-md tailwind-border-l tailwind-border-gray-200 tailwind-z-10"
          collapsible={false}
          reverseArrow={true}
          trigger={null}
          hidden={!sidebarVisible}
        >
          <div className="tailwind-h-full tailwind-overflow-y-auto tailwind-overflow-x-hidden">
            <LessonSidebarMenu
              title="Nội dung"
              onSelect={onMenuSelect}
              subitems={subItems}
            />
          </div>
        </Sider>
      }

      {/* Fixed buttons - changed to vertical column */}
      <div className="tailwind-fixed tailwind-right-5 tailwind-bottom-5 tailwind-flex tailwind-flex-col tailwind-gap-2.5 tailwind-z-50">
        {/* Theme selector */}
        <ThemeSelector showCustomizer={false} />

        {/* Menu button */}
        <Tooltip
          title={sidebarVisible ? 'Ẩn menu' : 'Hiện menu'}
          placement="left"
        >
          <Button
            className={buttonClass}
            type="primary"
            shape="circle"
            icon={
              sidebarVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />
            }
            onClick={toggleSidebar}
          />
        </Tooltip>

        {/* Config button */}
        <Tooltip title="Cấu hình" placement="left">
          <Button
            className={buttonClass}
            type="primary"
            shape="circle"
            icon={<SettingOutlined />}
            onClick={handleExitPreview}
          />
        </Tooltip>

        {/* Fullscreen button */}
        <Tooltip title={'Thoát toàn màn hình'} placement="left">
          <Button
            className={buttonClass}
            type="primary"
            shape="circle"
            icon={<FullscreenOutlined />}
            onClick={toggleFullscreen}
          />
        </Tooltip>
      </div>
    </>
  );
};

export default memo(FixedButtonsContainer);

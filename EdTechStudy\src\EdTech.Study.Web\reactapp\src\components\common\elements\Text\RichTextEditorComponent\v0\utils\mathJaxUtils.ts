/**
 * Utility functions for MathJax integration
 */

/**
 * Typeset MathJax formulas in the editor
 */
export const typesetMathJax = (): void => {
  if (
    (window as any).MathJax &&
    typeof (window as any).MathJax.typesetPromise === 'function'
  ) {
    try {
      // First, clear any previous typesetting to avoid conflicts
      if (typeof (window as any).MathJax.typesetClear === 'function') {
        try {
          (window as any).MathJax.typesetClear();
        } catch (clearError) {
          console.warn('Error clearing MathJax typesetting:', clearError);
        }
      }

      // Find all formula elements in the editor
      const editorContent = document.querySelector(
        '.e-richtexteditor .e-content'
      );
      if (editorContent) {
        const formulas = editorContent.querySelectorAll(
          '.mathjax-formula, .katex-formula, .math-latex'
        );

        // Process each formula element
        formulas.forEach((formula) => {
          try {
            // Ensure the formula has proper LaTeX content
            const latex = formula.getAttribute('data-latex');
            if (latex) {
              const decodedLatex = decodeURIComponent(latex);
              // Make sure the formula has proper delimiters
              if (
                !(formula.textContent || '').includes('\\(') &&
                !(formula.textContent || '').includes('\\)')
              ) {
                formula.textContent = `\\(${decodedLatex}\\)`;
              }
            }
          } catch (formulaError) {
            console.warn(`Error processing formula:`, formulaError);
          }
        });
      }

      // Run MathJax typesetting
      (window as any).MathJax.typesetPromise()
        .then(() => {
          // Typesetting completed successfully
        })
        .catch((error: any) => {
          console.error('MathJax typesetting error:', error);
        });
    } catch (error) {
      console.error('Error during MathJax typesetting:', error);
    }
  } else {
    console.warn('MathJax not available or typesetPromise not a function');
  }
};

/**
 * Configure MathJax with default settings
 */
export const configureMathJax = (): void => {
  // Define MathJax configuration if it doesn't exist
  if (!(window as any).MathJax) {
    (window as any).MathJax = {
      tex: {
        inlineMath: [
          ['$', '$'],
          ['\\(', '\\)'],
        ],
        displayMath: [
          ['$$', '$$'],
          ['\\[', '\\]'],
        ],
        processEscapes: true,
        processEnvironments: true,
        packages: [
          'base',
          'ams',
          'noerrors',
          'noundefined',
          'newcommand',
          'boldsymbol',
        ],
      },
      options: {
        skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
        enableMenu: false, // Disable the context menu for better editor integration
        processHtmlClass: 'mathjax-formula|katex-formula|math-latex', // Process these classes
      },
      startup: {
        typeset: true,
        elements: ['e-content', 'rich-text-content'], // Target these elements for typesetting
        document: document,
      },
      svg: {
        fontCache: 'global', // Improve performance with global font caching
      },
    };
  }
};

/**
 * Sanitize LaTeX formula by removing common delimiters
 */
export const sanitizeFormula = (formula: string): string => {
  if (!formula || formula.trim() === '') {
    return '';
  }

  let sanitizedFormula = formula;
  // Remove common LaTeX delimiters if they exist
  const delimiters = [
    ['\\(', '\\)'],
    ['$$', '$$'],
    ['\\[', '\\]'],
    ['$', '$'],
  ];

  for (const [start, end] of delimiters) {
    if (
      sanitizedFormula.startsWith(start) &&
      sanitizedFormula.endsWith(end)
    ) {
      sanitizedFormula = sanitizedFormula.slice(start.length, -end.length);
      break;
    }
  }

  return sanitizedFormula;
};

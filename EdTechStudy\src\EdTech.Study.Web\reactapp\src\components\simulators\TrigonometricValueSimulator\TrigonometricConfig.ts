// TrigonometricConfig.ts
import { ITextProps } from '../../../components/core/title/CoreTitle';

export interface SpecialAngle {
  angle: number;
  sin: string;
  cos: string;
  tan: string;
  cot: string;
}

export interface TrigonometricDisplayOptions {
  showUnitCircle: boolean;
  showGrid: boolean;
  showCoordinates: boolean;
  showAngleInRadians: boolean;
  showExactValues: boolean;
  showLabels: boolean;
  showSinCosLines: boolean;
  showTangentLine: boolean;
  showCotangentLine: boolean;
  showTable: boolean;
  showFormulas: boolean;
  // New options for panel visibility
  showPropertiesPanel: boolean;
  showTheoryPanel: boolean;
}

export interface TrigonometricConfig {
  titleProps: ITextProps;
  description: string;
  initialAngle: number; // in degrees
  language: 'en' | 'vi';
  specialAngles: SpecialAngle[];
  displayOptions: TrigonometricDisplayOptions;
  enableQuadrants: {
    first: boolean; // 0-90°
    second: boolean; // 90-180°
    third: boolean; // 180-270°
    fourth: boolean; // 270-360°
  };
  circleRadius: number;
  precision: number;
  angleSnapThreshold: number; // Snap to special angles if within this threshold (degrees)
}

// Predefined special angles with exact representations
export const specialAnglesData: SpecialAngle[] = [
  {
    angle: 0,
    sin: '0',
    cos: '1',
    tan: '0',
    cot: 'Không xác định',
  },
  {
    angle: 30,
    sin: '1/2',
    cos: '√3/2',
    tan: '1/√3',
    cot: '√3',
  },
  {
    angle: 45,
    sin: '√2/2',
    cos: '√2/2',
    tan: '1',
    cot: '1',
  },
  {
    angle: 60,
    sin: '√3/2',
    cos: '1/2',
    tan: '√3',
    cot: '1/√3',
  },
  {
    angle: 90,
    sin: '1',
    cos: '0',
    tan: 'Không xác định',
    cot: '0',
  },
  {
    angle: 120,
    sin: '√3/2',
    cos: '-1/2',
    tan: '-√3',
    cot: '-1/√3',
  },
  {
    angle: 135,
    sin: '√2/2',
    cos: '-√2/2',
    tan: '-1',
    cot: '-1',
  },
  {
    angle: 150,
    sin: '1/2',
    cos: '-√3/2',
    tan: '-1/√3',
    cot: '-√3',
  },
  {
    angle: 180,
    sin: '0',
    cos: '-1',
    tan: '0',
    cot: 'Không xác định',
  },
  {
    angle: 210,
    sin: '-1/2',
    cos: '-√3/2',
    tan: '1/√3',
    cot: '√3',
  },
  {
    angle: 225,
    sin: '-√2/2',
    cos: '-√2/2',
    tan: '1',
    cot: '1',
  },
  {
    angle: 240,
    sin: '-√3/2',
    cos: '-1/2',
    tan: '√3',
    cot: '1/√3',
  },
  {
    angle: 270,
    sin: '-1',
    cos: '0',
    tan: 'Không xác định',
    cot: '0',
  },
  {
    angle: 300,
    sin: '-√3/2',
    cos: '1/2',
    tan: '-√3',
    cot: '-1/√3',
  },
  {
    angle: 315,
    sin: '-√2/2',
    cos: '√2/2',
    tan: '-1',
    cot: '-1',
  },
  {
    angle: 330,
    sin: '-1/2',
    cos: '√3/2',
    tan: '-1/√3',
    cot: '-√3',
  },
  {
    angle: 360,
    sin: '0',
    cos: '1',
    tan: '0',
    cot: 'Không xác định',
  },
];

export const defaultTrigonometricConfig: TrigonometricConfig = {
  titleProps: {
    text: 'Trigonometric Values Visualizer',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  description:
    'Visualize the trigonometric values (sine, cosine, tangent, cotangent) for any angle',
  initialAngle: 45,
  language: 'vi',
  specialAngles: specialAnglesData,
  displayOptions: {
    showUnitCircle: true,
    showGrid: true,
    showCoordinates: true,
    showAngleInRadians: false,
    showExactValues: true,
    showLabels: true,
    showSinCosLines: true,
    showTangentLine: true,
    showCotangentLine: true,
    showTable: true,
    showFormulas: true,
    // Default values for new panel visibility options
    showPropertiesPanel: true,
    showTheoryPanel: true,
  },
  enableQuadrants: {
    first: true,
    second: true,
    third: true,
    fourth: true,
  },
  circleRadius: 180,
  precision: 4,
  angleSnapThreshold: 3,
};

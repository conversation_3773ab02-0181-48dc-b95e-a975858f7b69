import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  FreefallSimulatorConfig,
  FreefallEnvironment,
  FreefallDataPoint,
  FreefallSimulationResult,
} from './FreefallSimulatorConfig';
import {
  simulateFreeFall,
  formatWithUnits,
  findClosestDataPointIndex,
} from './FreefallUtils';
import { Button, Tooltip, message, Table } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepForwardOutlined,
  ReloadOutlined,
  AreaChartOutlined,
  TableOutlined,
  ExperimentOutlined,
  InfoCircleOutlined,
  CalculatorOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BarChartOutlined,
  FunctionOutlined,
} from '@ant-design/icons';

interface FreefallSimulatorComponentProps {
  config: FreefallSimulatorConfig;
  onSimulationComplete?: (result: FreefallSimulationResult) => void;
  onDataChange?: (data: any) => void;
}

const FreefallSimulatorComponent: React.FC<FreefallSimulatorComponentProps> = ({
  config,
  onSimulationComplete,
  onDataChange,
}) => {
  // Simulation state
  const [isSimulating, setIsSimulating] = useState(false);
  const [isSimulationComplete, setIsSimulationComplete] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [simulationResult, setSimulationResult] =
    useState<FreefallSimulationResult | null>(null);
  const [activeEnvironment, setActiveEnvironment] =
    useState<FreefallEnvironment | null>(null);
  const [currentDataPoint, setCurrentDataPoint] =
    useState<FreefallDataPoint | null>(null);

  // UI visibility state
  const [showGraph, setShowGraph] = useState(
    config.displayOptions.realTimeGraph
  );
  const [showFormulas, setShowFormulas] = useState(
    config.specialFeatures.showFormulas
  );

  // Canvas references
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>(0);
  const lastTimeRef = useRef<number>(0);

  // Initialize simulation
  useEffect(() => {
    // Set active environment
    const env = config.environments.find(
      (e) => e.id === config.activeEnvironmentId
    );
    setActiveEnvironment(env || config.environments[0]);

    // Reset simulation state
    setCurrentTime(0);
    setIsSimulating(false);
    setIsPaused(false);

    // Run initial simulation to get data
    if (env) {
      const result = simulateFreeFall(
        config.initialHeight,
        config.initialVelocity,
        env,
        config.object,
        config.timeStep
      );

      // Use the object's data for initial setup
      setSimulationResult(result);
      setCurrentDataPoint(result.dataPoints[0]);

      if (onDataChange) {
        onDataChange({
          action: 'simulation_initialized',
          environment: env.name,
          object: config.object.name,
          height: config.initialHeight,
          velocity: config.initialVelocity,
        });
      }
    }

    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [config, onDataChange]);

  // Handle animation frame updates
  // Use useCallback to ensure the animate function is properly memoized
  const animate = useCallback(
    (timestamp: number) => {
      if (!isSimulating || isPaused || !simulationResult) {
        lastTimeRef.current = 0;
        return; // Don't continue the animation loop if paused/stopped
      }

      if (!lastTimeRef.current) {
        lastTimeRef.current = timestamp;
      }

      const deltaTime = (timestamp - lastTimeRef.current) / 1000;
      lastTimeRef.current = timestamp;

      const newTime = Math.min(
        currentTime + deltaTime,
        simulationResult.timeElapsed
      );

      setCurrentTime(newTime);

      const index = findClosestDataPointIndex(
        simulationResult.dataPoints,
        newTime
      );
      if (index >= 0) {
        setCurrentDataPoint(simulationResult.dataPoints[index]);
      }

      // Check if simulation has reached the end (only when object hits ground)
      if (
        currentDataPoint &&
        currentDataPoint.position <= config.object.radius
      ) {
        setIsSimulating(false);
        setIsPaused(false);
        setIsSimulationComplete(true);

        if (onSimulationComplete && simulationResult) {
          onSimulationComplete(simulationResult);
        }

        message.success('Mô phỏng hoàn thành!');
      } else {
        // Always request next animation frame when simulation is still running
        animationFrameRef.current = requestAnimationFrame(animate);
      }
    },
    [
      currentTime,
      isSimulating,
      isPaused,
      onSimulationComplete,
      simulationResult,
      config.object.radius,
      currentDataPoint,
    ]
  );

  // Modified useEffect to properly handle animation frames
  useEffect(() => {
    if (isSimulating && !isPaused) {
      // Cancel any previous animation frame to avoid duplicate animations
      cancelAnimationFrame(animationFrameRef.current);

      // Reset last timestamp and start a new animation loop
      lastTimeRef.current = 0;
      animationFrameRef.current = requestAnimationFrame(animate);
    } else {
      // Cancel animation when paused or stopped
      cancelAnimationFrame(animationFrameRef.current);
    }

    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [isSimulating, isPaused, animate]);

  // Helper functions for color manipulation
  const lightenColor = (color: string, percent: number): string => {
    // Check if color exists and is a string - fixes the "color.replace is not a function" error
    if (!color || typeof color !== 'string') {
      // Return a default color if input is invalid
      return '#FF5733';
    }

    // Make sure color is in hex format (starts with #)
    if (!color.startsWith('#')) {
      // If it's in another format, return a default color
      return '#FF5733';
    }

    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = Math.min(255, (num >> 16) + amt);
    const G = Math.min(255, ((num >> 8) & 0x00ff) + amt);
    const B = Math.min(255, (num & 0x0000ff) + amt);
    return `#${((1 << 24) | (R << 16) | (G << 8) | B).toString(16).slice(1)}`;
  };

  const darkenColor = (color: string, percent: number): string => {
    // Check if color exists and is a string - fixes the "color.replace is not a function" error
    if (!color || typeof color !== 'string') {
      // Return a default color if input is invalid
      return '#CD5700';
    }

    // Make sure color is in hex format (starts with #)
    if (!color.startsWith('#')) {
      // If it's in another format, return a default color
      return '#CD5700';
    }

    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = Math.max(0, (num >> 16) - amt);
    const G = Math.max(0, ((num >> 8) & 0x00ff) - amt);
    const B = Math.max(0, (num & 0x0000ff) - amt);
    return `#${((1 << 24) | (R << 16) | (G << 8) | B).toString(16).slice(1)}`;
  };

  // Cloud drawing function
  const drawCloud = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    size: number
  ) => {
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.arc(x + size * 0.5, y - size * 0.3, size * 0.7, 0, Math.PI * 2);
    ctx.arc(x + size, y, size * 0.8, 0, Math.PI * 2);
    ctx.arc(x + size * 1.4, y + size * 0.1, size * 0.6, 0, Math.PI * 2);
    ctx.arc(x + size * 0.5, y + size * 0.4, size * 0.7, 0, Math.PI * 2);
    ctx.fill();
  };

  // Helper function to draw rounded rectangles
  const roundRect = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number
  ) => {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  };

  // Draw the current state on canvas
  useEffect(() => {
    if (!canvasRef.current || !currentDataPoint || !activeEnvironment) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set coordinate system (origin at bottom left)
    // Scale: 1 meter = X pixels (adjust based on canvas size and max height)
    const scale = canvas.height / (config.initialHeight * 1.2); // Leave some margin

    // Draw background with gradient sky effect
    const bgGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    bgGradient.addColorStop(0, '#87CEEB'); // Sky blue at the top
    bgGradient.addColorStop(1, '#E0F7FF'); // Lighter blue at the bottom
    ctx.fillStyle = bgGradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add some aesthetic clouds
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    // Cloud 1
    drawCloud(ctx, canvas.width * 0.2, 60, 40);
    // Cloud 2
    drawCloud(ctx, canvas.width * 0.6, 40, 30);
    // Cloud 3
    drawCloud(ctx, canvas.width * 0.8, 70, 35);

    // Draw grid if enabled
    if (config.displayOptions.showGrid) {
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;

      // Draw horizontal grid lines (every 10 meters)
      // Align with the ground level at canvas.height - 30
      for (let y = 0; y <= config.initialHeight * 1.2; y += 10) {
        // Adjust canvasY to match with the ruler's scale and ground level
        const canvasY = canvas.height - 30 - y * scale;
        ctx.beginPath();
        ctx.moveTo(0, canvasY);
        ctx.lineTo(canvas.width, canvasY);
        ctx.stroke();
      }
    }

    // Draw more realistic ground with texture
    // Ground gradient
    const groundGradient = ctx.createLinearGradient(
      0,
      canvas.height - 30,
      0,
      canvas.height
    );
    groundGradient.addColorStop(0, '#8d6e63'); // Light soil color
    groundGradient.addColorStop(1, '#5d4037'); // Darker soil color
    ctx.fillStyle = groundGradient;
    ctx.fillRect(0, canvas.height - 30, canvas.width, 30);

    // Add ground texture/pattern
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    for (let i = 0; i < canvas.width; i += 15) {
      const randomHeight = Math.random() * 8 + 2;
      ctx.fillRect(i, canvas.height - randomHeight, 10, randomHeight);
    }

    // Add a line at the top of the ground for clarity
    ctx.strokeStyle = '#5d4037';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, canvas.height - 30);
    ctx.lineTo(canvas.width, canvas.height - 30);
    ctx.stroke();

    // Calculate position on canvas
    // Make sure object doesn't go below ground level - clamp to ground level if it would
    // Object position is the center of the object, so we need to ensure the bottom of the object
    // doesn't go below ground level (canvas.height - 30)
    const position = Math.max(currentDataPoint.position, config.object.radius);

    // Center the object horizontally
    const canvasX = canvas.width / 2;
    const canvasY =
      canvas.height - 30 - (position - config.object.radius) * scale;

    // Draw object as a circle with realistic shading
    // Increase the object size by multiplying the radius with a factor of 8.0
    const enlargedRadius = config.object.radius * 8.0;

    // Create gradient for 3D effect
    const gradient = ctx.createRadialGradient(
      canvasX - enlargedRadius * scale * 0.3,
      canvasY - enlargedRadius * scale * 0.3,
      0,
      canvasX,
      canvasY,
      enlargedRadius * scale
    );
    gradient.addColorStop(0, lightenColor(config.object.color, 50));
    gradient.addColorStop(1, darkenColor(config.object.color, 20));

    // Draw the main circle with gradient fill
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(canvasX, canvasY, enlargedRadius * scale, 0, Math.PI * 2);
    ctx.fill();

    // Add subtle border
    ctx.strokeStyle = darkenColor(config.object.color, 30);
    ctx.lineWidth = 1;
    ctx.stroke();

    // Add highlight/reflection for more realism
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.beginPath();
    ctx.arc(
      canvasX - enlargedRadius * scale * 0.4,
      canvasY - enlargedRadius * scale * 0.4,
      enlargedRadius * scale * 0.3,
      0,
      Math.PI * 2
    );
    ctx.fill();

    // Add velocity label
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.fillStyle = currentDataPoint.velocity > 0 ? '#4CAF50' : '#E53935';
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 3;

    // Draw velocity text with outline and direction indicator
    const velocityDirection = currentDataPoint.velocity < 0 ? '↓' : '↑';
    const velocityText = `${Math.abs(currentDataPoint.velocity).toFixed(
      1
    )} m/s ${velocityDirection}`;

    ctx.strokeText(
      velocityText,
      canvasX,
      canvasY - enlargedRadius * scale - 10
    );
    ctx.fillText(velocityText, canvasX, canvasY - enlargedRadius * scale - 10);

    // Draw enhanced timer if enabled
    if (config.displayOptions.showTimer) {
      const timerX = 170;
      const timerY = 30;

      // Draw timer background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      roundRect(ctx, timerX - 60, timerY - 15, 120, 30, 8);
      ctx.fill();

      // Draw timer border
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw timer text with shadow
      ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
      ctx.shadowBlur = 5;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 2;
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        `⏱️ ${formatWithUnits(currentTime, 's')}`,
        timerX,
        timerY + 8
      );
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    }

    // Draw ruler if enabled
    if (config.displayOptions.showRuler) {
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;

      // Draw vertical ruler on the left side
      ctx.beginPath();
      ctx.moveTo(30, 50);
      ctx.lineTo(30, canvas.height - 30); // Update to match the ground level at canvas.height - 30
      ctx.stroke();

      // Draw tick marks and labels
      for (let y = 0; y <= config.initialHeight; y += 10) {
        const canvasY = canvas.height - 30 - y * scale; // Adjusted to start at ground level (canvas.height - 30)

        // Draw tick mark
        ctx.beginPath();
        ctx.moveTo(25, canvasY);
        ctx.lineTo(35, canvasY);
        ctx.stroke();

        // Label major ticks
        ctx.fillStyle = '#000000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`${y}`, 20, canvasY + 4);
      }

      // Draw ruler label at the top of the axis (horizontally)
      ctx.save();
      // Position at the top of the ruler
      ctx.textAlign = 'center';
      ctx.font = '14px Arial';
      ctx.fillStyle = '#000000';
      ctx.fillText('Chiều cao (m)', 60, 35); // Position above the ruler
      ctx.restore();
    }

    // Draw force diagrams if enabled
    if (config.displayOptions.showForceVectors && activeEnvironment) {
      const canvasX = canvas.width / 2 + 50;
      const canvasY = canvas.height / 2;

      // Draw weight force
      const weightForce = config.object.mass * activeEnvironment.gravity;
      const forceScale = 10; // Scale factor for force vectors

      ctx.strokeStyle = '#006400'; // Dark green for weight
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.moveTo(canvasX, canvasY);
      ctx.lineTo(canvasX, canvasY + weightForce * forceScale);
      ctx.stroke();

      // Arrow for weight
      ctx.beginPath();
      ctx.moveTo(canvasX, canvasY + weightForce * forceScale);
      ctx.lineTo(canvasX - 5, canvasY + weightForce * forceScale - 10);
      ctx.lineTo(canvasX + 5, canvasY + weightForce * forceScale - 10);
      ctx.closePath();
      ctx.fillStyle = '#006400';
      ctx.fill();

      // Label for weight
      ctx.font = '14px Arial';
      ctx.textAlign = 'left';
      ctx.fillText(
        `Trọng lực: ${formatWithUnits(weightForce, 'N')}`,
        canvasX + 10,
        canvasY + (weightForce * forceScale) / 2
      );

      // Draw drag force if air resistance is enabled
      if (activeEnvironment.airResistance) {
        const dragForce =
          0.5 *
          activeEnvironment.airDensity *
          Math.pow(currentDataPoint.velocity, 2) *
          activeEnvironment.dragCoefficient *
          Math.PI *
          Math.pow(config.object.radius, 2);

        // Only draw if there's significant drag
        if (Math.abs(dragForce) > 0.01) {
          ctx.strokeStyle = '#8B0000'; // Dark red for drag
          ctx.lineWidth = 3;
          ctx.beginPath();
          ctx.moveTo(canvasX, canvasY);

          // Drag force is opposite to velocity
          const dragDirection = currentDataPoint.velocity > 0 ? 1 : -1;
          ctx.lineTo(canvasX, canvasY - dragForce * forceScale * dragDirection);
          ctx.stroke();

          // Arrow for drag
          ctx.beginPath();
          ctx.moveTo(canvasX, canvasY - dragForce * forceScale * dragDirection);
          ctx.lineTo(
            canvasX - 5,
            canvasY -
              dragForce * forceScale * dragDirection +
              10 * dragDirection
          );
          ctx.lineTo(
            canvasX + 5,
            canvasY -
              dragForce * forceScale * dragDirection +
              10 * dragDirection
          );
          ctx.closePath();
          ctx.fillStyle = '#8B0000';
          ctx.fill();

          // Label for drag
          ctx.fillText(
            `Lực cản không khí: ${formatWithUnits(dragForce, 'N')}`,
            canvasX + 10,
            canvasY - (dragForce * forceScale * dragDirection) / 2
          );
        }
      }
    }
  }, [
    currentDataPoint,
    activeEnvironment,
    config,
    simulationResult,
    currentTime,
  ]);

  // Handle start/pause simulation
  const handlePlayPause = () => {
    if (isSimulating) {
      setIsPaused(!isPaused);
    } else {
      setIsSimulating(true);
      setIsPaused(false);
      setIsSimulationComplete(false); // Reset the completion flag
      setCurrentTime(0); // Đặt lại thời gian về 0 khi bắt đầu
      if (simulationResult) {
        setCurrentDataPoint(simulationResult.dataPoints[0]); // Đặt lại vị trí ban đầu
      }
    }

    if (onDataChange) {
      onDataChange({
        action: isSimulating ? (isPaused ? 'resume' : 'pause') : 'start',
        time: currentTime,
      });
    }
  };

  // Reset trạng thái khi khởi tạo lại
  const handleReset = () => {
    setCurrentTime(0);
    setIsSimulating(false);
    setIsPaused(false);
    setIsSimulationComplete(false); // Reset trạng thái hoàn tất

    // Ensure currentDataPoint is set to the initial state
    if (simulationResult && simulationResult.dataPoints.length > 0) {
      setCurrentDataPoint(simulationResult.dataPoints[0]);

      // Force a re-render of the canvas to ensure the velocity label is visible
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // Use requestAnimationFrame to ensure the component state is updated
          requestAnimationFrame(() => {
            if (ctx && canvas) {
              ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
          });
        }
      }
    }

    if (onDataChange) {
      onDataChange({
        action: 'reset',
      });
    }
  };

  // Handle single step forward
  const handleStep = () => {
    if (!simulationResult) return;

    // Find current index and move one step forward
    const currentIndex = findClosestDataPointIndex(
      simulationResult.dataPoints,
      currentTime
    );
    if (currentIndex < simulationResult.dataPoints.length - 1) {
      const nextPoint = simulationResult.dataPoints[currentIndex + 1];
      setCurrentTime(nextPoint.time);
      setCurrentDataPoint(nextPoint);

      if (onDataChange) {
        onDataChange({
          action: 'step',
          time: nextPoint.time,
        });
      }
    }
  };

  // Handle change in simulation speed - removed

  // Environment change is now handled in the parent component

  // Canvas ref for graphs
  const graphCanvasRef = useRef<HTMLCanvasElement>(null);

  // Draw the position-time graph on canvas
  useEffect(() => {
    if (!graphCanvasRef.current || !simulationResult || !showGraph) return;

    const canvas = graphCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Use all datapoints up to the current time, not just when simulation is complete
    const pointsToShow = simulationResult.dataPoints.filter(
      (p) => p.time <= currentTime
    );
    if (pointsToShow.length === 0) return;

    const padding = 40;
    const graphWidth = canvas.width - padding * 2;
    const graphHeight = canvas.height - padding * 2;

    const maxTime = simulationResult.timeElapsed;
    const maxPosition = Math.max(
      ...pointsToShow.map((p) => p.position),
      config.initialHeight * 1.1
    );

    // Draw axes
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;

    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, canvas.height - padding);
    ctx.lineTo(canvas.width - padding, canvas.height - padding);
    ctx.stroke();

    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, canvas.height - padding);
    ctx.stroke();

    // Add axis labels with improved styling
    // X-axis labels (time)
    for (let i = 0; i <= 5; i++) {
      const timeValue = (maxTime * i) / 5;
      const x = padding + (graphWidth * i) / 5;

      // Create rounded label background
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      roundRect(ctx, x - 15, canvas.height - padding + 5, 30, 20, 4);
      ctx.fill();

      // Add subtle border
      ctx.strokeStyle = 'rgba(203, 213, 225, 0.8)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Text
      ctx.fillStyle = '#334155'; // Slate 700
      ctx.font = '11px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(timeValue.toFixed(1) + 's', x, canvas.height - padding + 18);

      // Tick mark
      ctx.strokeStyle = '#64748b'; // Slate 500
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(x, canvas.height - padding - 3);
      ctx.lineTo(x, canvas.height - padding + 3);
      ctx.stroke();
    }

    // Y-axis labels (position)
    for (let i = 0; i <= 5; i++) {
      const posValue = (maxPosition * i) / 5;
      const y = canvas.height - padding - (graphHeight * i) / 5;

      // Create rounded label background
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      roundRect(ctx, padding - 35, y - 10, 30, 20, 4);
      ctx.fill();

      // Add subtle border
      ctx.strokeStyle = 'rgba(203, 213, 225, 0.8)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Text
      ctx.fillStyle = '#334155'; // Slate 700
      ctx.font = '11px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(posValue.toFixed(1) + 'm', padding - 20, y + 4);

      // Tick mark
      ctx.strokeStyle = '#64748b'; // Slate 500
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(padding - 3, y);
      ctx.lineTo(padding + 3, y);
      ctx.stroke();
    }

    // Draw axes with gradient
    ctx.lineWidth = 2;
    // X-axis with gradient
    const xAxisGradient = ctx.createLinearGradient(
      padding,
      0,
      canvas.width - padding,
      0
    );
    xAxisGradient.addColorStop(0, '#7e22ce'); // Purple 700
    xAxisGradient.addColorStop(1, '#a855f7'); // Purple 500
    ctx.strokeStyle = xAxisGradient;
    ctx.beginPath();
    ctx.moveTo(padding, canvas.height - padding);
    ctx.lineTo(canvas.width - padding, canvas.height - padding);
    ctx.stroke();

    // Y-axis with gradient
    const yAxisGradient = ctx.createLinearGradient(
      0,
      canvas.height - padding,
      0,
      padding
    );
    yAxisGradient.addColorStop(0, '#7e22ce'); // Purple 700
    yAxisGradient.addColorStop(1, '#a855f7'); // Purple 500
    ctx.strokeStyle = yAxisGradient;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, canvas.height - padding);
    ctx.stroke();

    // Axis titles with better styling
    // X-axis title
    ctx.fillStyle = '#1e293b'; // Slate 800
    ctx.font = 'bold 14px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('Thời gian (s)', canvas.width / 2, canvas.height - 10);

    // Y-axis title
    ctx.save();
    ctx.translate(0, canvas.height / 10);
    ctx.textAlign = 'center';
    ctx.fillText('Vị trí (m)', 30, 0);
    ctx.restore();

    // Highlight current point on graph if available
    if (currentTime > 0 && pointsToShow.length > 0) {
      const currentX = padding + (currentTime / maxTime) * graphWidth;
      const currentIndex = findClosestDataPointIndex(pointsToShow, currentTime);

      if (currentIndex >= 0) {
        const currentPoint = pointsToShow[currentIndex];
        const currentY =
          canvas.height -
          padding -
          (currentPoint.position / maxPosition) * graphHeight;

        // Draw vertical line at current time
        ctx.setLineDash([5, 3]);
        ctx.strokeStyle = 'rgba(126, 34, 206, 0.5)'; // Purple 700 with transparency
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(currentX, padding);
        ctx.lineTo(currentX, canvas.height - padding);
        ctx.stroke();
        ctx.setLineDash([]);

        // Draw current point with highlight
        ctx.fillStyle = 'rgba(126, 34, 206, 0.2)'; // Purple with transparency
        ctx.beginPath();
        ctx.arc(currentX, currentY, 10, 0, Math.PI * 2);
        ctx.fill();

        ctx.fillStyle = '#7e22ce'; // Purple 700
        ctx.beginPath();
        ctx.arc(currentX, currentY, 6, 0, Math.PI * 2);
        ctx.fill();

        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(currentX, currentY, 3, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    // Create gradient for line
    const lineGradient = ctx.createLinearGradient(
      padding,
      canvas.height - padding,
      padding,
      padding
    );
    lineGradient.addColorStop(0, '#9333ea'); // Purple 600
    lineGradient.addColorStop(0.5, '#a855f7'); // Purple 500
    lineGradient.addColorStop(1, '#c084fc'); // Purple 400

    // Create path for the line and the fill area
    const linePath = new Path2D();
    const fillPath = new Path2D();

    pointsToShow.forEach((point, index) => {
      const x = padding + (point.time / maxTime) * graphWidth;
      const y =
        canvas.height - padding - (point.position / maxPosition) * graphHeight;

      if (index === 0) {
        linePath.moveTo(x, y);
        fillPath.moveTo(x, canvas.height - padding);
        fillPath.lineTo(x, y);
      } else {
        linePath.lineTo(x, y);
        fillPath.lineTo(x, y);
      }
    });

    // Close the fill path
    if (pointsToShow.length > 0) {
      const lastPoint = pointsToShow[pointsToShow.length - 1];
      const lastX = padding + (lastPoint.time / maxTime) * graphWidth;
      fillPath.lineTo(lastX, canvas.height - padding);
      fillPath.closePath();
    }

    // Draw the fill with gradient and transparency
    const fillGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    fillGradient.addColorStop(0, 'rgba(192, 132, 252, 0.5)'); // Purple with transparency
    fillGradient.addColorStop(1, 'rgba(192, 132, 252, 0.05)');
    ctx.fillStyle = fillGradient;
    ctx.fill(fillPath);

    // Draw the line with shadow for better visibility
    ctx.shadowColor = 'rgba(147, 51, 234, 0.3)';
    ctx.shadowBlur = 5;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 2;
    ctx.strokeStyle = lineGradient;
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.stroke(linePath);

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // Draw points along the line
    pointsToShow.forEach((point, index) => {
      if (index % 10 === 0 || index === pointsToShow.length - 1) {
        // Draw fewer points for performance
        const x = padding + (point.time / maxTime) * graphWidth;
        const y =
          canvas.height -
          padding -
          (point.position / maxPosition) * graphHeight;

        // Draw a point
        ctx.fillStyle = '#7e22ce'; // Purple 700
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();

        // Draw a white inner circle for a better look
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(x, y, 2, 0, Math.PI * 2);
        ctx.fill();
      }
    });

    // Add title
    ctx.fillStyle = '#000000';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Biểu đồ vị trí theo thời gian', canvas.width / 2, 20);
  }, [
    graphCanvasRef,
    simulationResult,
    showGraph,
    currentTime,
    config.initialHeight,
  ]);

  // Render data table with enhanced styling
  const renderDataTable = () => {
    if (!config.displayOptions.showDataTable || !simulationResult) return null;

    const columns = [
      {
        title: 'Thời gian (s)',
        dataIndex: 'time',
        key: 'time',
        render: (value: number) => (
          <span className="tailwind-font-medium">{value.toFixed(2)}</span>
        ),
        sorter: (a: any, b: any) => a.time - b.time,
      },
      {
        title: 'Vị trí (m)',
        dataIndex: 'position',
        key: 'position',
        render: (value: number) => (
          <span className="tailwind-text-blue-600 tailwind-font-medium">
            {value.toFixed(2)}
          </span>
        ),
        sorter: (a: any, b: any) => a.position - b.position,
      },
      {
        title: 'Vận tốc (m/s)',
        dataIndex: 'velocity',
        key: 'velocity',
        render: (value: number) => {
          const velocityDirection = value < 0 ? '↓' : '↑';
          return (
            <span
              className={`tailwind-font-medium ${
                value < 0 ? 'tailwind-text-red-600' : 'tailwind-text-green-600'
              }`}
            >
              {Math.abs(value).toFixed(2)} {velocityDirection}
            </span>
          );
        },
        sorter: (a: any, b: any) => a.velocity - b.velocity,
      },
      {
        title: 'Gia tốc (m/s²)',
        dataIndex: 'acceleration',
        key: 'acceleration',
        render: (value: number) => (
          <span
            className={`tailwind-font-medium ${
              value < 0
                ? 'tailwind-text-purple-600'
                : 'tailwind-text-indigo-600'
            }`}
          >
            {value.toFixed(2)}
          </span>
        ),
        sorter: (a: any, b: any) => a.acceleration - b.acceleration,
      },
    ];

    // Filter data points based on current time
    const dataSource = simulationResult.dataPoints
      .filter((point) => point.time <= currentTime)
      .map((point, index) => ({
        key: index,
        time: point.time,
        position: point.position,
        velocity: point.velocity,
        acceleration: point.acceleration,
      }));

    return (
      <div className="tailwind-mt-5 tailwind-rounded-lg tailwind-border tailwind-border-gray-200 tailwind-shadow-sm tailwind-overflow-hidden">
        <div className="tailwind-bg-gradient-to-r tailwind-from-blue-500 tailwind-to-indigo-600 tailwind-text-white tailwind-px-4 tailwind-py-2 tailwind-flex tailwind-items-center">
          <TableOutlined className="tailwind-h-5 tailwind-w-5 tailwind-mr-2" />
          <h3 className="tailwind-font-bold">Bảng dữ liệu vật lý</h3>
        </div>
        <div className="tailwind-overflow-auto tailwind-max-h-60 tailwind-bg-white">
          <Table
            columns={columns}
            dataSource={dataSource}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              pageSizeOptions: ['5', '10', '20'],
              showTotal: (total) => `Tổng số: ${total} dòng dữ liệu`,
              size: 'small',
            }}
            size="small"
            bordered
            scroll={{ x: 'max-content' }}
            summary={(pageData) => {
              if (pageData.length === 0) return null;

              // Calculate averages without using Lodash
              const calculateAverage = <
                T extends Record<K, number>,
                K extends string
              >(
                data: readonly T[],
                key: K
              ): number => {
                const sum = data.reduce((acc, item) => acc + item[key], 0);
                return data.length > 0 ? sum / data.length : 0;
              };

              const avgPosition = calculateAverage(pageData, 'position');
              const avgVelocity = calculateAverage(pageData, 'velocity');
              const avgAcceleration = calculateAverage(
                pageData,
                'acceleration'
              );

              return (
                <Table.Summary fixed>
                  <Table.Summary.Row className="tailwind-bg-gray-50 tailwind-font-medium">
                    <Table.Summary.Cell index={0}>
                      Trung bình
                    </Table.Summary.Cell>
                    <Table.Summary.Cell
                      index={1}
                      className="tailwind-text-blue-600"
                    >
                      {avgPosition.toFixed(2)}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell
                      index={2}
                      className="tailwind-text-green-600"
                    >
                      {Math.abs(avgVelocity).toFixed(2)}{' '}
                      {avgVelocity < 0 ? '↓' : '↑'}
                    </Table.Summary.Cell>
                    <Table.Summary.Cell
                      index={3}
                      className="tailwind-text-purple-600"
                    >
                      {avgAcceleration.toFixed(2)}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
        </div>
      </div>
    );
  };
  // Render physics formulas with enhanced styling
  const renderFormulas = () => {
    return (
      <div className="tailwind-mt-5 tailwind-p-4 tailwind-bg-gradient-to-r tailwind-from-blue-50 tailwind-to-indigo-50 tailwind-rounded-lg tailwind-border tailwind-border-blue-100 tailwind-shadow-sm">
        <h3 className="tailwind-text-lg tailwind-font-bold tailwind-text-blue-800 tailwind-mb-3 tailwind-flex tailwind-items-center">
          <ExperimentOutlined className="tailwind-h-5 tailwind-w-5 tailwind-mr-2" />
          Công thức vật lý
        </h3>

        <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-2 tailwind-gap-4">
          <div className="tailwind-bg-white tailwind-p-3 tailwind-rounded-lg tailwind-border tailwind-border-indigo-100 tailwind-shadow-sm">
            <p className="tailwind-font-bold tailwind-text-indigo-700 tailwind-mb-2 tailwind-border-b tailwind-border-indigo-100 tailwind-pb-1">
              <CalculatorOutlined className="tailwind-mr-1" /> Phương trình
              chuyển động:
            </p>
            <ul className="tailwind-space-y-2">
              <li className="tailwind-flex tailwind-items-center">
                <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-blue-100 tailwind-text-blue-700 tailwind-w-8 tailwind-h-8 tailwind-rounded-full tailwind-mr-2 tailwind-font-bold tailwind-text-sm">
                  s
                </span>
                <span>Vị trí: s = s₀ + v₀t + ½at²</span>
              </li>
              <li className="tailwind-flex tailwind-items-center">
                <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-green-100 tailwind-text-green-700 tailwind-w-8 tailwind-h-8 tailwind-rounded-full tailwind-mr-2 tailwind-font-bold tailwind-text-sm">
                  v
                </span>
                <span>Vận tốc: v = v₀ + at</span>
              </li>
              <li className="tailwind-flex tailwind-items-center">
                <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-purple-100 tailwind-text-purple-700 tailwind-w-8 tailwind-h-8 tailwind-rounded-full tailwind-mr-2 tailwind-font-bold tailwind-text-sm">
                  a
                </span>
                <span>Gia tốc: a = g</span>
              </li>
            </ul>
          </div>

          <div className="tailwind-bg-white tailwind-p-3 tailwind-rounded-lg tailwind-border tailwind-border-indigo-100 tailwind-shadow-sm">
            <p className="tailwind-font-bold tailwind-text-indigo-700 tailwind-mb-2 tailwind-border-b tailwind-border-indigo-100 tailwind-pb-1">
              <InfoCircleOutlined className="tailwind-mr-1" /> Lực tác động:
            </p>
            <ul className="tailwind-space-y-2">
              <li className="tailwind-flex tailwind-items-center">
                <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-red-100 tailwind-text-red-700 tailwind-w-8 tailwind-h-8 tailwind-rounded-full tailwind-mr-2 tailwind-font-bold tailwind-text-sm">
                  F
                </span>
                <span>Trọng lực: F₍g₎ = mg</span>
              </li>
              {activeEnvironment?.airResistance && (
                <li className="tailwind-flex tailwind-items-center">
                  <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-orange-100 tailwind-text-orange-700 tailwind-w-8 tailwind-h-8 tailwind-rounded-full tailwind-mr-2 tailwind-font-bold tailwind-text-sm">
                    F₍d₎
                  </span>
                  <span>Lực cản không khí: F₍d₎ = ½ρv²C₍d₎A</span>
                </li>
              )}
              <li className="tailwind-flex tailwind-items-center">
                <span className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-yellow-100 tailwind-text-yellow-700 tailwind-w-8 tailwind-h-8 tailwind-rounded-full tailwind-mr-2 tailwind-font-bold tailwind-text-sm">
                  ΣF
                </span>
                <span>Hợp lực: ΣF = ma</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="tailwind-mt-2 tailwind-text-sm tailwind-text-gray-600 tailwind-italic">
          <p className="tailwind-flex tailwind-items-center">
            <InfoCircleOutlined className="tailwind-h-4 tailwind-w-4 tailwind-mr-1 tailwind-text-blue-500" />
            Trong đó: g - gia tốc trọng trường, v - vận tốc, a - gia tốc, m -
            khối lượng, ρ - mật độ không khí, C₍d₎ - hệ số cản, A - diện tích
            tiếp xúc
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="tailwind-freefall-simulator tailwind-w-full tailwind-overflow-hidden">
      <div className="tailwind-flex tailwind-flex-col tailwind-gap-4">
        {/* Main simulation area */}
        <div className="tailwind-flex-grow tailwind-bg-white tailwind-border tailwind-border-gray-200 tailwind-rounded-xl tailwind-p-5 tailwind-shadow-md tailwind-relative">
          {/* Simulation controls moved to the top */}
          <div className="tailwind-mb-4 tailwind-flex tailwind-justify-center tailwind-gap-3">
            <Button
              type="primary"
              size="middle"
              className={`tailwind-transition-all tailwind-duration-200 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-min-w-[110px] ${
                isSimulating && !isPaused
                  ? 'tailwind-bg-red-500 hover:tailwind-bg-red-600 tailwind-border-red-600'
                  : 'tailwind-bg-green-500 hover:tailwind-bg-green-600 tailwind-border-green-600'
              }`}
              icon={
                isSimulating && !isPaused ? (
                  <PauseCircleOutlined className="tailwind-text-xl" />
                ) : (
                  <PlayCircleOutlined className="tailwind-text-xl" />
                )
              }
              onClick={handlePlayPause}
            >
              <span className="tailwind-ml-1 tailwind-font-medium">
                {isSimulating
                  ? isPaused
                    ? 'Tiếp tục'
                    : 'Tạm dừng'
                  : 'Bắt đầu'}
              </span>
            </Button>

            {config.controls.showResetButton && (
              <Button
                size="middle"
                className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-min-w-[110px] tailwind-bg-blue-50 hover:tailwind-bg-blue-100 tailwind-border-blue-200 tailwind-text-blue-700"
                icon={<ReloadOutlined className="tailwind-text-blue-500" />}
                onClick={handleReset}
              >
                <span className="tailwind-ml-1 tailwind-font-medium tailwind-text-sm">
                  Khởi tạo lại
                </span>
              </Button>
            )}

            {config.controls.showStepButton && (
              <Button
                size="middle"
                className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-min-w-[100px] tailwind-bg-indigo-50 hover:tailwind-bg-indigo-100 tailwind-border-indigo-200 tailwind-text-indigo-700"
                icon={
                  <StepForwardOutlined className="tailwind-text-indigo-500" />
                }
                onClick={handleStep}
                disabled={isSimulating && !isPaused}
              >
                <span className="tailwind-ml-1 tailwind-font-medium tailwind-text-sm">
                  Bước tiếp
                </span>
              </Button>
            )}
          </div>

          {/* Environment selector removed - now only in quickSettingsPanel */}

          {/* Canvas for simulation with enhanced container */}
          <div className="tailwind-w-full tailwind-relative tailwind-border tailwind-border-gray-200 tailwind-rounded-lg tailwind-overflow-hidden tailwind-shadow-inner tailwind-bg-gradient-to-b tailwind-from-blue-50 tailwind-to-indigo-50">
            <canvas
              ref={canvasRef}
              width={800}
              height={500}
              className="tailwind-w-full"
            />

            {/* Optional overlay elements for visual enhancements */}
            <div className="tailwind-absolute tailwind-top-4 tailwind-right-4 tailwind-bg-white tailwind-bg-opacity-80 tailwind-rounded-lg tailwind-px-3 tailwind-py-2 tailwind-shadow-sm tailwind-border tailwind-border-gray-100">
              <div className="tailwind-text-xs tailwind-font-bold tailwind-text-gray-500">
                TRẠNG THÁI:
              </div>
              <div className="tailwind-text-sm tailwind-font-bold">
                {isSimulating ? (
                  isPaused ? (
                    <span className="tailwind-text-yellow-600">TẠM DỪNG</span>
                  ) : (
                    <span className="tailwind-text-green-600">ĐANG CHẠY</span>
                  )
                ) : isSimulationComplete ? (
                  <span className="tailwind-text-blue-600">HOÀN THÀNH</span>
                ) : (
                  <span className="tailwind-text-gray-600">CHƯA BẮT ĐẦU</span>
                )}
              </div>
            </div>
          </div>

          {/* Toggle buttons for graph and formulas moved to the bottom */}
          <div className="tailwind-mt-5 tailwind-flex tailwind-gap-4">
            <Tooltip
              title={showGraph ? 'Ẩn biểu đồ vị trí' : 'Hiện biểu đồ vị trí'}
            >
              <Button
                type={showGraph ? 'primary' : 'default'}
                icon={showGraph ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={() => setShowGraph(!showGraph)}
                className="tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-px-4 tailwind-py-2"
                style={{
                  backgroundColor: showGraph ? '#9333ea' : 'white',
                  borderColor: '#9333ea',
                  color: showGraph ? 'white' : '#9333ea',
                }}
                size="large"
              >
                <BarChartOutlined /> Biểu đồ vị trí
              </Button>
            </Tooltip>

            <Tooltip
              title={
                showFormulas ? 'Ẩn công thức vật lý' : 'Hiện công thức vật lý'
              }
            >
              <Button
                type={showFormulas ? 'primary' : 'default'}
                icon={showFormulas ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={() => setShowFormulas(!showFormulas)}
                className="tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-px-4 tailwind-py-2"
                style={{
                  backgroundColor: showFormulas ? '#2563eb' : 'white',
                  borderColor: '#2563eb',
                  color: showFormulas ? 'white' : '#2563eb',
                }}
                size="large"
              >
                <FunctionOutlined /> Công thức vật lý
              </Button>
            </Tooltip>
          </div>

          {/* Enhanced Position-time graph with better positioning */}
          {showGraph && simulationResult && (
            <div className="tailwind-mt-2 tailwind-border tailwind-rounded-lg tailwind-p-3 md:tailwind-p-4 tailwind-bg-white tailwind-shadow-sm tailwind-relative tailwind-overflow-hidden">
              <div className="tailwind-flex tailwind-flex-col sm:tailwind-flex-row sm:tailwind-justify-between sm:tailwind-items-center tailwind-gap-2 tailwind-mb-2">
                <h3 className="tailwind-text-base md:tailwind-text-lg tailwind-font-bold tailwind-text-gray-700 tailwind-flex tailwind-items-center">
                  <AreaChartOutlined className="tailwind-h-5 tailwind-w-5 tailwind-mr-2 tailwind-text-purple-600" />
                  Biểu đồ vị trí theo thời gian
                </h3>

                <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1">
                  <div className="tailwind-px-2 tailwind-py-1 tailwind-text-xs tailwind-font-bold tailwind-bg-purple-100 tailwind-text-purple-700 tailwind-rounded">
                    Thời gian: {formatWithUnits(currentTime, 's')}
                  </div>
                  <div className="tailwind-px-2 tailwind-py-1 tailwind-text-xs tailwind-font-bold tailwind-bg-blue-100 tailwind-text-blue-700 tailwind-rounded">
                    Vị trí:{' '}
                    {formatWithUnits(currentDataPoint?.position || 0, 'm')}
                  </div>
                </div>
              </div>

              {/* Graph decorations */}
              <div className="tailwind-absolute tailwind-inset-0 tailwind-pointer-events-none">
                <div className="tailwind-absolute tailwind-top-0 tailwind-left-0 tailwind-w-8 tailwind-h-8 tailwind-border-t-4 tailwind-border-l-4 tailwind-border-purple-500 tailwind-rounded-tl-lg"></div>
                <div className="tailwind-absolute tailwind-top-0 tailwind-right-0 tailwind-w-8 tailwind-h-8 tailwind-border-t-4 tailwind-border-r-4 tailwind-border-purple-500 tailwind-rounded-tr-lg"></div>
                <div className="tailwind-absolute tailwind-bottom-0 tailwind-left-0 tailwind-w-8 tailwind-h-8 tailwind-border-b-4 tailwind-border-l-4 tailwind-border-purple-500 tailwind-rounded-bl-lg"></div>
                <div className="tailwind-absolute tailwind-bottom-0 tailwind-right-0 tailwind-w-8 tailwind-h-8 tailwind-border-b-4 tailwind-border-r-4 tailwind-border-purple-500 tailwind-rounded-br-lg"></div>
              </div>

              <div
                style={{ width: '100%', height: 270 }}
                className="tailwind-mt-1"
              >
                <canvas
                  ref={graphCanvasRef}
                  width={800}
                  height={270}
                  className="tailwind-w-full tailwind-h-full"
                />
              </div>

              <div className="tailwind-flex tailwind-flex-col sm:tailwind-flex-row tailwind-justify-between tailwind-mt-2 tailwind-text-xs tailwind-text-gray-500 tailwind-gap-2">
                <div className="tailwind-flex tailwind-items-center">
                  <AreaChartOutlined className="tailwind-h-4 tailwind-w-4 tailwind-inline tailwind-mr-1 tailwind-text-purple-500" />
                  Đường tím thể hiện vị trí của vật
                </div>
                <div className="tailwind-flex tailwind-items-center">
                  <span className="tailwind-inline-block tailwind-w-3 tailwind-h-3 tailwind-bg-purple-500 tailwind-rounded-full tailwind-mr-1"></span>
                  Độ dốc = vận tốc
                </div>
              </div>
            </div>
          )}

          {/* Data table */}
          {renderDataTable()}

          {/* Physics formulas */}
          {showFormulas && renderFormulas()}
        </div>
      </div>
    </div>
  );
};

export default FreefallSimulatorComponent;

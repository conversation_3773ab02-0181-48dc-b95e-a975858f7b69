/**
 * IconRegistry.tsx
 * 
 * This file provides a registry for all icons from IconRegister.tsx.
 * It replaces the old IconStore.tsx that used IconDefinition.
 */

import React, { createContext, useContext, useMemo } from 'react';
import { IconCategory } from './IconUtils';
import * as RegisteredIcons from './IconRegister';
import { SVGProps } from 'react';

// Define the icon registry item type
export interface IconRegistryItem {
  name: string;
  component: React.ComponentType<SVGProps<SVGSVGElement>>;
  category: IconCategory;
  displayName: string;
}

// Helper function to convert component name to icon name
const getIconNameFromComponentName = (componentName: string): string => {
  // Remove 'Icon' suffix and convert to lowercase
  return componentName.replace(/Icon$/, '').toLowerCase();
};

// Helper function to get display name from component name
const getDisplayNameFromComponentName = (componentName: string): string => {
  // Remove 'Icon' suffix
  const nameWithoutSuffix = componentName.replace(/Icon$/, '');

  // Add spaces before capital letters and trim
  return nameWithoutSuffix.replace(/([A-Z])/g, ' $1').trim();
};

// Helper function to determine icon category based on component name
const getIconCategory = (componentName: string): IconCategory => {
  // Convert component name to lowercase for case-insensitive matching
  const name = componentName.toLowerCase();

  // USER category
  if (
    name.includes('user') ||
    name.includes('person') ||
    name.includes('account') ||
    name.includes('group') ||
    name.includes('profile')
  ) {
    return IconCategory.USER;
  }
  // DOCUMENT category
  else if (
    name.includes('file') ||
    name.includes('folder') ||
    name.includes('document')
  ) {
    return IconCategory.DOCUMENT;
  }
  // MEDIA category
  else if (
    name.includes('image') ||
    name.includes('video') ||
    name.includes('audio') ||
    name.includes('play') ||
    name.includes('pause')
  ) {
    return IconCategory.MEDIA;
  }
  // ACTION category
  else if (
    name.includes('add') ||
    name.includes('delete') ||
    name.includes('edit') ||
    name.includes('save') ||
    name.includes('cancel')
  ) {
    return IconCategory.ACTION;
  }
  // NAVIGATION category
  else if (
    name.includes('menu') ||
    name.includes('back') ||
    name.includes('forward') ||
    name.includes('home') ||
    name.includes('arrow')
  ) {
    return IconCategory.NAVIGATION;
  }
  // Default to UI category
  else {
    return IconCategory.UI;
  }
};

// Create registry items from all registered icons
const createIconRegistry = (): IconRegistryItem[] => {
  return Object.entries(RegisteredIcons)
    .filter(([name]) => name.endsWith('Icon') || name.endsWith('Component'))
    .map(([name, component]) => ({
      name: getIconNameFromComponentName(name),
      component: component as React.ComponentType<SVGProps<SVGSVGElement>>,
      category: getIconCategory(name),
      displayName: getDisplayNameFromComponentName(name),
    }));
};

// Create the icon registry
const IconRegistry: IconRegistryItem[] = createIconRegistry();

// Helper functions
export const getIconByName = (
  name: string
): React.ComponentType<SVGProps<SVGSVGElement>> | undefined => {
  const icon = IconRegistry.find((icon) => icon.name === name);
  return icon?.component;
};

export const getIconsByCategory = (
  category: IconCategory
): IconRegistryItem[] => {
  return IconRegistry.filter((icon) => icon.category === category);
};

export const getAllIcons = (): IconRegistryItem[] => {
  return IconRegistry;
};

// Context for icons
interface IconContextType {
  icons: IconRegistryItem[];
  getIcon: (name: string) => React.ComponentType<SVGProps<SVGSVGElement>> | undefined;
  getIconsByCategory: (category: IconCategory) => IconRegistryItem[];
  getAllIcons: () => IconRegistryItem[];
}

const IconContext = createContext<IconContextType>({
  icons: [],
  getIcon: () => undefined,
  getIconsByCategory: () => [],
  getAllIcons: () => [],
});

/**
 * Provider component for the IconContext
 */
export const IconProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const value = useMemo(
    () => ({
      icons: IconRegistry,
      getIcon: getIconByName,
      getIconsByCategory: getIconsByCategory,
      getAllIcons: getAllIcons,
    }),
    []
  );

  return <IconContext.Provider value={value}>{children}</IconContext.Provider>;
};

/**
 * Hook to access the IconContext
 */
export const useIconContext = () => useContext(IconContext);

/**
 * Hook to get an icon component by name
 */
export function useIcon(
  name: string,
  options: {
    size?: number | string;
    color?: string;
    className?: string;
    style?: React.CSSProperties;
    onClick?: () => void;
  } = {}
) {
  return useMemo(() => {
    const IconComponent = getIconByName(name);

    if (!IconComponent) {
      console.warn(`Icon with name "${name}" not found in IconRegistry`);
      return () => null;
    }

    return (props: SVGProps<SVGSVGElement>) => <IconComponent {...options} {...props} />;
  }, [name, options]);
}

export default IconRegistry;

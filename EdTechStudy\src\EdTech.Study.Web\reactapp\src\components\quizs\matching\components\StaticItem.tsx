import React from 'react';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { ItemProps } from '../../../../interfaces/quizs/mapping.interface';

// Simple non-sortable item component for better performance
const StaticItem: React.FC<ItemProps> = ({ item, isMatched, isIncorrect }) => {
  return (
    <div
      className={`matching-item ${isMatched ? 'matched' : ''} ${
        isIncorrect ? 'incorrect' : ''
      }`}
    >
      {item.type === 'text' ? (
        <div className="matching-text">{item.content}</div>
      ) : (
        <div className="matching-image">
          {typeof item.content === 'string' ? (
            <img src={item.content} alt="Matching item" loading="lazy" />
          ) : (
            item.content
          )}
        </div>
      )}
      {isMatched && (
        <CheckOutlined className="match-status-icon match-correct" />
      )}
      {isIncorrect && (
        <CloseOutlined className="match-status-icon match-incorrect" />
      )}
    </div>
  );
};

export default StaticItem;

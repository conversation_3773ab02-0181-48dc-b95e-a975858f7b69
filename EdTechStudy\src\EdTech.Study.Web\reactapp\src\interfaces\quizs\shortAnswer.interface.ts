import {
  QuestionComponentBaseProps,
  BaseQuestion,
  BaseAnswer,
} from './questionBase';

// Interface for ShortAnswer questions
export interface ShortAnswerQuestion extends BaseQuestion {
  type: 'shortanswer';
  question: string;
  correctAnswer: string;
  explanation?: string;
  points?: number;
  userAnswer?: ShortAnswerAnswer; // User's input answer
  caseSensitive?: boolean; // Whether to check case sensitivity
  allowPartialMatch?: boolean; // Whether to allow partial matches
  maxLength?: number; // Maximum length of the answer in characters
}

export interface ShortAnswerAnswer extends BaseAnswer {
  text: string;
}

// Props for the ShortAnswer component
export interface ShortAnswerComponentProps extends QuestionComponentBaseProps {
  question: ShortAnswerQuestion;
  allowManyTimes?: boolean;
  disabled?: boolean;
  onComplete?: (questionId: string, answer?: ShortAnswerAnswer) => void;
}

import React, { useEffect } from 'react';
import { Modal, Table, Typography, Button } from 'antd';
import { ExclamationCircleOutlined, CloseOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface ValidationErrorModalProps {
  errors: string[];
  onClose?: () => void;
}

const ValidationErrorModal: React.FC<ValidationErrorModalProps> = ({
  errors,
  onClose,
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  useEffect(() => {
    if (errors.length > 0) {
      setIsOpen(true);
    }
  }, [errors]);

  const handleClose = () => {
    setIsOpen(false);
    onClose?.();
  };

  const columns = [
    {
      title: 'Lỗi',
      dataIndex: 'error',
      key: 'error',
      render: (text: string) => <Text type="danger">{text}</Text>,
    },
  ];

  const dataSource = errors.map((error, index) => ({
    key: index,
    error,
  }));

  return (
    <Modal
      title={
        <div className="flex items-center">
          <ExclamationCircleOutlined className="text-red-500 mr-2" />
          <span>Lỗi kiểm tra dữ liệu</span>
        </div>
      }
      open={isOpen}
      onCancel={handleClose}
      footer={[
        <Button key="close" onClick={handleClose} icon={<CloseOutlined />}>
          Đóng
        </Button>,
      ]}
      width={800}
      maskClosable={true}
    >
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{ y: 400 }}
        size="small"
      />
    </Modal>
  );
};

export default ValidationErrorModal;

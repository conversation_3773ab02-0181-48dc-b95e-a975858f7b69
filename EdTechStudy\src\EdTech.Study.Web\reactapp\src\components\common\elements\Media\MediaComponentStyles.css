/* Common styles for Media Components (Audio, Image, Video) */

/* Card styles */
.media-component-card {
  border: none;
  box-shadow: var(--edtt-shadow);
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
}

/* Config panel */
.media-config-panel {
  padding: 16px;
  border: 1px solid var(--edtt-color-gray-100);
  border-radius: 8px;
  background-color: var(--edtt-color-bg-secondary);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* Header styles for media components */
.media-config-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  gap: 12px;
}

.media-config-header-left {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
}

.media-config-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.media-config-section {
  width: 100%;
  box-sizing: border-box;
}

.media-config-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
  display: inline-block;
  min-width: 120px;
}

.media-config-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.media-config-switch-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 16px;
  margin-bottom: 8px;
}

.media-config-switch-label {
  font-size: 14px;
  color: #333;
}

/* Upload area */
.media-upload-container {
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  position: relative;
}

.media-upload-dragger {
  border: 2px dashed var(--edtt-color-primary-300) !important;
  border-radius: 8px !important;
  background-color: var(--edtt-color-primary-100) !important;
  padding: 0.8rem !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
  min-height: 120px !important;
  max-height: none !important;
  /* Allow flexible height */
  margin: 0 auto !important;
  box-sizing: border-box !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  overflow: hidden !important;
  /* Prevent content overflow */
}

.media-upload-dragger:hover {
  border-color: var(--edtt-color-primary) !important;
  border-style: dashed !important;
  background-color: var(--edtt-color-primary-200) !important;
  transform: scale(1.01) !important;
}

/* Media File Uploader Container styles */
.media-file-uploader-container {
  border: 1px dashed var(--edtt-color-primary-300) !important;
  border-radius: 8px !important;
  background-color: var(--edtt-color-primary-100) !important;
  transition: all 0.3s ease !important;
}

.media-file-uploader-container:hover {
  border-color: var(--edtt-color-primary) !important;
  background-color: var(--edtt-color-primary-200) !important;
}

.ant-upload-drag {
  background-color: transparent !important;
  border: none !important;
  width: 100% !important;
  overflow: hidden !important;
}

/* Custom styles for the upload list */
.upload-list-container {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
  padding: 5px;
  border-top: 1px solid var(--edtt-color-primary-200);
}

.upload-list-item {
  display: flex;
  align-items: center;
  padding: 5px;
  margin-bottom: 5px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.8);
}

.upload-list-item:hover {
  background-color: var(--edtt-color-primary-100);
}

.upload-list-item-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
  flex-shrink: 0;
}

.upload-list-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-list-item-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.upload-list-item-name {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.upload-list-item-status {
  font-size: 11px;
  color: #888;
}

.upload-list-item-actions {
  display: flex;
  align-items: center;
}

/* Fix for Ant Design Upload component */
.ant-upload.ant-upload-drag .ant-upload {
  padding: 0 !important;
  width: 100% !important;
  height: auto !important;
  overflow: hidden !important;
}

.ant-upload-list {
  width: 100% !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  margin-top: 10px !important;
}

/* Pink button with white text that changes to white background with pink text on hover */
.pink-button {
  background-color: var(--edtt-color-primary) !important;
  border-color: var(--edtt-color-primary) !important;
  color: var(--edtt-color-white) !important;
}

.pink-button:hover,
.pink-button:focus {
  background-color: var(--edtt-color-white) !important;
  color: var(--edtt-color-primary) !important;
  border-color: var(--edtt-color-primary) !important;
}

/* Container styles */
.media-container {
  position: relative;
  /* Use hardware acceleration for smoother resizing */
  transform: translateZ(0);
  will-change: transform;
  /* Only transition non-size properties for better performance during resize */
  transition: opacity 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Resize handles */
.media-resize-handle {
  position: absolute;
  z-index: 100;
  transition: opacity 0.2s ease;
  opacity: 0;
}

.media-container:hover .media-resize-handle,
.media-resize-handle:hover {
  opacity: 1 !important;
}

.media-container.editing .media-resize-handle {
  opacity: 0.7;
}

/* Optimized container during resize operations */
.media-container.resizing {
  /* Disable transitions during resize for better performance */
  transition: none !important;
  /* Use hardware acceleration */
  transform: translateZ(0);
  will-change: width, height, transform;
  /* Reduce quality during resize for better performance */
  image-rendering: optimizeSpeed;
  /* Add a subtle visual feedback during resize */
  box-shadow: 0 0 0 1px var(--edtt-color-primary-300) !important;
  outline: 1px dashed var(--edtt-color-primary-300);
  outline-offset: -1px;
}

.media-container.resizing img {
  /* Disable transitions during resize */
  transition: none !important;
}

/* Improve resize handles visibility and interaction */
.media-resize-handle {
  position: absolute;
  z-index: 100;
  transition: opacity 0.2s ease, transform 0.1s ease;
  opacity: 0;
  background-color: var(--edtt-color-white);
  border: 1px solid var(--edtt-color-primary-400);
  box-shadow: var(--edtt-shadow);
}

.media-resize-handle:hover {
  transform: scale(1.1);
  opacity: 1 !important;
}

/* Make resize handles more visible in edit mode */
.media-container.editing .media-resize-handle {
  opacity: 0.8 !important;
}

/* Container for image display */
.image-resize-container {
  position: relative;
  width: 100% !important;
  height: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden !important;
}

/* Ensure image container takes full size of parent */
.image-container {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  overflow: hidden !important;
  /* Ensure aspect ratio is respected */
  box-sizing: border-box !important;
  position: relative !important;
}

.media-resize-handle-right {
  top: 50%;
  right: -10px;
  width: 20px;
  height: 40px;
  margin-top: -20px;
  cursor: ew-resize;
}

.media-resize-handle-bottom {
  bottom: -10px;
  left: 50%;
  width: 40px;
  height: 20px;
  margin-left: -20px;
  cursor: ns-resize;
}

.media-resize-handle-corner {
  right: -10px;
  bottom: -10px;
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
}

.media-resize-handle-bar {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--edtt-color-primary);
  border-radius: 3px;
}

.media-resize-handle-right .media-resize-handle-bar,
.media-resize-handle-left .media-resize-handle-bar {
  width: 4px;
  height: 20px;
  background-color: var(--edtt-color-primary-400);
}

.media-resize-handle-top .media-resize-handle-bar,
.media-resize-handle-bottom .media-resize-handle-bar {
  width: 20px;
  height: 4px;
  background-color: var(--edtt-color-primary-400);
}

.media-resize-handle-corner .media-resize-handle-bar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--edtt-color-primary-400);
  box-shadow: 0 0 8px var(--edtt-color-primary-300);
}

/* Time range slider */
.media-time-range-slider {
  margin: 16px 0;
}

.media-time-display {
  font-size: 12px;
  color: #666;
}

/* Control buttons */
.media-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 20px !important;
  width: 40px !important;
  height: 40px !important;
  margin: 0 4px !important;
}

.media-control-button:hover {
  background-color: var(--edtt-color-gray-100) !important;
  transform: scale(1.1);
}

/* Slider styles */
.ant-slider-track {
  background-color: var(--edtt-color-primary) !important;
}

.ant-slider-handle {
  border-color: var(--edtt-color-primary) !important;
}

.ant-slider-handle:focus {
  box-shadow: 0 0 0 5px var(--edtt-color-primary-200) !important;
}

/* Switch styles */
.ant-switch-checked {
  background-color: var(--edtt-color-primary) !important;
}

/* Override select styles */
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: var(--edtt-color-primary-100) !important;
  color: var(--edtt-color-primary) !important;
}

/* Input focus styles */
.ant-input:focus,
.ant-input-focused {
  border-color: var(--edtt-color-primary) !important;
  box-shadow: 0 0 0 2px var(--edtt-color-primary-200) !important;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: var(--edtt-color-primary) !important;
  box-shadow: 0 0 0 2px var(--edtt-color-primary-200) !important;
}

.ant-input-affix-wrapper-focused {
  border-color: var(--edtt-color-primary) !important;
  box-shadow: 0 0 0 2px var(--edtt-color-primary-200) !important;
}

/* URL input specific styles */
.ant-input-affix-wrapper:hover {
  border-color: var(--edtt-color-primary-400) !important;
}

.ant-input-affix-wrapper .ant-input:hover {
  border-color: var(--edtt-color-primary-400) !important;
}

/* Ensure prefix icon matches theme */
.ant-input-affix-wrapper .ant-input-prefix {
  color: var(--edtt-color-primary) !important;
}

/* Ensure input placeholder matches theme */
.ant-input::placeholder {
  color: var(--edtt-color-gray-400) !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  .media-config-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .media-config-label {
    margin-bottom: 8px;
  }

  .media-player-container {
    padding: 12px;
  }

  .media-control-button {
    padding: 0 4px;
  }

  /* Responsive image container */
  .image-container {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Smaller thumbnails on mobile */
  .image-thumbnails-container div[style*="width: 60px"] {
    width: 50px !important;
    height: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
  }

  /* Adjust controls spacing */
  .image-controls-container {
    padding: 0.25rem;
    gap: 0.25rem;
  }

  /* Ensure slider doesn't get too small */
  .image-controls-container .ant-slider {
    min-width: 80px;
  }

  /* Responsive upload area */
  .media-upload-dragger {
    padding: 0.5rem !important;
  }

  .upload-list-container {
    max-height: 150px;
  }

  .upload-list-item {
    padding: 3px;
  }

  .upload-list-item-thumbnail {
    width: 30px;
    height: 30px;
  }

  .upload-list-item-name {
    font-size: 11px;
  }

  .upload-list-item-status {
    font-size: 10px;
  }

  /* Video component responsive styles */
  .video-player-container {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Make navigation buttons smaller on mobile */
  .tailwind-grid-cols-\[60px_1fr_60px\] {
    grid-template-columns: 40px 1fr 40px !important;
  }

  /* Adjust button sizes for mobile */
  .tailwind-w-\[44px\], .tailwind-h-\[44px\] {
    width: 36px !important;
    height: 36px !important;
  }

  /* Ensure video controls are properly spaced */
  .tailwind-gap-x-4 {
    gap: 0.5rem !important;
  }

  /* Make video thumbnails wrap better on small screens */
  .tailwind-flex-wrap {
    flex-wrap: wrap !important;
  }
}

/* Extra small devices */
@media (max-width: 480px) {

  /* Stack controls vertically on very small screens */
  .image-controls-container>div {
    width: 100%;
    justify-content: center;
    margin-bottom: 0.5rem;
  }

  /* Make thumbnails scrollable horizontally */
  .image-thumbnails-container > div {
    flex-wrap: nowrap;
    overflow-x: auto;
    justify-content: flex-start;
    padding-bottom: 0.5rem;
    width: 100%;
  }

  /* Adjust upload area for very small screens */
  .media-upload-dragger {
    min-height: 100px !important;
    padding: 0.4rem !important;
  }

  .media-upload-dragger p {
    margin-bottom: 0.3rem !important;
  }

  .upload-list-container {
    max-height: 120px;
    margin-top: 5px;
  }

  .upload-list-item-actions button {
    padding: 0 !important;
    width: 24px !important;
    height: 24px !important;
    min-width: 24px !important;
  }

  /* Ensure text doesn't overflow */
  .tailwind-text-sm, .tailwind-text-xs {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Video component responsive styles for very small screens */
  .tailwind-grid-cols-\[60px_1fr_60px\] {
    grid-template-columns: 32px 1fr 32px !important;
  }

  /* Make navigation buttons even smaller on very small screens */
  .tailwind-w-\[44px\], .tailwind-h-\[44px\] {
    width: 30px !important;
    height: 30px !important;
    font-size: 16px !important;
  }

  /* Stack video controls vertically on very small screens */
  .tailwind-flex-wrap.tailwind-items-center.tailwind-justify-center {
    flex-direction: column;
    gap: 0.5rem;
  }

  /* Make video thumbnails smaller on very small screens */
  .tailwind-w-\[60px\], .tailwind-h-\[60px\], .tailwind-min-w-\[60px\], .tailwind-max-w-\[60px\] {
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }

  /* Adjust input fields for better mobile display */
  .tailwind-w-\[50\%\] {
    width: 100% !important;
  }

  /* Stack form controls on small screens */
  .tailwind-flex.tailwind-items-center.tailwind-justify-between {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}

/* Color input styling */
input[type="color"] {
  height: 32px;
  padding: 2px;
  cursor: pointer;
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

input[type="color"]::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

/* Specific styles for Audio Component */
.audio-player-container {
  transition: all 0.3s ease;
  position: relative;
}

/* Specific styles for Video Component */
.video-player-container {
  transition: all 0.3s ease;
  position: relative;
  background-color: #000;
  width: 100% !important;
  max-width: 100% !important;
}

.video-player-container:hover .video-controls {
  opacity: 1;
}

/* Responsive styles for different screen sizes */
@media (min-width: 1200px) {
  /* Larger navigation buttons on big screens */
  .tailwind-grid-cols-\[60px_1fr_60px\] {
    grid-template-columns: 70px 1fr 70px !important;
  }

  /* Larger buttons on big screens */
  .tailwind-w-\[44px\], .tailwind-h-\[44px\] {
    width: 50px !important;
    height: 50px !important;
  }
}

/* Medium screens */
@media (min-width: 992px) and (max-width: 1199px) {
  /* Slightly larger navigation buttons on medium screens */
  .tailwind-grid-cols-\[60px_1fr_60px\] {
    grid-template-columns: 65px 1fr 65px !important;
  }
}

/* Specific styles for Image Component */
.image-container img {
  display: block;
  max-width: 100%;
  margin: 0 auto;
}

/* Responsive image styles */
.responsive-image {
  max-width: none;
  /* Allow image to be larger than container for panning */
  height: auto;
  object-fit: contain;
  /* Use hardware acceleration for smoother transforms */
  transform: translateZ(0);
  will-change: transform;
  /* Optimize image rendering during transforms */
  image-rendering: auto;
  backface-visibility: hidden;
  /* Prevent default browser drag behavior */
  user-select: none;
  -webkit-user-drag: none;
}

/* Image thumbnails container */
.image-thumbnails-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  min-height: 70px; /* Increased height for larger thumbnails */
  max-height: 100px; /* Increased max height for larger thumbnails */
  background-color: var(--edtt-color-bg-default);
  box-shadow: var(--edtt-shadow);
  margin-top: 10px; /* Fixed spacing from video */
}

/* Ensure thumbnail container's children are properly displayed */
.image-thumbnails-container > div {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
  padding: 4px;
  max-height: 80px;
  overflow-y: hidden;
}

/* Vertical images container */
.vertical-images-container {
  margin-top: 1rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* Add spacing between vertical images */
.vertical-images-container .tailwind-flex-col>div:not(:last-child) {
  margin-bottom: 1.5rem;
}

/* Add a subtle separator between vertical images */
.vertical-images-container .tailwind-flex-col>div:not(:last-child)::after {
  content: '';
  display: block;
  height: 1px;
  background-color: var(--edtt-color-gray-100);
  margin-top: 1.5rem;
  width: 100%;
}

/* Image controls container */
.image-controls-container {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

/* PannableImage styles */
.pannable-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: none;
  /* Ngăn chặn các hành vi mặc định của trình duyệt trên thiết bị cảm ứng */
  z-index: 1;
  /* Đảm bảo container có z-index cao hơn các phần tử khác */
  user-select: none;
  /* Ngăn chặn việc chọn văn bản */
}

.pannable-image-container img {
  max-width: none;
  max-height: none;
  transition: transform 0.2s ease;
  pointer-events: none;
  /* Đảm bảo sự kiện chuột đi qua ảnh và được xử lý bởi container */
}

.pannable-image-container.panning {
  cursor: grabbing !important;
}

.pannable-image-container.panning img {
  transition: none;
}

.pannable-image-container>div {
  pointer-events: none;
  /* Đảm bảo sự kiện chuột đi qua div con và được xử lý bởi container */
}

.pannable-image-controls {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 20;
  /* Đảm bảo controls có z-index cao hơn container */
  display: flex;
  gap: 8px;
  background-color: var(--edtt-color-bg-default);
  opacity: 0.7;
  padding: 4px;
  border-radius: 4px;
  box-shadow: var(--edtt-shadow);
  pointer-events: auto !important;
  /* Đảm bảo controls vẫn nhận sự kiện chuột */
}

.pannable-image-controls button {
  pointer-events: auto !important;
  /* Đảm bảo buttons vẫn nhận sự kiện chuột */
}

.pannable-image-container:hover .pannable-image-controls {
  opacity: 1;
}

.pannable-image-container .pannable-image-controls {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}
/** @type {import('tailwindcss').Config} */
module.exports = {
  prefix: 'tailwind-',
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        // Base Colors
        white: 'var(--edtt-color-white)',
        black: 'var(--edtt-color-black)',
        gray: {
          100: 'var(--edtt-color-gray-100)',
          200: 'var(--edtt-color-gray-200)',
          700: 'var(--edtt-color-gray-700)',
          800: 'var(--edtt-color-gray-800)',
          900: 'var(--edtt-color-gray-900)',
        },

        // Primary Colors
        primary: {
          DEFAULT: 'var(--edtt-color-primary-base)',
          base: 'var(--edtt-color-primary-base)',
          100: 'var(--edtt-color-primary-100)',
          200: 'var(--edtt-color-primary-200)',
          300: 'var(--edtt-color-primary-300)',
          400: 'var(--edtt-color-primary-400)',
          500: 'var(--edtt-color-primary-500)',
          600: 'var(--edtt-color-primary-600)',
          700: 'var(--edtt-color-primary-700)',
        },

        // Secondary Colors
        secondary: {
          DEFAULT: 'var(--edtt-color-secondary-base)',
          base: 'var(--edtt-color-secondary-base)',
          100: 'var(--edtt-color-secondary-100)',
          200: 'var(--edtt-color-secondary-200)',
          300: 'var(--edtt-color-secondary-300)',
          400: 'var(--edtt-color-secondary-400)',
          500: 'var(--edtt-color-secondary-500)',
          600: 'var(--edtt-color-secondary-600)',
          700: 'var(--edtt-color-secondary-700)',
        },

        // Tertiary Colors
        tertiary: {
          DEFAULT: 'var(--edtt-color-tertiary-base)',
          base: 'var(--edtt-color-tertiary-base)',
          100: 'var(--edtt-color-tertiary-100)',
          200: 'var(--edtt-color-tertiary-200)',
          300: 'var(--edtt-color-tertiary-300)',
          400: 'var(--edtt-color-tertiary-400)',
          500: 'var(--edtt-color-tertiary-500)',
          600: 'var(--edtt-color-tertiary-600)',
          700: 'var(--edtt-color-tertiary-700)',
        },

        // Quaternary Colors
        quaternary: {
          DEFAULT: 'var(--edtt-color-quaternary-base)',
          base: 'var(--edtt-color-quaternary-base)',
          100: 'var(--edtt-color-quaternary-100)',
          200: 'var(--edtt-color-quaternary-200)',
          300: 'var(--edtt-color-quaternary-300)',
          400: 'var(--edtt-color-quaternary-400)',
          500: 'var(--edtt-color-quaternary-500)',
          600: 'var(--edtt-color-quaternary-600)',
          700: 'var(--edtt-color-quaternary-700)',
        },

        // Grey Colors
        grey: {
          DEFAULT: 'var(--edtt-color-grey-base)',
          base: 'var(--edtt-color-grey-base)',
          100: 'var(--edtt-color-grey-100)',
          200: 'var(--edtt-color-grey-200)',
          300: 'var(--edtt-color-grey-300)',
          400: 'var(--edtt-color-grey-400)',
          500: 'var(--edtt-color-grey-500)',
          600: 'var(--edtt-color-grey-600)',
          700: 'var(--edtt-color-grey-700)',
        },

        // Quinary Colors (available in learning theme)
        quinary: {
          DEFAULT: 'var(--edtt-color-quinary-base)',
          base: 'var(--edtt-color-quinary-base)',
          100: 'var(--edtt-color-quinary-100)',
          200: 'var(--edtt-color-quinary-200)',
          300: 'var(--edtt-color-quinary-300)',
          400: 'var(--edtt-color-quinary-400)',
          500: 'var(--edtt-color-quinary-500)',
          600: 'var(--edtt-color-quinary-600)',
          700: 'var(--edtt-color-quinary-700)',
        },

        // Senary Colors (available in dark theme)
        senary: {
          DEFAULT: 'var(--edtt-color-senary-base)',
          base: 'var(--edtt-color-senary-base)',
          100: 'var(--edtt-color-senary-100)',
          200: 'var(--edtt-color-senary-200)',
          300: 'var(--edtt-color-senary-300)',
          400: 'var(--edtt-color-senary-400)',
          500: 'var(--edtt-color-senary-500)',
          600: 'var(--edtt-color-senary-600)',
          700: 'var(--edtt-color-senary-700)',
        },

        // Status Colors
        status: {
          success: 'var(--edtt-color-status-success)',
          warning: 'var(--edtt-color-status-warning)',
          error: 'var(--edtt-color-status-error)',
          info: 'var(--edtt-color-status-info)',
        },

        // Common aliases for status colors
        success: 'var(--edtt-color-status-success)',
        warning: 'var(--edtt-color-status-warning)',
        error: 'var(--edtt-color-status-error)',
        info: 'var(--edtt-color-status-info)',
      },

      // Theme-based variables
      backgroundColor: {
        primary: 'var(--edtt-color-primary)',
        default: 'var(--edtt-color-bg-default)',
        secondary: 'var(--edtt-color-bg-secondary)',
        tertiary: 'var(--edtt-color-bg-tertiary)',
        inverse: 'var(--edtt-color-bg-inverse)',
        card: 'var(--edtt-color-bg-card)',
        overlay: 'var(--edtt-color-bg-overlay)',
        mask: 'var(--edtt-color-bg-mask)',
        spotlight: 'var(--edtt-color-bg-spotlight)',
        disabled: 'var(--edtt-color-disabled-bg)',
        selected: 'var(--edtt-color-selected)',
        hover: 'var(--edtt-color-hover)',
      },

      textColor: {
        primary: 'var(--edtt-color-text-primary)',
        default: 'var(--edtt-color-text-default)',
        secondary: 'var(--edtt-color-text-secondary)',
        tertiary: 'var(--edtt-color-text-tertiary)',
        inverse: 'var(--edtt-color-text-inverse)',
        disabled: 'var(--edtt-color-text-disabled)',
        link: 'var(--edtt-color-link)',
        'link-hover': 'var(--edtt-color-link-hover)',
        'link-active': 'var(--edtt-color-link-active)',
        placeholder: 'var(--edtt-color-placeholder)',
      },

      borderColor: {
        default: 'var(--edtt-color-border-default)',
        strong: 'var(--edtt-color-border-strong)',
        light: 'var(--edtt-color-border-light)',
        primary: 'var(--edtt-color-border-primary)',
        focus: 'var(--edtt-color-border-focus)',
        disabled: 'var(--edtt-color-border-disabled)',
      },

      boxShadow: {
        default: 'var(--edtt-shadow)',
        sm: 'var(--edtt-shadow-sm)',
        md: 'var(--edtt-shadow-md)',
        lg: 'var(--edtt-shadow-lg)',
        card: 'var(--edtt-shadow-card)',
        popup: 'var(--edtt-shadow-popup)',
        dropdown: 'var(--edtt-shadow-dropdown)',
        focus: 'var(--edtt-shadow-focus)',
      },

      transitionProperty: {
        slow: 'var(--edtt-transition-slow)',
        normal: 'var(--edtt-transition-normal)',
        fast: 'var(--edtt-transition-fast)',
      },
    },
  },
  plugins: [],
  blocklist: ['antd'],
  safelist: [
    // Make sure these important classes are always available
    {
      pattern:
        /bg-(primary|secondary|tertiary|quaternary|quinary|senary)-(100|200|300|400|500|600|700)/,
    },
    {
      pattern:
        /text-(primary|secondary|tertiary|quaternary|quinary|senary)-(100|200|300|400|500|600|700)/,
    },
    {
      pattern:
        /border-(primary|secondary|tertiary|quaternary|quinary|senary)-(100|200|300|400|500|600|700)/,
    },
    // Theme-based variables
    {
      pattern:
        /bg-(default|secondary|tertiary|inverse|card|overlay|mask|spotlight|disabled|selected|hover)/,
    },
    {
      pattern:
        /text-(default|secondary|tertiary|inverse|disabled|link|link-hover|link-active|placeholder)/,
    },
    {
      pattern: /border-(default|strong|light|primary|focus|disabled)/,
    },
    {
      pattern: /shadow-(default|sm|md|lg|card|popup|dropdown|focus)/,
    },
  ],
};

import React from 'react';
import Card from 'antd/es/card/Card';
import { IEdTechRenderProps } from '../../interfaces/AppComponents';
import { withEdComponentParams } from '../../hocs/withEdComponentParams/withEdComponentParams';
import CoreTitle, { ITextProps } from '../core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { EdTechRootState } from '../../store/store';
import { updateTitleAndSync } from '../../utils/titleSyncUtils';
import { z } from 'zod';
import { createComponentSchema } from '../../utils/schema/createComponentSchema';

interface LessonContentProps {
  titleProps?: ITextProps;
}

// Zod schema for component validation
export const lessonCardSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    content: z.string().optional(),
    imageUrl: z.string().optional(),
    showBorder: z.boolean().optional(),
    showShadow: z.boolean().optional(),
  },
});

// Main Lesson Content Component
const LessonCardComponent: React.FC<IEdTechRenderProps<LessonContentProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Tạo hàm overrideChangeParams để xử lý thay đổi params
  const onUpdateText = (data: ITextProps) => {
    // Sử dụng hàm utility để cập nhật title và đồng bộ với menu
    updateTitleAndSync(
      props.id,
      data,
      params,
      renderTreeData,
      dispatch,
      addOrUpdateParamComponent
    );
  };

  return (
    <Card
      className="tailwind-shadow-sm"
      title={
        <CoreTitle
          titleProps={{
            ...params?.titleProps,
            text: props?.title,
          }}
          isColorDisabled
          isEditing={isEditing}
          onUpdate={onUpdateText}
        />
      }
    >
      {props.children}
    </Card>
  );
};

export default withEdComponentParams(LessonCardComponent);

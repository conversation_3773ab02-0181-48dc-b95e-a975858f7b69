{"edComponentParams": [{"id": "abc12345", "params": {"id": "quiz-001", "questions": [{"id": "201", "type": "quiz", "title": "Câu hỏi Vật lý", "question": "<PERSON><PERSON>ng thức tính quãng đường khi biết vận tốc và thời gian là gì?", "answers": [{"id": "201A", "text": "S = v.t", "isCorrect": true}, {"id": "201B", "text": "S = v/t", "isCorrect": false}, {"id": "201C", "text": "S = v+t", "isCorrect": false}, {"id": "201D", "text": "S = v-t", "isCorrect": false}], "explanation": "Quãng đường S được tính bằng vận tốc v nhân với thời gian t.", "isCompleted": true, "status": "correct"}, {"id": "202", "type": "quiz", "title": "Câu hỏi Hóa học", "question": "Nguyên tố nào có số hiệu nguyên tử là 1?", "answers": [{"id": "202A", "text": "<PERSON><PERSON>", "isCorrect": false}, {"id": "202B", "text": "Oxy", "isCorrect": false}, {"id": "202C", "text": "Hydro", "isCorrect": true}, {"id": "202D", "text": "Carbon", "isCorrect": false}], "explanation": "Nguyên tố Hydro có số hiệu nguyên tử là 1 trong bảng tuần hoàn.", "isCompleted": true, "status": "incorrect"}, {"id": "203", "type": "quiz", "title": "Câu hỏi Sinh học", "question": "<PERSON>ơ quan nào trong cơ thể người chịu trách nhiệm bơm máu?", "answers": [{"id": "203A", "text": "Gan", "isCorrect": false}, {"id": "203B", "text": "<PERSON>", "isCorrect": true}, {"id": "203C", "text": "<PERSON><PERSON><PERSON>", "isCorrect": false}, {"id": "203D", "text": "Dạ dày", "isCorrect": false}], "explanation": "<PERSON> là cơ quan chịu tr<PERSON>ch nhiệm bơm máu đi kh<PERSON>p cơ thể.", "isCompleted": true, "status": "incorrect"}, {"id": "204", "type": "quiz", "title": "Câu hỏi Địa lý", "question": "Sông nào dài nhất thế giới?", "answers": [{"id": "204A", "text": "Sông Amazon", "isCorrect": false}, {"id": "204B", "text": "Sông Nile", "isCorrect": true}, {"id": "204C", "text": "Sông Mississippi", "isCorrect": false}, {"id": "204D", "text": "Sông Mekong", "isCorrect": false}], "explanation": "Sông Nile ở châu Phi là con sông dài nhất thế giới với chiều dài khoảng 6.650 km.", "isCompleted": true, "status": "correct"}, {"id": "205", "type": "quiz", "title": "<PERSON>âu hỏi <PERSON><PERSON> học", "question": "<PERSON><PERSON><PERSON> trị của bi<PERSON>u thức 5 + 3 × 2 là bao nhi<PERSON>u?", "answers": [{"id": "205A", "text": "16", "isCorrect": false}, {"id": "205B", "text": "11", "isCorrect": true}, {"id": "205C", "text": "13", "isCorrect": false}, {"id": "205D", "text": "10", "isCorrect": false}], "explanation": "<PERSON> thứ tự ưu tiên phép to<PERSON>: 3 × 2 = 6, sau đ<PERSON> 5 + 6 = 11.", "isCompleted": true, "status": "correct"}, {"id": "matching3", "type": "matching", "title": "<PERSON><PERSON>é<PERSON> Gia với Thủ Đô", "description": "Ghép mỗi quốc gia ở cột A với thủ đô tương ứng ở cột B.", "leftItems": [{"id": "m3l1", "content": "<PERSON><PERSON><PERSON>", "type": "text", "matchId": "m3r1"}, {"id": "m3l2", "content": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "matchId": "m3r2"}, {"id": "m3l3", "content": "Canada", "type": "text", "matchId": "m3r3"}], "rightItems": [{"id": "m3r1", "content": "Paris", "type": "text", "matchId": "m3l1"}, {"id": "m3r2", "content": "Tokyo", "type": "text", "matchId": "m3l2"}, {"id": "m3r3", "content": "Ottawa", "type": "text", "matchId": "m3l3"}], "shuffleItems": true, "isCompleted": true, "status": "correct"}, {"id": "matching4", "type": "matching", "title": "Ghép <PERSON>h<PERSON> vớ<PERSON>", "description": "Ghép mỗi nhà khoa học ở cột A với phát hiện của họ ở cột B.", "leftItems": [{"id": "m4l1", "content": "<PERSON>", "type": "text", "matchId": "m4r1"}, {"id": "m4l2", "content": "<PERSON>", "type": "text", "matchId": "m4r2"}, {"id": "m4l3", "content": "<PERSON>", "type": "text", "matchId": "m4r3"}], "rightItems": [{"id": "m4r1", "content": "<PERSON><PERSON><PERSON> luật Vạ<PERSON> vật <PERSON> dẫn", "type": "text", "matchId": "m4l1"}, {"id": "m4r2", "content": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "matchId": "m4l2"}, {"id": "m4r3", "content": "Phóng xạ", "type": "text", "matchId": "m4l3"}], "shuffleItems": true, "isCompleted": true, "status": "incorrect"}, {"id": "def67890", "type": "fillblanks", "title": "<PERSON><PERSON>u hỏi điền từ", "question": "<PERSON><PERSON> là ____ của Việt Nam. Đồng bằng sông Cửu Long có mạng lưới sông ngòi ____.", "blanks": [{"id": "ghi12345", "correctAnswer": "thủ đô", "alternativeAnswers": ["<PERSON><PERSON><PERSON> đô"], "hint": "Trung tâm chính trị của đất nước"}, {"id": "jkl67890", "correctAnswer": "dày đặc", "hint": "", "alternativeAnswers": ["day dac"]}], "explanation": "<PERSON><PERSON>i là thủ đô của Việt Nam.", "caseSensitive": false, "showHints": true, "points": 1, "isCompleted": true, "status": "correct"}]}}, {"id": "pqr67890", "params": {"titleProps": {"text": "<PERSON><PERSON><PERSON> ti<PERSON>u môn học"}}}, {"id": "stu12345", "params": {"titleProps": {"text": "<PERSON><PERSON>i g<PERSON>"}}}, {"id": "vwx67890", "params": {"titleProps": {"text": "<PERSON><PERSON><PERSON>"}}}, {"id": "yz123456", "params": {"titleProps": {"text": "<PERSON><PERSON><PERSON><PERSON> tập"}}}], "edTechRenderTreeData": {"id": "RootLessonComponent", "name": "RootLessonComponent", "title": "<PERSON><PERSON><PERSON>c", "type": "STRUCTURE", "order": 0, "path": "RootLessonComponent", "subItems": [{"id": "a1b2c3d4", "name": "LessonCommonComponent", "title": "<PERSON><PERSON><PERSON>", "type": "STRUCTURE", "order": 0, "path": "RootLessonComponent/a1b2c3d4", "subItems": [{"id": "pqr67890", "name": "LessonCardComponent", "title": "<PERSON><PERSON><PERSON> ti<PERSON>u môn học", "path": "RootLessonComponent/a1b2c3d4/pqr67890", "subItems": [], "order": 2}, {"id": "stu12345", "name": "LessonCardComponent", "title": "<PERSON><PERSON>i g<PERSON>", "path": "RootLessonComponent/a1b2c3d4/stu12345", "subItems": [{"name": "FreefallSimulator", "title": "FreefallSimulator", "order": 1, "path": "RootLessonComponent/a1b2c3d4/stu12345/b1c2d3e4", "subItems": [], "id": "b1c2d3e4", "type": "SIMULATORS"}, {"name": "BoxSimulator", "title": "<PERSON><PERSON><PERSON> chuy<PERSON> động", "order": 2, "path": "RootLessonComponent/a1b2c3d4/stu12345/c2d3e4f5", "subItems": [], "id": "c2d3e4f5", "type": "SIMULATORS"}], "order": 3}, {"id": "vwx67890", "name": "LessonCardComponent", "title": "<PERSON><PERSON><PERSON>", "path": "RootLessonComponent/a1b2c3d4/vwx67890", "subItems": [{"name": "CoordinateFinderGame", "title": "CoordinateFinderGame", "order": 1, "path": "RootLessonComponent/a1b2c3d4/vwx67890/d3e4f5g6", "subItems": [], "id": "d3e4f5g6", "type": "MINI_GAMES"}], "order": 4}, {"id": "yz123456", "name": "LessonCardComponent", "title": "<PERSON><PERSON><PERSON><PERSON> tập", "path": "RootLessonComponent/a1b2c3d4/yz123456", "subItems": [{"name": "PracticeEngineWrapper", "title": "<PERSON><PERSON><PERSON>", "order": 1, "path": "RootLessonComponent/a1b2c3d4/yz123456/abc12345", "subItems": [], "id": "abc12345", "type": "QUIZ"}], "order": 5}]}]}}
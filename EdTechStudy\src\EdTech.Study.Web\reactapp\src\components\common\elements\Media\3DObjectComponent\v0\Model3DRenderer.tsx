import React, {
  useRef,
  useEffect,
  useState,
  useMemo,
  useCallback,
} from 'react';
import { useFrame } from '@react-three/fiber';
import { Html, useBounds } from '@react-three/drei';
import { G<PERSON><PERSON>oa<PERSON>, OB<PERSON><PERSON>oader, FB<PERSON><PERSON>oader } from 'three-stdlib';
import * as THREE from 'three';
import { mightHaveCorsIssues } from '../../shared/utils';
// ✅ Fixed import to match the correct hook name
import { useSimpleBase64Worker } from '../../../../../../hooks/useSimpleBase64Worker';

// Error indicator component - memoized
const ErrorIndicator = React.memo(({ error }: { error: string }) => {
  return (
    <Html center>
      <div className="tailwind-text-center tailwind-p-4 tailwind-bg-red-50 tailwind-rounded-lg tailwind-shadow tailwind-max-w-md">
        <p className="tailwind-text-red-600 tailwind-font-medium">
          <PERSON><PERSON><PERSON>ng thể tải mô hình 3D
        </p>
        <p className="tailwind-text-gray-700 tailwind-text-sm tailwind-mt-1">
          {error}
        </p>
        <div className="tailwind-mt-3 tailwind-text-xs tailwind-text-gray-500 tailwind-text-left">
          <p className="tailwind-font-medium tailwind-mb-1">
            Kiểm tra các vấn đề sau:
          </p>
          <ul className="tailwind-list-disc tailwind-pl-4">
            <li>URL của mô hình có chính xác không?</li>
            <li>Định dạng tệp có được hỗ trợ không? (.obj, .glb, .fbx)</li>
            <li>Tệp có thể truy cập được không? (CORS, quyền truy cập)</li>
            <li>Tệp có bị hỏng hoặc không đúng định dạng không?</li>
          </ul>
        </div>
      </div>
    </Html>
  );
});

// Model component props
export interface Model3DRendererProps {
  url: string;
  format: 'glb' | 'obj' | 'fbx';
  autoRotate: boolean;
  rotationSpeed: number;
  onLoad?: () => void;
  onError?: (error: any) => void;
  onProgress?: (progress: number) => void;
}

// Helper function to create a standard material - memoized
const createStandardMaterial = (color = 0xcccccc, map?: THREE.Texture) => {
  return new THREE.MeshStandardMaterial({
    color,
    map,
    roughness: 0.7,
    metalness: 0.2,
    side: THREE.DoubleSide,
  });
};

// Helper function to process materials - memoized
const processMaterial = (material: THREE.Material | null) => {
  if (!material) {
    return createStandardMaterial();
  }

  material.needsUpdate = true;

  // Convert basic/phong materials to standard for better lighting
  if (
    material instanceof THREE.MeshBasicMaterial ||
    material instanceof THREE.MeshPhongMaterial
  ) {
    return createStandardMaterial(
      material.color.getHex(),
      material.map || undefined
    );
  }

  // Adjust standard material properties
  if (
    material instanceof THREE.MeshStandardMaterial ||
    material instanceof THREE.MeshPhysicalMaterial
  ) {
    if (material.roughness === 0) material.roughness = 0.5;
    if (material.metalness === 0) material.metalness = 0.2;
    material.side = THREE.DoubleSide;
  }

  return material;
};

// Model component
const Model3DRenderer: React.FC<Model3DRendererProps> = ({
  url,
  format,
  autoRotate,
  rotationSpeed,
  onLoad,
  onError,
  onProgress,
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const bounds = useBounds();
  const [model, setModel] = useState<THREE.Object3D | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);

  // ✅ Base64 Worker Integration with correct hook
  const { base64ToBlobUrl, isWorkerReady } = useSimpleBase64Worker();
  const [conversionProgress, setConversionProgress] = useState<number>(0);
  const [conversionStage, setConversionStage] = useState<string>('');

  // Use refs to track current values of autoRotate and rotationSpeed
  const autoRotateRef = useRef(autoRotate);
  const rotationSpeedRef = useRef(rotationSpeed);

  // Track if component is mounted to prevent state updates after unmounting
  const isMountedRef = useRef(true);

  // Update refs when props change
  useEffect(() => {
    autoRotateRef.current = autoRotate;
    rotationSpeedRef.current = rotationSpeed;
  }, [autoRotate, rotationSpeed]);

  // Cleanup blob URL when component unmounts
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  // Handle model rotation using refs instead of direct props
  useFrame((_, delta) => {
    if (groupRef.current && autoRotateRef.current) {
      groupRef.current.rotation.y += delta * rotationSpeedRef.current * 0.5;
    }
  });

  // Helper function to process a loaded model - memoized
  const processLoadedModel = useCallback((model: THREE.Object3D) => {
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // Process materials
        if (!child.material) {
          child.material = createStandardMaterial();
        } else if (Array.isArray(child.material)) {
          child.material = child.material.map(processMaterial);
        } else {
          child.material = processMaterial(child.material);
        }
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    return model;
  }, []);

  // Helper function to fetch and execute b64.js file to get base64 data - memoized
  const fetchBase64FromScript = useCallback(
    async (scriptUrl: string): Promise<string> => {
      try {
        // Check if we're in offline mode (file:// protocol)
        if (window.location.protocol === 'file:') {
          return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            const currentUrl = window.location.href;
            // Remove the file name to get the directory
            const directory = currentUrl.substring(
              0,
              currentUrl.lastIndexOf('/')
            );

            script.src = directory + '/' + scriptUrl.replace('./', '');
            script.onload = () => {
              try {
                if (!(window as any).uploadFile) {
                  reject(
                    new Error('Failed to extract base64 data from script')
                  );
                }
                // The script should define a global variable or function
                const jsData = (window as any).uploadFile();
                if (jsData && jsData.base64) {
                  resolve(jsData.base64);
                } else {
                  reject(new Error('Script did not define valid base64 data'));
                }
              } catch (err) {
                reject(new Error('Failed to extract base64 data from script'));
              } finally {
                // Clean up the script tag
                document.head.removeChild(script);
              }
            };
            script.onerror = () => {
              document.head.removeChild(script);
              reject(new Error('Failed to load script file'));
            };
            document.head.appendChild(script);
          });
        } else {
          // For online mode, use fetch as before
          const response = await fetch(scriptUrl);
          if (!response.ok) {
            throw new Error(
              `Failed to fetch script: ${response.status} ${response.statusText}`
            );
          }

          const scriptContent = await response.text();
          // Create a function to execute the script and capture the base64 data
          const executeScript = new Function(
            scriptContent + '; return uploadFile();'
          );
          const jsData = executeScript();
          const base64Data = jsData.base64;
          if (!base64Data || typeof base64Data !== 'string') {
            throw new Error('Script did not return valid base64 data');
          }

          return base64Data;
        }
      } catch (error) {
        throw new Error(
          `Failed to load base64 from script: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        );
      }
    },
    []
  );

  // Helper function to get MIME type based on format - memoized
  const getMimeType = useCallback((format: string): string => {
    switch (format.toLowerCase()) {
      case 'glb':
        return 'model/gltf-binary';
      case 'obj':
        return 'text/plain';
      case 'fbx':
        return 'application/octet-stream';
      default:
        return 'application/octet-stream';
    }
  }, []);

  // Helper function to load a model with a specific loader - memoized
  const loadModelWithLoader = useCallback(
    async (
      loader: GLTFLoader | OBJLoader | FBXLoader,
      url: string
    ): Promise<THREE.Object3D> => {
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Model loading timeout'));
        }, 30000);

        setTimeout(() => {
          loader.load(
            url,
            (result) => {
              clearTimeout(timeoutId);
              // Handle different loader result types
              if ('scene' in result) {
                resolve(result.scene); // GLTFLoader returns { scene }
              } else {
                resolve(result); // OBJLoader and FBXLoader return Object3D directly
              }
            },
            (progressEvent) => {
              // Handle loading progress
              if (progressEvent.lengthComputable) {
                const progress =
                  (progressEvent.loaded / progressEvent.total) * 100;
                // Report loading progress (separate from conversion)
                if (onProgress) {
                  // If we had conversion, this is the remaining 70%
                  const baseProgress = conversionProgress > 0 ? 30 : 0;
                  const loadingProgress =
                    (progress * (100 - baseProgress)) / 100;
                  onProgress(baseProgress + loadingProgress);
                }
              }
            },
            (error) => {
              clearTimeout(timeoutId);
              reject(error);
            }
          );
        }, 100);
      });
    },
    [onProgress, conversionProgress]
  );

  // Load model based on format - optimized useEffect
  useEffect(() => {
    if (!url) return;

    // Reset state when loading a new model
    setLoading(true);
    setError(null);
    setConversionProgress(0);
    setConversionStage('');

    // Initial progress
    if (onProgress) {
      onProgress(0);
    }

    const loadModel = async () => {
      try {
        let loadedModel: THREE.Object3D | null = null;
        let modelUrl = url;

        // Check if URL points to a .b64.js file
        if (url.endsWith('.js')) {
          try {
            // Fetch base64 data from script
            const base64Data = await fetchBase64FromScript(url);
            const mimeType = getMimeType(format);

            // ✅ Use Worker for base64 conversion with mounting check
            if (!isMountedRef.current) {
              throw new Error('Component unmounted during base64 processing');
            }

            modelUrl = await base64ToBlobUrl(
              base64Data,
              mimeType,
              {
                chunkSize: 1024 * 1024, // 1MB chunks
                maxFileSize: 200 * 1024 * 1024, // 200MB max
              },
              (progress: number, stage?: string) => {
                // Check if component is still mounted before updating state
                if (isMountedRef.current) {
                  setConversionProgress(progress);
                  setConversionStage(stage || '');

                  // Report conversion progress (30% of total)
                  if (onProgress) {
                    onProgress(progress * 0.3);
                  }
                }
              }
            );

            // Check again after async operation
            if (!isMountedRef.current) {
              throw new Error('Component unmounted after base64 processing');
            }

            // Store blob URL for cleanup
            if (isMountedRef.current) {
              setBlobUrl(modelUrl);
            }
          } catch (conversionError) {
            throw new Error(
              `Failed to process b64.js file: ${
                conversionError instanceof Error
                  ? conversionError.message
                  : 'Unknown error'
              }`
            );
          }
        }

        // Load model based on format using the processed URL
        const loadingManager = new THREE.LoadingManager();

        if (format === 'glb') {
          loadedModel = await loadModelWithLoader(
            new GLTFLoader(loadingManager),
            modelUrl
          );
        } else if (format === 'obj') {
          loadedModel = await loadModelWithLoader(
            new OBJLoader(loadingManager),
            modelUrl
          );
        } else if (format === 'fbx') {
          loadedModel = await loadModelWithLoader(
            new FBXLoader(loadingManager),
            modelUrl
          );
        }

        if (loadedModel) {
          // Process the model (materials, shadows, etc.)
          processLoadedModel(loadedModel);

          // Set the model only if component is still mounted
          if (isMountedRef.current) {
            console.log('✅ Setting loaded model and updating states');
            setModel(loadedModel);

            // Reset conversion progress since we're done
            setConversionProgress(0);
            setConversionStage('');

            // Fit model to view
            if (groupRef.current) {
              console.log('🎯 Fitting model to view bounds');
              // Wait for the next frame to ensure the model is in the scene
              setTimeout(() => {
                if (isMountedRef.current) {
                  try {
                    bounds.refresh().fit();
                    console.log(
                      '✅ Model fitted successfully, setting loading to false'
                    );
                    setLoading(false);
                    if (onProgress) onProgress(100);
                    if (onLoad) onLoad();
                  } catch (fitError) {
                    console.warn(
                      '⚠️ Model fitting failed, but setting loading to false anyway:',
                      fitError
                    );
                    // Ensure loading state is updated even if fitting fails
                    setLoading(false);
                    if (onProgress) onProgress(100);
                    if (onLoad) onLoad();
                  }
                }
              }, 100);
            } else {
              console.log(
                '✅ No group ref, setting loading to false immediately'
              );
              setLoading(false);
              if (onProgress) onProgress(100);
              if (onLoad) onLoad();
            }
          }
        }
      } catch (error) {
        // Provide more detailed error message
        let errorMessage = 'Could not load model';

        if (error instanceof Error) {
          errorMessage = `Loading error: ${error.message}`;
        }

        // Check for network errors
        if (
          error instanceof TypeError &&
          error.message.includes('Failed to fetch')
        ) {
          errorMessage =
            'Network error: Could not access the model file. Check the URL and ensure it is accessible.';
        }

        // Check for CORS errors
        if (error instanceof DOMException && error.name === 'SecurityError') {
          errorMessage =
            'CORS error: The model file is blocked by cross-origin policy. Ensure proper CORS headers are set.';
        }

        // Check for potential CORS issues
        if (mightHaveCorsIssues(url)) {
          errorMessage =
            'Possible CORS issue detected: The model is hosted on a different domain. ' +
            'This may cause loading problems. Consider hosting the model on the same server ' +
            'or configuring CORS headers on the server hosting the model.';
        }

        // Only update state if component is still mounted
        if (isMountedRef.current) {
          setError(errorMessage);
          setLoading(false);
          if (onError) onError(error);
        }
      }
    };

    // Start the model loading process
    loadModel();

    // Add a safety timeout to ensure loading indicator doesn't get stuck
    const safetyTimeoutId = setTimeout(() => {
      if (loading && isMountedRef.current) {
        setLoading(false);
      }
    }, 15000); // 15 seconds safety timeout

    // Clean up the safety timeout
    return () => clearTimeout(safetyTimeoutId);
  }, [url, format, base64ToBlobUrl]); // Include base64ToBlobUrl in dependencies

  // Memoize the processed model to prevent re-processing on every render
  const processedModel = useMemo(() => {
    if (!model) return null;

    // Normalize and process the model
    const normalizeModel = (inputModel: THREE.Object3D): THREE.Object3D => {
      // Clone the model to avoid modifying the original
      const processedModel = inputModel.clone();

      // Normalize the model scale and position
      const box = new THREE.Box3().setFromObject(processedModel);
      const size = box.getSize(new THREE.Vector3());
      const center = box.getCenter(new THREE.Vector3());

      // Calculate scale to normalize size (max dimension to 2 units)
      const maxDimension = Math.max(size.x, size.y, size.z);
      const scale = maxDimension > 0 ? 2 / maxDimension : 1;

      // Apply transformations to the model
      processedModel.scale.multiplyScalar(scale);
      processedModel.position.sub(center.multiplyScalar(scale));

      return processedModel;
    };

    return normalizeModel(model);
  }, [model]);

  // Debug logging for render state
  console.log('🎨 Model3DRenderer render state:', {
    loading,
    error: !!error,
    model: !!model,
    processedModel: !!processedModel,
    conversionProgress,
    conversionStage,
    isWorkerReady,
  });

  // Show conversion progress if converting
  if (conversionProgress > 0 && conversionProgress < 100) {
    console.log('🔄 Showing conversion progress:', conversionProgress);
    return (
      <Html center>
        <div className="tailwind-text-center tailwind-p-4 tailwind-bg-white tailwind-rounded-lg tailwind-shadow tailwind-max-w-md">
          <p className="tailwind-text-gray-800 tailwind-font-medium tailwind-mb-2">
            Đang chuyển đổi mô hình 3D
          </p>
          <div className="tailwind-w-full tailwind-bg-gray-200 tailwind-rounded-full tailwind-h-2 tailwind-mb-2">
            <div
              className="tailwind-bg-blue-500 tailwind-h-2 tailwind-rounded-full tailwind-transition-all"
              style={{ width: `${conversionProgress}%` }}
            />
          </div>
          <p className="tailwind-text-gray-600 tailwind-text-sm">
            {Math.round(conversionProgress)}% - {conversionStage}
          </p>
          {!isWorkerReady && (
            <p className="tailwind-text-yellow-600 tailwind-text-xs tailwind-mt-1">
              Đang sử dụng main thread fallback
            </p>
          )}
        </div>
      </Html>
    );
  }

  // If error, show error message
  if (error) {
    return <ErrorIndicator error={error} />;
  }

  // If no model, show default cube
  if (!model || !processedModel) {
    console.log('❌ No model or processedModel available, returning empty');
    return <></>;
  }

  // Return the processed model
  console.log('🎉 Rendering 3D model successfully!');
  return (
    <group ref={groupRef}>
      <primitive object={processedModel} />
    </group>
  );
};

export default React.memo(Model3DRenderer);

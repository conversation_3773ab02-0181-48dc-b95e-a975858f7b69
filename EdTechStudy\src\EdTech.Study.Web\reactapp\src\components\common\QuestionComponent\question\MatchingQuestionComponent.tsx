import React, { useRef, useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, Tooltip, Badge } from 'antd';
import { MatchingQuestion } from '../QuestionBankConfig';
import {
  ArrowRightOutlinedIcon,
  DeleteIcon,
  InfoCircleOutlinedIcon,
  RedoOutlinedIcon,
} from '../../../icons/IconIndex';

interface MatchingQuestionProps {
  question: MatchingQuestion;
  onAnswer: (answer: any) => void;
  disabled?: boolean;
}

interface MatchingItemProps {
  index: number;
  originalIndex: number;
  text: string;
  isSelected: boolean;
  isMatched: boolean;
  matchedItemLabels?: string[];
  onClick: () => void;
  onRemove?: (index: number) => void;
  side: 'left' | 'right';
  disabled?: boolean;
}

const MatchingItem: React.FC<MatchingItemProps> = ({
  index,
  originalIndex,
  text,
  isSelected,
  isMatched,
  matchedItemLabels,
  onClick,
  onRemove,
  side,
  disabled = false,
}) => {
  const isLeft = side === 'left';
  const itemLabel = isLeft ? `${index + 1}` : String.fromCharCode(65 + index);

  return (
    <div
      data-index={index}
      data-original-index={originalIndex}
      data-side={side}
      onClick={disabled ? undefined : onClick}
      className={`
        tailwind-p-3
        tailwind-rounded-lg
        tailwind-border-2
        tailwind-transition-all
        tailwind-flex
        tailwind-items-center
        tailwind-justify-between
        tailwind-mb-3
        tailwind-bg-white
        ${
          isSelected
            ? `tailwind-bg-${isLeft ? 'indigo' : 'green'}-50 tailwind-border-${
                isLeft ? 'indigo' : 'green'
              }-500 tailwind-shadow-md`
            : isMatched
            ? 'tailwind-bg-green-50 tailwind-border-green-300 tailwind-shadow-sm'
            : 'tailwind-border-gray-200 hover:tailwind-border-indigo-200 hover:tailwind-bg-gray-50'
        }
        ${
          disabled
            ? 'tailwind-cursor-not-allowed tailwind-opacity-70'
            : 'tailwind-cursor-pointer'
        }
      `}
    >
      <div className="tailwind-flex tailwind-items-center tailwind-gap-3 tailwind-flex-grow">
        <div
          className={`
          tailwind-w-8
          tailwind-h-8
          tailwind-min-w-8
          tailwind-flex
          tailwind-items-center
          tailwind-justify-center
          tailwind-rounded-full
          tailwind-font-medium
          ${
            isLeft
              ? 'tailwind-bg-indigo-100 tailwind-text-indigo-800'
              : 'tailwind-bg-green-100 tailwind-text-green-800'
          }
        `}
        >
          {itemLabel}
        </div>
        <span className="tailwind-font-medium">{text}</span>

        {isMatched && matchedItemLabels && matchedItemLabels.length > 0 && (
          <Badge
            count={matchedItemLabels.length}
            className="tailwind-ml-auto tailwind-mr-3"
            style={{ backgroundColor: isLeft ? '#6366f1' : '#10b981' }}
            title={`${matchedItemLabels.length} kết nối`}
          />
        )}
      </div>

      {isMatched &&
        onRemove &&
        matchedItemLabels &&
        matchedItemLabels.length > 0 && (
          <div className="tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-ml-2">
            <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1 tailwind-items-center tailwind-max-w-[150px]">
              {matchedItemLabels.map((label, idx) => (
                <Tooltip key={idx} title={`Xóa kết nối với ${label}`}>
                  <Button
                    type="default"
                    size="small"
                    className="!tailwind-p-0 !tailwind-min-w-[28px] !tailwind-h-7 tailwind-inline-flex tailwind-items-center tailwind-justify-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemove(idx);
                    }}
                  >
                    <span className="tailwind-text-xs tailwind-mr-0">
                      {label}
                    </span>
                    <DeleteIcon className="tailwind-text-red-500 tailwind-text-xs" />
                  </Button>
                </Tooltip>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

const MatchingQuestionComponent: React.FC<MatchingQuestionProps> = ({
  question,
  onAnswer,
  disabled = false,
}) => {
  const [selections, setSelections] = useState<number[][]>([]); // Array of [leftIndex, rightOriginalIndex] pairs
  const [selectedLeftIndices, setSelectedLeftIndices] = useState<number[]>([]);
  const [selectedRightIndices, setSelectedRightIndices] = useState<number[]>(
    []
  );
  const [shuffledRightIndices, setShuffledRightIndices] = useState<number[]>(
    []
  );
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Xáo trộn mảng
  const shuffleArray = useCallback((arr: any[]) => {
    const newArr = [...arr];
    for (let i = newArr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArr[i], newArr[j]] = [newArr[j], newArr[i]];
    }
    return newArr;
  }, []);

  // Khởi tạo xáo trộn các mục bên phải
  useEffect(() => {
    const indices = Array.from(
      { length: question.rightItems.length },
      (_, i) => i
    );
    setShuffledRightIndices(shuffleArray(indices));
  }, [question.rightItems.length, shuffleArray]);

  // Hàm vẽ đường nối trên canvas
  const drawConnections = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Xóa canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Vẽ các đường nối dựa trên selections
    selections.forEach(([leftIndex, rightOriginalIndex]) => {
      // Kiểm tra tính hợp lệ của các chỉ số
      if (
        leftIndex < 0 ||
        leftIndex >= question.leftItems.length ||
        rightOriginalIndex < 0 ||
        rightOriginalIndex >= question.rightItems.length
      ) {
        console.warn(
          `Invalid indices in matching: leftIndex=${leftIndex}, rightOriginalIndex=${rightOriginalIndex}`
        );
        return;
      }

      // Tìm vị trí hiển thị của rightOriginalIndex trong shuffledRightIndices
      const rightDisplayIndex = shuffledRightIndices.findIndex(
        (idx) => idx === rightOriginalIndex
      );

      if (rightDisplayIndex === -1) {
        console.warn(
          `Could not find display index for right item ${rightOriginalIndex}`
        );
        return;
      }

      const leftElement = container.querySelector(
        `[data-side="left"][data-index="${leftIndex}"]`
      );
      const rightElement = container.querySelector(
        `[data-side="right"][data-index="${rightDisplayIndex}"]`
      );

      if (leftElement && rightElement) {
        const leftRect = leftElement.getBoundingClientRect();
        const rightRect = rightElement.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // Điểm bắt đầu ở bên phải của item bên trái
        const startX = leftRect.right - containerRect.left;
        const startY = leftRect.top + leftRect.height / 2 - containerRect.top;

        // Điểm kết thúc ở bên trái của item bên phải
        const endX = rightRect.left - containerRect.left;
        const endY = rightRect.top + rightRect.height / 2 - containerRect.top;

        // Tạo gradient cho đường nối
        const gradient = ctx.createLinearGradient(startX, startY, endX, endY);
        gradient.addColorStop(0, '#6366f1'); // Indigo ở điểm bắt đầu
        gradient.addColorStop(1, '#10b981'); // Green ở điểm kết thúc

        // Thiết lập style
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 3; // Thicker line for better visibility

        // Vẽ đường cong Bezier
        ctx.beginPath();
        ctx.moveTo(startX, startY);

        // Điểm điều khiển cho đường cong
        const controlPoint1X = startX + (endX - startX) / 3;
        const controlPoint1Y = startY;
        const controlPoint2X = endX - (endX - startX) / 3;
        const controlPoint2Y = endY;

        ctx.bezierCurveTo(
          controlPoint1X,
          controlPoint1Y,
          controlPoint2X,
          controlPoint2Y,
          endX,
          endY
        );
        ctx.stroke();

        // Vẽ điểm tròn ở hai đầu đường nối
        ctx.fillStyle = '#6366f1';
        ctx.beginPath();
        ctx.arc(startX, startY, 5, 0, Math.PI * 2); // Larger circle for better visibility
        ctx.fill();

        ctx.fillStyle = '#10b981';
        ctx.beginPath();
        ctx.arc(endX, endY, 5, 0, Math.PI * 2); // Larger circle for better visibility
        ctx.fill();
      }
    });
  }, [
    selections,
    shuffledRightIndices,
    question.leftItems.length,
    question.rightItems.length,
  ]);

  // Cập nhật kích thước canvas và vẽ lại khi cần
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const updateCanvasSize = () => {
      const rect = container.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;
      drawConnections();
    };

    // Cập nhật kích thước ban đầu
    updateCanvasSize();

    // Thêm listener cho sự kiện resize
    window.addEventListener('resize', updateCanvasSize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, [drawConnections]);

  // Vẽ lại khi selections hoặc shuffledRightIndices thay đổi
  useEffect(() => {
    drawConnections();
  }, [selections, shuffledRightIndices, drawConnections]);

  // Xử lý khi người dùng click vào một mục bên trái
  const handleLeftItemClick = (index: number) => {
    if (disabled) return;

    // Toggle selection: nếu đang chọn, bỏ chọn; nếu chưa chọn, thêm vào danh sách chọn
    setSelectedLeftIndices((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // Xử lý khi người dùng click vào một mục bên phải
  const handleRightItemClick = (originalIndex: number) => {
    if (disabled) return;

    // Nếu đã có mục bên trái được chọn, tạo kết nối mới
    if (selectedLeftIndices.length > 0) {
      const newSelections = [...selections];
      selectedLeftIndices.forEach((leftIndex) => {
        // Kiểm tra xem cặp này đã tồn tại chưa
        if (
          !newSelections.some(
            (pair) => pair[0] === leftIndex && pair[1] === originalIndex
          )
        ) {
          newSelections.push([leftIndex, originalIndex]);
        }
      });
      setSelections(newSelections);
      setSelectedLeftIndices([]); // Reset selection sau khi tạo kết nối
    }
    // Nếu không có mục bên trái nào được chọn, toggle selection bên phải
    else {
      setSelectedRightIndices((prev) => {
        if (prev.includes(originalIndex)) {
          return prev.filter((i) => i !== originalIndex);
        } else {
          return [...prev, originalIndex];
        }
      });
    }
  };

  // Xử lý xóa một kết nối
  const handleRemoveConnection = (connectionIndex: number) => {
    if (disabled) return;
    setSelections((prev) => {
      return prev.filter((_, idx) => idx !== connectionIndex);
    });
  };

  // Đặt lại toàn bộ kết nối
  const handleResetSelections = () => {
    if (disabled) return;
    setSelections([]);
    setSelectedLeftIndices([]);
    setSelectedRightIndices([]);

    // Xáo trộn lại mục bên phải
    setShuffledRightIndices(shuffleArray([...shuffledRightIndices]));
  };

  // Gửi kết quả khi người dùng nhấn nút kiểm tra
  const handleSubmit = () => {
    if (disabled) return;

    // Ensure selections are in the correct format expected by checkAnswer
    const validSelections = selections.filter(
      ([leftIndex, rightIndex]) =>
        leftIndex >= 0 &&
        leftIndex < question.leftItems.length &&
        rightIndex >= 0 &&
        rightIndex < question.rightItems.length
    );

    // Sort the selections by left index for consistent comparison
    const sortedSelections = [...validSelections].sort((a, b) => a[0] - b[0]);

    onAnswer(sortedSelections);
  };

  // Hàm lấy nhãn của các mục đã kết nối
  const getMatchedLabels = (
    index: number,
    side: 'left' | 'right'
  ): string[] => {
    if (side === 'left') {
      const matchedRightIndices = selections
        .filter((pair) => pair[0] === index)
        .map((pair) => pair[1]);
      return matchedRightIndices.map((idx) =>
        String.fromCharCode(65 + shuffledRightIndices.indexOf(idx))
      );
    } else {
      const originalIndex = shuffledRightIndices[index];
      const matchedLeftIndices = selections
        .filter((pair) => pair[1] === originalIndex)
        .map((pair) => pair[0]);
      return matchedLeftIndices.map((idx) => `${idx + 1}`);
    }
  };

  const getTotalConnections = (): number => {
    // Tính tổng số kết nối có thể tạo ra
    return Math.max(question.leftItems.length, question.rightItems.length);
  };

  const getCompletionPercentage = (): number => {
    // Phần trăm hoàn thành
    const total = getTotalConnections();
    return total > 0 ? Math.floor((selections.length / total) * 100) : 0;
  };

  return (
    <div className="tailwind-space-y-4 tailwind-mb-4">
      {/* Main content */}
      <div
        ref={containerRef}
        className="tailwind-bg-gray-50 tailwind-p-4 tailwind-rounded-lg tailwind-border tailwind-border-gray-200 tailwind-relative"
      >
        {/* Canvas for drawing lines */}
        <canvas
          ref={canvasRef}
          className="tailwind-absolute tailwind-inset-0 tailwind-pointer-events-none"
        />

        {/* Selection status and actions */}
        <div className="tailwind-flex tailwind-justify-between tailwind-mb-3 tailwind-relative tailwind-z-10 tailwind-items-center">
          <div className="tailwind-flex tailwind-items-center">
            {selectedLeftIndices.length > 0 && (
              <div className="tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-rounded-lg tailwind-py-1 tailwind-px-3 tailwind-text-sm tailwind-font-medium tailwind-mr-2">
                {selectedLeftIndices.length} mục được chọn từ cột A
              </div>
            )}
            {selectedRightIndices.length > 0 && (
              <div className="tailwind-bg-green-100 tailwind-text-green-800 tailwind-rounded-lg tailwind-py-1 tailwind-px-3 tailwind-text-sm tailwind-font-medium">
                {selectedRightIndices.length} mục được chọn từ cột B
              </div>
            )}
            {selectedLeftIndices.length === 0 &&
              selectedRightIndices.length === 0 && (
                <div className="tailwind-text-sm tailwind-text-gray-600">
                  Chọn mục từ cột A hoặc B để bắt đầu ghép cặp
                </div>
              )}
          </div>

          <div className="tailwind-flex tailwind-items-center">
            {/* Progress indicator */}
            <div className="tailwind-mr-3 tailwind-flex tailwind-items-center">
              <div className="tailwind-h-2 tailwind-w-20 tailwind-bg-gray-200 tailwind-rounded-full tailwind-overflow-hidden">
                <div
                  className="tailwind-h-full tailwind-bg-indigo-500 tailwind-rounded-full"
                  style={{ width: `${getCompletionPercentage()}%` }}
                ></div>
              </div>
              <span className="tailwind-text-xs tailwind-text-gray-600 tailwind-ml-2">
                {selections.length}/{getTotalConnections()} kết nối
              </span>
            </div>

            <Tooltip title="Đặt lại toàn bộ kết nối">
              <Button
                icon={<RedoOutlinedIcon />}
                onClick={handleResetSelections}
                disabled={disabled || selections.length === 0}
                size="small"
              >
                Đặt lại
              </Button>
            </Tooltip>
          </div>
        </div>

        {/* Matching items container with aligned rows */}
        <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-10 tailwind-relative tailwind-z-10">
          {/* Left column items */}
          <div>
            <h4 className="tailwind-font-medium tailwind-text-indigo-800 tailwind-mb-2">
              Cột A
            </h4>
            {question.leftItems.map((leftItem, leftIndex) => {
              const isLeftSelected = selectedLeftIndices.includes(leftIndex);
              const matchedPairs = selections.filter(
                (pair) => pair[0] === leftIndex
              );
              const isLeftMatched = matchedPairs.length > 0;

              return (
                <MatchingItem
                  key={leftIndex}
                  index={leftIndex}
                  originalIndex={leftIndex}
                  text={leftItem}
                  isSelected={isLeftSelected}
                  isMatched={isLeftMatched}
                  matchedItemLabels={getMatchedLabels(leftIndex, 'left')}
                  onClick={() => handleLeftItemClick(leftIndex)}
                  onRemove={(connectionIdx) =>
                    handleRemoveConnection(
                      selections.findIndex(
                        (pair) =>
                          pair[0] === leftIndex &&
                          pair[1] === matchedPairs[connectionIdx][1]
                      )
                    )
                  }
                  side="left"
                  disabled={disabled}
                />
              );
            })}
          </div>

          {/* Right column items */}
          <div>
            <h4 className="tailwind-font-medium tailwind-text-green-800 tailwind-mb-2">
              Cột B
            </h4>
            {/* Use shuffledRightIndices to display items in random order */}
            {shuffledRightIndices.map((originalIndex, displayIndex) => {
              const isRightSelected =
                selectedRightIndices.includes(originalIndex);
              const isRightMatched = selections.some(
                (pair) => pair[1] === originalIndex
              );
              const matchedPairsRight = selections.filter(
                (pair) => pair[1] === originalIndex
              );

              return (
                <MatchingItem
                  key={displayIndex}
                  index={displayIndex}
                  originalIndex={originalIndex}
                  text={question.rightItems[originalIndex]}
                  isSelected={isRightSelected}
                  isMatched={isRightMatched}
                  matchedItemLabels={getMatchedLabels(displayIndex, 'right')}
                  onClick={() => handleRightItemClick(displayIndex)}
                  onRemove={(connectionIdx) => {
                    handleRemoveConnection(
                      selections.findIndex(
                        (pair) =>
                          pair[0] === matchedPairsRight[connectionIdx][0] &&
                          pair[1] === originalIndex
                      )
                    );
                  }}
                  side="right"
                  disabled={disabled}
                />
              );
            })}
          </div>
        </div>

        {/* Tip indicator */}
        {selectedLeftIndices.length > 0 && (
          <div className="tailwind-mt-3 tailwind-flex tailwind-items-center tailwind-text-sm tailwind-text-indigo-600 tailwind-relative tailwind-z-10 tailwind-bg-indigo-50 tailwind-p-2 tailwind-rounded">
            <InfoCircleOutlinedIcon className="tailwind-mr-1" />
            <span>
              Chọn một mục từ cột B để kết nối với {selectedLeftIndices.length}{' '}
              mục đã chọn bên trái
            </span>
          </div>
        )}
        {selectedRightIndices.length > 0 && (
          <div className="tailwind-mt-3 tailwind-flex tailwind-items-center tailwind-text-sm tailwind-text-green-600 tailwind-relative tailwind-z-10 tailwind-bg-green-50 tailwind-p-2 tailwind-rounded">
            <InfoCircleOutlinedIcon className="tailwind-mr-1" />
            <span>
              Chọn một mục từ cột A để kết nối với {selectedRightIndices.length}{' '}
              mục đã chọn bên phải
            </span>
          </div>
        )}
      </div>

      {/* Connections summary - optional */}
      {selections.length > 0 && (
        <div className="tailwind-p-3 tailwind-bg-gray-50 tailwind-rounded-lg tailwind-border tailwind-border-gray-200 tailwind-mb-4">
          <p className="tailwind-mb-2 tailwind-font-medium">
            Các kết nối đã tạo:
          </p>
          <div className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-2 tailwind-gap-2">
            {selections.map((pair, index) => (
              <div
                key={index}
                className="tailwind-flex tailwind-items-center tailwind-p-2 tailwind-bg-white tailwind-rounded tailwind-border"
              >
                <div className="tailwind-flex-grow tailwind-flex tailwind-items-center">
                  <span className="tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-w-6 tailwind-h-6 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mr-1 tailwind-text-xs">
                    {pair[0] + 1}
                  </span>
                  <span className="tailwind-font-medium tailwind-mr-1">
                    {question.leftItems[pair[0]]}
                  </span>
                  <ArrowRightOutlinedIcon className="tailwind-mx-1 tailwind-text-gray-400" />
                  <span className="tailwind-bg-green-100 tailwind-text-green-800 tailwind-w-6 tailwind-h-6 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-font-medium tailwind-mx-1 tailwind-text-xs">
                    {String.fromCharCode(
                      65 + shuffledRightIndices.indexOf(pair[1])
                    )}
                  </span>
                  <span className="tailwind-font-medium">
                    {question.rightItems[pair[1]]}
                  </span>
                </div>
                <Button
                  type="text"
                  danger
                  icon={<DeleteIcon />}
                  onClick={() =>
                    setSelections((prev) => prev.filter((_, i) => i !== index))
                  }
                  size="small"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action buttons */}
      <div className="tailwind-mt-6 tailwind-flex tailwind-justify-center">
        <Button
          onClick={handleSubmit}
          disabled={disabled || selections.length === 0}
          type="primary"
          size="large"
          className={`tailwind-px-6 tailwind-py-2 tailwind-min-w-[200px]`}
          style={{
            background: selections.length === 0 ? undefined : '#4f46e5',
            borderColor: selections.length === 0 ? undefined : '#4338ca',
          }}
        >
          Kiểm tra đáp án
        </Button>
      </div>
    </div>
  );
};

export default MatchingQuestionComponent;

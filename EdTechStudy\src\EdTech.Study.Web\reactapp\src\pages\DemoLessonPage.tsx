import React, { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import '../styles/modal-fixes.css';
import { CombinedThemeProvider } from '../providers/theme';
import { Provider } from 'react-redux';
import { storeEdTech } from '../store/store';
import { EdTechProviderContainer } from '../providers/EdTechProvider';
import { ConfigManager } from '../utils/ConfigManager';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { AppConfigSlice } from '../store/slices/AppConfig/AppConfigSlice';
import { ETypeMode } from '../enums/AppEnums';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';
// Register Syncfusion license
registerLicenseSyncfusionBase();

storeEdTech.dispatch(AppConfigSlice.actions.setMode(ETypeMode.CONFIGURATION));
(window as any).initReactApp = ConfigManager.initConfigLessonPage;
export const DemoLessonPage: React.FC = () => {
  return (
    <Provider store={storeEdTech}>
      <CombinedThemeProvider>
        <Suspense fallback={<LoadingScreen />}>
          <EdTechProviderContainer />
        </Suspense>
      </CombinedThemeProvider>
    </Provider>
  );
};

// Tách hàm init ra để có thể dùng lại và test dễ hơn
const initApp = () => {
  const rootElement = document.getElementById('DemoLessonPage');

  if (rootElement) {
    const root = ReactDOM.createRoot(rootElement);
    root.render(<DemoLessonPage />);
  } else {
    console.error('Cannot find root element with id "DemoLessonPage"');
  }
};

// Khởi chạy ứng dụng
initApp();

import React, { memo, useEffect, useState, ReactNode } from 'react';
import {
  MapSimulatorConfig,
  defaultMapSimulatorConfig,
} from './MapSimulatorConfig';
import MapSimulatorComponent from './MapSimulatorComponent';
import MapSimulatorConfigModal from './MapSimulatorConfigModal';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { EngineContainer } from '../../../components/common/engines';
import { ITextProps } from '../../../components/core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { updateTitleAndSync } from '../../../utils/titleSyncUtils';
import { EdTechRootState } from '../../../store/store';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

interface MapSimulatorProps extends MapSimulatorConfig {}

export const mapSimulatorSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    mapUrl: z.string().optional(),
    markers: z
      .array(
        z.object({
          lat: z.number(),
          lng: z.number(),
          name: z.string(),
          description: z.string().optional(),
        })
      )
      .optional(),
    layers: z.array(z.string()).optional(),
  },
});

const MapSimulator: React.FC<IEdTechRenderProps<MapSimulatorProps>> = (
  props
) => {
  const { id, params, addOrUpdateParamComponent: updateParamComponent } = props;
  const [simulatorConfig, setSimulatorConfig] = useState<MapSimulatorConfig>({
    ...defaultMapSimulatorConfig,
    ...params,
  });
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);

  const handleConfigSubmit = (config: MapSimulatorConfig) => {
    updateParamComponent({
      ...defaultMapSimulatorConfig,
      ...config,
    });
    setIsConfigModalOpen(false);
  };

  useEffect(() => {
    setSimulatorConfig({
      ...defaultMapSimulatorConfig,
      ...params,
    });
  }, [params]);

  // Title props for EngineContainer
  const titleProps: ITextProps = simulatorConfig.titleProps || {
    text: 'Mô phỏng bản đồ',
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Instructions content for EngineContainer
  const instructionsContent = {
    objective:
      'Tương tác với bản đồ để khám phá các tính năng địa lý và địa điểm.',
    steps: [
      'Bật/tắt các lớp bản đồ để xem các loại địa hình và dữ liệu khác nhau',
      'Hiển thị/ẩn các điểm đánh dấu để tìm hiểu về các địa điểm quan trọng',
      'Tương tác với bản đồ bằng cách phóng to, thu nhỏ và di chuyển',
      'Tìm kiếm địa điểm bằng cách sử dụng công cụ tìm kiếm',
    ],
    notes:
      'Bạn có thể tùy chỉnh các lớp bản đồ và điểm đánh dấu trong phần cấu hình.',
  };

  // Xử lý sự kiện thay đổi dữ liệu từ MapSimulatorComponent
  const handleDataChange = (data: any) => {
    // Xử lý các loại sự kiện khác nhau
    switch (data.action) {
      case 'marker_added':
      case 'marker_edited':
      case 'marker_removed':
      case 'marker_moved':
      case 'marker_toggled':
        // Cập nhật danh sách markers trong cấu hình
        if (data.allMarkers) {
          const updatedConfig = {
            ...simulatorConfig,
            markers: data.allMarkers,
          };
          setSimulatorConfig(updatedConfig);
          updateParamComponent(updatedConfig);
        }
        break;
      case 'config_updated':
        // Đã cập nhật cấu hình từ component con
        console.log('Config updated in simulator component');
        break;
      default:
        break;
    }
  };

  // Main component
  const mainComponent: ReactNode = (
    <MapSimulatorComponent
      config={simulatorConfig}
      onDataChange={handleDataChange}
    />
  );

  // Config modal
  const configModal: ReactNode = (
    <MapSimulatorConfigModal
      isOpen={isConfigModalOpen}
      onClose={() => setIsConfigModalOpen(false)}
      onSubmit={handleConfigSubmit}
      defaultConfig={simulatorConfig}
    />
  );

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Custom title update handler that also updates local state
  const handleMapTitleUpdate = (updates: Partial<ITextProps>) => {
    // Update the title in the simulator config and local state
    const updatedConfig = updateTitleAndSync(
      id,
      updates,
      simulatorConfig,
      renderTreeData,
      dispatch,
      updateParamComponent
    );

    // Update local state
    if (updatedConfig !== simulatorConfig) {
      setSimulatorConfig(updatedConfig);
    }
  };

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      allowConfiguration={true}
      mainComponent={mainComponent}
      configModal={configModal}
      instructionsContent={instructionsContent}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      onTitleUpdate={handleMapTitleUpdate}
      id={id}
    />
  );
};

export default memo(withEdComponentParams(MapSimulator));

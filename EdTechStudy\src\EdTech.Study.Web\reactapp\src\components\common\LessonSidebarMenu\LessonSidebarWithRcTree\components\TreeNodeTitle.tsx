import React from 'react';
import { Tooltip } from 'antd';
import { DownOutlined, RightOutlined, MenuOutlined } from '@ant-design/icons';
import { TreeMenuItem } from '../types';
import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';

interface TreeNodeTitleProps {
  nodeData: TreeMenuItem;
  isEditing: boolean;
  isExpanded: boolean;
  hasChildren: boolean;
  level: number;
  handleExpandClick: (e: React.MouseEvent) => void;
}

const TreeNodeTitle: React.FC<TreeNodeTitleProps> = ({
  nodeData,
  isEditing,
  isExpanded,
  hasChildren,
  level,
  handleExpandClick,
}) => {
  const paddingStyle = {
    paddingLeft: `${level * 16}px`,
  };

  return (
    <div
      className={`rc-tree-node-content ${
        nodeData.type === ETypeEdTechComponent.LAYOUT
          ? `tailwind-font-roboto tailwind-font-semibold tailwind-text-[#908588]`
          : ''
      }`}
      style={paddingStyle}
    >
      {isEditing && (
        <div className="content-drag-drop tailwind-pr-2">
          <MenuOutlined />
        </div>
      )}
      <Tooltip title={nodeData.title}>
        <span className="node-title">
          {hasChildren && (
            <span className="expand-icon" onClick={handleExpandClick}>
              {isExpanded ? <DownOutlined /> : <RightOutlined />}
            </span>
          )}
          {nodeData.key.includes('objective') && (
            <span className="objective-marker">★</span>
          )}
          {nodeData.title}
        </span>
      </Tooltip>
    </div>
  );
};

export default TreeNodeTitle;

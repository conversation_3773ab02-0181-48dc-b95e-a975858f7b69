.practice-engines-container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.practice-content {
  flex: 1;
  overflow-y: auto;
  /* padding: 1rem; */
  margin-bottom: 1rem;
}

.practice-navigation {
  padding: 1rem;
  background-color: #fff;
}

.practice-progress {
  margin-bottom: 1rem;
}

.practice-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.practice-indicator {
  margin: 0 1rem;
  color: #666;
}

.practice-completion-card {
  max-width: 600px;
  margin: 2rem auto;
  text-align: center;
}

.practice-completion-emoji {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.practice-score-container {
  margin: 2rem 0;
}

.practice-score-details {
  margin-top: 1rem;
}

/* Fullscreen styles */
.fullscreen-practice {
  background-color: #fff;
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  padding: 1rem !important;
}

.fullscreen-practice .practice-content {
  max-height: calc(100vh - 180px);
}

.fullscreen-practice .practice-navigation {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* Animation for fullscreen transition */
.fullscreen-practice {
  transition: all 0.3s ease-in-out;
}

/* Responsive adjustments for fullscreen */
@media (max-width: 768px) {
  .fullscreen-practice .practice-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .fullscreen-practice .practice-navigation {
    padding: 0.5rem;
  }

  .fullscreen-practice .practice-content {
    max-height: calc(100vh - 220px);
  }
}

.practice-config-controls {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.form-config>.ant-form-item {
  margin-bottom: 1rem;
}

#practice-engine-wrapper {
  background-color: #fff;
}
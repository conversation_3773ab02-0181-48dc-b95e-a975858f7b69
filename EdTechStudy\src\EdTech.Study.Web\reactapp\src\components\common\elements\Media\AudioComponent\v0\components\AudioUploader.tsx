import React, { useRef } from 'react';
import { MediaFileUploaderContainer } from '../../../shared';
import { useAudio } from '../context/AudioContext';

interface AudioUploaderProps {
  fullScreen?: boolean;
}

export const AudioUploader: React.FC<AudioUploaderProps> = ({
  fullScreen = false,
}) => {
  const { isEditing, handleUploadSuccess } = useAudio();

  // Container ref for drag and drop
  const containerRef = useRef<HTMLDivElement>(null);

  // For full screen view (when no audio is present)
  if (fullScreen) {
    return (
      <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-w-full tailwind-h-full">
        <MediaFileUploaderContainer
          mediaType="audio"
          onUploadSuccess={handleUploadSuccess}
          allowDrop={true}
          dropAreaHeight="100%"
          dropAreaWidth="100%"
          className={isEditing ? 'editing' : ''}
          isEditing={isEditing}
          containerRef={containerRef}
          style={{
            backgroundColor: 'var(--edtt-color-primary-100)',
          }}
        />
        <p className="tailwind-text-xs tailwind-text-gray-500 tailwind-mt-2">
          Hoặc kéo & thả để thêm âm thanh
        </p>
      </div>
    );
  }

  // For the upload button at the bottom of the player
  return (
    <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center">
      <MediaFileUploaderContainer
        mediaType="audio"
        onUploadSuccess={handleUploadSuccess}
        allowDrop={false}
      />
      <p className="tailwind-text-xs tailwind-text-gray-500">
        Hoặc kéo & thả để thêm âm thanh
      </p>
    </div>
  );
};

import { useRef, useState, useEffect, RefObject } from 'react';
import {
  isFullscreenSupported,
  getFullscreenElement,
  requestFullscreen,
  exitFullscreen,
  addFullscreenChangeListener,
  removeFullscreenChangeListener,
  getFullscreenClasses
} from './fullscreenUtils';

/**
 * Options for configuring fullscreen behavior
 */
export interface FullscreenOptions {
  /** Whether to automatically fall back to CSS approach if browser doesn't support Fullscreen API */
  useCssFallback?: boolean;
  /** Initial fullscreen state */
  initialState?: boolean;
  /** Z-index value when in CSS fallback mode */
  zIndex?: number;
  /** Background color in CSS fallback mode */
  backgroundColor?: string;
  /** Callback function called when entering fullscreen */
  onEnterFullscreen?: () => void;
  /** Callback function called when exiting fullscreen */
  onExitFullscreen?: () => void;
  /** Whether to handle F11 key */
  interceptF11?: boolean;
}

/**
 * Return type for the useFullscreen hook
 */
export interface FullscreenResult {
  /** Whether currently in fullscreen mode */
  isFullscreen: boolean;
  /** Function to toggle fullscreen state */
  toggleFullscreen: () => void;
  /** Function to enter fullscreen mode */
  enterFullscreen: () => void;
  /** Function to exit fullscreen mode */
  exitFullscreen: () => void;
  /** Reference to attach to the container element */
  containerRef: RefObject<HTMLDivElement>;
  /** CSS classes to apply to the container (used in CSS fallback mode) */
  containerClasses: string;
  /** Whether the browser supports Fullscreen API */
  isSupported: boolean;
}

/**
 * Custom hook for managing fullscreen functionality
 * Uses Fullscreen API with vendor prefix support and optional CSS fallback
 */
const useFullscreen = (options: FullscreenOptions = {}): FullscreenResult => {
  const {
    useCssFallback = true,
    initialState = false,
    zIndex = 9999,
    backgroundColor = 'white',
    onEnterFullscreen,
    onExitFullscreen,
    interceptF11 = false
  } = options;

  // Reference to the container that will be toggled to fullscreen
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Check if browser supports Fullscreen API
  const isSupported = isFullscreenSupported();
  
  // State to track fullscreen status
  const [isFullscreen, setIsFullscreen] = useState<boolean>(initialState);
  
  // State to track whether we're using CSS fallback
  const [usingCssFallback, setUsingCssFallback] = useState<boolean>(
    !isSupported && useCssFallback
  );

  // Handle entering fullscreen
  const enterFullscreen = () => {
    if (isSupported && containerRef.current) {
      requestFullscreen(containerRef.current).catch((err: any) => {
        console.error(`Error entering fullscreen:`, err);
        
        // Fallback to CSS approach if enabled
        if (useCssFallback) {
          setUsingCssFallback(true);
          setIsFullscreen(true);
        }
      });
    } else if (useCssFallback) {
      setUsingCssFallback(true);
      setIsFullscreen(true);
    }
    
    if (onEnterFullscreen) {
      onEnterFullscreen();
    }
  };

  // Handle exiting fullscreen
  const handleExitFullscreen = () => {
    if (isSupported && getFullscreenElement()) {
      exitFullscreen().catch((err: any) => {
        console.error(`Error exiting fullscreen:`, err);
        
        // Fallback to CSS approach if enabled
        if (useCssFallback) {
          setUsingCssFallback(true);
          setIsFullscreen(false);
        }
      });
    } else if (usingCssFallback) {
      setIsFullscreen(false);
    }
    
    if (onExitFullscreen) {
      onExitFullscreen();
    }
  };

  // Toggle fullscreen state
  const toggleFullscreen = () => {
    if (isFullscreen) {
      handleExitFullscreen();
    } else {
      enterFullscreen();
    }
  };

  // Monitor fullscreen changes when using the Fullscreen API
  useEffect(() => {
    if (isSupported) {
      const handleFullscreenChange = () => {
        const isInFullscreen = !!getFullscreenElement();
        setIsFullscreen(isInFullscreen);
        
        if (isInFullscreen && onEnterFullscreen) {
          onEnterFullscreen();
        } else if (!isInFullscreen && onExitFullscreen) {
          onExitFullscreen();
        }
      };

      // Add event listeners for all vendor prefixed events
      addFullscreenChangeListener(handleFullscreenChange);
      
      return () => {
        removeFullscreenChangeListener(handleFullscreenChange);
      };
    }
  }, [isSupported, onEnterFullscreen, onExitFullscreen]);

  // Handle F11 key if interceptF11 is true
  useEffect(() => {
    if (interceptF11) {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'F11') {
          e.preventDefault();
          toggleFullscreen();
        }
      };
      
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [interceptF11, toggleFullscreen]);

  // Handle body overflow style when using CSS fallback
  useEffect(() => {
    if (usingCssFallback && isFullscreen) {
      document.body.style.overflow = 'hidden';
    } else if (usingCssFallback) {
      document.body.style.overflow = '';
    }

    return () => {
      if (usingCssFallback) {
        document.body.style.overflow = '';
      }
    };
  }, [usingCssFallback, isFullscreen]);

  // Generate container classes for CSS-based fullscreen
  const containerClasses = usingCssFallback
    ? getFullscreenClasses(isFullscreen, zIndex, backgroundColor)
    : '';

  return {
    isFullscreen,
    toggleFullscreen,
    enterFullscreen,
    exitFullscreen: handleExitFullscreen,
    containerRef,
    containerClasses,
    isSupported
  };
};

export default useFullscreen;
import React from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

interface CarouselNavButtonProps {
  type: 'prev' | 'next';
  onClick: () => void;
  className?: string;
}

const CarouselNavButton: React.FC<CarouselNavButtonProps> = ({
  type,
  onClick,
  className = '',
}) => {
  const isNext = type === 'next';
  const Icon = isNext ? RightOutlined : LeftOutlined;
  const ariaLabel = isNext ? 'Next slide' : 'Previous slide';
  const positionClass = isNext ? 'tailwind-ml-auto' : 'tailwind-mr-auto';

  return (
    <button
      className={`tailwind-w-10 tailwind-h-10 tailwind-rounded-full tailwind-bg-white tailwind-bg-opacity-90 tailwind-border-0 tailwind-shadow-md 
                 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-cursor-pointer tailwind-transition-all tailwind-duration-300 
                 tailwind-text-gray-600 tailwind-text-lg tailwind-relative tailwind-z-10 tailwind-pointer-events-auto
                 hover:tailwind-bg-white hover:tailwind-scale-110 hover:tailwind-text-blue-500 hover:tailwind-shadow-lg
                 focus:tailwind-outline-none focus:tailwind-shadow-outline-blue
                 active:tailwind-scale-95 ${positionClass} ${className}`}
      onClick={onClick}
      aria-label={ariaLabel}
      type="button"
    >
      <Icon className="tailwind-text-xl tailwind-font-bold" />
    </button>
  );
};

export default CarouselNavButton;

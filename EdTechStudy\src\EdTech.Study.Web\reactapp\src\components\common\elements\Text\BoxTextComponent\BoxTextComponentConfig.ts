import { z } from 'zod';
import { createComponentSchema } from '../../../../../utils/schema/createComponentSchema';

export interface BoxTextComponentProps {
  // Box styling
  height?: number | string;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  boxShadow?: boolean;
  boxAlign?: 'left' | 'center' | 'right'; // Căn lề hộp văn bản so với engine

  // Scale factor for normal mode - determines box width relative to engine width
  scaleFactor?: number;

  // Text styling
  text?: string;
  textColor?: string;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | number;
  fontStyle?: 'normal' | 'italic';
  lineHeight?: number;
  textAlign?: 'left' | 'center' | 'right';
  padding?: number;
}

// Zod schema for component validation
export const boxTextSchema = createComponentSchema({
  paramsSchema: {
    // Box styling
    height: z.union([z.number(), z.string()]).optional(),
    backgroundColor: z.string().optional(),
    borderColor: z.string().optional(),
    borderWidth: z.number().optional(),
    borderRadius: z.number().optional(),
    boxShadow: z.boolean().optional(),
    boxAlign: z.enum(['left', 'center', 'right']).optional(),

    // Scale factor
    scaleFactor: z.number().optional(),

    // Text styling
    text: z.string().optional(),
    textColor: z.string().optional(),
    fontSize: z.number().optional(),
    fontWeight: z.union([z.enum(['normal', 'bold']), z.number()]).optional(),
    fontStyle: z.enum(['normal', 'italic']).optional(),
    lineHeight: z.number().optional(),
    textAlign: z.enum(['left', 'center', 'right']).optional(),
    padding: z.number().optional(),
  },
});

export const defaultProps: BoxTextComponentProps = {
  // Box defaults
  height: 150,
  backgroundColor: '#ffffff',
  borderColor: 'var(--edtt-color-primary)',
  borderWidth: 1,
  borderRadius: 8,
  boxShadow: true,
  boxAlign: 'left',
  scaleFactor: 0.3,

  // Text defaults
  text: '',
  textColor: '#333333',
  fontSize: 16,
  fontWeight: 'normal',
  fontStyle: 'normal',
  lineHeight: 1.5,
  textAlign: 'left',
  padding: 16,
};

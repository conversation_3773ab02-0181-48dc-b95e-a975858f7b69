import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import {
  MatchingItemType,
  MatchingQuestion,
  MatchingQuestionAnswer,
} from '../../../../interfaces/quizs/questionBase';
import { MatchingLogicResult } from '../../../../interfaces/quizs/mapping.interface';
import { Guid } from 'guid-typescript';

interface UseMatchingLogicProps {
  id: string;
  question: MatchingQuestion;
  resetExternalTrigger?: boolean;
  configMode?: boolean;
  handleChangeAnswer: (answer: MatchingQuestionAnswer) => any;
}

// Function to generate visually distinct colors for pairs
const generateDistinctColor = (index: number): string => {
  // Use a color palette with distinct, visually pleasing colors
  const colorPalette = [
    '#FF5252', // Red
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#FFC107', // Amber
    '#9C27B0', // Purple
    '#00BCD4', // Cyan
    '#FF9800', // Orange
    '#3F51B5', // Indigo
    '#8BC34A', // Light Green
    '#E91E63', // Pink
    '#607D8B', // Blue Grey
    '#795548', // Brown
  ];

  // Get color from palette or generate one if we run out
  if (index < colorPalette.length) {
    return colorPalette[index];
  } else {
    // Generate random color for additional pairs
    const hue = (index * 137) % 360; // Golden angle approximation for even distribution
    return `hsl(${hue}, 70%, 65%)`;
  }
};

export const useMatchingLogic = ({
  id,
  question,
  resetExternalTrigger,
  configMode = false,
  handleChangeAnswer,
}: UseMatchingLogicProps): MatchingLogicResult => {
  // Core item states
  const [leftItems, setLeftItems] = useState<MatchingItemType[]>([]);
  const [rightItems, setRightItems] = useState<MatchingItemType[]>([]);
  const shufflePositions = useRef<
    {
      id: string;
      left: MatchingItemType[];
      right: MatchingItemType[];
    }[]
  >([]);

  // States for click-to-pair functionality
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [pairedItems, setPairedItems] = useState<{
    [key: string]: { partnerId: string; color: string };
  }>({});
  // const [pairColors, setPairColors] = useState<{ [key: string]: string }>({});
  const [pairCount, setPairCount] = useState<number>(0);

  // Memoize item ids
  const leftIds = useMemo(() => leftItems.map((item) => item.id), [leftItems]);
  const rightIds = useMemo(
    () => rightItems.map((item) => item.id),
    [rightItems]
  );

  // Utility function to shuffle array
  const shuffleArray = useCallback(<T>(array: T[]): T[] => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);

  // Initialize data
  const handleRefresh = useCallback(() => {
    let left = [...question.leftItems];
    let right = [...question.rightItems];

    // In exercise mode, shuffle the items if shuffleItems is true
    let exist = shufflePositions.current.some((p) => p.id === id);

    if (
      !exist &&
      !configMode &&
      !resetExternalTrigger &&
      question.shuffleItems
    ) {
      left = shuffleArray(left);
      right = shuffleArray(right);
      shufflePositions.current.push({ id: id, left: left, right: right });
    } else if (exist) {
      const position = shufflePositions.current.find((p) => p.id === id);
      left = position?.left || [];
      right = position?.right || [];
    }

    setLeftItems(left);
    setRightItems(right);

    // Reset pairing states
    setSelectedItemId(null);
  }, [
    question.id,
    configMode,
    resetExternalTrigger,
    shuffleArray,
    shufflePositions.current,
  ]);

  const pairedItemsRef = useRef<{
    [key: string]: { partnerId: string; color: string };
  }>({});

  useEffect(() => {
    if (question.userSelect && question.userSelect.length > 0) {
      const newPairedItems: {
        [key: string]: { partnerId: string; color: string };
      } = {};

      question.userSelect.forEach((answer, index) => {
        newPairedItems[answer.left] = {
          partnerId: answer.right,
          color: generateDistinctColor(index),
        };
        newPairedItems[answer.right] = {
          partnerId: answer.left,
          color: generateDistinctColor(index),
        };
      });

      pairedItemsRef.current = newPairedItems;
      setPairedItems(newPairedItems);
      setPairCount(question.userSelect.length);
    } else {
      pairedItemsRef.current = {};
      setPairedItems({});
      setPairCount(0);
      // setPairColors({});
    }
  }, [question.userSelect]);

  // Add another effect to make sure it persists
  useEffect(() => {
    if (
      Object.keys(pairedItemsRef.current).length > 0 &&
      Object.keys(pairedItems).length === 0
    ) {
      setPairedItems(pairedItemsRef.current);
    }
  }, [pairedItems]);

  // Handler for item clicks
  const handleItemClick = useCallback(
    (itemId: string) => {
      // If in config mode, don't handle clicks
      if (configMode) return;

      // If item is already paired, do nothing
      if (Object.keys(pairedItems).includes(itemId)) return;

      // Check if item is in left or right column
      const isLeftItem = leftItems.some((item) => item.id === itemId);
      const isRightItem = rightItems.some((item) => item.id === itemId);

      // If no item is currently selected, select this one
      if (!selectedItemId) {
        setSelectedItemId(itemId);
        return;
      }

      // If the same item is clicked again, deselect it
      if (selectedItemId === itemId) {
        setSelectedItemId(null);
        return;
      }

      // If previously selected item is from the same column, just switch selection
      const selectedIsLeft = leftItems.some(
        (item) => item.id === selectedItemId
      );
      if ((isLeftItem && selectedIsLeft) || (isRightItem && !selectedIsLeft)) {
        setSelectedItemId(itemId);
        return;
      }

      // Create a pair with a distinct color
      const color = generateDistinctColor(pairCount);
      const newPairedItems = { ...pairedItems };

      newPairedItems[selectedItemId] = { partnerId: itemId, color };
      newPairedItems[itemId] = { partnerId: selectedItemId, color };

      setPairedItems(newPairedItems);
      setPairCount((prevCount) => prevCount + 1);
      handleChangeAnswer({
        id: Guid.create().toString(),
        left: selectedItemId,
        right: itemId,
      } as MatchingQuestionAnswer);
      setSelectedItemId(null);
    },
    [selectedItemId, pairedItems, leftItems, rightItems, configMode, pairCount]
  );

  // Reset exercise function
  const resetExercise = useCallback(() => {
    if (!question) return;

    let left = [...question.leftItems];
    let right = [...question.rightItems];

    // In exercise mode, shuffle the items if shuffleItems is true
    let exist = shufflePositions.current.some((p) => p.id === id);

    if (!exist && question.shuffleItems && !configMode) {
      left = shuffleArray(left);
      right = shuffleArray(right);
      shufflePositions.current.push({ id: id, left: left, right: right });
    } else if (exist) {
      const position = shufflePositions.current.find((p) => p.id === id);
      left = position?.left || [];
      right = position?.right || [];
    }

    setLeftItems(left);
    setRightItems(right);

    // Reset pairing states
    setSelectedItemId(null);
    setPairedItems({});
    // setPairColors({});
    setPairCount(0);
  }, [
    question.leftItems,
    question.rightItems,
    configMode,
    shuffleArray,
    shufflePositions.current,
  ]);

  // Get results for matching
  const getPairs = useCallback(() => {
    const result: { left: string; right: string }[] = [];

    // Process only left items to avoid duplicates
    leftItems.forEach((leftItem) => {
      const pair = pairedItems[leftItem.id];
      if (pair) {
        // Find if the paired item is in right column
        const rightIndex = rightItems.findIndex(
          (item) => item.id === pair.partnerId
        );
        if (rightIndex !== -1) {
          result.push({
            left: leftItem.id,
            right: pair.partnerId,
          });
        }
      }
    });

    return result;
  }, [leftItems, rightItems, pairedItems]);

  // Initialize when question changes
  useEffect(() => {
    if (!question) return;
    handleRefresh();
  }, [resetExternalTrigger, handleRefresh, question.id]);

  // Check if an item is paired
  const isItemPaired = useCallback(
    (itemId: string) => {
      return Object.keys(pairedItems).includes(itemId);
    },
    [pairedItems]
  );

  // Get the pair color for an item
  const getPairColor = useCallback(
    (itemId: string) => {
      return pairedItems[itemId]?.color || '';
    },
    [pairedItems]
  );

  return {
    leftItems,
    rightItems,
    leftIds,
    rightIds,
    connections: {}, // Keep for backward compatibility
    activeId: selectedItemId, // Reuse for selected item
    activeItem: selectedItemId
      ? [...leftItems, ...rightItems].find(
          (item) => item.id === selectedItemId
        ) || null
      : null,
    handleDragStart: () => {}, // Kept for compatibility
    handleDragEnd: () => {}, // Kept for compatibility
    resetExercise,
    // New properties for click-to-pair
    selectedItemId,
    pairedItems,
    isItemPaired,
    getPairColor,
    handleItemClick,
    getPairs,
  };
};

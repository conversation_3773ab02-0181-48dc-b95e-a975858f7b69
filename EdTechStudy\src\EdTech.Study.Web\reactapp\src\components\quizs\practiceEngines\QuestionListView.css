.question-list-view {
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  overflow-y: auto;
}

.question-list-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #d9d9d9;
}

.question-list-item .item-config {
  position: relative;
  padding: 5px;
  border-radius: 5px;
}

.question-list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.question-list-item:not(.active):hover .question-list-content.config-mode,
.question-list-item:not(.active):hover
  .question-list-content.config-mode
  > .ant-card {
  background-color: transparent;
}

.question-list-item:hover .item-config {
  background-color: #fff !important;
}

.question-list-item .item-config:hover {
  cursor: text;
}

.question-list-item.active {
  border-color: #1890ff;
  background-color: rgb(245, 245, 245);
}

.question-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.question-list-content.config-mode {
  background-color: #fff;
}

.question-list-content .ant-card {
  box-shadow: none;
  margin: 0;
  border: none;
}

.question-list-content .ant-card-body {
  padding: 8px;
}

.add-question-button-container {
  padding: 8px 0;
}

.add-question-button-container .ant-btn {
  height: 40px;
  border-style: dashed;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.add-question-button-container .ant-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.empty-question-list {
  text-align: center;
  padding: 32px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

# Slice Utilities

Th<PERSON> mục này chứa các utilities để tái sử dụng mã nguồn cho các Redux slices tương tự nhau.

## createParamsSlice

File `createParamsSlice.ts` chứa các factory functions và reducers dùng chung để tạo ra các Redux slices làm việc với parameters.

### Cách sử dụng:

1. Import các utilities cần thiết:
```typescript
import {
  createParamsSlice,
  IEdComponentParamsState,
  createSelectAllParamsSelector,
  createSelectParamByIdSelector
} from '../utils/createParamsSlice';
```

2. Định nghĩa tên slice:
```typescript
const MY_PARAMS_SLICE = 'MY_PARAMS_SLICE';
```

3. Tạo slice sử dụng factory function:
```typescript
export const MyParamsSlice = createParamsSlice(MY_PARAMS_SLICE);
```

4. Export các actions từ slice:
```typescript
export const {
  setParams,
  addParam,
  updateParam,
  addOrUpdateParam,
  removeParam,
  clearParams,
  setLoading,
  setError,
} = MyParamsSlice.actions;
```

5. Tạo và export các selectors:
```typescript
// Thay 'myParams' bằng key trong Redux state của bạn
export const selectAllMyParams = createSelectAllParamsSelector('myParams');
export const selectMyParamById = createSelectParamByIdSelector('myParams');
```

6. Export reducer:
```typescript
export default MyParamsSlice.reducer;
```

### Lợi ích:

1. **Tính nhất quán**: Đảm bảo các slices tương tự nhau có cùng một cách thức hoạt động.
2. **Dễ bảo trì**: Khi cần thay đổi logic, chỉ cần sửa một chỗ duy nhất.
3. **Giảm lặp code**: Tránh copy-paste code giữa các slice khác nhau.
4. **Dễ mở rộng**: Khi cần thêm tính năng mới, chỉ cần thêm vào file utility.

### Cấu trúc:

- `paramReducers`: Các reducers dùng chung cho các slice liên quan đến parameters.
- `createParamsSlice()`: Factory function để tạo slice với các reducers dùng chung.
- `createSelectAllParamsSelector()`: Factory function để tạo selector lấy tất cả parameters.
- `createSelectParamByIdSelector()`: Factory function để tạo selector lấy parameter theo ID.

## createTreeDataSlice

File `createTreeDataSlice.ts` chứa các factory functions và reducers dùng chung để tạo ra các Redux slices làm việc với dữ liệu dạng cây (tree).

### Cách sử dụng:

1. Import các utilities cần thiết:
```typescript
import {
  createTreeDataSlice,
  ITreeDataState,
  createSelectAllTreeDataSelector,
  createSelectNodeByPathSelector,
  createSelectLoadingSelector,
  createSelectErrorSelector,
  TreeUtils
} from '../utils/createTreeDataSlice';
```

2. Định nghĩa tên slice:
```typescript
const MY_TREE_DATA_SLICE = 'MY_TREE_DATA_SLICE';
```

3. Tạo slice sử dụng factory function:
```typescript
export const MyTreeDataSlice = createTreeDataSlice(MY_TREE_DATA_SLICE);
```

4. Re-export interface và types cần thiết:
```typescript
export type MyTreeDataState = ITreeDataState;
export { TreeUtils };
```

5. Export các actions từ slice:
```typescript
export const {
  initData,
  addItems,
  updateItems,
  addOrUpdateItems,
  removeItem,
  clearData,
  setLoading,
  setError,
} = MyTreeDataSlice.actions;
```

6. Tạo và export các selectors:
```typescript
// Thay 'myTreeData' bằng key trong Redux state của bạn
export const selectAllMyTreeData = createSelectAllTreeDataSelector('myTreeData');
export const selectMyNodeByPath = createSelectNodeByPathSelector('myTreeData');
export const selectMyTreeLoading = createSelectLoadingSelector('myTreeData');
export const selectMyTreeError = createSelectErrorSelector('myTreeData');
```

7. Export reducer:
```typescript
export default MyTreeDataSlice.reducer;
```

### TreeUtils

TreeUtils là một object chứa các hàm tiện ích để làm việc với cấu trúc dữ liệu dạng cây:

- `findNodeByPath()`: Tìm kiếm nút trong cây theo đường dẫn (path).
- `getTargetArray()`: Lấy mảng mục tiêu dựa trên nút cha.
- `sortByOrder()`: Sắp xếp các mục theo thuộc tính order.

Bạn có thể sử dụng các hàm này trực tiếp trong các component hoặc trong các custom actions khi cần thao tác với dữ liệu cây.

### Lợi ích

1. **Quản lý dữ liệu cây phức tạp**: Giảm thiểu code trùng lặp để làm việc với cấu trúc dữ liệu dạng cây.
2. **Tạo nhanh các slice mới**: Dễ dàng tạo ra các slice mới quản lý dữ liệu cây với chỉ vài dòng code.
3. **Tương thích cao**: Vẫn hỗ trợ tất cả các tính năng của Redux Toolkit.
4. **Dễ mở rộng**: Có thể dễ dàng thêm các tính năng mới hoặc tùy chỉnh các logic hiện có.

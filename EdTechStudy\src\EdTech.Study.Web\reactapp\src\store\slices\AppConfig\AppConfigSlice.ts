import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  ETypeMode,
  EThemeType,
  EEngineInteractionMode,
} from '../../../enums/AppEnums';
import { applyTempToData } from '../utils/createTreeDataSlice';
/**
 * Interface định nghĩa trạng thái tương tác với engine
 */
export interface IEngineInteractionState {
  pathEngineActive?: string;
  pathEngineHover?: string;
  interactionMode?: EEngineInteractionMode;
}

// Định nghĩa interface cho state
export interface IAppConfigState {
  mode: ETypeMode;
  lastUpdated: string | undefined;
  theme: EThemeType;
  themeCustomizations: Record<EThemeType, Record<string, string>>;
  isDefaultData: boolean;
  language: string;
  engineState: IEngineInteractionState;
}

// Initial state
const initialState: IAppConfigState = {
  mode: ETypeMode.DEFAULT,
  lastUpdated: undefined,
  theme: EThemeType.DEFAULT,
  themeCustomizations: {
    [EThemeType.DEFAULT]: {},
    [EThemeType.DARK]: {},
    [EThemeType.LEARNING]: {},
  },
  isDefaultData: false,
  language: 'vi',
  engineState: {
    pathEngineActive: undefined,
    pathEngineHover: undefined,
    interactionMode: EEngineInteractionMode.CONFIGURATION,
  },
};

// Tạo slice
export const AppConfigSlice = createSlice({
  name: 'appConfig',
  initialState,
  reducers: {
    /**
     * Cập nhật trạng thái chỉnh sửa
     */
    setMode: (state, action: PayloadAction<ETypeMode>) => {
      state.mode = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    /**
     * Thiết lập theme
     */
    setTheme: (state, action: PayloadAction<EThemeType>) => {
      state.theme = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    /**
     * Cập nhật tùy chỉnh màu sắc cho theme
     */
    updateThemeCustomization: (
      state,
      action: PayloadAction<{
        theme: EThemeType;
        customizations: Record<string, string>;
      }>
    ) => {
      const { theme, customizations } = action.payload;
      state.themeCustomizations[theme] = {
        ...state.themeCustomizations[theme],
        ...customizations,
      };
      state.lastUpdated = new Date().toISOString();
    },

    /**
     * Đặt lại tùy chỉnh màu sắc cho theme về mặc định
     */
    resetThemeCustomization: (state, action: PayloadAction<EThemeType>) => {
      state.themeCustomizations[action.payload] = {};
      state.lastUpdated = new Date().toISOString();
    },

    /**
     * Thiết lập ngôn ngữ
     */
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    /**
     * Reset cấu hình về mặc định
     */
    resetConfig: () => {
      return {
        ...initialState,
        lastUpdated: new Date().toISOString(),
      };
    },

    /**
     * Cập nhật toàn bộ cấu hình
     */
    updateConfig: (state, action: PayloadAction<Partial<IAppConfigState>>) => {
      return {
        ...state,
        ...action.payload,
        lastUpdated: new Date().toISOString(),
      };
    },

    /**
     * Cập nhật trạng thái dữ liệu mặc định
     */
    setIsDefaultData: (state, action: PayloadAction<boolean>) => {
      state.isDefaultData = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    /**
     * Cập nhật trạng thái engine
     */
    updateEngineState: (
      state,
      action: PayloadAction<Partial<IEngineInteractionState>>
    ) => {
      state.engineState = {
        ...state.engineState,
        ...action.payload,
      };
      state.lastUpdated = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    builder.addCase(applyTempToData.fulfilled, (state) => {
      state.isDefaultData = false;
      state.lastUpdated = new Date().toISOString();
    });
  },
});

// Export actions
export const {
  setMode,
  setTheme,
  updateThemeCustomization,
  resetThemeCustomization,
  setLanguage,
  resetConfig,
  updateConfig,
  setIsDefaultData,
  updateEngineState,
} = AppConfigSlice.actions;

// Export selectors
export const selectMode = (state: { appConfig: IAppConfigState }) =>
  state.appConfig.mode;

export const selectTheme = (state: { appConfig: IAppConfigState }) =>
  state.appConfig.theme;

export const selectThemeCustomizations = (state: {
  appConfig: IAppConfigState;
}) => state.appConfig.themeCustomizations;

export const selectCurrentThemeCustomizations = (state: {
  appConfig: IAppConfigState;
}) => state.appConfig.themeCustomizations[state.appConfig.theme];

export const selectLanguage = (state: { appConfig: IAppConfigState }) =>
  state.appConfig.language;

export const selectIsDarkMode = (state: { appConfig: IAppConfigState }) =>
  state.appConfig.theme === EThemeType.DARK;

export const selectLastUpdated = (state: { appConfig: IAppConfigState }) =>
  state.appConfig.lastUpdated;

export const selectEngineInteractionMode = (state: {
  appConfig: IAppConfigState;
}) => state.appConfig.engineState.interactionMode;

// Export reducer
export default AppConfigSlice.reducer;

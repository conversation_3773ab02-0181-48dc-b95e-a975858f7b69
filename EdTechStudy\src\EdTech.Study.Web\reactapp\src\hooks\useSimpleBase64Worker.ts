// hooks/useSimpleBase64Worker.ts - ✅ Renamed to match import
import { useRef, useState, useEffect, useCallback } from 'react';

interface WorkerTask {
  id: string;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  onProgress?: (
    progress: number,
    stage?: string,
    extra?: Record<string, any>
  ) => void;
}

interface Base64WorkerOptions {
  chunkSize?: number;
  maxFileSize?: number;
}

interface UseBase64WorkerReturn {
  base64ToBlobUrl: (
    base64Data: string,
    mimeType: string,
    options?: Base64WorkerOptions,
    onProgress?: (
      progress: number,
      stage?: string,
      extra?: Record<string, any>
    ) => void
  ) => Promise<string>;
  validateBase64: (
    base64Data: string
  ) => Promise<{ isValid: boolean; cleanedLength: number }>;
  isWorkerReady: boolean;
  cleanup: () => void;
}

export const useSimpleBase64Worker = (): UseBase64WorkerReturn => {
  const workerRef = useRef<Worker | null>(null);
  const tasksRef = useRef<Map<string, WorkerTask>>(new Map());
  const [isWorkerReady, setIsWorkerReady] = useState(false);
  const isMountedRef = useRef(true);

  // Initialize worker
  useEffect(() => {
    isMountedRef.current = true;

    const initializeWorker = async () => {
      try {
        console.log('🚀 Initializing Base64 Worker...');
        console.log('📍 Protocol:', window.location.protocol);
        console.log('🌐 Location:', window.location.href);

        // ✅ Simplified worker creation logic
        if (await tryCreateStandardWorker()) {
          console.log('✅ Standard worker created successfully');
        } else {
          console.log('📁 Falling back to inline worker');
          createInlineWorker();
        }

        setupWorkerHandlers();

        if (isMountedRef.current) {
          setIsWorkerReady(true);
          console.log('✅ Base64 Worker initialized successfully');
        }
      } catch (error) {
        console.warn(
          '⚠️ Base64 Worker creation failed, using main thread fallback:',
          error
        );
        if (isMountedRef.current) {
          setIsWorkerReady(false);
          console.log('🔄 Fallback to main thread processing enabled');
        }
      }
    };

    initializeWorker();

    return () => {
      isMountedRef.current = false;
      cleanup();
    };
  }, []);

  // ✅ Simplified worker creation with better error handling
  const tryCreateStandardWorker = async (): Promise<boolean> => {
    const workerPaths = [
      './EdTech/reactapp/fileProcessor.worker.js',
      '/EdTech/reactapp/fileProcessor.worker.js',
      `${window.location.origin}/fileProcessor.worker.js`,
      // ✅ Also try without checking if file exists (faster)
      './workers/fileProcessor.worker.js',
      '/workers/fileProcessor.worker.js',
    ];

    for (const path of workerPaths) {
      try {
        console.log(`🔧 Trying worker path: ${path}`);

        // ✅ Try to create worker directly instead of checking with HEAD request
        workerRef.current = new Worker(path);
        console.log(`✅ Worker created successfully with path: ${path}`);
        return true;
      } catch (error) {
        console.warn(`❌ Failed to create worker with path ${path}:`, error);
      }
    }

    return false;
  };

  const createInlineWorker = () => {
    // ✅ Fixed inline worker code with proper escape sequences
    const workerCode = `
      // Inline Base64 Processing Worker
      self.addEventListener('message', async (event) => {
        const { id, type, base64Data, mimeType, options = {} } = event.data;

        try {
          if (type === 'BASE64_TO_UINT8ARRAY') {
            await processBase64ToUint8Array(id, base64Data, mimeType, options);
          } else if (type === 'VALIDATE_BASE64') {
            validateBase64(id, base64Data);
          }
        } catch (error) {
          self.postMessage({
            id,
            type: 'ERROR',
            error: error.message
          });
        }
      });

      async function processBase64ToUint8Array(id, base64Data, mimeType, options) {
        try {
          // Clean base64 string - ✅ Fixed escape sequences and improved logic
          let base64String = base64Data.replace(/^data:[^;]+;base64,/, '');
          base64String = base64String.replace(/\\s/g, '');
          if (base64String.includes(',')) {
            const parts = base64String.split(',');
            base64String = parts[parts.length - 1]; // Take last part after comma
          }

          // Validate
          const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
          if (!base64String || base64String.length === 0 || !base64Regex.test(base64String) || base64String.length % 4 !== 0) {
            throw new Error('Invalid base64 string format');
          }

          // ✅ For small files, process directly without chunking
          if (base64String.length < 1024 * 1024) { // Less than 1MB base64
            const binaryString = atob(base64String);
            const finalBytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
              finalBytes[i] = binaryString.charCodeAt(i);
            }

            // ✅ Send result - try transferable first, fallback to regular postMessage
            try {
              self.postMessage({
                id,
                type: 'SUCCESS',
                result: {
                  uint8Array: finalBytes,
                  mimeType,
                  originalSize: base64Data.length,
                  processedSize: finalBytes.length
                }
              }, [finalBytes.buffer]);
            } catch (transferError) {
              // Fallback to regular postMessage if transferable fails
              self.postMessage({
                id,
                type: 'SUCCESS',
                result: {
                  uint8Array: finalBytes,
                  mimeType,
                  originalSize: base64Data.length,
                  processedSize: finalBytes.length
                }
              });
            }
            return;
          }

          // ✅ For large files, use improved chunking
          const chunkSize = options.chunkSize || 1024 * 1024;
          const base64ChunkSize = Math.floor(chunkSize / 3) * 4; // Ensure multiple of 4
          const totalChunks = Math.ceil(base64String.length / base64ChunkSize);
          const uint8Chunks = [];

          for (let i = 0; i < totalChunks; i++) {
            const start = i * base64ChunkSize;
            const end = Math.min(start + base64ChunkSize, base64String.length);
            let base64Chunk = base64String.slice(start, end);

            // ✅ Only pad the last chunk
            if (i === totalChunks - 1) {
              const missingPadding = base64Chunk.length % 4;
              if (missingPadding > 0) {
                base64Chunk += '='.repeat(4 - missingPadding);
              }
            } else {
              // For non-final chunks, ensure valid boundary
              const remainder = base64Chunk.length % 4;
              if (remainder !== 0) {
                base64Chunk = base64Chunk.slice(0, base64Chunk.length - remainder);
              }
            }

            // Skip empty chunks
            if (base64Chunk.length === 0) {
              continue;
            }

            try {
              const binaryString = atob(base64Chunk);
              const bytes = new Uint8Array(binaryString.length);

              for (let j = 0; j < binaryString.length; j++) {
                bytes[j] = binaryString.charCodeAt(j);
              }

              uint8Chunks.push(bytes);

              const progress = ((i + 1) / totalChunks) * 95;
              self.postMessage({
                id,
                type: 'PROGRESS',
                progress,
                stage: 'base64_decoding'
              });

              if (i % 10 === 0) {
                await new Promise(resolve => setTimeout(resolve, 0));
              }
            } catch (chunkError) {
              throw new Error('Failed to decode base64 chunk ' + (i + 1) + '/' + totalChunks + ': ' + chunkError.message);
            }
          }

          const totalLength = uint8Chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const finalBytes = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of uint8Chunks) {
            finalBytes.set(chunk, offset);
            offset += chunk.length;
          }

          // ✅ Send result - try transferable first, fallback to regular postMessage
          try {
            self.postMessage({
              id,
              type: 'SUCCESS',
              result: {
                uint8Array: finalBytes,
                mimeType,
                originalSize: base64Data.length,
                processedSize: finalBytes.length
              }
            }, [finalBytes.buffer]);
          } catch (transferError) {
            // Fallback to regular postMessage if transferable fails
            self.postMessage({
              id,
              type: 'SUCCESS',
              result: {
                uint8Array: finalBytes,
                mimeType,
                originalSize: base64Data.length,
                processedSize: finalBytes.length
              }
            });
          }

        } catch (error) {
          self.postMessage({
            id,
            type: 'ERROR',
            error: 'Base64 processing failed: ' + error.message
          });
        }
      }

      function validateBase64(id, base64Data) {
        try {
          let cleanBase64 = base64Data.replace(/^data:[^;]+;base64,/, '').replace(/\\s/g, '');
          const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
          const isValid = base64Regex.test(cleanBase64) && cleanBase64.length % 4 === 0;

          self.postMessage({
            id,
            type: 'VALIDATION_RESULT',
            isValid,
            cleanedLength: cleanBase64.length
          });
        } catch (error) {
          self.postMessage({
            id,
            type: 'ERROR',
            error: 'Validation failed: ' + error.message
          });
        }
      }
    `;

    try {
      const blob = new Blob([workerCode], { type: 'application/javascript' });
      const workerUrl = URL.createObjectURL(blob);

      workerRef.current = new Worker(workerUrl);
      console.log('✅ Inline worker created successfully');

      // Cleanup blob URL after worker is created
      setTimeout(() => URL.revokeObjectURL(workerUrl), 1000);
    } catch (error) {
      console.error('❌ Failed to create inline worker:', error);
      throw error;
    }
  };

  const setupWorkerHandlers = () => {
    if (!workerRef.current) return;

    workerRef.current.onmessage = (event) => {
      const {
        id,
        type,
        progress,
        result,
        error,
        stage,
        isValid,
        cleanedLength,
        ...extra
      } = event.data;
      const task = tasksRef.current.get(id);

      if (!task) return;

      switch (type) {
        case 'PROGRESS':
          task.onProgress?.(progress, stage, extra);
          break;

        case 'SUCCESS':
          tasksRef.current.delete(id);
          task.resolve(result);
          break;

        case 'VALIDATION_RESULT':
          tasksRef.current.delete(id);
          task.resolve({ isValid, cleanedLength });
          break;

        case 'ERROR':
          tasksRef.current.delete(id);
          task.reject(new Error(error));
          break;
      }
    };

    workerRef.current.onerror = (error) => {
      console.error('❌ Worker error details:', {
        message: error.message,
        filename: error.filename,
        lineno: error.lineno,
        colno: error.colno,
        error: error.error,
        type: error.type,
      });

      if (isMountedRef.current) {
        setIsWorkerReady(false);
      }

      // Reject all pending tasks with detailed error
      const errorMessage = `Worker error: ${
        error.message || 'Unknown worker error'
      } at ${error.filename}:${error.lineno}:${error.colno}`;
      tasksRef.current.forEach((task) => {
        task.reject(new Error(errorMessage));
      });
      tasksRef.current.clear();
    };
  };

  const cleanup = useCallback(() => {
    if (workerRef.current) {
      // Cancel all pending tasks
      tasksRef.current.forEach((task) => {
        task.reject(new Error('Worker terminated'));
      });
      tasksRef.current.clear();

      workerRef.current.terminate();
      workerRef.current = null;
    }
    setIsWorkerReady(false);
  }, []);

  const base64ToBlobUrl = useCallback(
    (
      base64Data: string,
      mimeType: string,
      options: Base64WorkerOptions = {},
      onProgress?: (
        progress: number,
        stage?: string,
        extra?: Record<string, any>
      ) => void
    ): Promise<string> => {
      return new Promise((resolve, reject) => {
        console.log('🔄 base64ToBlobUrl called:', {
          workerExists: !!workerRef.current,
          isWorkerReady,
          isMounted: isMountedRef.current,
          base64Length: base64Data.length,
          mimeType,
        });

        if (!workerRef.current || !isWorkerReady) {
          console.log('🔄 Using fallback main thread processing');
          return fallbackBase64ToBlobUrl(base64Data, mimeType, onProgress)
            .then(resolve)
            .catch(reject);
        }

        console.log('🔄 Using worker for base64 processing');

        const id = `base64_task_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        tasksRef.current.set(id, {
          id,
          resolve: (result) => {
            try {
              if (!isMountedRef.current) {
                reject(
                  new Error(
                    'Component unmounted before base64 processing completed'
                  )
                );
                return;
              }

              // ✅ Handle transferable object result properly
              let uint8Array: Uint8Array;

              if (result.uint8Array instanceof Uint8Array) {
                uint8Array = result.uint8Array;
              } else if (
                result.uint8Array &&
                result.uint8Array.buffer instanceof ArrayBuffer
              ) {
                // Handle case where data was transferred
                uint8Array = new Uint8Array(result.uint8Array.buffer);
              } else {
                // Fallback: recreate from data
                uint8Array = new Uint8Array(result.uint8Array);
              }

              const blob = new Blob([uint8Array], { type: result.mimeType });
              const blobUrl = URL.createObjectURL(blob);
              resolve(blobUrl);
            } catch (error) {
              reject(
                new Error(
                  `Failed to create blob URL: ${
                    error instanceof Error ? error.message : 'Unknown error'
                  }`
                )
              );
            }
          },
          reject: (error) => {
            if (!isMountedRef.current) {
              reject(new Error('Component unmounted during base64 processing'));
            } else {
              reject(error);
            }
          },
          onProgress: (progress, stage, extra) => {
            if (isMountedRef.current && onProgress) {
              onProgress(progress, stage, extra);
            }
          },
        });

        // ✅ Add timeout with fallback
        const timeoutId = setTimeout(() => {
          if (tasksRef.current.has(id)) {
            console.warn('⏰ Worker task timeout, falling back to main thread');
            tasksRef.current.delete(id);
            fallbackBase64ToBlobUrl(base64Data, mimeType, onProgress)
              .then(resolve)
              .catch(reject);
          }
        }, 30000);

        // Store timeout ID with task for cleanup
        const originalResolve = tasksRef.current.get(id)?.resolve;
        const originalReject = tasksRef.current.get(id)?.reject;

        if (originalResolve && originalReject) {
          tasksRef.current.set(id, {
            ...tasksRef.current.get(id)!,
            resolve: (result) => {
              clearTimeout(timeoutId);
              originalResolve(result);
            },
            reject: (error) => {
              clearTimeout(timeoutId);
              originalReject(error);
            },
          });
        }

        // Send task to worker
        try {
          console.log(`📤 Sending task ${id} to worker`);
          workerRef.current.postMessage({
            id,
            type: 'BASE64_TO_UINT8ARRAY',
            base64Data,
            mimeType,
            options,
          });
        } catch (error) {
          console.error('❌ Failed to send message to worker:', error);
          clearTimeout(timeoutId);
          tasksRef.current.delete(id);
          fallbackBase64ToBlobUrl(base64Data, mimeType, onProgress)
            .then(resolve)
            .catch(reject);
        }
      });
    },
    [isWorkerReady]
  );

  const validateBase64 = useCallback(
    (
      base64Data: string
    ): Promise<{ isValid: boolean; cleanedLength: number }> => {
      return new Promise((resolve, reject) => {
        if (!workerRef.current || !isWorkerReady) {
          // Simple validation on main thread
          try {
            const cleaned = base64Data
              .replace(/^data:[^;]+;base64,/, '')
              .replace(/\s/g, '');
            const isValid =
              /^[A-Za-z0-9+/]*={0,2}$/.test(cleaned) &&
              cleaned.length % 4 === 0;
            resolve({ isValid, cleanedLength: cleaned.length });
          } catch (error) {
            reject(error);
          }
          return;
        }

        const id = `validation_task_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        tasksRef.current.set(id, { id, resolve, reject });

        workerRef.current.postMessage({
          id,
          type: 'VALIDATE_BASE64',
          base64Data,
        });
      });
    },
    [isWorkerReady]
  );

  return {
    base64ToBlobUrl,
    validateBase64,
    isWorkerReady,
    cleanup,
  };
};

// ✅ Improved fallback processing on main thread
async function fallbackBase64ToBlobUrl(
  base64Data: string,
  mimeType: string,
  onProgress?: (progress: number, stage?: string) => void
): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      onProgress?.(0, 'main_thread_fallback');

      // Clean base64 string
      let base64String = base64Data
        .replace(/^data:[^;]+;base64,/, '')
        .replace(/\s/g, '');
      if (base64String.includes(',')) {
        const parts = base64String.split(',');
        base64String = parts[parts.length - 1]; // Take last part after comma
      }

      // Validate base64
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (
        !base64String ||
        base64String.length === 0 ||
        !base64Regex.test(base64String) ||
        base64String.length % 4 !== 0
      ) {
        reject(new Error('Invalid base64 string format'));
        return;
      }

      // ✅ For small files, process directly
      if (base64String.length < 1024 * 1024) {
        // Less than 1MB base64
        try {
          const binaryString = atob(base64String);
          const finalBytes = new Uint8Array(binaryString.length);

          for (let i = 0; i < binaryString.length; i++) {
            finalBytes[i] = binaryString.charCodeAt(i);
          }

          const blob = new Blob([finalBytes], { type: mimeType });
          const blobUrl = URL.createObjectURL(blob);
          onProgress?.(100, 'conversion_complete');
          resolve(blobUrl);
        } catch (error) {
          reject(
            new Error(
              'Failed to decode base64: ' +
                (error instanceof Error ? error.message : 'Unknown error')
            )
          );
        }
        return;
      }

      // ✅ For large files, process in smaller chunks to avoid blocking UI
      const chunkSize = 64 * 1024; // 64KB chunks for main thread
      const base64ChunkSize = Math.floor(chunkSize / 3) * 4; // Ensure multiple of 4
      const totalChunks = Math.ceil(base64String.length / base64ChunkSize);
      const uint8Chunks: Uint8Array[] = [];
      let currentChunk = 0;

      const processNextChunk = () => {
        try {
          if (currentChunk >= totalChunks) {
            // Combine chunks
            const totalLength = uint8Chunks.reduce(
              (sum, chunk) => sum + chunk.length,
              0
            );
            const finalBytes = new Uint8Array(totalLength);
            let offset = 0;

            for (const chunk of uint8Chunks) {
              finalBytes.set(chunk, offset);
              offset += chunk.length;
            }

            const blob = new Blob([finalBytes], { type: mimeType });
            const blobUrl = URL.createObjectURL(blob);
            onProgress?.(100, 'conversion_complete');
            resolve(blobUrl);
            return;
          }

          const start = currentChunk * base64ChunkSize;
          const end = Math.min(start + base64ChunkSize, base64String.length);
          let base64Chunk = base64String.slice(start, end);

          // ✅ Only pad the last chunk
          if (currentChunk === totalChunks - 1) {
            const missingPadding = base64Chunk.length % 4;
            if (missingPadding > 0) {
              base64Chunk += '='.repeat(4 - missingPadding);
            }
          } else {
            // For non-final chunks, ensure valid boundary
            const remainder = base64Chunk.length % 4;
            if (remainder !== 0) {
              base64Chunk = base64Chunk.slice(
                0,
                base64Chunk.length - remainder
              );
            }
          }

          // Skip empty chunks
          if (base64Chunk.length === 0) {
            currentChunk++;
            setTimeout(processNextChunk, 0);
            return;
          }

          try {
            const binaryString = atob(base64Chunk);
            const bytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            uint8Chunks.push(bytes);
            currentChunk++;

            const progress = (currentChunk / totalChunks) * 100;
            onProgress?.(progress, 'main_thread_processing');

            // Schedule next chunk
            setTimeout(processNextChunk, 0);
          } catch (chunkError) {
            reject(
              new Error(
                `Failed to process chunk ${currentChunk + 1}/${totalChunks}: ${
                  chunkError instanceof Error
                    ? chunkError.message
                    : 'Unknown error'
                }`
              )
            );
          }
        } catch (error) {
          reject(
            new Error(
              `Processing error: ${
                error instanceof Error ? error.message : 'Unknown error'
              }`
            )
          );
        }
      };

      processNextChunk();
    } catch (error) {
      reject(
        new Error(
          `Fallback processing failed: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        )
      );
    }
  });
}

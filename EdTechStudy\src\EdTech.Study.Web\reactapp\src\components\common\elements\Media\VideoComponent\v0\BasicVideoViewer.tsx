import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
  useEffect,
} from 'react';

// Props for the BasicVideoViewer component
export interface BasicVideoViewerProps {
  videoUrl: string;
  videoType: 'upload' | 'embed';
  videoId?: string;
  videoRef?: React.RefObject<HTMLVideoElement>;
  isEditing?: boolean;
  controls?: boolean;
  autoPlay?: boolean;
  loop?: boolean;
  startTime?: number;
  endTime?: number;
  onTimeUpdate?: (currentTime: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  isFullscreen?: boolean; // Add support for fullscreen mode
}

// Define methods that can be called from outside
export interface BasicVideoViewerRef {
  play: () => void;
  pause: () => void;
  togglePlay: () => void;
  seekTo: (time: number) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
}

const BasicVideoViewer: React.ForwardRefRenderFunction<
  BasicVideoViewerRef,
  BasicVideoViewerProps
> = (
  {
    videoUrl,
    videoType,
    videoId,
    videoRef: externalVideoRef,
    controls = false,
    autoPlay = false,
    loop = false,
    startTime,
    endTime,
    onTimeUpdate,
    onPlay,
    onPause,
    onEnded,
    isFullscreen = false,
  },
  ref
) => {
  // Internal video ref if no external ref is provided
  const internalVideoRef = useRef<HTMLVideoElement>(null);
  const videoRef = externalVideoRef || internalVideoRef;

  // State for video player
  const [isPlaying, setIsPlaying] = useState<boolean>(autoPlay);
  const [currentTime, setCurrentTime] = useState<number>(startTime || 0);

  // Handle time update from video element
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime;
      setCurrentTime(current);

      // Call external onTimeUpdate if provided
      if (onTimeUpdate) {
        onTimeUpdate(current);
      }

      // Handle end time if set
      if (endTime && current >= endTime) {
        videoRef.current.pause();
        setIsPlaying(false);
        if (loop) {
          videoRef.current.currentTime = startTime || 0;
          videoRef.current.play();
          setIsPlaying(true);
        }
        // Call onEnded if provided
        if (onEnded) {
          onEnded();
        }
      }
    }
  };

  // Handle video metadata loaded
  const handleMetadataLoaded = () => {
    if (videoRef.current) {
      // Set initial time if startTime is configured
      if (startTime) {
        videoRef.current.currentTime = startTime;
        setCurrentTime(startTime);
      }
    }
  };

  // Play/pause toggle
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        if (onPause) onPause();
      } else {
        videoRef.current.play();
        if (onPlay) onPlay();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Seek to specific time
  const seekTo = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  // Get current time
  const getCurrentTime = (): number => {
    return videoRef.current ? videoRef.current.currentTime : 0;
  };

  // Get video duration
  const getDuration = (): number => {
    return videoRef.current ? videoRef.current.duration : 0;
  };

  // Play video
  const play = () => {
    if (videoRef.current && !isPlaying) {
      videoRef.current.play();
      setIsPlaying(true);
      if (onPlay) onPlay();
    }
  };

  // Pause video
  const pause = () => {
    if (videoRef.current && isPlaying) {
      videoRef.current.pause();
      setIsPlaying(false);
      if (onPause) onPause();
    }
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    play,
    pause,
    togglePlay,
    seekTo,
    getCurrentTime,
    getDuration,
  }));

  // Effect to sync video state when props change
  useEffect(() => {
    if (videoRef.current) {
      // Update loop state
      videoRef.current.loop = loop;

      // Update autoplay state
      videoRef.current.autoplay = autoPlay;

      // Update controls state - always show controls in fullscreen mode
      videoRef.current.controls = isFullscreen ? true : controls;

      // Set start time if provided
      if (startTime !== undefined && currentTime < startTime) {
        videoRef.current.currentTime = startTime;
        setCurrentTime(startTime);
      }
    }
  }, [loop, autoPlay, controls, startTime, currentTime, isFullscreen]);

  // Render based on video type
  if (videoType === 'embed') {
    // Handle all video URLs in a unified way
    let embedSrc = videoUrl;

    // Special handling for YouTube and Vimeo to use their embed APIs
    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
      embedSrc = `https://www.youtube.com/embed/${videoId}?autoplay=${
        autoPlay ? 1 : 0
      }&controls=${controls ? 1 : 0}&loop=${loop ? 1 : 0}&mute=0&start=${
        startTime || 0
      }&end=${endTime || ''}`;
    } else if (videoUrl.includes('vimeo.com') && videoId) {
      embedSrc = `https://player.vimeo.com/video/${videoId}?autoplay=${
        autoPlay ? 1 : 0
      }&loop=${loop ? 1 : 0}&muted=0`;
    }

    return (
      <div className="basic-video-viewer tailwind-relative tailwind-w-full tailwind-h-full tailwind-overflow-hidden tailwind-flex tailwind-justify-center tailwind-items-center tailwind-p-0 tailwind-box-border tailwind-bg-black tailwind-max-w-full tailwind-max-h-full">
        <iframe
          src={embedSrc}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="tailwind-relative tailwind-w-full tailwind-h-full tailwind-border-none tailwind-object-contain tailwind-max-h-full tailwind-max-w-full tailwind-block tailwind-mx-auto"
        />

        {/* For embedded videos, we don't show the mute/unmute button as it's controlled by the embedded player */}
      </div>
    );
  } else {
    // Upload video type
    return (
      <div className="basic-video-viewer tailwind-relative tailwind-w-full tailwind-h-full tailwind-overflow-hidden tailwind-select-none tailwind-flex tailwind-justify-center tailwind-items-center tailwind-p-0 tailwind-box-border tailwind-bg-black tailwind-max-w-full tailwind-max-h-full">
        <video
          ref={videoRef}
          src={videoUrl}
          preload="metadata"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleMetadataLoaded}
          className="tailwind-w-full tailwind-h-full tailwind-object-contain responsive-video tailwind-bg-black tailwind-max-h-full tailwind-max-w-full tailwind-block tailwind-mx-auto"
          controls={controls}
          autoPlay={autoPlay}
          loop={loop}
        />

        {/* Center play button when paused and controls are disabled */}
        {!isPlaying && !controls && (
          <div
            className="tailwind-absolute tailwind-inset-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-cursor-pointer"
            onClick={togglePlay}
          >
            <div className="tailwind-rounded-full tailwind-bg-black tailwind-bg-opacity-50 tailwind-p-4 tailwind-cursor-pointer hover:tailwind-bg-opacity-70 tailwind-transition-all">
              <svg
                viewBox="0 0 24 24"
                fill="white"
                width="48"
                height="48"
                className="tailwind-block"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </div>
          </div>
        )}
      </div>
    );
  }
};

// Export with forwardRef to access methods from outside
export default forwardRef(BasicVideoViewer);

import React, { forwardRef } from 'react';
import './icon-styles.css';

// SVG Icon Component
export interface IconProps {
  name?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  size?: number | string;
  color?: string;
  viewBox?: string;
  title?: string;
  spin?: boolean;
  rotate?: number;
  tabIndex?: number;
  'aria-label'?: string;
  role?: string;
  svgContent?: string;
  children?: React.ReactNode;
  iconType?: IconType;
}

// Define icon types
export enum IconType {
  SVG = 'svg',
}

/**
 * Icon component for rendering SVG icons
 *
 * Uses Tailwind CSS classes for styling:
 * - Size: tailwind-w-{size} tailwind-h-{size} (e.g., tailwind-w-6 tailwind-h-6)
 * - Color: tailwind-text-{color} (e.g., tailwind-text-primary, tailwind-text-red-500)
 * - Transitions: tailwind-transition-colors tailwind-duration-200
 */
export const Icon = forwardRef<SVGSVGElement, IconProps>(
  (
    {
      name,
      className = '',
      style = {},
      onClick,
      size = 24,
      color = 'currentColor',
      viewBox = '0 0 24 24',
      title,
      spin = false,
      rotate,
      tabIndex,
      'aria-label': ariaLabel,
      role,
      svgContent,
      children,
      iconType = IconType.SVG,
      ...rest
    },
    ref
  ) => {
    const iconStyle: React.CSSProperties = {
      ...style,
    };

    // Handle size via style for backward compatibility
    // In new code, prefer using Tailwind classes like tailwind-w-6 tailwind-h-6
    if (size) {
      // Add 'px' unit if size is a number to ensure proper rendering
      const sizeValue = typeof size === 'number' ? `${size}px` : size;
      iconStyle.width = sizeValue;
      iconStyle.height = sizeValue;
      // Set min-width and min-height to ensure the icon doesn't shrink
      iconStyle.minWidth = sizeValue;
      iconStyle.minHeight = sizeValue;
      // Ensure icon is properly centered and sized
      iconStyle.display = 'inline-flex';
      iconStyle.alignItems = 'center';
      iconStyle.justifyContent = 'center';
      // Ensure content is centered within the SVG
      iconStyle.position = 'relative';
    }

    // Handle explicit color via style for backward compatibility
    // In new code, prefer using Tailwind classes like tailwind-text-primary
    if (color && color !== 'currentColor') {
      iconStyle.fill = color;
    }

    // Handle spin animation
    if (spin) {
      iconStyle.animation = 'spin 1s infinite linear';
    }

    // Handle rotation
    if (rotate) {
      iconStyle.transform = `rotate(${rotate}deg)`;
    }

    // Base SVG icon class
    // Keep svg-icon for backward compatibility
    let iconClassName = `svg-icon ${className}`;

    // Add name-specific class if provided (for backward compatibility)
    if (name) {
      iconClassName += ` svg-icon-${name}`;
    }

    // If svgContent is provided, render it using dangerouslySetInnerHTML
    if (svgContent) {
      return (
        <svg
          ref={ref}
          className={iconClassName}
          style={iconStyle}
          viewBox={viewBox}
          xmlns="http://www.w3.org/2000/svg"
          onClick={onClick}
          aria-label={ariaLabel || title}
          role={role}
          tabIndex={tabIndex}
          dangerouslySetInnerHTML={{ __html: svgContent }}
          preserveAspectRatio="xMidYMid meet"
          overflow="visible"
          {...rest}
        />
      );
    }

    // Otherwise, render children
    return (
      <svg
        ref={ref}
        className={iconClassName}
        style={iconStyle}
        viewBox={viewBox}
        xmlns="http://www.w3.org/2000/svg"
        onClick={onClick}
        aria-label={ariaLabel || title}
        role={role}
        tabIndex={tabIndex}
        preserveAspectRatio="xMidYMid meet"
        overflow="visible"
        {...rest}
      >
        {title && <title>{title}</title>}
        {children}
      </svg>
    );
  }
);

Icon.displayName = 'Icon';

/**
 * Create a factory function to generate SVG Icon components
 *
 * @param svgContent - The SVG content (paths, etc.)
 * @param viewBox - The viewBox attribute for the SVG
 * @param componentName - Optional name for the component (for better debugging)
 * @param category - Optional category for the icon (from IconCategory enum)
 * @param defaultProps - Default props to pass to the Icon component
 * @returns A React component that renders the SVG icon
 *
 * @example
 * // Basic usage
 * const HomeIcon = createSvgIcon('<path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />', '0 0 24 24', 'HomeIcon');
 *
 * // With explicit category
 * const CustomIcon = createSvgIcon(
 *   '<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />',
 *   '0 0 24 24',
 *   'CustomIcon',
 *   IconCategory.ACTION
 * );
 *
 * // With Tailwind classes
 * // <DeleteIcon className="tailwind-text-red-500 tailwind-w-6 tailwind-h-6" />
 */
export const createSvgIcon = (
  svgContent: React.ReactNode | string,
  viewBox: string = '0 0 24 24',
  componentName?: string | Partial<IconProps>,
  category?: IconCategory | Partial<IconProps>,
  defaultProps: Partial<IconProps> = {}
) => {
  // Set default color to currentColor if not specified in defaultProps
  if (!defaultProps.color) {
    defaultProps.color = 'currentColor';
  }
  // Handle case where componentName is actually defaultProps
  if (componentName && typeof componentName !== 'string') {
    defaultProps = componentName;
    componentName = undefined;
    category = undefined;
  }

  // Handle case where category is actually defaultProps
  if (category && typeof category !== 'string') {
    defaultProps = category;
    category = undefined;
  }

  // Define the component type with displayName and category properties
  type IconComponentType = React.FC<any> & {
    displayName?: string;
    iconCategory?: IconCategory;
  };

  let IconComponent: IconComponentType;

  if (typeof svgContent === 'string') {
    IconComponent = ((props: any) => (
      <Icon
        viewBox={viewBox}
        svgContent={svgContent}
        iconType={IconType.SVG}
        {...defaultProps}
        {...props}
      />
    )) as IconComponentType;
  } else {
    IconComponent = ((props: any) => (
      <Icon
        viewBox={viewBox}
        iconType={IconType.SVG}
        {...defaultProps}
        {...props}
      >
        {svgContent}
      </Icon>
    )) as IconComponentType;
  }

  // Set display name for better debugging
  if (componentName && typeof componentName === 'string') {
    IconComponent.displayName = componentName;
  }

  // Set icon category if provided
  if (category && typeof category === 'string') {
    IconComponent.iconCategory = category as IconCategory;
  }

  return IconComponent;
};

// Define icon component type
export type IconComponent = React.ComponentType<
  React.SVGProps<SVGSVGElement> & {
    size?: number | string;
    color?: string;
  }
>;

// Define icon categories
export enum IconCategory {
  DOCUMENT = 'DOCUMENT',
  MEDIA = 'MEDIA',
  ACTION = 'ACTION',
  NAVIGATION = 'NAVIGATION',
  UI = 'UI',
  LAYOUT = 'LAYOUT',
  USER = 'USER',
  COMMUNICATION = 'COMMUNICATION',
  TRANSPORTATION = 'TRANSPORTATION',
  HARDWARE = 'HARDWARE',
  CONTENT = 'CONTENT',
  ALERT = 'ALERT',
  MAPS = 'MAPS',
}

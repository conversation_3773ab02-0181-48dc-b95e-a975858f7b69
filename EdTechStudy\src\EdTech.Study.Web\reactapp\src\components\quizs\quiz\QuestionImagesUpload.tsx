import React, { useState } from 'react';
import { Upload, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import fileApi from '../../../api/fileApi';

interface QuestionImagesUploadProps {
  images: { url: string; alt?: string }[];
  onImagesChange: (images: { url: string; alt?: string }[]) => void;
}

const QuestionImagesUpload: React.FC<QuestionImagesUploadProps> = ({
  images,
  onImagesChange,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>(
    images.map((img, index) => ({
      uid: `-${index}`,
      name: img.alt || `Image ${index + 1}`,
      status: 'done',
      url: img.url.startsWith('http')
        ? img.url
        : (import.meta.env.PROD ? '/' : 'https://localhost:44348/') + img.url,
    }))
  );

  const handleChange = ({
    fileList: newFileList,
  }: {
    fileList: UploadFile[];
  }) => {
    setFileList(
      newFileList.map((file) => ({
        ...file,
        url: import.meta.env.PROD
          ? file.response?.url!
          : 'https://localhost:44348/' + file.response?.url!,
      }))
    );

    const newImages = newFileList
      .filter((file) => file.status === 'done' && file.response?.url)
      .map((file) => ({
        url: import.meta.env.PROD
          ? file.response?.url!
          : 'https://localhost:44348/' + file.response?.url!,
        alt: file.name || 'Question image',
      }));
    onImagesChange(newImages);
  };

  const handleRemove = async (file: UploadFile) => {
    const removeFile = fileList.find((f) => f.uid == file.uid);
    if (removeFile && removeFile.url) {
      try {
        let url = removeFile.url;
        if (url.startsWith('https://localhost:44348/')) {
          url = url.replace('https://localhost:44348/', '');
        }
        await fileApi.Remove(url);
      } catch (error) {
        console.error('Error removing file:', error);
      }
    }
    const newFileList = fileList.filter((f) => f.uid !== file.uid);
    setFileList(newFileList);
    const newImages = newFileList
      .filter((f) => f.status === 'done' && f.url)
      .map((f) => ({
        url: f.url!,
        alt: f.name || 'Question image',
      }));

    onImagesChange(newImages);
  };

  return (
    <div className="question-images-upload">
      <Upload
        action={
          (import.meta.env.PROD ? '/' : 'https://localhost:44348/') +
          'api/edtech-files'
        }
        listType="picture-card"
        fileList={fileList}
        onChange={handleChange}
        onRemove={handleRemove}
        multiple
        accept="image/*"
        beforeUpload={(file) => {
          const isImage = file.type.startsWith('image/');
          if (!isImage) {
            message.error('Chỉ được tải lên file ảnh!');
          }
          return isImage;
        }}
      >
        {fileList.length < 5 && (
          <div>
            <UploadOutlined />
            <div style={{ marginTop: 8 }}>Tải ảnh lên</div>
          </div>
        )}
      </Upload>
    </div>
  );
};

export default QuestionImagesUpload;

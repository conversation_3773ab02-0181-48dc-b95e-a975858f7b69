import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Space,
  Tooltip,
  Di<PERSON>r,
  Drawer,
  ColorPicker,
} from 'antd';
import type { Color } from 'antd/es/color-picker';
import { SettingOutlined } from '@ant-design/icons';
import { ThemeName, useTheme } from '../../../../providers/theme';
import {
  getDefaultThemeColors,
  getThemeVariable,
} from '../../../../utils/themeUtils';
import { EThemeType } from '../../../../enums/AppEnums';
import { useDispatch } from 'react-redux';
import {
  updateThemeCustomization,
  resetThemeCustomization,
} from '../../../../store/slices/AppConfig/AppConfigSlice';

const { Title, Text } = Typography;

interface ColorSetting {
  variableName: string;
  displayName: string;
  description: string;
}

const colorSettings: ColorSetting[] = [
  {
    variableName: 'primary-color',
    displayName: '<PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> ch<PERSON>h sử dụng trong toàn bộ giao diện',
  },
  {
    variableName: 'secondary-color',
    displayName: 'Màu Phụ',
    description: 'Màu phụ cho các thành phần thay thế',
  },
  {
    variableName: 'background-color',
    displayName: 'Màu Nền',
    description: 'Màu nền chính cho trang',
  },
  {
    variableName: 'text-color',
    displayName: 'Màu Chữ',
    description: 'Màu chữ chính',
  },
  {
    variableName: 'card-background',
    displayName: 'Nền Thẻ',
    description: 'Màu nền cho các thẻ và bảng điều khiển',
  },
];

// Hàm lấy tên hiển thị của theme
const getThemeDisplayName = (themeName: ThemeName): string => {
  const displayNames: Record<EThemeType, string> = {
    [EThemeType.DEFAULT]: 'Mặc định',
    [EThemeType.DARK]: 'Tối',
    [EThemeType.LEARNING]: 'Học tập',
  };
  return displayNames[themeName as EThemeType] || themeName;
};

interface ThemeCustomizerProps {
  position?: 'left' | 'right';
}

const ThemeCustomizer: React.FC<ThemeCustomizerProps> = ({
  position = 'right',
}) => {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const [visible, setVisible] = useState(false);
  const [customColors, setCustomColors] = useState<Record<string, string>>({});

  // Initialize color values from CSS variables when the theme changes
  useEffect(() => {
    const initialColors: Record<string, string> = {};
    colorSettings.forEach((setting) => {
      initialColors[setting.variableName] = getThemeVariable(
        setting.variableName
      );
    });
    setCustomColors(initialColors);
  }, [theme]);

  // Handle color change
  const handleColorChange = (variableName: string, color: Color | string) => {
    const colorValue = typeof color === 'string' ? color : color.toHexString();
    setCustomColors((prev) => ({ ...prev, [variableName]: colorValue }));

    // Apply color to CSS variable
    document.documentElement.style.setProperty(`--${variableName}`, colorValue);
  };

  // Reset colors to default
  const handleReset = () => {
    // Dispatch action to reset theme customization
    dispatch(resetThemeCustomization(theme as EThemeType));

    // Get default colors for the current theme
    const defaultColors = getDefaultThemeColors(theme as EThemeType);

    // Apply default colors to CSS variables
    Object.entries(defaultColors).forEach(([variableName, value]) => {
      document.documentElement.style.setProperty(
        `--${variableName}`,
        String(value)
      );
    });

    // Re-initialize colors from CSS variables
    const resetColors: Record<string, string> = {};
    colorSettings.forEach((setting) => {
      resetColors[setting.variableName] = getThemeVariable(
        setting.variableName
      );
    });
    setCustomColors(resetColors);
  };

  // Save colors to Redux
  const handleSave = () => {
    dispatch(
      updateThemeCustomization({
        theme: theme as EThemeType,
        customizations: customColors,
      })
    );
  };

  return (
    <>
      <Tooltip title="Customize Theme">
        <Button
          type="primary"
          shape="circle"
          icon={<SettingOutlined />}
          onClick={() => setVisible(true)}
          style={{
            position: 'fixed',
            [position]: '20px',
            bottom: '20px',
            zIndex: 1000,
          }}
        />
      </Tooltip>

      <Drawer
        title="Tùy chỉnh giao diện"
        placement={position}
        onClose={() => setVisible(false)}
        open={visible}
        width={300}
        className="themed-background themed-text"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={5} className="themed-text">
            Giao diện hiện tại: {getThemeDisplayName(theme)}
          </Title>
          <Text className="themed-text">
            Điều chỉnh màu sắc cho giao diện hiện tại.
          </Text>

          <Divider />

          {colorSettings.map((setting) => (
            <div key={setting.variableName} style={{ marginBottom: 16 }}>
              <Text strong className="themed-text">
                {setting.displayName}
              </Text>
              <Tooltip title={setting.description}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginTop: 8,
                  }}
                >
                  <ColorPicker
                    value={customColors[setting.variableName]}
                    onChange={(color) =>
                      handleColorChange(setting.variableName, color)
                    }
                    showText
                    className="themed-text"
                  />
                </div>
              </Tooltip>
            </div>
          ))}

          <Divider />

          <Space>
            <Button onClick={handleReset} className="themed-button-secondary">
              Khôi phục mặc định
            </Button>
            <Button
              type="primary"
              onClick={handleSave}
              className="themed-button-primary"
            >
              Lưu màu sắc
            </Button>
          </Space>
        </Space>
      </Drawer>
    </>
  );
};

export default ThemeCustomizer;

import React, { createContext, useContext, useEffect } from 'react';
import {
  ThemeContextType,
  ThemeName,
  ThemeProviderProps,
} from './ThemeProvider.interfaces';
import { EThemeType } from '../../enums/AppEnums';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectTheme,
  selectCurrentThemeCustomizations,
  setTheme as setReduxTheme,
} from '../../store/slices/AppConfig/AppConfigSlice';

// Danh sách tất cả các theme có sẵn
const allThemes: ThemeName[] = Object.values(EThemeType);

// Create a theme context with default values
const ThemeContext = createContext<ThemeContextType>({
  theme: EThemeType.DEFAULT,
  setTheme: () => {},
  availableThemes: allThemes,
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = EThemeType.DEFAULT,
}) => {
  const dispatch = useDispatch();
  const reduxTheme = useSelector(selectTheme);
  const themeCustomizations = useSelector(selectCurrentThemeCustomizations);

  // Sử dụng theme từ Redux hoặc mặc định nếu chưa có
  const theme = reduxTheme || defaultTheme;

  // Effect to apply theme to document when it changes
  useEffect(() => {
    const root = document.documentElement;
    root.setAttribute('data-theme', theme);

    // Then apply any saved custom colors from themeCustomizations (these will override defaults)
    if (themeCustomizations) {
      Object.entries(themeCustomizations).forEach(([variableName, value]) => {
        document.documentElement.style.setProperty(`--${variableName}`, value);
      });
    }
  }, [theme, themeCustomizations]);

  // Initial theme setup
  useEffect(() => {
    // Add a CSS class to enable transitions after initial load
    // This prevents transition flashes on page load
    setTimeout(() => {
      document.documentElement.classList.add('theme-transitions-enabled');
    }, 100);
  }, []);

  // Update theme in Redux store
  const handleSetTheme = (newTheme: ThemeName) => {
    dispatch(setReduxTheme(newTheme));
  };

  // Context value
  const value = {
    theme,
    setTheme: handleSetTheme,
    availableThemes: allThemes,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

export default ThemeProvider;

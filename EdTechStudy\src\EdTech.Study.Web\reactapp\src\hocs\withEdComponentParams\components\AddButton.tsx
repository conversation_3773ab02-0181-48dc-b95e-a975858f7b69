import React, { useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  IEdTechComponent,
  IEdTechRenderProps,
} from '../../../interfaces/AppComponents';
import { useGridStructure } from '../../../hooks/grid/useGridStructure';
import { ETypeEdTechComponent } from '../../../enums/AppEnums';

interface AddButtonProps {
  props: IEdTechRenderProps;
  subNodes?: IEdTechComponent[];
}

/**
 * Component thêm mới engine với cấu trúc grid
 */
const AddButton: React.FC<AddButtonProps> = ({ props, subNodes = [] }) => {
  const { isEditing, name, type } = props;
  const gridStructure = useGridStructure();
  if (
    type !== ETypeEdTechComponent.LAYOUT &&
    type !== ETypeEdTechComponent.CONTENT
  )
    return null;

  // <PERSON>ác định tooltip dựa trên loại component
  const tooltipTitle = useMemo(() => {
    switch (name) {
      case 'LessonRowComponent':
        return 'Thêm cột';
      default:
        if (
          (props.subItems || []).some(
            (item) => item.name === 'LessonRowComponent'
          )
        ) {
          return 'Thêm hàng';
        }
        return 'Thêm cấu trúc hàng cột';
    }
  }, [name, props.subItems]);

  const handleClick = useCallback(() => {
    if (!isEditing) return;

    // Sử dụng hook để tự động xác định và cập nhật cấu trúc grid
    gridStructure.updateGridStructure(props, subNodes);
  }, [props, isEditing, subNodes, gridStructure]);

  return (
    <Tooltip title={tooltipTitle}>
      <Button
        type="primary"
        size="small"
        icon={<PlusOutlined />}
        onClick={handleClick}
      />
    </Tooltip>
  );
};

export default React.memo(AddButton);

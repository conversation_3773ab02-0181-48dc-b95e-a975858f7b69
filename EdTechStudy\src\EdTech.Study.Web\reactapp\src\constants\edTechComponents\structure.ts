import { LESSON_COMMON_STRUCTURE_DATA } from '../../components/structures';
import LessonCommonComponent, {
  lessonCommonSchema,
} from '../../components/structures/components/LessonCommonComponent/LessonCommonComponent';
import {
  RootLessonComponent,
  rootLessonSchema,
} from '../../components/structures/components/RootLessonComponent';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const STRUCTURE_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'RootLessonComponent',
    title: '<PERSON><PERSON>i Học Gốc',
    components: [
      {
        version: '1.0.0',
        component: RootLessonComponent,
        schema: rootLessonSchema,
      },
    ],
    type: ETypeEdTechComponent.STRUCTURE,
    tags: undefined,
    description:
      'Thành phần gốc của bài học, cung cấp cấu trúc cơ bản và bố cục cho toàn bộ nội dung bài học.',
    schema: rootLessonSchema,
  },
  {
    name: 'LessonCommonComponent',
    title: 'Bài Học',
    components: [
      {
        version: '1.0.0',
        component: LessonCommonComponent,
        schema: lessonCommonSchema,
      },
    ],
    type: ETypeEdTechComponent.STRUCTURE,
    tags: undefined,
    description:
      'Component hiển thị nội dung bài học trong một card có tiêu đề, giúp tổ chức và trình bày nội dung một cách rõ ràng và có cấu trúc.',
    schema: lessonCommonSchema,
  },
  {
    name: 'LESSON_COMMON_STRUCTURE',
    title: 'Cấu trúc bài giảng mặc định',
    type: ETypeEdTechComponent.STRUCTURE,
    tags: undefined,
    components: undefined,
    structure: LESSON_COMMON_STRUCTURE_DATA,
    schema: undefined,
  },
];

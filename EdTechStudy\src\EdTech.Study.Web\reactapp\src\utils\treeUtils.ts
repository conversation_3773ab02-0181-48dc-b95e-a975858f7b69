import { MenuItem } from '../components/common';
import { TreeMenuItem } from '../components/common/LessonSidebarMenu/LessonSidebarWithRcTree/LessonSidebarWithRcTree';
import { IEdTechRenderTreeData } from '../interfaces/AppComponents';

/**
 * Finds a node in the tree data by its ID
 * @param data The tree data to search in
 * @param id The ID of the node to find
 * @returns The found node or null if not found
 */
export const findNodeById = (
  data: IEdTechRenderTreeData[] | undefined,
  id: string
): IEdTechRenderTreeData | null => {
  if (!data) return null;

  for (const item of data) {
    if (item.id === id) {
      return item;
    }
    if (item.subItems && item.subItems.length > 0) {
      const foundNode = findNodeById(item.subItems, id);
      if (foundNode) return foundNode;
    }
  }
  return null;
};

export const convertMenuItemsToTreeMenuItems = (
  items: MenuItem[],
  basePath: string = ''
): TreeMenuItem[] => {
  return items.map((item) => {
    // Construct the path for this item
    const itemPath = basePath ? `${basePath}/${item.key}` : item.key;

    // Create the TreeMenuItem
    const treeMenuItem: TreeMenuItem = {
      key: item.key,
      title: item.label,
      type: item.type,
      path: itemPath,
      isLeaf: !item.children || item.children.length === 0,
      disableCheckbox: true,
    };

    // Process children recursively if they exist
    if (item.children && item.children.length > 0) {
      treeMenuItem.children = convertMenuItemsToTreeMenuItems(
        item.children,
        itemPath
      );
    }

    return treeMenuItem;
  });
};

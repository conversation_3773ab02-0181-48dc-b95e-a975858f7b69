/**
 * Utility functions for working with fullscreen features
 */

/**
 * Check if the Fullscreen API is supported in the current browser
 */
export const isFullscreenSupported = (): boolean => {
  if (typeof document === 'undefined') return false;
  
  return !!(
    document.fullscreenEnabled ||
    (document as any).webkitFullscreenEnabled ||
    (document as any).mozFullScreenEnabled ||
    (document as any).msFullscreenEnabled
  );
};

/**
 * Get the current fullscreen element with vendor prefix support
 */
export const getFullscreenElement = (): Element | null => {
  if (typeof document === 'undefined') return null;
  
  return (
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement ||
    null
  );
};

/**
 * Request fullscreen on an element with vendor prefix support
 */
export const requestFullscreen = (element: HTMLElement): Promise<void> => {
  if (!element) {
    return Promise.reject(new Error('No element provided'));
  }

  if (element.requestFullscreen) {
    return element.requestFullscreen();
  } else if ((element as any).webkitRequestFullscreen) {
    return (element as any).webkitRequestFullscreen();
  } else if ((element as any).mozRequestFullScreen) {
    return (element as any).mozRequestFullScreen();
  } else if ((element as any).msRequestFullscreen) {
    return (element as any).msRequestFullscreen();
  }
  
  return Promise.reject(new Error('Fullscreen API not supported'));
};

/**
 * Exit fullscreen with vendor prefix support
 */
export const exitFullscreen = (): Promise<void> => {
  if (typeof document === 'undefined') {
    return Promise.reject(new Error('Document not available'));
  }

  if (document.exitFullscreen) {
    return document.exitFullscreen();
  } else if ((document as any).webkitExitFullscreen) {
    return (document as any).webkitExitFullscreen();
  } else if ((document as any).mozCancelFullScreen) {
    return (document as any).mozCancelFullScreen();
  } else if ((document as any).msExitFullscreen) {
    return (document as any).msExitFullscreen();
  }
  
  return Promise.reject(new Error('Fullscreen API not supported'));
};

/**
 * Add fullscreen change event listener with vendor prefix support
 */
export const addFullscreenChangeListener = (
  handler: EventListener
): void => {
  document.addEventListener('fullscreenchange', handler);
  document.addEventListener('webkitfullscreenchange', handler);
  document.addEventListener('mozfullscreenchange', handler);
  document.addEventListener('MSFullscreenChange', handler);
};

/**
 * Remove fullscreen change event listener with vendor prefix support
 */
export const removeFullscreenChangeListener = (
  handler: EventListener
): void => {
  document.removeEventListener('fullscreenchange', handler);
  document.removeEventListener('webkitfullscreenchange', handler);
  document.removeEventListener('mozfullscreenchange', handler);
  document.removeEventListener('MSFullscreenChange', handler);
};

/**
 * Handle dynamic sizing based on fullscreen state
 * @param baseSize Base size value
 * @param fullscreenMultiplier Multiplier to apply when fullscreen
 * @param isFullscreen Current fullscreen state
 * @returns Calculated size
 */
export const getResponsiveSize = (
  baseSize: number,
  fullscreenMultiplier: number = 1.2,
  isFullscreen: boolean = false
): number => {
  return isFullscreen ? baseSize * fullscreenMultiplier : baseSize;
};

/**
 * Get responsive font size with appropriate unit
 */
export const getResponsiveFontSize = (
  baseSize: number,
  fullscreenMultiplier: number = 1.2,
  isFullscreen: boolean = false,
  unit: 'px' | 'rem' | 'em' = 'rem'
): string => {
  const size = getResponsiveSize(baseSize, fullscreenMultiplier, isFullscreen);
  return `${size}${unit}`;
};

/**
 * Toggle fullscreen for an element
 * @param element Element to toggle fullscreen
 * @returns Promise that resolves when the operation is complete
 */
export const toggleFullscreen = (element: HTMLElement | null): Promise<void> => {
  if (!element) {
    return Promise.reject(new Error('No element provided'));
  }
  
  if (getFullscreenElement()) {
    return exitFullscreen();
  } else {
    return requestFullscreen(element);
  }
};

/**
 * Get appropriate CSS classes based on fullscreen state
 * Used as fallback when Fullscreen API is not available
 */
export const getFullscreenClasses = (
  isFullscreen: boolean, 
  zIndex: number = 9999, 
  backgroundColor: string = 'white'
): string => {
  return isFullscreen
    ? `tailwind-fixed tailwind-inset-0 tailwind-z-[${zIndex}] tailwind-bg-${backgroundColor} tailwind-p-0 tailwind-m-0 tailwind-overflow-auto tailwind-w-screen tailwind-h-screen tailwind-max-w-none tailwind-max-h-none tailwind-rounded-none tailwind-border-none tailwind-shadow-none tailwind-transition-all tailwind-duration-300`
    : 'tailwind-transition-all tailwind-duration-300';
};
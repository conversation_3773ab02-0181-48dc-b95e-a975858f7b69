/* TableComponentStyles.css */

.table-engine-card {
  width: 100%;
  margin-bottom: 16px;
}

.table-engine-table {
  width: 100%;
  border-collapse: collapse;
}

.table-engine-table td {
  transition: all 0.2s;
  min-height: 40px; /* Increase default row height */
  height: 40px; /* Set a fixed height for all cells */
  position: relative; /* Needed for absolute positioning of textarea */
  vertical-align: middle; /* Center content vertically */
  word-break: break-word; /* Allow text to wrap */
  overflow: hidden; /* Prevent content overflow */
}

/* Add cursor pointer to cells in edit mode */
.table-engine-table[data-editing="true"] td {
  cursor: text;
}

/* Style for selected cells */
.table-engine-table td.selected-cell {
  background-color: rgba(24, 144, 255, 0.1) !important;
}

/* Style for cells in selection range */
.table-engine-table td.in-selection-range {
  background-color: rgba(24, 144, 255, 0.2) !important;
}

.table-controls {
  margin-bottom: 16px;
}

/* Styles for textarea in table cells */
.table-engine-table .ant-input-textarea {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  display: flex;
}

.table-engine-table .ant-input-textarea textarea.ant-input {
  flex: 1;
  height: 100% !important;
  min-height: 100% !important;
  max-height: 100% !important;
  overflow: auto;
  line-height: normal;
}

/* Tailwind utility classes */
.tailwind-flex {
  display: flex;
}

.tailwind-flex-col {
  flex-direction: column;
}

.tailwind-flex-wrap {
  flex-wrap: wrap;
}

.tailwind-items-center {
  align-items: center;
}

.tailwind-gap-1 {
  gap: 0.25rem;
}

.tailwind-gap-2 {
  gap: 0.5rem;
}

.tailwind-w-full {
  width: 100%;
}

.tailwind-w-64 {
  width: 16rem;
}

.tailwind-w-24 {
  width: 6rem;
}

.tailwind-w-6 {
  width: 1.5rem;
}

.tailwind-h-6 {
  height: 1.5rem;
}

.tailwind-h-10 {
  height: 2.5rem;
}

.tailwind-h-3 {
  height: 0.75rem;
}

.tailwind-h-2 {
  height: 0.5rem;
}

.tailwind-mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.tailwind-my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.tailwind-mb-1 {
  margin-bottom: 0.25rem;
}

.tailwind-mb-2 {
  margin-bottom: 0.5rem;
}

.tailwind-mb-4 {
  margin-bottom: 1rem;
}

.tailwind-mt-4 {
  margin-top: 1rem;
}

.tailwind-p-2 {
  padding: 0.5rem;
}

.tailwind-p-3 {
  padding: 0.75rem;
}

.tailwind-p-4 {
  padding: 1rem;
}

.tailwind-border {
  border-width: 1px;
  border-style: solid;
}

.tailwind-border-l {
  border-left-width: 1px;
  border-left-style: solid;
}

.tailwind-border-b {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}

.tailwind-border-b-2 {
  border-bottom-width: 2px;
  border-bottom-style: solid;
}

.tailwind-border-2 {
  border-width: 2px;
  border-style: solid;
}

.tailwind-border-gray-200 {
  border-color: #e5e7eb;
}

.tailwind-border-gray-300 {
  border-color: #d1d5db;
}

.tailwind-border-gray-600 {
  border-color: #4b5563;
}

.tailwind-rounded {
  border-radius: 0.25rem;
}

.tailwind-rounded-md {
  border-radius: 0.375rem;
}

.tailwind-bg-white {
  background-color: #ffffff;
}

.tailwind-bg-gray-50 {
  background-color: #f9fafb;
}

.tailwind-bg-gray-100 {
  background-color: #f3f4f6;
}

.tailwind-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.tailwind-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.tailwind-font-medium {
  font-weight: 500;
}

.tailwind-text-gray-500 {
  color: #6b7280;
}

.tailwind-shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.tailwind-cursor-pointer {
  cursor: pointer;
}

.tailwind-overflow-x-auto {
  overflow-x: auto;
}

.tailwind-grid {
  display: grid;
}

.tailwind-grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.hover\:tailwind-bg-gray-50:hover {
  background-color: #f9fafb;
}

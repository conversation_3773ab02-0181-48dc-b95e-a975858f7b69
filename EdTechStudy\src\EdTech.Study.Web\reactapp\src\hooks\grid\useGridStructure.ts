import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { nanoid } from 'nanoid';
import {
  updateItems,
  removeItem,
  addOrUpdateTreeStructure,
  addItems,
} from '../../store/slices/AppSlices/EdTechRenderDataSlice';
import {
  IEdTechComponent,
  IEdTechRenderProps,
  IEdTechRenderTreeData,
} from '../../interfaces/AppComponents';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { AppFunctions } from '../../utils/AppFunctions';
import { EdTechRootState } from '../../store/store';

/**
 * Hook quản lý các thao tác với cấu trúc lưới (hàng, cột)
 */
export const useGridStructure = () => {
  const dispatch = useDispatch();
  
  // Lấy tree data ở top level của hook
  const treeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // ======== UTILITY FUNCTIONS ========

  /**
   * Lấy version mới nhất của component
   */
  const getLatestVersion = useCallback((componentName: string): string => {
    const componentInfo = AppFunctions.getComponentRender({
      name: componentName,
    });
    return componentInfo?.version || '1.0.0';
  }, []);

  /**
   * Xây dựng đường dẫn (path) đúng cách
   */
  const buildPath = useCallback((basePath: string, id: string): string => {
    // Normalize base path to remove trailing slashes
    const normalizedBase = basePath ? basePath.replace(/\/+$/, '') : '';
    return normalizedBase ? `${normalizedBase}/${id}` : id;
  }, []);

  /**
   * Tạo một bản sao sạch của IEdTechRenderTreeData và đảm bảo các path được định dạng đúng
   */
  const createCleanTreeData = useCallback(
    (
      item: IEdTechRenderTreeData,
      parentPath?: string
    ): IEdTechRenderTreeData => {
      // Tạo ID mới nếu cần
      const id = item.id || nanoid(8);

      // Xây dựng path chính xác
      let correctPath = item.path;

      // Nếu có parentPath, đảm bảo path hiện tại là con của nó
      if (parentPath) {
        // Kiểm tra xem path hiện tại có đúng là con của parentPath không
        if (!correctPath.startsWith(parentPath)) {
          correctPath = buildPath(parentPath, id);
        }
      }

      // Nếu path kết thúc bằng '/' hoặc có nhiều '/' liên tiếp, sửa lại
      correctPath = correctPath.replace(/\/+$/, '').replace(/\/+/g, '/');

      // Lấy version mới nhất nếu không có
      const version = item.version || getLatestVersion(item.name);

      const cleanItem: IEdTechRenderTreeData = {
        id,
        name: item.name,
        title: item.title,
        type: item.type,
        order: item.order,
        path: correctPath,
        version,
      };

      // Sao chép subItems nếu có
      if (item.subItems && item.subItems.length > 0) {
        cleanItem.subItems = item.subItems.map((subItem) =>
          createCleanTreeData(subItem, correctPath)
        );
      }

      return cleanItem;
    },
    [buildPath, getLatestVersion]
  );

  /**
   * Cập nhật trạng thái với node đã được cập nhật
   */
  const updateNodeState = useCallback(
    (path: string, updatedNode: IEdTechRenderTreeData) => {
      dispatch(
        updateItems({
          items: [{ path, data: updatedNode }],
        })
      );
    },
    [dispatch]
  );

  /**
   * Xóa item và kiểm tra cấu trúc hàng-cột
   * Nếu xóa cột mà hàng không còn cột nào, thì xóa luôn hàng đó
   */
  const deleteItemWithGridCheck = useCallback((nodeToDelete: IEdTechRenderTreeData) => {
    if (!nodeToDelete) return;
    
    // Kiểm tra xem node cần xóa có phải là một cột không
    const isColumn = nodeToDelete.name === 'LessonColumnComponent';
    
    if (!isColumn) {
      // Nếu không phải cột, xóa bình thường
      dispatch(removeItem({ targetNode: nodeToDelete }));
      return;
    }
    
    // Lấy path của cột và tách để lấy path của hàng chứa nó
    const columnPath = nodeToDelete.path;
    const pathSegments = columnPath.split('/');
    
    // Nếu không đủ phần tử để xác định hàng, xóa bình thường
    if (pathSegments.length < 2) {
      dispatch(removeItem({ targetNode: nodeToDelete }));
      return;
    }
    
    // Lấy path của hàng chứa cột
    const rowPath = pathSegments.slice(0, -1).join('/');
    
    // Tìm node hàng bằng cách duyệt qua tree
    const findNodeByPath = (nodes: IEdTechRenderTreeData[], targetPath: string): IEdTechRenderTreeData | null => {
      for (const node of nodes) {
        if (node.path === targetPath) {
          return node;
        }
        if (node.subItems && node.subItems.length > 0) {
          const found = findNodeByPath(node.subItems, targetPath);
          if (found) return found;
        }
      }
      return null;
    };
    
    const rowNode = findNodeByPath([treeData], rowPath);
    
    // Nếu không tìm thấy hàng, xóa bình thường
    if (!rowNode) {
      dispatch(removeItem({ targetNode: nodeToDelete }));
      return;
    }
    
    // Đếm số cột trong hàng (bao gồm cả cột sắp xóa)
    const columnsInRow = rowNode.subItems?.filter(item => item.name === 'LessonColumnComponent') || [];
    
    // Nếu hàng chỉ có 1 cột (chính là cột sắp xóa), xóa luôn hàng
    if (columnsInRow.length <= 1) {
      // Xóa hàng thay vì xóa cột
      dispatch(removeItem({ targetNode: rowNode }));
    } else {
      // Nếu hàng còn có nhiều cột khác, chỉ xóa cột đó
      dispatch(removeItem({ targetNode: nodeToDelete }));
    }
  }, [dispatch, treeData]);

  /**
   * Xây dựng subItem từ IEdTechComponent
   */
  const buildSubItem = useCallback(
    (
      component: IEdTechComponent,
      basePath: string,
      index: number
    ): IEdTechRenderTreeData => {
      const childId = nanoid(8);
      return {
        id: childId,
        name: component.name,
        title: component.title,
        type: component.type,
        order: index,
        path: buildPath(basePath, childId),
        version: getLatestVersion(component.name),
      };
    },
    [buildPath, getLatestVersion]
  );

  // ======== GRID STRUCTURE BUILDERS ========

  /**
   * Tạo một cột mới
   */
  const createColumn = useCallback(
    (
      parentPath: string,
      title: string,
      order: number,
      subItems: IEdTechRenderTreeData[] = []
    ): IEdTechRenderTreeData => {
      const columnId = nanoid(8);
      return {
        id: columnId,
        name: 'LessonColumnComponent',
        title,
        type: ETypeEdTechComponent.LAYOUT,
        order,
        path: buildPath(parentPath, columnId),
        version: getLatestVersion('LessonColumnComponent'),
        subItems,
      };
    },
    [buildPath, getLatestVersion]
  );

  /**
   * Tạo một hàng mới
   */
  const createRow = useCallback(
    (
      parentPath: string,
      title: string,
      order: number,
      columns: IEdTechRenderTreeData[] = []
    ): IEdTechRenderTreeData => {
      const rowId = nanoid(8);
      return {
        id: rowId,
        name: 'LessonRowComponent',
        title,
        type: ETypeEdTechComponent.LAYOUT,
        order,
        path: buildPath(parentPath, rowId),
        version: getLatestVersion('LessonRowComponent'),
        subItems: columns,
      };
    },
    [buildPath, getLatestVersion]
  );

  // ======== GRID OPERATION FUNCTIONS ========

  /**
   * Thêm cột vào hàng
   */
  const addColumnToRow = useCallback(
    (node: IEdTechRenderProps, subNodes: IEdTechComponent[] = []) => {
      const { path, subItems = [] } = node;
      const columnCount = subItems.filter(
        (x) => x.name === 'LessonColumnComponent'
      ).length;

      // Tạo cột mới trước (không có subItems)
      const newColumn = createColumn(
        path,
        `Cột ${columnCount + 1}`,
        columnCount,
        []
      );

      // Xây dựng subItems cho cột với ID thực tế
      const columnSubItems = subNodes.map((component, index) =>
        buildSubItem(component, newColumn.path, index)
      );

      // Thêm subItems vào cột
      newColumn.subItems = columnSubItems;

      // Tạo danh sách subItems mới với path được chuẩn hóa
      const cleanSubItems = subItems.map((item) =>
        createCleanTreeData(item, path)
      );
      cleanSubItems.push(newColumn);

      // Tạo node mới và cập nhật state
      const updatedNode = createCleanTreeData(
        {
          ...(node as IEdTechRenderTreeData),
          subItems: cleanSubItems,
        },
        node.path.split('/').slice(0, -1).join('/')
      );

      updateNodeState(path, updatedNode);

      return newColumn;
    },
    [buildSubItem, createColumn, createCleanTreeData, updateNodeState]
  );

  /**
   * Chuyển đổi cột thành cấu trúc hàng-cột
   */
  const transformColumnToGrid = useCallback(
    (node: IEdTechRenderProps) => {
      const { path, subItems = [] } = node;
      const cleanSubItems = subItems.map((item) =>
        createCleanTreeData(item, path)
      );

      // Tạo hàng mới (không có subItems ban đầu)
      const newRow = createRow(path, 'Hàng 1', 0, []);

      // Tạo cột 1 sử dụng rowPath thực tế
      const column1 = createColumn(newRow.path, 'Cột 1', 0, cleanSubItems);

      // Tạo cột 2 (trống)
      const column2 = createColumn(newRow.path, 'Cột 2', 1, []);

      // Thêm cột vào hàng
      newRow.subItems = [column1, column2];

      // Xóa các item hiện tại
      dispatch(removeItem({ targetNode: subItems }));

      // Tạo node mới và cập nhật state
      const updatedNode = createCleanTreeData(
        {
          ...(node as IEdTechRenderTreeData),
          subItems: [newRow],
        },
        node.path.split('/').slice(0, -1).join('/')
      );

      updateNodeState(path, updatedNode);

      return newRow;
    },
    [createColumn, createRow, createCleanTreeData, dispatch, updateNodeState]
  );

  /**
   * Thêm các item vào cột
   */
  const addItemsToColumn = useCallback(
    (node: IEdTechRenderProps, subNodes: IEdTechComponent[] = []) => {
      if (!subNodes.length) return [];

      // Sử dụng addItems trực tiếp
      dispatch(
        addItems({
          parentNode: node,
          items: subNodes,
        })
      );

      // Trả về mảng dự kiến sẽ được thêm vào
      const { path } = node;
      return subNodes.map((component, index) =>
        buildSubItem(component, path, (node.subItems?.length || 0) + index)
      );
    },
    [dispatch, buildSubItem]
  );

  /**
   * Thêm hàng mới (chỉ có 1 cột) vào cột đã có hàng
   */
  const addRowToColumn = useCallback(
    (node: IEdTechRenderProps, subNodes: IEdTechComponent[] = []) => {
      const { path, subItems = [] } = node;
      const rowCount = subItems.filter(
        (x) => x.name === 'LessonRowComponent'
      ).length;

      // Tạo hàng mới (không có subItems ban đầu)
      const newRow = createRow(
        path,
        `Hàng ${rowCount + 1}`,
        subItems.length,
        []
      );

      // Tạo cột với path đã biết
      const column = createColumn(newRow.path, 'Cột 1', 0, []);

      // Xây dựng subItems cho cột với path đúng
      const columnSubItems = subNodes.map((component, index) =>
        buildSubItem(component, column.path, index)
      );

      // Thêm subItems vào cột
      column.subItems = columnSubItems;

      // Thêm cột vào hàng
      newRow.subItems = [column];

      // Tạo danh sách subItems mới
      const cleanSubItems = subItems.map((item) =>
        createCleanTreeData(item, path)
      );
      cleanSubItems.push(newRow);

      // Tạo node mới và cập nhật state
      const updatedNode = createCleanTreeData(
        {
          ...(node as IEdTechRenderTreeData),
          subItems: cleanSubItems,
        },
        node.path.split('/').slice(0, -1).join('/')
      );

      updateNodeState(path, updatedNode);

      return newRow;
    },
    [
      buildSubItem,
      createColumn,
      createRow,
      createCleanTreeData,
      updateNodeState,
    ]
  );

  /**
   * Tạo một cấu trúc grid mới
   */
  const createNewGrid = useCallback(
    (
      parentNode: IEdTechRenderProps | undefined,
      children: IEdTechComponent[],
      additionalColumns: number = 0
    ) => {
      const basePath = parentNode?.path || '';

      // Tạo hàng mới (không có subItems ban đầu)
      const row = createRow(basePath, 'Hàng 1', 0, []);

      // Tạo cột đầu tiên với path đúng
      const firstColumn = createColumn(row.path, 'Cột 1', 0, []);

      // Xây dựng subItems cho cột đầu tiên
      const columnItems = children.map((component, index) =>
        buildSubItem(component, firstColumn.path, index)
      );

      // Thêm subItems vào cột đầu tiên
      firstColumn.subItems = columnItems;

      // Tạo các cột bổ sung nếu cần
      const additionalCols = [];
      for (let i = 0; i < additionalColumns; i++) {
        additionalCols.push(createColumn(row.path, `Cột ${i + 2}`, i + 1, []));
      }

      // Thêm tất cả cột vào hàng
      row.subItems = [firstColumn, ...additionalCols];

      // Thêm vào cấu trúc cây nếu có parentNode
      if (parentNode) {
        const { path, subItems = [] } = parentNode;

        // Tạo danh sách subItems mới và đảm bảo path chính xác
        const cleanSubItems = subItems.map((item) =>
          createCleanTreeData(item, path)
        );
        cleanSubItems.push(row);

        // Tạo node mới và cập nhật state
        const parentParentPath = path.split('/').slice(0, -1).join('/');
        const updatedNode = createCleanTreeData(
          {
            ...(parentNode as IEdTechRenderTreeData),
            subItems: cleanSubItems,
          },
          parentParentPath
        );

        updateNodeState(path, updatedNode);
      }

      return row;
    },
    [
      buildSubItem,
      createColumn,
      createRow,
      createCleanTreeData,
      updateNodeState,
    ]
  );

  /**
   * Cập nhật cấu trúc đặc biệt
   */
  const updateSpecialStructure = useCallback(
    (
      parentNode: IEdTechRenderProps | undefined,
      structure: IEdTechRenderTreeData
    ) => {
      // Sử dụng một cấu trúc sạch
      const parentPath = parentNode?.path;
      const cleanStructure = createCleanTreeData(structure, parentPath);

      dispatch(
        addOrUpdateTreeStructure({
          parentNode,
          items: [cleanStructure],
        })
      );

      return cleanStructure;
    },
    [dispatch, createCleanTreeData]
  );

  /**
   * Phương thức thông minh tự động xác định và xử lý cấu trúc lưới
   */
  const updateGridStructure = useCallback(
    (
      node: IEdTechRenderProps | IEdTechComponent[] | null | undefined,
      subNodes: IEdTechComponent[] = [],
      additionalColumns: number = 0
    ) => {
      // Trường hợp 1: node là mảng IEdTechComponent[]
      if (Array.isArray(node)) {
        return createNewGrid(undefined, node, additionalColumns);
      }

      // Trường hợp 2: node không được cung cấp hoặc null
      if (!node) {
        return createNewGrid(undefined, subNodes, additionalColumns);
      }

      // Trường hợp 3: node là IEdTechRenderProps
      const currentNode = node as IEdTechRenderProps;

      // Tự động xác định chế độ dựa trên loại và trạng thái của node
      if (currentNode.name === 'LessonRowComponent') {
        // Node là hàng -> Thêm cột vào hàng
        return addColumnToRow(currentNode, subNodes);
      } else {
        // Tất cả các trường hợp còn lại (không phải row)
        const hasRowComponent = (currentNode.subItems || []).some(
          (item) => item.name === 'LessonRowComponent'
        );

        if (hasRowComponent) {
          // Node có chứa hàng -> Thêm hàng mới
          return addRowToColumn(currentNode, subNodes);
        } else if (subNodes && subNodes.length > 0) {
          // Node không chứa hàng nhưng có subNodes -> Thêm items vào node
          return addItemsToColumn(currentNode, subNodes);
        } else {
          // Node không chứa hàng và không có subNodes -> Chuyển đổi thành grid
          return transformColumnToGrid(currentNode);
        }
      }
    },
    [
      addColumnToRow,
      addItemsToColumn,
      addRowToColumn,
      createNewGrid,
      transformColumnToGrid,
    ]
  );

  // Public API
  return {
    // Core methods
    updateGridStructure,
    updateSpecialStructure,

    // Grid structure builders
    createNewGrid,
    addColumnToRow,
    transformColumnToGrid,
    addItemsToColumn,
    addRowToColumn,

    // Utility functions
    createCleanTreeData,
    buildPath,
    getLatestVersion,
    deleteItemWithGridCheck,

    // Convenience methods
    addEmptyColumnToRow: (node: IEdTechRenderProps) => addColumnToRow(node, []),
    addColumnWithItems: (node: IEdTechRenderProps, items: IEdTechComponent[]) =>
      addColumnToRow(node, items),
  };
};

import React, { memo, useEffect, useState, ReactNode } from 'react';
import {
  FreefallSimulatorConfig,
  defaultFreefallSimulatorConfig,
} from './FreefallSimulatorConfig';
import FreefallSimulatorComponent from './FreefallSimulatorComponent';
import { Button, InputNumber, Select, Tooltip, Card } from 'antd';
import {
  InfoCircleOutlined,
  SettingOutlined,
  ArrowDownOutlined,
  ThunderboltOutlined,
  GlobalOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import FreefallSimulatorConfigModal from './FreefallSimulatorConfigModal';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { EngineContainer } from '../../../components/common/engines';
import { ITextProps } from '../../../components/core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { updateTitleAndSync } from '../../../utils/titleSyncUtils';
import { EdTechRootState } from '../../../store/store';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

interface FreefallSimulatorProps extends FreefallSimulatorConfig {}

export const freefallSimulatorSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    initialHeight: z.number().optional(),
    gravity: z.number().optional(),
    airResistance: z.number().optional(),
    mass: z.number().optional(),
    showGraphs: z.boolean().optional(),
  },
});

const FreefallSimulator: React.FC<
  IEdTechRenderProps<FreefallSimulatorProps>
> = (props) => {
  const { id, params, addOrUpdateParamComponent: updateParamComponent } = props;
  const [simulatorConfig, setSimulatorConfig] =
    useState<FreefallSimulatorConfig>({
      ...defaultFreefallSimulatorConfig,
      ...params,
    });
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);
  const [initialHeight, setInitialHeight] = useState<number>(
    simulatorConfig.initialHeight
  );
  const [initialVelocity, setInitialVelocity] = useState<number>(
    simulatorConfig.initialVelocity
  );
  const [activeEnvironmentId, setActiveEnvironmentId] = useState<string>(
    simulatorConfig.activeEnvironmentId
  );

  const handleConfigSubmit = (config: FreefallSimulatorConfig) => {
    setSimulatorConfig(config);
    updateParamComponent({
      ...simulatorConfig,
      ...config,
    });

    setInitialHeight(config.initialHeight);
    setInitialVelocity(config.initialVelocity);
    setActiveEnvironmentId(config.activeEnvironmentId);
    setIsConfigModalOpen(false);
  };

  const applyQuickSettings = () => {
    const updatedConfig: FreefallSimulatorConfig = {
      ...simulatorConfig,
      initialHeight,
      initialVelocity,
      activeEnvironmentId,
    };
    setSimulatorConfig(updatedConfig);
  };

  useEffect(() => {
    setSimulatorConfig({
      ...defaultFreefallSimulatorConfig,
      ...params,
    });
  }, [params]);

  // Title props for the EngineContainer - fixed title as per requirements
  const titleProps: ITextProps = {
    text: 'Mô phỏng Rơi Tự Do',
    fontSize: 24,
    align: 'left',
    bold: true, // Must be boolean as per ITextProps interface
  };

  // Instructions content for the EngineContainer using structured approach
  const instructionsContent = {
    objective:
      'Mô phỏng rơi tự do giúp bạn khám phá các quy luật vật lý của chuyển động rơi tự do trong các môi trường khác nhau.',
    steps: [
      'Điều chỉnh các thông số ban đầu như chiều cao, vận tốc ban đầu và môi trường',
      'Bấm nút "Bắt đầu" để chạy mô phỏng và quan sát chuyển động của vật thể',
      'Điều chỉnh tốc độ mô phỏng để quan sát chuyển động nhanh hơn hoặc chậm hơn',
      'Theo dõi các đại lượng vật lý như vị trí, vận tốc và gia tốc theo thời gian',
      'Thay đổi môi trường để so sánh chuyển động rơi tự do trên Trái Đất, Mặt Trăng và Sao Hỏa',
    ],
  };

  // Quick settings panel for the simulator - improved for better usability and more compact
  const quickSettingsPanel: ReactNode = (
    <Card
      title={
        <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
          <SettingOutlined className="tailwind-text-blue-600" />
          <span>Cài đặt mô phỏng</span>
        </div>
      }
      className="tailwind-shadow-sm tailwind-border-gray-200"
      styles={{
        header: {
          backgroundColor: '#f0f9ff', // Light blue background
          borderBottom: '1px solid #bae6fd',
        },
        body: {
          padding: '16px', // Smaller padding for more compact look
        },
      }}
      size="small"
    >
      <div className="tailwind-flex tailwind-items-end tailwind-gap-3">
        <div className="tailwind-w-[30%]">
          <label className="tailwind-text-xs tailwind-font-medium tailwind-text-gray-700 tailwind-mb-1 tailwind-flex tailwind-items-center">
            <ArrowDownOutlined className="tailwind-mr-1 tailwind-text-green-600" />
            Chiều cao (m):
          </label>
          <InputNumber
            min={1}
            max={1000}
            value={initialHeight}
            onChange={(value) => setInitialHeight(value as number)}
            style={{ width: '100%' }}
            className="tailwind-w-full"
            size="small"
          />
        </div>

        <div className="tailwind-w-[30%]">
          <label className="tailwind-text-xs tailwind-font-medium tailwind-text-gray-700 tailwind-mb-1 tailwind-flex tailwind-items-center">
            <ThunderboltOutlined className="tailwind-mr-1 tailwind-text-yellow-600" />
            Vận tốc (m/s):
            <Tooltip title="Giá trị dương hướng lên trên, âm hướng xuống dưới">
              <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
            </Tooltip>
          </label>
          <InputNumber
            min={-50}
            max={50}
            value={initialVelocity}
            onChange={(value) => setInitialVelocity(value as number)}
            style={{ width: '100%' }}
            className="tailwind-w-full"
            size="small"
          />
        </div>

        <div className="tailwind-w-[30%]">
          <label className="tailwind-text-xs tailwind-font-medium tailwind-text-gray-700 tailwind-mb-1 tailwind-flex tailwind-items-center">
            <GlobalOutlined className="tailwind-mr-1 tailwind-text-blue-600" />
            Môi trường:
          </label>
          <Select
            value={activeEnvironmentId}
            onChange={setActiveEnvironmentId}
            style={{ width: '100%' }}
            className="tailwind-w-full"
            size="small"
          >
            {simulatorConfig.environments.map((env) => (
              <Select.Option key={env.id} value={env.id}>
                {env.name}
                {env.airResistance && ' (có lực cản)'}
              </Select.Option>
            ))}
          </Select>
        </div>

        <Button
          onClick={applyQuickSettings}
          type="primary"
          icon={<PlayCircleOutlined />}
          style={{ backgroundColor: '#3b82f6', borderColor: '#3b82f6' }}
          className="tailwind-font-medium"
          size="small"
        >
          Áp dụng
        </Button>
      </div>
    </Card>
  );

  // Main component that includes quick settings and the simulator component
  const mainComponent: ReactNode = (
    <div className="tailwind-flex tailwind-flex-col tailwind-gap-2">
      {quickSettingsPanel}
      <FreefallSimulatorComponent
        config={simulatorConfig}
        onDataChange={(data) => {
          // Handle data changes if needed

          // If environment changed in the component, update the parent state
          if (data.action === 'environment_changed') {
            setActiveEnvironmentId(data.environmentId);
          }
        }}
      />
    </div>
  );

  // Config modal
  const configModal: ReactNode = (
    <FreefallSimulatorConfigModal
      isOpen={isConfigModalOpen}
      onClose={() => setIsConfigModalOpen(false)}
      onSubmit={handleConfigSubmit}
      defaultConfig={simulatorConfig}
    />
  );

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Custom title update handler that also updates local state
  const handleFreefallTitleUpdate = (updates: Partial<ITextProps>) => {
    // Update the title in the simulator config and local state
    const updatedConfig = updateTitleAndSync(
      id,
      updates,
      simulatorConfig,
      renderTreeData,
      dispatch,
      updateParamComponent
    );

    // Update local state
    if (updatedConfig !== simulatorConfig) {
      setSimulatorConfig(updatedConfig);
    }
  };

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      allowConfiguration={true}
      mainComponent={mainComponent}
      configModal={configModal}
      instructionsContent={instructionsContent}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      onTitleUpdate={handleFreefallTitleUpdate}
      id={id}
    />
  );
};

export default memo(withEdComponentParams(FreefallSimulator));

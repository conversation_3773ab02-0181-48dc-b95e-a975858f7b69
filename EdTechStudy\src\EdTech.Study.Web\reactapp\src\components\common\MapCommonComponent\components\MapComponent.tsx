import React, { useEffect, useRef, useState, useMemo, ReactElement } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ZoomControl,
  useMapEvents,
  Polyline,
  Circle,
  Polygon,
  useMap,
} from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Đảm bảo marker icons được load đúng cách trong React
import icon from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';
import {
  MapPosition,
  MarkerOptions,
  ControlPosition,
  MapControlsConfig,
  defaultMapControlsConfig,
  calculateControlOffset,
  getPositionCSS,
  ControlOffset,
} from '../MapUtils';
import '../map.css';

// Import controls
import {
  ScaleControl,
  CompassControl,
  CoordinatesControl,
  SearchControl,
} from './controls';

// Import GeoJSONLayer component
import GeoJSONLayer from './GeoJSONLayer';
import HoangSaTruongSaData from '../hoangsa_truongsa.json';

// Đ<PERSON>nh nghĩa interface cho props của component
interface MapComponentProps {
  initialCenter?: MapPosition;
  initialZoom?: number;
  markers?: MarkerOptions[];
  polylines?: Array<{
    positions: MapPosition[];
    color?: string;
    weight?: number;
  }>;
  polygons?: Array<{
    positions: MapPosition[];
    color?: string;
    fillColor?: string;
    fillOpacity?: number;
  }>;
  circles?: Array<{
    center: MapPosition;
    radius: number;
    color?: string;
  }>;
  width?: string | number;
  height?: string | number;
  scrollWheelZoom?: boolean;
  doubleClickZoom?: boolean;
  className?: string;
  controls?: MapControlsConfig;
  onMapClick?: (position: MapPosition) => void;
  onMapDrag?: (center: MapPosition) => void;
  onZoomChange?: (zoom: number) => void;
  onMarkerClick?: (marker: MarkerOptions) => void;
  onMarkerDragEnd?: (marker: MarkerOptions, newPosition: MapPosition) => void;
  onLocationFound?: (position: MapPosition) => void;
  onMapReady?: (map: L.Map) => void; // Thêm callback khi map sẵn sàng
  children?: React.ReactNode;
}

// Định nghĩa default icon
const DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});

// Đặt icon mặc định cho tất cả markers
L.Marker.prototype.options.icon = DefaultIcon;

// Component lắng nghe sự kiện bản đồ
const MapEvents = ({
  onMapClick,
  onMapDrag,
  onZoomChange,
}: {
  onMapClick?: (position: MapPosition) => void;
  onMapDrag?: (center: MapPosition) => void;
  onZoomChange?: (zoom: number) => void;
}) => {
  const map = useMapEvents({
    click(e) {
      if (onMapClick) {
        onMapClick({ lat: e.latlng.lat, lng: e.latlng.lng });
      }
    },
    dragend() {
      if (onMapDrag) {
        const center = map.getCenter();
        onMapDrag({ lat: center.lat, lng: center.lng });
      }
    },
    zoomend() {
      if (onZoomChange) {
        onZoomChange(map.getZoom());
      }
    },
  });

  return null;
};

// Thêm interface cho control item
interface ControlItem {
  key: string;
  enabled: boolean;
  position: ControlPosition;
  maxWidth?: number;
  size?: number;
  decimals?: number;
  placeholder?: string;
  offset?: ControlOffset;
}

// Thêm hàm để nhóm các control theo vị trí
const groupControlsByPosition = (controls: MapControlsConfig) => {
  const grouped: Record<ControlPosition, ControlItem[]> = {
    topleft: [],
    topright: [],
    bottomleft: [],
    bottomright: [],
  };

  // Nhóm các control theo vị trí
  Object.entries(controls).forEach(([key, value]) => {
    if (value?.enabled && value?.position && value.position in grouped) {
      grouped[value.position as ControlPosition].push({
        key,
        ...value,
      } as ControlItem);
    }
  });

  return grouped;
};

// Component to handle map initialization and prevent reinitialization
const MapInitializer: React.FC<{ onMapReady?: (map: L.Map) => void }> = ({ onMapReady }) => {
  const map = useMap();
  
  // Use useEffect with empty dependency array to run only once
  useEffect(() => {
    if (onMapReady) {
      onMapReady(map);
    }
  }, [map, onMapReady]);
  
  return null;
};

// Inject global CSS styles once
const MapStyles = () => {
  useEffect(() => {
    // Create a unique ID for our style element to avoid duplicates
    const styleId = 'leaflet-custom-styles';
    
    // Check if the style element already exists
    if (!document.getElementById(styleId)) {
      const styleElement = document.createElement('style');
      styleElement.id = styleId;
      styleElement.innerHTML = `
        .leaflet-container {
          z-index: 10;
        }
        
        .leaflet-top {
          z-index: 1000;
        }
        
        .leaflet-bottom {
          z-index: 1000;
        }
        
        .leaflet-bottom.leaflet-right {
          right: 0;
        }
        
        .leaflet-control-attribution {
          position: fixed !important;
          bottom: 0 !important;
          left: 0 !important;
          right: 0 !important;
          background: rgba(255, 255, 255, 0.8) !important;
          margin: 0 !important;
          padding: 0 5px !important;
          height: 20px !important;
          line-height: 20px !important;
          white-space: nowrap !important;
          width: 100% !important;
          text-align: center !important;
        }
        
        .leaflet-control {
          margin: 0;
          clear: none;
        }
        
        /* Ensure all UI modals have higher z-index than map */
        .fixed.inset-0.z-50 {
          z-index: 2000 !important;
        }
        
        /* Make sure overlay pane is on top of base layers */
        .leaflet-overlay-pane {
          z-index: 410 !important;
        }
        
        /* Enhance styling for Vietnamese territories */
        .vietnam-territory {
          stroke-dasharray: 5, 5;
          animation: dash 10s linear infinite;
          stroke: #9B9BB5 !important;
          stroke-width: 2px !important;
        }
        
        @keyframes dash {
          to {
            stroke-dashoffset: 1000;
          }
        }
      `;
      document.head.appendChild(styleElement);
    }
    
    // No need for cleanup as we want these styles to persist
  }, []);
  
  return null;
};

// The main map component content that's loaded inside the map container
const MapContent: React.FC<{
  markers: MarkerOptions[];
  polylines: Array<{
    positions: MapPosition[];
    color?: string;
    weight?: number;
  }>;
  polygons: Array<{
    positions: MapPosition[];
    color?: string;
    fillColor?: string;
    fillOpacity?: number;
  }>;
  circles: Array<{
    center: MapPosition;
    radius: number;
    color?: string;
  }>;
  onMapClick?: (position: MapPosition) => void;
  onMapDrag?: (center: MapPosition) => void;
  onZoomChange?: (zoom: number) => void;
  onMarkerClick?: (marker: MarkerOptions) => void;
  onMarkerDragEnd?: (marker: MarkerOptions, newPosition: MapPosition) => void;
  onLocationFound?: (position: MapPosition) => void;
  onMapReady?: (map: L.Map) => void;
  mapId: string;
  mergedControls: MapControlsConfig;
  groupedControls: Record<ControlPosition, ControlItem[]>;
  filterFeatures: (feature: any) => boolean;
  children?: React.ReactNode;
}> = ({ 
  markers, 
  polylines, 
  polygons, 
  circles, 
  onMapClick, 
  onMapDrag, 
  onZoomChange, 
  onMarkerClick, 
  onMarkerDragEnd,
  onLocationFound,
  onMapReady,
  mapId,
  groupedControls,
  filterFeatures,
  children 
}) => {
  // Render controls theo vị trí - defined within the component that uses it
  const renderControls = (): ReactElement[] => {
    // Create a flat array to collect all control elements
    const allControls: ReactElement[] = [];
    
    // Process each position and its controls
    Object.entries(groupedControls).forEach(([position, controls]) => {
      // Xác định xem có cần đảo ngược thứ tự hay không
      const isBottomPosition = position.startsWith('bottom');

      // Add each control from this position to the flat array
      controls.forEach((control, index) => {
        const calculatedOffset = calculateControlOffset(
          position as ControlPosition,
          index,
          controls.length,
          isBottomPosition // Đảo ngược thứ tự cho các control ở bottom
        );
        const positionStyle = getPositionCSS(position as ControlPosition);
        const finalOffset = {
          ...calculatedOffset,
          ...(control.offset || {}),
        };

        let controlElement: ReactElement | null = null;
        
        switch (control.key) {
          case 'zoom':
            controlElement = (
              <div key={`zoom-${position}-${mapId}`} style={{ ...positionStyle, ...finalOffset }}>
                <ZoomControl position={position as L.ControlPosition} />
              </div>
            );
            break;
          case 'scale':
            controlElement = (
              <div key={`scale-${position}-${mapId}`} style={{ ...positionStyle, ...finalOffset }}>
                <ScaleControl
                  position={position as ControlPosition}
                  maxWidth={control.maxWidth}
                />
              </div>
            );
            break;
          case 'compass':
            controlElement = (
              <div key={`compass-${position}-${mapId}`} style={{ ...positionStyle, ...finalOffset }}>
                <CompassControl
                  position={position as ControlPosition}
                  size={control.size}
                />
              </div>
            );
            break;
          case 'coordinates':
            controlElement = (
              <div
                key={`coordinates-${position}-${mapId}`}
                style={{ ...positionStyle, ...finalOffset }}
              >
                <CoordinatesControl
                  position={position as ControlPosition}
                  decimals={control.decimals}
                />
              </div>
            );
            break;
          case 'search':
            controlElement = (
              <div key={`search-${position}-${mapId}`} style={{ ...positionStyle, ...finalOffset }}>
                <SearchControl
                  position={position as ControlPosition}
                  placeholder={control.placeholder}
                  onLocationFound={onLocationFound}
                />
              </div>
            );
            break;
        }
        
        // Add the control element to our flat array if it's not null
        if (controlElement) {
          allControls.push(controlElement);
        }
      });
    });
    
    return allControls;
  };

  return (
    <>
      <MapInitializer onMapReady={onMapReady} />
      
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      />
      
      {/* Render các control với offset */}
      {renderControls()}
      
      {/* Component lắng nghe sự kiện */}
      <MapEvents
        onMapClick={onMapClick}
        onMapDrag={onMapDrag}
        onZoomChange={onZoomChange}
      />
      
      {/* Hiển thị markers */}
      {markers.map((marker, index) => (
        <Marker
          key={`marker-${index}`}
          position={[marker.position.lat, marker.position.lng]}
          icon={marker.icon || DefaultIcon}
          draggable={marker.draggable}
          eventHandlers={{
            click: () => {
              if (onMarkerClick) {
                onMarkerClick(marker);
              }
            },
            dragend: (e) => {
              if (marker.draggable && onMarkerDragEnd) {
                const latlng = (e.target as L.Marker).getLatLng();
                onMarkerDragEnd(marker, { lat: latlng.lat, lng: latlng.lng });
              }
            },
          }}
        >
          {(marker.title || marker.description) && (
            <Popup>
              {marker.title && (
                <div className="font-bold">{marker.title}</div>
              )}
              {marker.description && <div>{marker.description}</div>}
            </Popup>
          )}
        </Marker>
      ))}
      
      {/* Hiển thị polylines */}
      {polylines.map((line, index) => (
        <Polyline
          key={`polyline-${index}`}
          positions={line.positions.map((pos) => [pos.lat, pos.lng])}
          color={line.color || 'blue'}
          weight={line.weight || 3}
        />
      ))}
      
      {/* Hiển thị polygons */}
      {polygons.map((polygon, index) => (
        <Polygon
          key={`polygon-${index}`}
          positions={polygon.positions.map((pos) => [pos.lat, pos.lng])}
          color={polygon.color || 'blue'}
          fillColor={polygon.fillColor || polygon.color || 'blue'}
          fillOpacity={polygon.fillOpacity || 0.2}
        />
      ))}
      
      {/* Hiển thị circles */}
      {circles.map((circle, index) => (
        <Circle
          key={`circle-${index}`}
          center={[circle.center.lat, circle.center.lng]}
          radius={circle.radius}
          color={circle.color || 'red'}
        />
      ))}
      
      {/* Hiển thị Hoàng Sa, Trường Sa GeoJSON */}
      {HoangSaTruongSaData && (
        <GeoJSONLayer data={HoangSaTruongSaData} filter={filterFeatures} />
      )}
      
      {/* Children props cho phép thêm các component tùy chỉnh */}
      {children}
    </>
  );
};

// Component chính cho bản đồ
const MapComponent: React.FC<MapComponentProps> = ({
  initialCenter = { lat: 21.028511, lng: 105.804817 }, // Mặc định là Hà Nội
  initialZoom = 13,
  markers = [],
  polylines = [],
  polygons = [],
  circles = [],
  width = '100%',
  height = '500px',
  scrollWheelZoom = true,
  doubleClickZoom = true,
  className = '',
  controls = defaultMapControlsConfig,
  onMapClick,
  onMapDrag,
  onZoomChange,
  onMarkerClick,
  onMarkerDragEnd,
  onLocationFound,
  onMapReady,
  children,
}) => {
  // Use a state variable for the unique map ID
  const [mapId] = useState(() => `map-${Math.random().toString(36).substr(2, 9)}`);
  const [mapMounted, setMapMounted] = useState(false);
  
  const mapContainerRef = useRef<HTMLDivElement>(null);
  
  // Cleanup existing map instances before mounting new ones
  useEffect(() => {
    // Force cleanup of any existing map instances in the DOM
    if (mapContainerRef.current) {
      const existingMaps = mapContainerRef.current.querySelectorAll('.leaflet-container');
      if (existingMaps.length > 0) {
        // Remove all existing Leaflet-related elements
        existingMaps.forEach(map => {
          // Try to access the Leaflet instance and remove it properly
          const leafletInstance = (map as any)._leaflet_id;
          if (leafletInstance) {
            try {
              (window as any).L?.Map?.['_cleanupMap'](leafletInstance);
            } catch (e) {
              console.warn('Could not clean up map instance:', e);
            }
          }
          
          // Remove the element
          map.parentNode?.removeChild(map);
        });
      }
    }
    
    // Set mounted state to false to force re-render of MapContainer
    setMapMounted(false);
    
    // After a small delay, set mounted to true
    const timer = setTimeout(() => {
      setMapMounted(true);
    }, 50);
    
    return () => {
      clearTimeout(timer);
    };
  }, []);
  
  // Memoize merged controls to prevent unnecessary re-renders
  const mergedControls = useMemo(() => ({
    ...defaultMapControlsConfig,
    ...controls,
  }), [controls]);
  
  // Memoize grouped controls to prevent unnecessary re-calculations
  const groupedControls = useMemo(() => 
    groupControlsByPosition(mergedControls),
  [mergedControls]);

  // Style cho container
  const containerStyle = {
    width,
    height,
    position: 'relative',
    zIndex: 10,
  };

  // Hàm lọc tính năng GeoJSON
  const filterFeatures = (feature: any) => {
    if (!feature.properties || !feature.properties.region) return true;
    return true; // Hiển thị tất cả các vùng
  };
  
  return (
    <div
      className={`map-component-container ${className}`}
      style={containerStyle as React.CSSProperties}
      ref={mapContainerRef}
    >
      {/* Add global styles only once */}
      <MapStyles />
      
      {/* Only render the MapContainer when it's safe to do so */}
      {mapMounted && (
        <MapContainer
          key={mapId}
          id={mapId}
          className={`map-instance-${mapId}`}
          center={[initialCenter.lat, initialCenter.lng]}
          zoom={initialZoom}
          scrollWheelZoom={scrollWheelZoom}
          doubleClickZoom={doubleClickZoom}
          style={{ width: '100%', height: '100%' }}
          zoomControl={false}
          attributionControl={false}
          minZoom={3}
        >
          <MapContent
            markers={markers}
            polylines={polylines}
            polygons={polygons}
            circles={circles}
            onMapClick={onMapClick}
            onMapDrag={onMapDrag}
            onZoomChange={onZoomChange}
            onMarkerClick={onMarkerClick}
            onMarkerDragEnd={onMarkerDragEnd}
            onLocationFound={onLocationFound}
            onMapReady={onMapReady}
            mapId={mapId}
            mergedControls={mergedControls}
            groupedControls={groupedControls}
            filterFeatures={filterFeatures}
            children={children}
          />
        </MapContainer>
      )}
    </div>
  );
};

export default MapComponent;
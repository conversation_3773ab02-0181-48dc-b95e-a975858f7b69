import { createSlice, PayloadAction, Draft } from '@reduxjs/toolkit';
import { IEdTechRenderParam } from '../../../interfaces/AppComponents';
import { applyTempToData, setId } from './createTreeDataSlice';
import pubsub from '../../../utils/pubsub';
import { EventChange } from '../../../constants/eventChange';
import { EdTechRootState } from '../../store';

// Define the slice state interface
export interface IEdComponentParamsState {
  id: string;
  data: IEdTechRenderParam[];
  loading: boolean;
  error: string | null;
}

// Initial state
export const initialParamsState: IEdComponentParamsState = {
  id: '',
  data: [],
  loading: false,
  error: null,
};

/**
 * Common function publish to update EdComponentParams
 * @description This function is used to publish an update event for EdComponentParams.
 * IF not isEditing or forceUpdate is true, it will publish the update event.
 * @param state - The current state of the params slice
 * @param forceUpdate - Flag to force update the component
 * @returns void
 */
export const publishUpdateEdComponentParams = (
  state: IEdComponentParamsState,
  forceUpdate: boolean = false
) => {
  // Use setTimeout to defer the execution outside of the reducer cycle
  if (forceUpdate) {
    pubsub.publish(EventChange.EdTechParamsChange, {
      id: state.id,
      data: state.data,
    });
  }
};

/**
 * Common reducer functions for params slices
 */
export const paramReducers = {
  // Set all parameters
  setParams: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<IEdTechRenderParam[]>
  ) => {
    state.data = action.payload;
  },

  // Add a single parameter
  addParam: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<IEdTechRenderParam>
  ) => {
    if (!state.data) state.data = [];
    state.data.push(action.payload);
    publishUpdateEdComponentParams(state);
  },

  // Update a specific parameter by id
  updateParam: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<IEdTechRenderParam>
  ) => {
    const tempIndex = state.data.findIndex(
      (param) => param.id === action.payload.id
    );
    if (tempIndex !== -1) {
      state.data[tempIndex] = action.payload;
    }
    publishUpdateEdComponentParams(state);
  },

  // Add or update a parameter
  addOrUpdateParam: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<IEdTechRenderParam>
  ) => {
    if (!state.data) state.data = [];
    const tempIndex = state.data.findIndex(
      (param) => param.id === action.payload.id
    );
    if (tempIndex !== -1) {
      state.data[tempIndex] = action.payload;
    } else {
      state.data.push(action.payload);
    }
    publishUpdateEdComponentParams(state);
  },

  // Remove a parameter by id
  removeParam: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<{ id: string }>
  ) => {
    state.data = state.data.filter((param) => param.id !== action.payload.id);
    publishUpdateEdComponentParams(state);
  },

  // Clear all parameters
  clearParams: (state: Draft<IEdComponentParamsState>) => {
    state.data = [];
    state.id = '';
  },

  // Set loading state
  setLoading: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<boolean>
  ) => {
    state.loading = action.payload;
  },

  // Set error state
  setError: (
    state: Draft<IEdComponentParamsState>,
    action: PayloadAction<string | null>
  ) => {
    state.error = action.payload;
  },
};

/**
 * Factory function to create a params slice with common reducers
 * @param sliceName - Name of the slice
 * @returns A configured slice object with common reducers
 */
export function createParamsSlice(sliceName: string) {
  return createSlice({
    name: sliceName,
    initialState: initialParamsState,
    reducers: paramReducers,
    extraReducers: (builder) => {
      builder.addCase(applyTempToData.fulfilled, (state, action: any) => {
        const payload = action.payload as EdTechRootState;
        let dataTreeTemp = '';
        if (payload.edTechRenderTreeData.data) {
          dataTreeTemp = JSON.stringify(payload.edTechRenderTreeData.data);
        }

        const dataFilter = state.data?.filter((param) => {
          return dataTreeTemp.includes(param.id);
        });

        state.data = dataFilter;
        publishUpdateEdComponentParams(state, true);
      });
      builder.addCase(setId, (state, action: any) => {
        state.id = action.payload;
      });
    },
  });
}

/**
 * Create selector for all params
 * @param stateKey - Key in the Redux state where this slice is stored
 * @returns A selector function for all params
 */
export function createSelectAllParamsSelector(stateKey: string) {
  return (state: any) => state[stateKey]?.data || [];
}

/**
 * Create selector for finding param by id
 * @param stateKey - Key in the Redux state where this slice is stored
 * @returns A selector function for finding param by id
 */
export function createSelectParamByIdSelector(stateKey: string) {
  return (state: any, id: string) =>
    state[stateKey]?.data?.find((param: IEdTechRenderParam) => param.id === id);
}

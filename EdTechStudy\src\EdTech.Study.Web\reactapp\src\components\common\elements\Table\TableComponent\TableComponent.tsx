// TableComponent.tsx
import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  ColorPicker,
  InputNumber,
  Popover,
  Modal,
  Input,
  message,
  Select,
} from 'antd';
import { EngineContainer } from '../../../engines';
import TooltipAntdCustom from '../../../../customs/antd/TooltipAntdCustom';
import {
  TableOutlined,
  FontSizeOutlined,
  PlusOutlined,
  MinusOutlined,
  MergeCellsOutlined,
  SplitCellsOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  BorderVerticleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import './TableComponentStyles.css';
import { IEdTechRenderProps } from '../../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { z } from 'zod';
import { createComponentSchema } from '../../../../../utils/schema/createComponentSchema';
import { DeleteIcon } from '../../../../icons/IconIndex';

// Define types for table cells
interface TableCell {
  id: string;
  content: string;
  backgroundColor?: string;
  textColor?: string;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold';
  fontStyle?: 'normal' | 'italic';
  textAlign?: 'left' | 'center' | 'right';
  verticalAlign?: 'top' | 'middle' | 'bottom';
  colSpan?: number;
  rowSpan?: number;
  borderTop?: string;
  borderRight?: string;
  borderBottom?: string;
  borderLeft?: string;
}

// Define table data structure
interface TableData {
  rows: number;
  columns: number;
  cells: { [key: string]: TableCell };
  tableWidth?: string | number;
  tableBorderColor?: string;
  tableBorderWidth?: number;
  tableBackgroundColor?: string;
  cellPadding?: number;
  cellSpacing?: number;
  tableLayout?: 'auto' | 'fixed';
  tableStyle?: 'default' | 'striped' | 'bordered' | 'minimal';
}

export interface TableComponentProps {
  tableData?: TableData;
}

// Define default table data
const createDefaultTableData = (rows = 3, columns = 3): TableData => {
  const cells: { [key: string]: TableCell } = {};

  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < columns; j++) {
      const cellId = `${i}-${j}`;
      cells[cellId] = {
        id: cellId,
        content: '',
        backgroundColor: i === 0 ? '#f0f0f0' : '#ffffff',
        textColor: '#000000',
        fontSize: 14,
        fontWeight: i === 0 ? 'bold' : 'normal',
        fontStyle: 'normal',
        textAlign: 'left',
        verticalAlign: 'middle',
        colSpan: 1,
        rowSpan: 1,
      };
    }
  }

  return {
    rows,
    columns,
    cells,
    tableWidth: '100%',
    tableBorderColor: '#d9d9d9',
    tableBorderWidth: 1,
    tableBackgroundColor: '#ffffff',
    cellPadding: 12, // Increased padding for better content spacing
    cellSpacing: 0,
    tableLayout: 'fixed',
    tableStyle: 'default',
  };
};

const TableComponent: React.FC<IEdTechRenderProps<TableComponentProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Initialize table data from props or default
  const [tableData, setTableData] = useState<TableData>(
    params?.tableData || createDefaultTableData()
  );

  // Selected cell for editing
  const [selectedCell, setSelectedCell] = useState<string | null>(null);

  // Cell editing state
  const [editingContent, setEditingContent] = useState<string>('');
  const [isCellEditing, setIsCellEditing] = useState<boolean>(false);

  // Selection range for operations on multiple cells
  const [selectionStart, setSelectionStart] = useState<string | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<string | null>(null);
  const [selectedCellsInRange, setSelectedCellsInRange] = useState<string[]>(
    []
  );
  const [isSelecting, setIsSelecting] = useState<boolean>(false);

  // Table creation modal
  const [tableCreationVisible, setTableCreationVisible] =
    useState<boolean>(false);
  const [newTableRows, setNewTableRows] = useState<number>(3);
  const [newTableColumns, setNewTableColumns] = useState<number>(3);

  // Refs
  const tableRef = useRef<HTMLTableElement>(null);
  const selectedCellRef = useRef<HTMLTableCellElement>(null);

  // Update tableData from props when they change
  useEffect(() => {
    if (params?.tableData) {
      setTableData(params.tableData);
    }
  }, [params?.tableData]);

  // Xóa useEffect để tránh vòng lặp vô hạn

  // Save tableData to component params
  const updateTableData = (newData: TableData) => {
    setTableData(newData);
    if (addOrUpdateParamComponent) {
      addOrUpdateParamComponent({
        tableData: newData,
      });
    }
  };

  // Create a new table
  const createNewTable = () => {
    const newTableData = createDefaultTableData(newTableRows, newTableColumns);
    updateTableData(newTableData);
    setTableCreationVisible(false);
  };

  // Handle cell click for selection and editing
  const handleCellClick = (cellId: string) => {
    try {
      if (!isEditing) return;

      if (!tableData.cells[cellId]) {
        return;
      }

      // Đóng chế độ chỉnh sửa nếu đang chuyển sang ô khác
      if (isCellEditing && selectedCell && selectedCell !== cellId) {
        setIsCellEditing(false);
      }

      setSelectedCell(cellId);
      setEditingContent(tableData.cells[cellId]?.content || '');

      // Bắt đầu chế độ chỉnh sửa ngay khi click vào ô
      setIsCellEditing(true);

      // Xử lý sự kiện click ra ngoài bảng
      const handleDocumentClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        const isClickOutsideTable = !tableRef.current?.contains(target);

        if (isClickOutsideTable) {
          setIsCellEditing(false);
          document.removeEventListener('mousedown', handleDocumentClick);
        }
      };

      document.addEventListener('mousedown', handleDocumentClick);
    } catch (error) {}
  };

  // Handle cell double click (now just calls handleCellClick for consistency)
  const handleCellDoubleClick = (cellId: string) => {
    // Sử dụng lại hàm handleCellClick để đảm bảo tính nhất quán
    handleCellClick(cellId);
  };

  // Close cell editing
  const closeCellEditing = () => {
    try {
      // Delay để tránh xung đột giữa các sự kiện
      setTimeout(() => {
        setIsCellEditing(false);
      }, 10);
    } catch (error) {
      setIsCellEditing(false);
    }
  };

  // Update cell content
  const updateCellContent = (cellId: string, content: string) => {
    try {
      const newTableData = JSON.parse(JSON.stringify(tableData));

      if (!newTableData.cells[cellId]) {
        return;
      }
      newTableData.cells[cellId] = {
        ...newTableData.cells[cellId],
        content,
      };

      updateTableData(newTableData);
    } catch (error) {}
  };

  // Apply style to a cell
  const applyStyleToCell = (cellId: string, style: Partial<TableCell>) => {
    try {
      const newTableData = JSON.parse(JSON.stringify(tableData));

      if (!newTableData.cells[cellId]) {
        return;
      }
      newTableData.cells[cellId] = {
        ...newTableData.cells[cellId],
        ...style,
      };

      updateTableData(newTableData);
    } catch (error) {}
  };

  // Calculate selected cells range without updating state
  const calculateSelectedCellsRange = () => {
    if (!selectionStart || !selectionEnd) return [];

    const [startRow, startCol] = selectionStart.split('-').map(Number);
    const [endRow, endCol] = selectionEnd.split('-').map(Number);

    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);

    const selectedCells: string[] = [];

    for (let i = minRow; i <= maxRow; i++) {
      for (let j = minCol; j <= maxCol; j++) {
        const cellId = `${i}-${j}`;
        if (tableData.cells[cellId]) {
          selectedCells.push(cellId);
        }
      }
    }

    return selectedCells;
  };

  // Hàm getSelectedCellsRange đã được thay thế bằng calculateSelectedCellsRange để tránh vòng lặp vô hạn

  // Apply style to multiple cells
  const applyStyleToSelectedCells = (style: Partial<TableCell>) => {
    try {
      // Sử dụng calculateSelectedCellsRange thay vì getSelectedCellsRange để tránh cập nhật state
      const selectedCells = calculateSelectedCellsRange();
      if (selectedCells.length === 0 && selectedCell) {
        // If no range selected, apply to the currently selected cell
        applyStyleToCell(selectedCell, style);
        return;
      }

      const newTableData = JSON.parse(JSON.stringify(tableData));

      selectedCells.forEach((cellId) => {
        if (!newTableData.cells[cellId]) {
          return;
        }
        newTableData.cells[cellId] = {
          ...newTableData.cells[cellId],
          ...style,
        };
      });

      updateTableData(newTableData);
    } catch (error) {}
  };

  // Add row
  const addRow = (afterRow: number) => {
    const newTableData = { ...tableData };
    const newRowIndex = afterRow + 1;

    // Shift cell IDs for rows below the new row
    const updatedCells: { [key: string]: TableCell } = {};

    Object.keys(newTableData.cells).forEach((cellId) => {
      const [row, col] = cellId.split('-').map(Number);

      if (row < newRowIndex) {
        // Cells above the new row remain unchanged
        updatedCells[cellId] = newTableData.cells[cellId];
      } else {
        // Shift cells below the new row down by one
        const newCellId = `${row + 1}-${col}`;
        updatedCells[newCellId] = {
          ...newTableData.cells[cellId],
          id: newCellId,
        };
      }
    });

    // Create cells for the new row
    for (let col = 0; col < newTableData.columns; col++) {
      const newCellId = `${newRowIndex}-${col}`;
      updatedCells[newCellId] = {
        id: newCellId,
        content: '',
        backgroundColor: '#ffffff',
        textColor: '#000000',
        fontSize: 14,
        fontWeight: 'normal',
        fontStyle: 'normal',
        textAlign: 'left',
        verticalAlign: 'middle',
        colSpan: 1,
        rowSpan: 1,
      };
    }

    newTableData.cells = updatedCells;
    newTableData.rows += 1;

    updateTableData(newTableData);
  };

  // Add column
  const addColumn = (afterCol: number) => {
    const newTableData = { ...tableData };
    const newColIndex = afterCol + 1;

    // Shift cell IDs for columns to the right of the new column
    const updatedCells: { [key: string]: TableCell } = {};

    Object.keys(newTableData.cells).forEach((cellId) => {
      const [row, col] = cellId.split('-').map(Number);

      if (col < newColIndex) {
        // Cells to the left of the new column remain unchanged
        updatedCells[cellId] = newTableData.cells[cellId];
      } else {
        // Shift cells to the right of the new column by one
        const newCellId = `${row}-${col + 1}`;
        updatedCells[newCellId] = {
          ...newTableData.cells[cellId],
          id: newCellId,
        };
      }
    });

    // Create cells for the new column
    for (let row = 0; row < newTableData.rows; row++) {
      const newCellId = `${row}-${newColIndex}`;
      updatedCells[newCellId] = {
        id: newCellId,
        content: '',
        backgroundColor: row === 0 ? '#f0f0f0' : '#ffffff',
        textColor: '#000000',
        fontSize: 14,
        fontWeight: row === 0 ? 'bold' : 'normal',
        fontStyle: 'normal',
        textAlign: 'left',
        verticalAlign: 'middle',
        colSpan: 1,
        rowSpan: 1,
      };
    }

    newTableData.cells = updatedCells;
    newTableData.columns += 1;

    updateTableData(newTableData);
  };

  // Delete row
  const deleteRow = (rowIndex: number) => {
    if (tableData.rows <= 1) {
      message.error('Không thể xóa hàng duy nhất');
      return;
    }

    const newTableData = { ...tableData };
    const updatedCells: { [key: string]: TableCell } = {};

    Object.keys(newTableData.cells).forEach((cellId) => {
      const [row, col] = cellId.split('-').map(Number);

      if (row < rowIndex) {
        // Cells above the deleted row remain unchanged
        updatedCells[cellId] = newTableData.cells[cellId];
      } else if (row > rowIndex) {
        // Shift cells below the deleted row up by one
        const newCellId = `${row - 1}-${col}`;
        updatedCells[newCellId] = {
          ...newTableData.cells[cellId],
          id: newCellId,
        };
      }
      // Cells in the deleted row are not copied
    });

    newTableData.cells = updatedCells;
    newTableData.rows -= 1;

    updateTableData(newTableData);
    setSelectedCell(null);
  };

  // Delete column
  const deleteColumn = (colIndex: number) => {
    if (tableData.columns <= 1) {
      message.error('Không thể xóa cột duy nhất');
      return;
    }

    const newTableData = { ...tableData };
    const updatedCells: { [key: string]: TableCell } = {};

    Object.keys(newTableData.cells).forEach((cellId) => {
      const [row, col] = cellId.split('-').map(Number);

      if (col < colIndex) {
        // Cells to the left of the deleted column remain unchanged
        updatedCells[cellId] = newTableData.cells[cellId];
      } else if (col > colIndex) {
        // Shift cells to the right of the deleted column left by one
        const newCellId = `${row}-${col - 1}`;
        updatedCells[newCellId] = {
          ...newTableData.cells[cellId],
          id: newCellId,
        };
      }
      // Cells in the deleted column are not copied
    });

    newTableData.cells = updatedCells;
    newTableData.columns -= 1;

    updateTableData(newTableData);
    setSelectedCell(null);
  };

  // Update table properties
  const updateTableProperties = (properties: Partial<TableData>) => {
    const newTableData = { ...tableData, ...properties };
    updateTableData(newTableData);
  };

  // Apply table style
  const applyTableStyle = (style: string) => {
    let newTableData = { ...tableData };

    switch (style) {
      case 'default':
        newTableData = {
          ...newTableData,
          tableBorderColor: '#d9d9d9',
          tableBorderWidth: 1,
          tableBackgroundColor: '#ffffff',
          cellPadding: 8,
          tableStyle: 'default',
        };
        break;
      case 'striped':
        newTableData = {
          ...newTableData,
          tableBorderColor: '#d9d9d9',
          tableBorderWidth: 1,
          tableBackgroundColor: '#ffffff',
          cellPadding: 8,
          tableStyle: 'striped',
        };

        // Apply striped rows
        Object.keys(newTableData.cells).forEach((cellId) => {
          const [row, _] = cellId.split('-').map(Number);
          if (row === 0) {
            // Header row
            newTableData.cells[cellId] = {
              ...newTableData.cells[cellId],
              backgroundColor: '#f5f5f5',
              fontWeight: 'bold',
            };
          } else if (row % 2 === 1) {
            // Odd rows
            newTableData.cells[cellId] = {
              ...newTableData.cells[cellId],
              backgroundColor: '#fafafa',
            };
          } else {
            // Even rows
            newTableData.cells[cellId] = {
              ...newTableData.cells[cellId],
              backgroundColor: '#ffffff',
            };
          }
        });
        break;
      case 'bordered':
        newTableData = {
          ...newTableData,
          tableBorderColor: '#000000',
          tableBorderWidth: 1,
          tableBackgroundColor: '#ffffff',
          cellPadding: 8,
          tableStyle: 'bordered',
        };
        break;
      case 'minimal':
        newTableData = {
          ...newTableData,
          tableBorderColor: '#f0f0f0',
          tableBorderWidth: 1,
          tableBackgroundColor: '#ffffff',
          cellPadding: 8,
          tableStyle: 'minimal',
        };

        // Apply minimal styling
        Object.keys(newTableData.cells).forEach((cellId) => {
          const [row, _] = cellId.split('-').map(Number);
          if (row === 0) {
            // Header row
            newTableData.cells[cellId] = {
              ...newTableData.cells[cellId],
              backgroundColor: '#ffffff',
              fontWeight: 'bold',
              borderBottom: '2px solid #f0f0f0',
            };
          } else {
            // Data rows
            newTableData.cells[cellId] = {
              ...newTableData.cells[cellId],
              backgroundColor: '#ffffff',
              borderTop: 'none',
              borderLeft: 'none',
              borderRight: 'none',
              borderBottom: '1px solid #f0f0f0',
            };
          }
        });
        break;
    }

    updateTableData(newTableData);
  };

  // Merge selected cells
  const mergeCells = () => {
    // Sử dụng calculateSelectedCellsRange thay vì getSelectedCellsRange để tránh cập nhật state
    const selectedCells = calculateSelectedCellsRange();
    if (selectedCells.length <= 1) {
      message.info('Chọn nhiều ô để thực hiện gộp ô');
      return;
    }

    // Find the bounds of the selection
    const cellCoords = selectedCells.map((cellId) => {
      const [row, col] = cellId.split('-').map(Number);
      return { row, col };
    });

    const minRow = Math.min(...cellCoords.map((c) => c.row));
    const maxRow = Math.max(...cellCoords.map((c) => c.row));
    const minCol = Math.min(...cellCoords.map((c) => c.col));
    const maxCol = Math.max(...cellCoords.map((c) => c.col));

    const rowSpan = maxRow - minRow + 1;
    const colSpan = maxCol - minCol + 1;

    // Get the top-left cell
    const topLeftCellId = `${minRow}-${minCol}`;

    // Collect all content from selected cells
    const combinedContent = selectedCells
      .map((cellId) => tableData.cells[cellId]?.content || '')
      .filter(Boolean)
      .join(' ');

    // Create a deep copy of tableData to avoid modifying read-only objects
    const newTableData = JSON.parse(JSON.stringify(tableData));

    // Update the top-left cell with rowSpan and colSpan
    newTableData.cells[topLeftCellId] = {
      ...newTableData.cells[topLeftCellId],
      rowSpan,
      colSpan,
      content: combinedContent,
    };

    // Mark other cells as hidden
    selectedCells.forEach((cellId) => {
      if (cellId !== topLeftCellId) {
        newTableData.cells[cellId] = {
          ...newTableData.cells[cellId],
          rowSpan: 0,
          colSpan: 0,
        };
      }
    });

    updateTableData(newTableData);
    setSelectionStart(null);
    setSelectionEnd(null);
    setSelectedCell(topLeftCellId);
  };

  // Split a merged cell
  const splitCell = (cellId: string) => {
    const cell = tableData.cells[cellId];
    if (!cell || ((cell.rowSpan || 1) <= 1 && (cell.colSpan || 1) <= 1)) {
      message.info('Ô này không phải là ô đã gộp');
      return;
    }

    const [row, col] = cellId.split('-').map(Number);
    // Create a deep copy of tableData to avoid modifying read-only objects
    const newTableData = JSON.parse(JSON.stringify(tableData));

    // Reset the target cell
    newTableData.cells[cellId] = {
      ...newTableData.cells[cellId],
      rowSpan: 1,
      colSpan: 1,
    };

    // Reset all cells within the merged area
    for (let i = 0; i < (cell.rowSpan || 1); i++) {
      for (let j = 0; j < (cell.colSpan || 1); j++) {
        if (i === 0 && j === 0) continue; // Skip the target cell

        const currentCellId = `${row + i}-${col + j}`;
        newTableData.cells[currentCellId] = {
          ...newTableData.cells[currentCellId],
          rowSpan: 1,
          colSpan: 1,
          content: '',
        };
      }
    }

    updateTableData(newTableData);
  };

  // Render table controls
  const renderTableControls = () => {
    if (!isEditing) return null;

    return (
      <div className="table-controls tailwind-bg-gray-50 tailwind-p-2 tailwind-rounded-md tailwind-mb-4 tailwind-border tailwind-border-gray-200">
        {/* Phần chứa các công cụ */}
        <div className="tailwind-flex tailwind-flex-wrap tailwind-justify-between tailwind-gap-2">
          {/* Phần công cụ chính */}
          <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1">
            <TooltipAntdCustom title="Tạo bảng mới">
              <Button
                icon={<TableOutlined />}
                onClick={() => setTableCreationVisible(true)}
              />
            </TooltipAntdCustom>

            <TooltipAntdCustom title="Thêm hàng">
              <Button
                icon={<PlusOutlined />}
                onClick={() => {
                  if (selectedCell) {
                    const [row, _] = selectedCell.split('-').map(Number);
                    addRow(row);
                  } else {
                    addRow(tableData.rows - 1);
                  }
                }}
              />
            </TooltipAntdCustom>

            <TooltipAntdCustom title="Thêm cột">
              <Button
                icon={<BorderVerticleOutlined />}
                onClick={() => {
                  if (selectedCell) {
                    const [_, col] = selectedCell.split('-').map(Number);
                    addColumn(col);
                  } else {
                    addColumn(tableData.columns - 1);
                  }
                }}
              />
            </TooltipAntdCustom>

            <TooltipAntdCustom title="Xóa hàng">
              <Button
                icon={<MinusOutlined />}
                disabled={!selectedCell}
                onClick={() => {
                  if (selectedCell) {
                    const [row, _] = selectedCell.split('-').map(Number);
                    deleteRow(row);
                  }
                }}
              />
            </TooltipAntdCustom>

            <TooltipAntdCustom title="Xóa cột">
              <Button
                icon={<DeleteIcon />}
                disabled={!selectedCell}
                onClick={() => {
                  if (selectedCell) {
                    const [_, col] = selectedCell.split('-').map(Number);
                    deleteColumn(col);
                  }
                }}
              />
            </TooltipAntdCustom>

            <div className="tailwind-border-l tailwind-border-gray-300 tailwind-mx-2" />

            <TooltipAntdCustom title="Gộp ô">
              <Button
                icon={<MergeCellsOutlined />}
                onClick={mergeCells}
                disabled={selectedCellsInRange.length <= 1}
              />
            </TooltipAntdCustom>

            <TooltipAntdCustom title="Tách ô">
              <Button
                icon={<SplitCellsOutlined />}
                onClick={() => selectedCell && splitCell(selectedCell)}
                disabled={
                  !selectedCell ||
                  ((tableData.cells[selectedCell]?.rowSpan || 1) <= 1 &&
                    (tableData.cells[selectedCell]?.colSpan || 1) <= 1)
                }
              />
            </TooltipAntdCustom>
            <TooltipAntdCustom title="Cài đặt bảng">
              <Popover
                title="Kiểu bảng"
                trigger="click"
                content={
                  <div className="tailwind-w-64">
                    <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-2">
                      <div
                        className="tailwind-p-2 tailwind-border tailwind-border-gray-200 tailwind-rounded tailwind-cursor-pointer hover:tailwind-bg-gray-50"
                        onClick={() => applyTableStyle('default')}
                      >
                        <div className="tailwind-text-xs tailwind-font-medium tailwind-mb-1">
                          Mặc định
                        </div>
                        <div className="tailwind-bg-white tailwind-border tailwind-border-gray-300 tailwind-h-10"></div>
                      </div>

                      <div
                        className="tailwind-p-2 tailwind-border tailwind-border-gray-200 tailwind-rounded tailwind-cursor-pointer hover:tailwind-bg-gray-50"
                        onClick={() => applyTableStyle('striped')}
                      >
                        <div className="tailwind-text-xs tailwind-font-medium tailwind-mb-1">
                          Sọc ngang
                        </div>
                        <div className="tailwind-border tailwind-border-gray-300">
                          <div className="tailwind-bg-gray-100 tailwind-h-3"></div>
                          <div className="tailwind-bg-white tailwind-h-2"></div>
                          <div className="tailwind-bg-gray-50 tailwind-h-2"></div>
                          <div className="tailwind-bg-white tailwind-h-2"></div>
                        </div>
                      </div>

                      <div
                        className="tailwind-p-2 tailwind-border tailwind-border-gray-200 tailwind-rounded tailwind-cursor-pointer hover:tailwind-bg-gray-50"
                        onClick={() => applyTableStyle('bordered')}
                      >
                        <div className="tailwind-text-xs tailwind-font-medium tailwind-mb-1">
                          Viền đậm
                        </div>
                        <div className="tailwind-bg-white tailwind-border-2 tailwind-border-gray-600 tailwind-h-10"></div>
                      </div>

                      <div
                        className="tailwind-p-2 tailwind-border tailwind-border-gray-200 tailwind-rounded tailwind-cursor-pointer hover:tailwind-bg-gray-50"
                        onClick={() => applyTableStyle('minimal')}
                      >
                        <div className="tailwind-text-xs tailwind-font-medium tailwind-mb-1">
                          Tối giản
                        </div>
                        <div className="tailwind-bg-white tailwind-h-10">
                          <div className="tailwind-h-3 tailwind-border-b-2 tailwind-border-gray-300"></div>
                          <div className="tailwind-h-6 tailwind-border-b tailwind-border-gray-200"></div>
                        </div>
                      </div>
                    </div>

                    <div className="tailwind-mt-4">
                      <div className="tailwind-mb-2">
                        <div className="tailwind-text-xs tailwind-mb-1">
                          Màu viền
                        </div>
                        <ColorPicker
                          showText
                          value={tableData.tableBorderColor}
                          onChange={(color) =>
                            updateTableProperties({
                              tableBorderColor: color.toHexString(),
                            })
                          }
                        />
                      </div>

                      <div className="tailwind-mb-2">
                        <div className="tailwind-text-xs tailwind-mb-1">
                          Độ rộng viền (px)
                        </div>
                        <InputNumber
                          className="tailwind-w-full"
                          size="small"
                          min={0}
                          max={5}
                          value={tableData.tableBorderWidth}
                          onChange={(value) =>
                            value !== null &&
                            updateTableProperties({ tableBorderWidth: value })
                          }
                        />
                      </div>

                      <div className="tailwind-mb-2">
                        <div className="tailwind-text-xs tailwind-mb-1">
                          Khoảng cách đệm (px)
                        </div>
                        <InputNumber
                          className="tailwind-w-full"
                          size="small"
                          min={0}
                          max={20}
                          value={tableData.cellPadding}
                          onChange={(value) =>
                            value !== null &&
                            updateTableProperties({ cellPadding: value })
                          }
                        />
                      </div>

                      <div className="tailwind-mb-2">
                        <div className="tailwind-text-xs tailwind-mb-1">
                          Độ rộng bảng
                        </div>
                        <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                          <Select
                            className="tailwind-flex-1"
                            size="small"
                            value={
                              typeof tableData.tableWidth === 'string'
                                ? tableData.tableWidth
                                : 'custom'
                            }
                            onChange={(value) => {
                              if (value === 'custom') {
                                updateTableProperties({ tableWidth: 600 });
                              } else {
                                updateTableProperties({ tableWidth: value });
                              }
                            }}
                            options={[
                              { value: '100%', label: 'Toàn màn hình' },
                              { value: '75%', label: '75%' },
                              { value: '50%', label: '50%' },
                              { value: 'custom', label: 'Tùy chỉnh' },
                            ]}
                          />

                          {typeof tableData.tableWidth === 'number' && (
                            <InputNumber
                              size="small"
                              min={200}
                              max={2000}
                              value={tableData.tableWidth}
                              onChange={(value) =>
                                value !== null &&
                                updateTableProperties({ tableWidth: value })
                              }
                              addonAfter="px"
                            />
                          )}
                        </div>
                      </div>

                      <div className="tailwind-mb-2">
                        <div className="tailwind-text-xs tailwind-mb-1">
                          Bố cục bảng
                        </div>
                        <Select
                          className="tailwind-w-full"
                          size="small"
                          value={tableData.tableLayout}
                          onChange={(value) =>
                            updateTableProperties({ tableLayout: value })
                          }
                          options={[
                            { value: 'auto', label: 'Tự động' },
                            { value: 'fixed', label: 'Cố định' },
                          ]}
                        />
                      </div>
                    </div>
                  </div>
                }
              >
                <Button icon={<SettingOutlined />} />
              </Popover>
            </TooltipAntdCustom>

            <div className="tailwind-border-l tailwind-border-gray-300 tailwind-mx-2" />

            <TooltipAntdCustom title="Căn trái">
              <Button
                icon={<AlignLeftOutlined />}
                onClick={() => applyStyleToSelectedCells({ textAlign: 'left' })}
              />
            </TooltipAntdCustom>
            <TooltipAntdCustom title="Căn giữa">
              <Button
                icon={<AlignCenterOutlined />}
                onClick={() =>
                  applyStyleToSelectedCells({ textAlign: 'center' })
                }
              />
            </TooltipAntdCustom>
            <TooltipAntdCustom title="Căn phải">
              <Button
                icon={<AlignRightOutlined />}
                onClick={() =>
                  applyStyleToSelectedCells({ textAlign: 'right' })
                }
              />
            </TooltipAntdCustom>
            <TooltipAntdCustom title="In đậm">
              <Button
                onClick={() => {
                  const firstCell = selectedCellsInRange[0];
                  const currentWeight = tableData.cells[firstCell]?.fontWeight;
                  const newWeight =
                    currentWeight === 'bold' ? 'normal' : 'bold';
                  applyStyleToSelectedCells({ fontWeight: newWeight });
                }}
              >
                B
              </Button>
            </TooltipAntdCustom>
            <TooltipAntdCustom title="In nghiêng">
              <Button
                onClick={() => {
                  const firstCell = selectedCellsInRange[0];
                  const currentStyle = tableData.cells[firstCell]?.fontStyle;
                  const newStyle =
                    currentStyle === 'italic' ? 'normal' : 'italic';
                  applyStyleToSelectedCells({ fontStyle: newStyle });
                }}
              >
                I
              </Button>
            </TooltipAntdCustom>
            <TooltipAntdCustom title="Màu nền">
              <ColorPicker
                showText={false}
                onChange={(color) =>
                  applyStyleToSelectedCells({
                    backgroundColor: color.toHexString(),
                  })
                }
              />
            </TooltipAntdCustom>
            <TooltipAntdCustom title="Màu chữ">
              <ColorPicker
                showText={false}
                onChange={(color) =>
                  applyStyleToSelectedCells({ textColor: color.toHexString() })
                }
              />
            </TooltipAntdCustom>

            <TooltipAntdCustom title="Định dạng ô">
              <Popover
                title="Định dạng ô"
                trigger="click"
                content={
                  <div className="tailwind-w-64">
                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Căn chỉnh ngang
                      </div>
                      <div className="tailwind-flex tailwind-gap-1">
                        <Button
                          size="small"
                          icon={<AlignLeftOutlined />}
                          type={
                            selectedCell &&
                            tableData.cells[selectedCell]?.textAlign === 'left'
                              ? 'primary'
                              : 'default'
                          }
                          onClick={() =>
                            selectedCell &&
                            applyStyleToSelectedCells({ textAlign: 'left' })
                          }
                        />
                        <Button
                          size="small"
                          icon={<AlignCenterOutlined />}
                          type={
                            selectedCell &&
                            tableData.cells[selectedCell]?.textAlign ===
                              'center'
                              ? 'primary'
                              : 'default'
                          }
                          onClick={() =>
                            selectedCell &&
                            applyStyleToSelectedCells({ textAlign: 'center' })
                          }
                        />
                        <Button
                          size="small"
                          icon={<AlignRightOutlined />}
                          type={
                            selectedCell &&
                            tableData.cells[selectedCell]?.textAlign === 'right'
                              ? 'primary'
                              : 'default'
                          }
                          onClick={() =>
                            selectedCell &&
                            applyStyleToSelectedCells({ textAlign: 'right' })
                          }
                        />
                      </div>
                    </div>

                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Căn chỉnh dọc
                      </div>
                      <Select
                        className="tailwind-w-full"
                        size="small"
                        value={
                          selectedCell
                            ? tableData.cells[selectedCell]?.verticalAlign
                            : 'middle'
                        }
                        onChange={(value) =>
                          selectedCell &&
                          applyStyleToSelectedCells({ verticalAlign: value })
                        }
                        options={[
                          { value: 'top', label: 'Trên' },
                          { value: 'middle', label: 'Giữa' },
                          { value: 'bottom', label: 'Dưới' },
                        ]}
                      />
                    </div>

                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Màu nền
                      </div>
                      <ColorPicker
                        showText
                        value={
                          selectedCell
                            ? tableData.cells[selectedCell]?.backgroundColor
                            : '#ffffff'
                        }
                        onChange={(color) =>
                          selectedCell &&
                          applyStyleToSelectedCells({
                            backgroundColor: color.toHexString(),
                          })
                        }
                      />
                    </div>

                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Màu chữ
                      </div>
                      <ColorPicker
                        showText
                        value={
                          selectedCell
                            ? tableData.cells[selectedCell]?.textColor
                            : '#000000'
                        }
                        onChange={(color) =>
                          selectedCell &&
                          applyStyleToSelectedCells({
                            textColor: color.toHexString(),
                          })
                        }
                      />
                    </div>

                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Cỡ chữ
                      </div>
                      <InputNumber
                        className="tailwind-w-full"
                        size="small"
                        min={8}
                        max={36}
                        value={
                          selectedCell
                            ? tableData.cells[selectedCell]?.fontSize
                            : 14
                        }
                        onChange={(value) =>
                          selectedCell &&
                          value &&
                          applyStyleToSelectedCells({ fontSize: value })
                        }
                        addonAfter="px"
                      />
                    </div>

                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Kiểu chữ
                      </div>
                      <div className="tailwind-flex tailwind-gap-1">
                        <Button
                          size="small"
                          type={
                            selectedCell &&
                            tableData.cells[selectedCell]?.fontWeight === 'bold'
                              ? 'primary'
                              : 'default'
                          }
                          onClick={() => {
                            if (selectedCell) {
                              const newWeight =
                                tableData.cells[selectedCell]?.fontWeight ===
                                'bold'
                                  ? 'normal'
                                  : 'bold';
                              applyStyleToSelectedCells({
                                fontWeight: newWeight,
                              });
                            }
                          }}
                        >
                          B
                        </Button>
                        <Button
                          size="small"
                          type={
                            selectedCell &&
                            tableData.cells[selectedCell]?.fontStyle ===
                              'italic'
                              ? 'primary'
                              : 'default'
                          }
                          onClick={() => {
                            if (selectedCell) {
                              const newStyle =
                                tableData.cells[selectedCell]?.fontStyle ===
                                'italic'
                                  ? 'normal'
                                  : 'italic';
                              applyStyleToSelectedCells({
                                fontStyle: newStyle,
                              });
                            }
                          }}
                        >
                          I
                        </Button>
                      </div>
                    </div>

                    <div className="tailwind-mb-2">
                      <div className="tailwind-text-xs tailwind-mb-1">
                        Viền ô
                      </div>
                      <Select
                        className="tailwind-w-full"
                        size="small"
                        placeholder="Kiểu viền"
                        onChange={(value) => {
                          if (!selectedCell) return;

                          const borderStyle = `1px solid ${tableData.tableBorderColor}`;

                          switch (value) {
                            case 'all':
                              applyStyleToSelectedCells({
                                borderTop: borderStyle,
                                borderRight: borderStyle,
                                borderBottom: borderStyle,
                                borderLeft: borderStyle,
                              });
                              break;
                            case 'none':
                              applyStyleToSelectedCells({
                                borderTop: 'none',
                                borderRight: 'none',
                                borderBottom: 'none',
                                borderLeft: 'none',
                              });
                              break;
                            case 'outside':
                              // Outside borders depend on cell position
                              const selectedCells =
                                selectedCellsInRange.length > 0
                                  ? [...selectedCellsInRange]
                                  : [];
                              if (selectedCells.length === 0 && selectedCell) {
                                selectedCells.push(selectedCell);
                              }

                              const newTableData = { ...tableData };

                              // Find min/max row/col in selection
                              const coords = selectedCells.map((id) => {
                                const [row, col] = id.split('-').map(Number);
                                return { row, col };
                              });

                              const minRow = Math.min(
                                ...coords.map((c) => c.row)
                              );
                              const maxRow = Math.max(
                                ...coords.map((c) => c.row)
                              );
                              const minCol = Math.min(
                                ...coords.map((c) => c.col)
                              );
                              const maxCol = Math.max(
                                ...coords.map((c) => c.col)
                              );

                              selectedCells.forEach((cellId) => {
                                const [row, col] = cellId
                                  .split('-')
                                  .map(Number);

                                const cell = { ...newTableData.cells[cellId] };
                                cell.borderTop =
                                  row === minRow ? borderStyle : 'none';
                                cell.borderBottom =
                                  row === maxRow ? borderStyle : 'none';
                                cell.borderLeft =
                                  col === minCol ? borderStyle : 'none';
                                cell.borderRight =
                                  col === maxCol ? borderStyle : 'none';

                                newTableData.cells[cellId] = cell;
                              });

                              updateTableData(newTableData);
                              break;
                            case 'inside':
                              // Inside borders depend on cell position
                              const insideSelectedCells =
                                selectedCellsInRange.length > 0
                                  ? [...selectedCellsInRange]
                                  : [];
                              if (
                                insideSelectedCells.length === 0 &&
                                selectedCell
                              ) {
                                insideSelectedCells.push(selectedCell);
                              }

                              const newInsideTableData = { ...tableData };

                              // Find min/max row/col in selection
                              const insideCoords = insideSelectedCells.map(
                                (id) => {
                                  const [row, col] = id.split('-').map(Number);
                                  return { row, col };
                                }
                              );

                              const insideMinRow = Math.min(
                                ...insideCoords.map((c) => c.row)
                              );
                              const insideMaxRow = Math.max(
                                ...insideCoords.map((c) => c.row)
                              );
                              const insideMinCol = Math.min(
                                ...insideCoords.map((c) => c.col)
                              );
                              const insideMaxCol = Math.max(
                                ...insideCoords.map((c) => c.col)
                              );

                              insideSelectedCells.forEach((cellId) => {
                                const [row, col] = cellId
                                  .split('-')
                                  .map(Number);

                                const cell = {
                                  ...newInsideTableData.cells[cellId],
                                };
                                cell.borderTop =
                                  row > insideMinRow ? borderStyle : 'none';
                                cell.borderBottom =
                                  row < insideMaxRow ? borderStyle : 'none';
                                cell.borderLeft =
                                  col > insideMinCol ? borderStyle : 'none';
                                cell.borderRight =
                                  col < insideMaxCol ? borderStyle : 'none';

                                newInsideTableData.cells[cellId] = cell;
                              });

                              updateTableData(newInsideTableData);
                              break;
                          }
                        }}
                        options={[
                          { value: 'all', label: 'Tất cả viền' },
                          { value: 'outside', label: 'Viền ngoài' },
                          { value: 'inside', label: 'Viền trong' },
                          { value: 'none', label: 'Không viền' },
                        ]}
                      />
                    </div>
                  </div>
                }
              >
                <Button icon={<FontSizeOutlined />} />
              </Popover>
            </TooltipAntdCustom>
          </div>
        </div>
      </div>
    );
  };

  // Create the table HTML
  const renderTable = () => {
    // Generate column styles based on tableData
    const colGroup = [];
    for (let i = 0; i < tableData.columns; i++) {
      colGroup.push(
        <col key={i} style={{ width: `${100 / tableData.columns}%` }} />
      );
    }

    // Generate table rows and cells
    const rows = [];
    for (let i = 0; i < tableData.rows; i++) {
      const cells = [];
      for (let j = 0; j < tableData.columns; j++) {
        const cellId = `${i}-${j}`;
        const cell = tableData.cells[cellId];

        // Skip cells that are hidden due to rowspan/colspan
        if (cell && (cell.rowSpan === 0 || cell.colSpan === 0)) {
          continue;
        }

        const cellStyle: React.CSSProperties = {
          backgroundColor: cell?.backgroundColor || '#ffffff',
          color: cell?.textColor || '#000000',
          fontSize: `${cell?.fontSize || 14}px`,
          fontWeight: cell?.fontWeight || 'normal',
          fontStyle: cell?.fontStyle || 'normal',
          textAlign: cell?.textAlign || 'left',
          verticalAlign: cell?.verticalAlign || 'middle',
          padding: `${tableData.cellPadding}px`,
          borderTop:
            cell?.borderTop ||
            `${tableData.tableBorderWidth}px solid ${tableData.tableBorderColor}`,
          borderRight:
            cell?.borderRight ||
            `${tableData.tableBorderWidth}px solid ${tableData.tableBorderColor}`,
          borderBottom:
            cell?.borderBottom ||
            `${tableData.tableBorderWidth}px solid ${tableData.tableBorderColor}`,
          borderLeft:
            cell?.borderLeft ||
            `${tableData.tableBorderWidth}px solid ${tableData.tableBorderColor}`,
          position: 'relative',
          cursor: isEditing ? 'pointer' : 'default',
        };

        if (tableData.tableStyle === 'striped' && i > 0 && i % 2 === 1) {
          cellStyle.backgroundColor = '#fafafa';
        }

        if (tableData.tableStyle === 'minimal') {
          if (i === 0) {
            // Header row
            cellStyle.borderTop = 'none';
            cellStyle.borderLeft = 'none';
            cellStyle.borderRight = 'none';
            cellStyle.borderBottom = '2px solid ' + tableData.tableBorderColor;
          } else {
            // Data rows
            cellStyle.borderTop = 'none';
            cellStyle.borderLeft = 'none';
            cellStyle.borderRight = 'none';
            cellStyle.borderBottom = '1px solid ' + tableData.tableBorderColor;
          }
        }

        // Add selection styles
        if (isEditing && selectedCell === cellId) {
          if (isCellEditing) {
            // When actively editing, use a more prominent highlight
            cellStyle.boxShadow = 'inset 0 0 0 2px var(--edtt-color-primary)';
            cellStyle.backgroundColor = '#fff'; // Ensure white background when editing
          } else {
            // When just selected but not editing
            cellStyle.boxShadow = 'inset 0 0 0 2px #1890ff';
          }
        }

        // Xác định các class cho ô
        const cellClasses = [];

        // Thêm class cho ô được chọn
        if (selectedCell === cellId) {
          cellClasses.push('selected-cell');
        }

        // Thêm class cho các ô trong vùng chọn
        if (selectedCellsInRange.includes(cellId) && !isCellEditing) {
          cellClasses.push('in-selection-range');
        }

        cells.push(
          <td
            key={cellId}
            id={cellId}
            ref={selectedCell === cellId ? selectedCellRef : null}
            style={cellStyle}
            className={cellClasses.join(' ')}
            rowSpan={cell?.rowSpan || 1}
            colSpan={cell?.colSpan || 1}
            onClick={() => handleCellClick(cellId)}
            onDoubleClick={() => handleCellDoubleClick(cellId)}
            onMouseDown={() => {
              if (isEditing) {
                // Bắt đầu chọn
                setSelectionStart(cellId);
                setSelectionEnd(cellId);
                setIsSelecting(true);

                // Bôi đen ô đầu tiên ngay lập tức
                setSelectedCellsInRange([cellId]);
              }
            }}
            onMouseUp={() => {
              if (isEditing && selectionStart) {
                // Kết thúc chọn
                setSelectionEnd(cellId);
                setIsSelecting(false);
                // Sử dụng calculateSelectedCellsRange để tính toán các ô được chọn
                const cells = calculateSelectedCellsRange();
                setSelectedCellsInRange(cells);
              }
            }}
            onMouseEnter={() => {
              if (isEditing && selectionStart && isSelecting) {
                // Cập nhật ngay khi chuột đi vào ô
                setSelectionEnd(cellId);

                // Tính toán vùng chọn mới bao gồm cả ô hiện tại
                const [startRow, startCol] = selectionStart
                  .split('-')
                  .map(Number);
                const [endRow, endCol] = cellId.split('-').map(Number);

                const minRow = Math.min(startRow, endRow);
                const maxRow = Math.max(startRow, endRow);
                const minCol = Math.min(startCol, endCol);
                const maxCol = Math.max(startCol, endCol);

                const newSelectedCells: string[] = [];

                for (let i = minRow; i <= maxRow; i++) {
                  for (let j = minCol; j <= maxCol; j++) {
                    const id = `${i}-${j}`;
                    if (tableData.cells[id]) {
                      newSelectedCells.push(id);
                    }
                  }
                }

                // Cập nhật state ngay lập tức để hiển thị bôi đen
                setSelectedCellsInRange(newSelectedCells);
              }
            }}
          >
            {isCellEditing && selectedCell === cellId ? (
              <Input.TextArea
                value={editingContent}
                onChange={(e) => {
                  setEditingContent(e.target.value);
                  if (selectedCell) {
                    updateCellContent(selectedCell, e.target.value);
                  }
                }}
                onBlur={() => {
                  setIsCellEditing(false);
                }}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    closeCellEditing();
                  }
                }}
                autoFocus
                style={{
                  border: 'none',
                  background: '#fff',
                  resize: 'none',
                  padding: 0,
                  fontSize: 'inherit',
                  fontWeight: 'inherit',
                  fontStyle: 'inherit',
                  color: 'inherit',
                  textAlign: (cell?.textAlign as any) || 'left',
                  width: '100%',
                  height: '100%',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  overflow: 'hidden',
                  lineHeight: 'normal',
                }}
              />
            ) : (
              cell?.content || ''
            )}
          </td>
        );
      }

      rows.push(<tr key={i}>{cells}</tr>);
    }

    // Table styles
    const tableStyle: React.CSSProperties = {
      width: tableData.tableWidth,
      tableLayout: tableData.tableLayout,
      borderCollapse: tableData.cellSpacing === 0 ? 'collapse' : 'separate',
      borderSpacing: `${tableData.cellSpacing}px`,
      backgroundColor: tableData.tableBackgroundColor,
      margin: '0 auto',
    };

    if (tableData.tableStyle === 'bordered') {
      tableStyle.border = `${(tableData.tableBorderWidth || 1) * 2}px solid ${
        tableData.tableBorderColor || '#d9d9d9'
      }`;
    }

    return (
      <table
        ref={tableRef}
        style={tableStyle}
        className="table-engine-table"
        data-editing={isEditing ? 'true' : 'false'}
      >
        <colgroup>{colGroup}</colgroup>
        <tbody>{rows}</tbody>
      </table>
    );
  };

  // Create table modal
  const renderTableCreationModal = () => {
    return (
      <Modal
        title="Tạo bảng mới"
        open={tableCreationVisible}
        onCancel={() => setTableCreationVisible(false)}
        onOk={createNewTable}
      >
        <div className="tailwind-p-4">
          <div className="tailwind-flex tailwind-items-center tailwind-mb-4">
            <div className="tailwind-w-24">Số hàng:</div>
            <InputNumber
              min={1}
              max={20}
              value={newTableRows}
              onChange={(value) => value && setNewTableRows(value)}
            />
          </div>

          <div className="tailwind-flex tailwind-items-center">
            <div className="tailwind-w-24">Số cột:</div>
            <InputNumber
              min={1}
              max={10}
              value={newTableColumns}
              onChange={(value) => value && setNewTableColumns(value)}
            />
          </div>

          <div className="tailwind-my-4 tailwind-bg-gray-100 tailwind-p-3 tailwind-rounded">
            <div
              className="tailwind-grid tailwind-gap-1"
              style={{
                gridTemplateColumns: `repeat(${Math.min(
                  newTableColumns,
                  10
                )}, 1fr)`,
                gridTemplateRows: `repeat(${Math.min(newTableRows, 10)}, 1fr)`,
              }}
            >
              {Array.from({ length: Math.min(newTableRows, 10) }).map(
                (_, rowIndex) =>
                  Array.from({ length: Math.min(newTableColumns, 10) }).map(
                    (_, colIndex) => (
                      <div
                        key={`${rowIndex}-${colIndex}`}
                        className="tailwind-w-6 tailwind-h-6 tailwind-bg-white tailwind-border tailwind-border-gray-300"
                      />
                    )
                  )
              )}
            </div>
          </div>

          <div className="tailwind-text-gray-500 tailwind-text-sm">
            Bạn có thể thay đổi kích thước và định dạng bảng sau khi tạo.
          </div>
        </div>
      </Modal>
    );
  };

  // Render the main component content
  const renderMainComponent = () => {
    return (
      <div className="tailwind-flex tailwind-flex-col">
        {/* Table controls when in edit mode */}
        {renderTableControls()}

        {/* Table content */}
        <div className="tailwind-overflow-x-auto">{renderTable()}</div>

        {/* Table creation modal */}
        {renderTableCreationModal()}
      </div>
    );
  };

  return (
    <EngineContainer
      node={props}
      mainComponent={renderMainComponent()}
      allowConfiguration={false}
      showFullscreenButton={false}
      id={props.path}
    />
  );
};

// Zod schema for component validation
export const tableComponentSchema = createComponentSchema({
  paramsSchema: {
    tableData: z
      .object({
        rows: z.number(),
        columns: z.number(),
        cells: z.record(
          z.string(),
          z.object({
            id: z.string(),
            content: z.string(),
            backgroundColor: z.string().optional(),
            textColor: z.string().optional(),
            fontSize: z.number().optional(),
            fontWeight: z.enum(['normal', 'bold']).optional(),
            fontStyle: z.enum(['normal', 'italic']).optional(),
            textAlign: z.enum(['left', 'center', 'right']).optional(),
            verticalAlign: z.enum(['top', 'middle', 'bottom']).optional(),
            colSpan: z.number().optional(),
            rowSpan: z.number().optional(),
            borderTop: z.string().optional(),
            borderRight: z.string().optional(),
            borderBottom: z.string().optional(),
            borderLeft: z.string().optional(),
          })
        ),
        tableWidth: z.union([z.string(), z.number()]).optional(),
        tableBorderColor: z.string().optional(),
        tableBorderWidth: z.number().optional(),
        tableBackgroundColor: z.string().optional(),
        cellPadding: z.number().optional(),
        cellSpacing: z.number().optional(),
        tableLayout: z.enum(['auto', 'fixed']).optional(),
        tableStyle: z
          .enum(['default', 'striped', 'bordered', 'minimal'])
          .optional(),
      })
      .optional(),
  },
});

export default withEdComponentParams(TableComponent);

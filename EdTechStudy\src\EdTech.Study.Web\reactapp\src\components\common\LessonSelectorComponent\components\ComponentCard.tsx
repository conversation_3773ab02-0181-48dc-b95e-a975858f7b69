import React from 'react';
import { Card, Typography } from 'antd';
import { ETypeEdTechComponent } from '../../../../enums/AppEnums';
import { IEdTechComponent } from '../../../../interfaces/AppComponents';
import { getDefaultThumbNail } from '../utils/thumbnailUtils';
import TagBadge from './TagBadge';
import { getLocalizedType } from '../../../../utils/localizationUtils';

const { Paragraph } = Typography;

interface ComponentCardProps {
  component: IEdTechComponent;
  isSelected: boolean;
  onSelect: (component: IEdTechComponent) => void;
}

const ComponentCard: React.FC<ComponentCardProps> = ({
  component,
  isSelected,
  onSelect,
}) => {
  const getTypeColor = () => {
    switch (component.type.toString()) {
      case ETypeEdTechComponent.MINI_GAMES:
        return 'tailwind-bg-green-100 tailwind-text-green-800';
      case ETypeEdTechComponent.SIMULATORS:
        return 'tailwind-bg-purple-100 tailwind-text-purple-800';
      case ETypeEdTechComponent.LAYOUT:
        return 'tailwind-bg-blue-100 tailwind-text-blue-800';
      case ETypeEdTechComponent.DEMO:
        return 'tailwind-bg-orange-100 tailwind-text-orange-800';
      case ETypeEdTechComponent.QUIZ:
        return 'tailwind-bg-yellow-100 tailwind-text-yellow-800';
      case ETypeEdTechComponent.STRUCTURE:
        return 'tailwind-bg-indigo-100 tailwind-text-indigo-800';
      case ETypeEdTechComponent.COMMON:
        return 'tailwind-bg-pink-100 tailwind-text-pink-800';
      case ETypeEdTechComponent.CONTENT:
        return 'tailwind-bg-teal-100 tailwind-text-teal-800';
      default:
        return 'tailwind-bg-gray-100 tailwind-text-gray-800';
    }
  };

  return (
    <Card
      hoverable
      className={`tailwind-w-full hover:tailwind-shadow-md tailwind-transition-all tailwind-duration-300 ${
        isSelected
          ? 'tailwind-border-blue-500 tailwind-border-2 tailwind-shadow tailwind-bg-blue-50'
          : 'hover:tailwind-border-blue-300'
      }`}
      cover={
        <div className="tailwind-h-44 tailwind-bg-gray-200 tailwind-overflow-hidden tailwind-relative tailwind-group">
          <img
            alt={component.title}
            src={
              component.thumbnailUrl ||
              getDefaultThumbNail(component.type.toString())
            }
            className={`tailwind-w-full tailwind-h-full tailwind-object-cover tailwind-transition-transform tailwind-duration-300 ${
              isSelected
                ? 'tailwind-brightness-105'
                : 'group-hover:tailwind-brightness-105'
            }`}
          />
          <div
            className={`tailwind-absolute tailwind-top-2 tailwind-right-2 ${getTypeColor()} tailwind-rounded-md tailwind-px-2 tailwind-py-0.5 tailwind-text-xs tailwind-font-medium tailwind-shadow-sm`}
          >
            {getLocalizedType(component.type)}
          </div>
          {isSelected && (
            <div className="tailwind-absolute tailwind-bottom-0 tailwind-left-0 tailwind-right-0 tailwind-bg-blue-500 tailwind-py-1 tailwind-text-center tailwind-text-white tailwind-text-xs tailwind-font-medium">
              Đã chọn
            </div>
          )}
        </div>
      }
      onClick={() => onSelect(component)}
    >
      <Card.Meta
        title={
          <div className="tailwind-text-base tailwind-font-semibold tailwind-text-gray-800 tailwind-mb-2 tailwind-overflow-hidden tailwind-text-ellipsis tailwind-whitespace-nowrap">
            {component.title}
          </div>
        }
        description={
          <div>
            {component.description && (
              <Paragraph
                ellipsis={{ rows: 2 }}
                className="tailwind-text-sm tailwind-text-gray-600 tailwind-mb-3 tailwind-min-h-[2.5rem]"
              >
                {component.description}
              </Paragraph>
            )}
            <div className="tailwind-flex tailwind-flex-wrap tailwind-justify-between tailwind-items-center tailwind-gap-2 tailwind-mt-2">
              {component.tags && component.tags.length > 0 && (
                <TagBadge tags={component.tags} />
              )}
            </div>
          </div>
        }
      />
    </Card>
  );
};

export default ComponentCard;

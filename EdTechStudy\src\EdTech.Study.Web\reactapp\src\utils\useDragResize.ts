import { useRef, useEffect, useCallback } from 'react';

interface BaseDragResizeOptions {
  direction: 'horizontal' | 'vertical' | 'both';
}

interface HorizontalOrVerticalOptions extends BaseDragResizeOptions {
  direction: 'horizontal' | 'vertical';
  onChange: (delta: number) => void;
}

interface BothDirectionsOptions extends BaseDragResizeOptions {
  direction: 'both';
  onChange: (deltaX: number, deltaY: number) => void;
}

type DragResizeOptions = HorizontalOrVerticalOptions | BothDirectionsOptions;

/**
 * A custom hook for implementing drag-to-resize functionality
 * 
 * @param options Configuration options for the drag resize behavior
 * @param options.direction The direction of resizing: 'horizontal', 'vertical', or 'both'
 * @param options.onChange Callback function that receives the delta(s) of the drag operation
 * 
 * @returns An object containing the startDrag function to initiate dragging
 */
export function useDragResize(options: DragResizeOptions) {
  const { direction, onChange } = options;
  
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const isDraggingRef = useRef(false);
  const startPosXRef = useRef(0);
  const startPosYRef = useRef(0);

  // Create and manage the overlay element
  useEffect(() => {
    overlayRef.current = document.createElement('div');
    overlayRef.current.style.position = 'fixed';
    overlayRef.current.style.top = '0';
    overlayRef.current.style.left = '0';
    overlayRef.current.style.right = '0';
    overlayRef.current.style.bottom = '0';
    overlayRef.current.style.zIndex = '1000';
    
    // Set cursor based on direction
    if (direction === 'horizontal') {
      overlayRef.current.style.cursor = 'col-resize';
    } else if (direction === 'vertical') {
      overlayRef.current.style.cursor = 'row-resize';
    } else {
      overlayRef.current.style.cursor = 'nwse-resize';
    }
    
    overlayRef.current.style.display = 'none';
    document.body.appendChild(overlayRef.current);

    return () => {
      if (overlayRef.current) {
        document.body.removeChild(overlayRef.current);
      }
    };
  }, [direction]);

  const startDrag = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (!overlayRef.current) return;

      isDraggingRef.current = true;
      
      // Store starting positions
      startPosXRef.current = e.clientX;
      startPosYRef.current = e.clientY;
      
      // Set cursor based on direction
      if (direction === 'horizontal') {
        document.body.style.cursor = 'col-resize';
      } else if (direction === 'vertical') {
        document.body.style.cursor = 'row-resize';
      } else {
        document.body.style.cursor = 'nwse-resize';
      }
      
      document.body.style.userSelect = 'none';
      overlayRef.current.style.display = 'block';

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDraggingRef.current) return;
        
        if (direction === 'both') {
          // For 'both' direction, calculate both X and Y deltas
          const deltaX = e.clientX - startPosXRef.current;
          const deltaY = e.clientY - startPosYRef.current;
          
          // Call onChange with both deltas for 'both' direction
          (onChange as (deltaX: number, deltaY: number) => void)(deltaX, deltaY);
        } else {
          // For single-direction resizing, calculate the appropriate delta
          const currentPos = direction === 'horizontal' ? e.clientX : e.clientY;
          const startPos = direction === 'horizontal' ? startPosXRef.current : startPosYRef.current;
          const delta = currentPos - startPos;
          
          // Call onChange with single delta for horizontal/vertical directions
          (onChange as (delta: number) => void)(delta);
        }
      };

      const handleMouseUp = () => {
        if (!isDraggingRef.current || !overlayRef.current) return;
        
        isDraggingRef.current = false;
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        overlayRef.current.style.display = 'none';
        
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    [direction, onChange]
  );

  return { startDrag };
}

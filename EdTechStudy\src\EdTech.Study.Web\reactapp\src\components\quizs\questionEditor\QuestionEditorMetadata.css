/* Styling for the metadata panel in QuestionEditor */
.metadata-panel {
  background-color: #f9f9f9;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.metadata-section {
  margin-bottom: 20px;
}

.metadata-section:last-child {
  margin-bottom: 0;
}

.metadata-section h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  color: #222;
}

.metadata-section h4 .anticon {
  margin-right: 8px;
  color: #1890ff;
}

.tags-container,
.topics-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
  min-height: 32px;
  padding: 4px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
}

.empty-container {
  color: #999;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  width: 100%;
}

/* Styling for tags */
.tags-container .ant-tag {
  margin: 4px;
  padding: 4px 8px;
  user-select: none;
}

.topics-container .ant-tag {
  margin: 4px;
  padding: 4px 8px;
  user-select: none;
}

/* Input styles for adding new tags/topics */
.metadata-input {
  margin-top: 8px;
}

.metadata-input .ant-input-suffix {
  cursor: pointer;
}

.metadata-input .ant-input-suffix:hover {
  color: #1890ff;
}

/* Animation for tag removal */
.ant-tag-close-icon {
  transition: all 0.2s;
}

.ant-tag-close-icon:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

/* Styling for settings tabs */
.settings-tabs {
  margin-bottom: 16px;
}

.settings-tabs .ant-btn {
  margin-right: 8px;
}

.settings-tabs .ant-btn:last-child {
  margin-right: 0;
}

/* Styling for the columns section */
.metadata-panel .columns-section .ant-checkbox-wrapper {
  display: block;
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.metadata-panel .columns-section .ant-checkbox-wrapper:hover {
  background-color: #f0f7ff;
}

/* Tag and topic search styles */
.search-container {
  margin-bottom: 16px;
}

.search-container .ant-input-search {
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .settings-tabs .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .settings-tabs .ant-btn .anticon {
    margin-right: 4px;
  }

  .metadata-section h4 {
    font-size: 14px;
  }
}

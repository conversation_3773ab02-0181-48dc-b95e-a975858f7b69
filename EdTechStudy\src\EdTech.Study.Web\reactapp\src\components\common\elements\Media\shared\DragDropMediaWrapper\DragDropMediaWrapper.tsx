import React, { useState, useRef } from 'react';
import { message, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { MediaType } from '../MediaFileUploader/types';
import tempFileApi from '../../../../../../api/tempFileApi';
import { ConfigManager } from '../../../../../../utils/ConfigManager';

interface DragDropMediaWrapperProps {
  /**
   * Type of media to upload (image, video, audio, model3d)
   */
  mediaType: MediaType;

  /**
   * Whether the component is in editing mode
   */
  isEditing: boolean;

  /**
   * Callback when upload is successful
   */
  onUploadSuccess: (files: any[]) => void;

  /**
   * Children to render inside the wrapper
   */
  children: React.ReactNode;

  /**
   * Additional CSS class for the container
   */
  className?: string;

  /**
   * Additional inline styles
   */
  style?: React.CSSProperties;

  /**
   * Whether to show a visual indicator when dragging
   */
  showDragIndicator?: boolean;

  /**
   * Additional upload props
   */
  uploadProps?: any;
}

/**
 * A wrapper component that adds drag and drop file upload functionality to any content
 */
const DragDropMediaWrapper: React.FC<DragDropMediaWrapperProps> = ({
  mediaType,
  isEditing,
  onUploadSuccess,
  children,
  className = '',
  style = {},
  showDragIndicator = true,
}) => {
  // State for tracking drag and upload status
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  // Ref to track if we're currently handling a drop
  const isHandlingDrop = useRef<boolean>(false);

  // Check file type validity
  const isValidFileType = (file: File) => {
    const fileName = file.name.toLowerCase();

    switch (mediaType) {
      case 'image':
        return file.type.startsWith('image/');
      case 'video':
        return file.type.startsWith('video/');
      case 'audio':
        return file.type.startsWith('audio/');
      case 'model3d':
        // 3D models might not have standard MIME types, so check extensions
        return (
          fileName.endsWith('.gltf') ||
          fileName.endsWith('.glb') ||
          fileName.endsWith('.obj') ||
          fileName.endsWith('.fbx') ||
          fileName.endsWith('.3d')
        );
      default:
        return true;
    }
  };

  // Get the appropriate text based on media type
  const getMediaTypeText = () => {
    switch (mediaType) {
      case 'image':
        return 'hình ảnh';
      case 'video':
        return 'video';
      case 'audio':
        return 'âm thanh';
      case 'model3d':
        return 'mô hình 3D';
      default:
        return 'tệp';
    }
  };

  // Handle file upload
  const handleUpload = async (files: File[]) => {
    if (!files || files.length === 0) return;

    // Filter valid files by type
    const validFiles = files.filter(isValidFileType);

    if (validFiles.length === 0) {
      message.error(`Chỉ được tải lên ${getMediaTypeText()}!`);
      return;
    }

    // Start upload
    setIsUploading(true);
    message.loading({
      content: `Đang tải ${getMediaTypeText()} lên...`,
      key: 'uploadMessage',
      duration: 0,
    });

    try {
      // Create FormData with all files
      const formData = new FormData();
      validFiles.forEach((file) => {
        formData.append('files', file);
      });
      const id = ConfigManager.getIdConfigFromParams();
      // Upload all files at once
      const response = await tempFileApi.UploadMultiple(id, formData);

      if (response.data?.successful && response.data.successful.length > 0) {
        // Call success callback
        onUploadSuccess(response.data.successful);

        // Show success message
        message.success({
          content: `Tải ${getMediaTypeText()} lên thành công!`,
          key: 'uploadMessage',
          duration: 2,
        });
      }

      // Show error message if any files failed
      if (response.data?.failed && response.data.failed.length > 0) {
        message.error(
          `Không thể tải lên ${
            response.data.failed.length
          } ${getMediaTypeText()}`
        );
      }
    } catch (error) {
      message.error({
        content: `Không thể tải ${getMediaTypeText()} lên. Vui lòng thử lại.`,
        key: 'uploadMessage',
        duration: 3,
      });
      console.error('Upload error:', error);
    } finally {
      // End upload
      setIsUploading(false);
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    // Only allow drag & drop in edit mode
    if (!isEditing) return;

    e.preventDefault();
    e.stopPropagation();

    if (!isDragging) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if we're leaving the container (not just moving between child elements)
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (
      x <= rect.left ||
      x >= rect.right ||
      y <= rect.top ||
      y >= rect.bottom
    ) {
      setIsDragging(false);
    }
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    // Only allow drag & drop in edit mode
    if (!isEditing) return;

    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    // Prevent multiple simultaneous drops
    if (isHandlingDrop.current) return;
    isHandlingDrop.current = true;

    try {
      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files);

        // Filter files by media type
        const validFiles = files.filter(isValidFileType);

        if (validFiles.length === 0) {
          message.error(`Chỉ được tải lên ${getMediaTypeText()}!`);
          return;
        }

        // Upload valid files
        await handleUpload(validFiles);
      }
    } finally {
      isHandlingDrop.current = false;
    }
  };

  return (
    <div
      className={`tailwind-relative ${className}`}
      style={style}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* Show drag indicator when dragging */}
      {isEditing && isDragging && showDragIndicator && (
        <div className="tailwind-absolute tailwind-inset-0 tailwind-z-10 tailwind-border-2 tailwind-border-dashed tailwind-border-primary tailwind-bg-primary-100 tailwind-bg-opacity-50 tailwind-flex tailwind-items-center tailwind-justify-center">
          <div className="tailwind-text-center tailwind-p-4 tailwind-bg-white tailwind-bg-opacity-90 tailwind-rounded-md tailwind-shadow-md">
            <p className="tailwind-font-medium tailwind-text-primary">
              Thả để tải {getMediaTypeText()} lên
            </p>
          </div>
        </div>
      )}

      {/* Show loading overlay when uploading */}
      {isUploading && (
        <div className="tailwind-absolute tailwind-inset-0 tailwind-z-20 tailwind-bg-white tailwind-bg-opacity-70 tailwind-flex tailwind-items-center tailwind-justify-center">
          <Spin
            indicator={
              <LoadingOutlined
                style={{ fontSize: 32, color: 'var(--edtt-color-primary)' }}
                spin
              />
            }
            tip={`Đang tải ${getMediaTypeText()} lên...`}
          />
        </div>
      )}

      {/* Render children */}
      {children}
    </div>
  );
};

export default DragDropMediaWrapper;

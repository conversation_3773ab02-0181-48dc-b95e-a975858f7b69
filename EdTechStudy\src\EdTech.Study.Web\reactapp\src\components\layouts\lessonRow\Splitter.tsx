import React, { useState, useEffect, useRef, useCallback } from 'react';
import './styles.css';
import { useDragResize } from './useDragResize';

interface SplitterProps {
  children: React.ReactNode[];
  onResize?: (sizes: number[]) => void;
  isEditing?: boolean;
  initialSizes?: number[];
}

const Splitter: React.FC<SplitterProps> = ({
  children,
  onResize,
  isEditing = false,
  initialSizes = [],
}) => {
  const [sizes, setSizes] = useState<number[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const panelRefsArray = useRef<Array<HTMLDivElement | null>>([]);
  const observedPanelsRef = useRef<Set<HTMLDivElement>>(new Set());
  const isResizingRef = useRef(false);
  const startSizesRef = useRef<number[]>([]);
  const activeIndexRef = useRef<number>(-1);

  // Initialize sizes
  useEffect(() => {
    if (children.length > 0) {
      if (initialSizes.length === children.length) {
        setSizes(initialSizes);
      } else {
        const initialSize = 100 / children.length;
        setSizes(Array(children.length).fill(initialSize));
      }
      panelRefsArray.current = Array(children.length).fill(null);
    }
  }, [children.length, initialSizes]);

  // Setup ResizeObserver
  useEffect(() => {
    const observer = new ResizeObserver(() => {
      setSizes((prev) => [...prev]); // Trigger re-render
    });

    panelRefsArray.current.forEach((panel) => {
      if (panel && !observedPanelsRef.current.has(panel)) {
        observer.observe(panel);
        observedPanelsRef.current.add(panel);
      }
    });

    return () => {
      observedPanelsRef.current.forEach((panel) => observer.unobserve(panel));
      observedPanelsRef.current.clear();
      observer.disconnect();
    };
  }, [children.length]);

  // Debounced window resize
  useEffect(() => {
    let timeout: ReturnType<typeof setTimeout>;
    const handleResize = () => {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        setSizes((prev) => [...prev]);
      }, 100);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeout);
    };
  }, []);

  // Memoized divider dimensions
  const getDividerDimensions = useCallback(
    (leftIndex: number, rightIndex: number) => {
      const leftPanel = panelRefsArray.current[leftIndex];
      const rightPanel = panelRefsArray.current[rightIndex];
      if (!leftPanel || !rightPanel) return { height: '100%', top: 0 };

      const leftRect = leftPanel.getBoundingClientRect();
      const rightRect = rightPanel.getBoundingClientRect();
      const topMax = Math.max(leftRect.top, rightRect.top);
      const bottomMin = Math.min(leftRect.bottom, rightRect.bottom);

      if (topMax >= bottomMin) return { height: '0px', top: 0 };

      const contactHeight = bottomMin - topMax;
      const containerRect = containerRef.current?.getBoundingClientRect();
      const relativeTop = containerRect ? topMax - containerRect.top : 0;

      return { height: `${contactHeight}px`, top: relativeTop };
    },
    []
  );

  // Drag resize logic
  const { startDrag } = useDragResize({
    direction: 'horizontal',
    onChange: (delta: number) => {
      if (!isResizingRef.current || !containerRef.current) return;

      const containerWidth = containerRef.current.offsetWidth;
      const deltaPercent = (delta / containerWidth) * 100;

      const newSizes = [...startSizesRef.current];
      const leftPanelIndex = activeIndexRef.current;
      const rightPanelIndex = leftPanelIndex + 1;

      if (leftPanelIndex >= 0 && rightPanelIndex < newSizes.length) {
        const minSize = 5;
        const maxSize = 95;

        let newLeftSize = startSizesRef.current[leftPanelIndex] + deltaPercent;
        newLeftSize = Math.max(minSize, Math.min(maxSize, newLeftSize));

        let newRightSize =
          startSizesRef.current[rightPanelIndex] - deltaPercent;
        newRightSize = Math.max(minSize, Math.min(maxSize, newRightSize));

        const otherPanelsSize = startSizesRef.current.reduce(
          (acc, size, idx) => {
            if (idx !== leftPanelIndex && idx !== rightPanelIndex) {
              return acc + size;
            }
            return acc;
          },
          0
        );

        const totalSize = newLeftSize + newRightSize + otherPanelsSize;
        if (Math.abs(totalSize - 100) > 0.1) {
          const adjustmentFactor =
            (100 - otherPanelsSize) / (newLeftSize + newRightSize);
          newLeftSize *= adjustmentFactor;
          newRightSize *= adjustmentFactor;
        }

        newSizes[leftPanelIndex] = newLeftSize;
        newSizes[rightPanelIndex] = newRightSize;

        setSizes(newSizes);
        if (onResize) {
          onResize(newSizes);
        }
      }
    },
  });

  const handleMouseDown = useCallback(
    (index: number, e: React.MouseEvent) => {
      if (!isEditing) return;
      isResizingRef.current = true;
      startSizesRef.current = [...sizes];
      activeIndexRef.current = index;
      startDrag(e);
    },
    [isEditing, sizes, startDrag]
  );

  const setPanelRef = useCallback(
    (index: number, ref: HTMLDivElement | null) => {
      panelRefsArray.current[index] = ref;
    },
    []
  );

  return (
    <div className="splitter-container" ref={containerRef}>
      {React.Children.map(children, (child, index) => (
        <React.Fragment key={index}>
          <div
            ref={(ref) => setPanelRef(index, ref)}
            className="splitter-panel tailwind-bg-white"
            style={{ flex: `0 0 ${sizes[index]}%`, width: `${sizes[index]}%` }}
          >
            {child}
          </div>
          {index < children.length - 1 && (
            <div
              className={`splitter-divider ${isEditing ? 'editing' : ''}`}
              style={{
                ...getDividerDimensions(index, index + 1),
                left: `calc(${sizes
                  .slice(0, index + 1)
                  .reduce((a, b) => a + b, 0)}% - 2px)`,
              }}
              onMouseDown={(e) => handleMouseDown(index, e)}
            >
              {isEditing && <div className="splitter-divider-bar" />}
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default Splitter;

import React, { useState } from 'react';
import { Badge, Tooltip, Button } from 'antd';
import { DownOutlined } from '@ant-design/icons';

interface TagBadgeProps {
  tags: string[];
  maxDisplay?: number;
}

const TagBadge: React.FC<TagBadgeProps> = ({ tags, maxDisplay = 2 }) => {
  const [showAll, setShowAll] = useState(false);

  if (!tags || tags.length === 0) return null;

  const displayTags = showAll ? tags : tags.slice(0, maxDisplay);
  const hasMore = tags.length > maxDisplay;

  return (
    <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1">
      {displayTags.map((tag) => (
        <Badge key={tag} count={tag} style={{ backgroundColor: '#1890ff' }} />
      ))}
      {hasMore && !showAll && (
        <Tooltip title="Xem tất cả">
          <Button
            type="text"
            size="small"
            icon={<DownOutlined />}
            onClick={() => setShowAll(true)}
          />
        </Tooltip>
      )}
      {showAll && hasMore && (
        <Tooltip title="Thu gọn">
          <Button
            type="text"
            size="small"
            icon={<DownOutlined style={{ transform: 'rotate(180deg)' }} />}
            onClick={() => setShowAll(false)}
          />
        </Tooltip>
      )}
    </div>
  );
};

export default TagBadge;

import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';
import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';

// Function to find the highest-level parent CONTENT or LAYOUT item that contains the target item
export const findParentContentItem = (
  id: string,
  subItems: IEdTechRenderTreeData[]
): IEdTechRenderTreeData | null => {
  // First check if the item is a root item
  const rootItem = subItems.find((item) => item.id === id);
  if (rootItem) {
    return rootItem; // Return the item itself if it's a root item
  }

  // Find all possible parent items that contain the target (directly or indirectly)
  const possibleParents: IEdTechRenderTreeData[] = [];

  // Helper function to check if an item contains the target in its hierarchy
  const containsTarget = (
    item: IEdTechRenderTreeData,
    targetId: string
  ): boolean => {
    // Check if this is the target
    if (item.id === targetId) {
      return true;
    }

    // Check if any direct children match the target
    if (item.subItems) {
      // Check direct children first
      if (item.subItems.some((subItem) => subItem.id === targetId)) {
        return true;
      }

      // Then check deeper in the hierarchy
      if (item.subItems.some((subItem) => containsTarget(subItem, targetId))) {
        return true;
      }
    }

    return false;
  };

  // Find all items that contain the target and are of type CONTENT or LAYOUT
  const findAllParents = (items: IEdTechRenderTreeData[], targetId: string) => {
    for (const item of items) {
      // Skip the item if it's the target itself
      if (item.id === targetId) {
        continue;
      }

      // Check if this item contains the target
      if (containsTarget(item, targetId)) {
        // If it's a CONTENT or LAYOUT type, add it to possible parents
        possibleParents.push(item);
      }

      // Continue searching in subItems
      if (item.subItems && item.subItems.length > 0) {
        findAllParents(item.subItems, targetId);
      }
    }
  };

  // Start the search from root items
  findAllParents(subItems, id);

  // If no parents found, return null
  if (possibleParents.length === 0) {
    return null;
  }

  // Find the highest-level parent (the one with the shortest path)
  // Sort by path length (shorter path = higher in the hierarchy)
  possibleParents.sort((a, b) => {
    const pathA = a.path?.split('/') || [];
    const pathB = b.path?.split('/') || [];
    return pathA.length - pathB.length;
  });

  // Return the highest-level parent
  return possibleParents[0];
};

// Function to find a specific subitem by ID
export const findSubItemById = (
  id: string,
  subItems: IEdTechRenderTreeData[]
): IEdTechRenderTreeData | null => {
  // First check if it's a root item
  const rootItem = subItems.find((item) => item.id === id);
  if (rootItem) {
    return rootItem; // Return the item itself if it's a root item
  }

  // If not a root item, search in subItems
  for (const item of subItems) {
    if (item.type === ETypeEdTechComponent.CONTENT && item.subItems) {
      const foundSubItem = item.subItems.find((subItem) => subItem.id === id);
      if (foundSubItem) {
        return foundSubItem;
      }
    }
  }
  return null;
};

// Recursive function to find an item by ID in the nested structure
export const findItemById = (
  id: string,
  subItems: IEdTechRenderTreeData[]
): IEdTechRenderTreeData | undefined => {
  for (const item of subItems) {
    if (item.id === id) {
      return item;
    }
    if (item.subItems && item.subItems.length > 0) {
      const found = findItemById(id, item.subItems);
      if (found) {
        return found;
      }
    }
  }
  return undefined;
};

import React, { useState, ReactNode, useEffect, memo } from 'react';
import { Space, Button, Tooltip } from 'antd';
import {
  FullscreenContainer,
  FullscreenButton,
  useFullscreenAdapter,
} from '../Fullscreen';
import CoreLessonTitle, { ITextProps } from '../../core/title/CoreTitle';
import InstructionComponent from './InstructionComponent';
import { useDispatch } from 'react-redux';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { updateItems } from '../../../store/slices/AppSlices/EdTechRenderDataSlice';
import VersionSelectorButton from './components/DropDownSelectVersion';
import { CardAntdCustom } from '../../customs/antd/CardAntdCustom';
import {
  SettingOutlinedIcon,
  InfoCircleOutlinedIcon,
} from '../../icons/IconIndex';

enum EModeEngineContainer {
  CARD_WRAPPER = 'CARD_WRAPPER',
  NONE_WRAPPER = 'NONE_WRAPPER',
}

// Type for the props that will be passed to the EngineContainer
export interface EngineContainerProps {
  // Base props
  node: IEdTechRenderProps;
  mode?: 'CARD_WRAPPER' | 'NONE_WRAPPER';
  titleProps?: ITextProps;
  allowConfiguration?: boolean;
  showFullscreenButton?: boolean;

  // Components
  mainComponent: ReactNode;
  configModal?: ReactNode;
  instructionsContent?:
    | ReactNode
    | {
        objective: string;
        steps: string[];
        notes?: string;
        isFullscreen?: boolean;
      }; // Can be either ReactNode (legacy) or structured props for InstructionComponent

  // Callback for title updates
  onTitleUpdate?: (updates: Partial<ITextProps>) => void;

  // Callback for fullscreen state changes
  onFullscreenChange?: (isFullscreen: boolean) => void;

  // Show/hide config modal
  isConfigModalOpen?: boolean;
  onConfigModalOpenChange?: (isOpen: boolean) => void;

  // Component ID for deletion (usually the path)
  id?: string;

  // Additional customization
  className?: string;
}

/**
 * EngineContainer - A common template for engine components (games, simulations, exercises, etc.)
 * Provides consistent layout, fullscreen capability, and configuration options
 */
const EngineContainer: React.FC<EngineContainerProps> = ({
  titleProps,
  node,
  mode = EModeEngineContainer.CARD_WRAPPER,
  allowConfiguration = true,
  showFullscreenButton = true,
  mainComponent,
  configModal,
  instructionsContent,
  onTitleUpdate,
  onFullscreenChange,
  isConfigModalOpen: externalIsConfigModalOpen,
  onConfigModalOpenChange,
  id,
  className = '',
}) => {
  id = id ? id : node.path;

  // Redux dispatch and state for deleting the engine
  const dispatch = useDispatch();
  // State for showing/hiding instructions and configuration modal
  const [showInstructions, setShowInstructions] = useState<boolean>(true);
  const [internalIsConfigModalOpen, setInternalIsConfigModalOpen] =
    useState<boolean>(false);

  // Use external config modal state if provided, otherwise use internal
  const isConfigModalOpen =
    externalIsConfigModalOpen !== undefined
      ? externalIsConfigModalOpen
      : internalIsConfigModalOpen;

  const isEditing = node.isEditing;

  // Handle config modal open state changes
  const handleConfigModalOpenChange = (open: boolean) => {
    if (onConfigModalOpenChange) {
      onConfigModalOpenChange(open);
    } else {
      setInternalIsConfigModalOpen(open);
    }
  };

  // Use the fullscreen adapter hook to get responsive helpers
  const {
    isFullscreen,
    fullscreenRef,
    handleToggleFullscreen,
    getResponsiveClass,
    getResponsiveStyles,
  } = useFullscreenAdapter();

  // Notify parent of fullscreen state changes
  useEffect(() => {
    if (onFullscreenChange) {
      onFullscreenChange(isFullscreen);
    }
  }, [isFullscreen, onFullscreenChange]);
  // Prepare default text props if none provided
  const defaultTextProps: ITextProps = {
    text: titleProps?.text || 'Engine Title',
    fontSize: titleProps?.fontSize || 24,
    color: titleProps?.color,
    align: titleProps?.align || 'left',
    bold: titleProps?.bold || true,
  };

  // Prepare responsive classes and styles for different screen sizes
  const cardClassName = getResponsiveClass(
    `tailwind-rounded-lg tailwind-shadow-md tailwind-w-full ${className}`,
    `tailwind-rounded-lg tailwind-shadow-md tailwind-w-full tailwind-h-full ${className}`
  );

  const headerClassName = getResponsiveClass(
    `tailwind-flex tailwind-flex-wrap tailwind-gap-1 tailwind-justify-between tailwind-items-center ${
      isEditing ? 'tailwind-mb-4' : ''
    }`,
    `tailwind-flex tailwind-flex-wrap tailwind-gap-1 tailwind-justify-between tailwind-items-center ${
      isEditing ? 'tailwind-mb-4' : ''
    }`
  );

  // No longer using responsive button sizes as we're using fixed sizes

  // Handle title updates
  const handleTitleUpdate = (updates: Partial<ITextProps>) => {
    if (onTitleUpdate) {
      onTitleUpdate(updates);
    }

    // Only update the title in the tree if text is provided
    // This allows empty titles to be set and maintained
    dispatch(
      updateItems({
        items: [
          {
            data: {
              // Use updates.text even if it's an empty string
              title:
                updates.text !== undefined
                  ? updates.text
                  : titleProps?.text || '',
            },
            path: node.path,
          },
        ],
      })
    );
  };

  // Apply isEditing to make the border visible when in edit mode
  const titleWrapperClass = isEditing
    ? 'tailwind-p-1 tailwind-cursor-pointer'
    : '';

  // Handle different types of instructionsContent
  const wrappedInstructions = (() => {
    if (!instructionsContent) return null;

    // Check if instructionsContent is already in the structured format
    if (
      typeof instructionsContent === 'object' &&
      instructionsContent !== null &&
      !React.isValidElement(instructionsContent) &&
      'objective' in instructionsContent &&
      'steps' in instructionsContent
    ) {
      // It's already structured props
      const structuredProps = instructionsContent as {
        objective: string;
        steps: string[];
        notes?: string;
        isFullscreen?: boolean;
      };

      return (
        <InstructionComponent
          title="Hướng dẫn sử dụng"
          objective={structuredProps.objective}
          steps={structuredProps.steps}
          notes={structuredProps.notes}
          isFullscreen={isFullscreen}
        />
      );
    }

    // If instructionsContent is a string, use it as objective
    if (typeof instructionsContent === 'string') {
      return (
        <InstructionComponent
          title="Hướng dẫn sử dụng"
          objective={instructionsContent}
          steps={['Xem hướng dẫn', 'Thực hiện các bước', 'Kiểm tra kết quả']}
          isFullscreen={isFullscreen}
        />
      );
    }

    // Legacy support for ReactNode content
    // This is a temporary solution until all components are updated
    return <div className="tailwind-mb-4">{instructionsContent}</div>;
  })();

  const renderContent = () => {
    return (
      <>
        <div className={headerClassName}>
          <div className="tailwind-flex-1">
            {titleProps !== undefined && (
              <div
                className={`tailwind-flex tailwind-items-center tailwind-gap-2 ${titleWrapperClass}`}
              >
                <CoreLessonTitle
                  titleProps={titleProps || defaultTextProps}
                  isColorDisabled={true}
                  isEditing={isEditing}
                  onUpdate={handleTitleUpdate}
                />
              </div>
            )}
          </div>

          <Space wrap size={8} className="tailwind-ml-auto">
            {isEditing && <VersionSelectorButton nodeTree={node} />}
            {instructionsContent && (
              <Tooltip
                title={showInstructions ? 'Ẩn hướng dẫn' : 'Xem hướng dẫn'}
              >
                <Button
                  type={showInstructions ? 'primary' : 'default'}
                  icon={
                    <InfoCircleOutlinedIcon
                      height={16}
                      width={16}
                      className="tailwind-text-white"
                    />
                  }
                  onClick={() => setShowInstructions(!showInstructions)}
                  shape="default"
                  size="middle"
                  className="engine-guide-btn !tailwind-w-[26px] !tailwind-h-[26px] tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-rounded-[4px]"
                />
              </Tooltip>
            )}
            {showFullscreenButton && (
              <FullscreenButton
                isFullscreen={isFullscreen}
                toggleFullscreen={handleToggleFullscreen}
                size="middle"
                shape="default"
                className="engine-fullscreen-btn !tailwind-w-[26px] !tailwind-h-[26px] tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-rounded-[4px]"
              />
            )}
            {allowConfiguration && configModal && (
              <Tooltip title="Cấu hình">
                <Button
                  type={'primary'}
                  icon={<SettingOutlinedIcon />}
                  onClick={() => handleConfigModalOpenChange(true)}
                  shape="default"
                  size="middle"
                  className="engine-config-btn !tailwind-w-[26px] !tailwind-h-[26px] tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-rounded-[4px]"
                />
              </Tooltip>
            )}
          </Space>
        </div>

        {/* Instructions section */}
        {instructionsContent && showInstructions && wrappedInstructions}

        {/* Main component - with flex-grow in fullscreen mode to take remaining space */}
        <div className={isFullscreen ? 'tailwind-flex-grow' : ''}>
          {mainComponent}
        </div>

        {/* Configuration modal (rendered conditionally) */}
        {isConfigModalOpen && configModal}
      </>
    );
  };

  const renderBody = () => {
    // Get responsive styles based on fullscreen state
    const containerStyle = getResponsiveStyles(
      {}, // Normal mode - no additional styles
      { display: 'flex', flexDirection: 'column', minHeight: '100vh' } // Fullscreen mode
    );

    switch (mode) {
      case EModeEngineContainer.CARD_WRAPPER:
        return (
          <CardAntdCustom
            className={`engine-container tailwind-p-[24px] ${cardClassName}`}
            style={isFullscreen ? containerStyle : undefined}
          >
            {renderContent()}
          </CardAntdCustom>
        );
      case EModeEngineContainer.NONE_WRAPPER:
        return (
          <div
            className="tailwind-p-[24px] tailwind-bg-default tailwind-rounded-lg tailwind-border-[1px] tailwind-border-solid tailwind-border-default"
            style={isFullscreen ? containerStyle : undefined}
          >
            {renderContent()}
          </div>
        );

      default:
        return (
          <div style={isFullscreen ? containerStyle : undefined}>
            {renderContent()}
          </div>
        );
    }
  };

  return (
    <FullscreenContainer
      className="tailwind-w-full tailwind-max-w-full"
      ref={fullscreenRef}
      fullscreenClasses="tailwind-h-screen "
    >
      {renderBody()}
    </FullscreenContainer>
  );
};

export default memo(EngineContainer);

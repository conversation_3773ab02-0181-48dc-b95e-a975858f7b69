import React, { useState, useEffect, Suspense } from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON>, But<PERSON>, Drawer } from 'antd';
import {
  QuestionCircleOutlined,
  FlagOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Question } from '../../../interfaces/quizs/questionBase';
import { mapQuestionComponents } from '../../../interfaces/quizs/questionBase';
import HashHelper from '../../../utils/HashHelper';
import { SaveControls } from './components';
import './styles/SaveControls.css';
import './styles/QuestionListView.css';
import LoadingScreen from '../../common/Loading/LoadingScreen';

const { Text } = Typography;

interface QuestionListViewProps {
  questions: Question[];
  showConfigMode: boolean;
  currentQuestionId?: string;
  disableCreateQuestion?: boolean;
  onSelectQuestion: (questionId: string) => void;
  onAddQuestion: (position: number) => void;
  onSaveQuestions?: () => void;
}

const QuestionListView: React.FC<QuestionListViewProps> = ({
  questions,
  currentQuestionId,
  showConfigMode,
  disableCreateQuestion = false,
  onSelectQuestion,
  onAddQuestion,
  onSaveQuestions,
}) => {
  const [showStyleDrawer, setShowStyleDrawer] = useState(false);
  const [showSettingsDrawer, setShowSettingsDrawer] = useState(false);
  const AddQuestionButton = ({ position }: { position: number }) => (
    <div className="add-question-button-container">
      <Button
        type="dashed"
        icon={<PlusOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          onAddQuestion(position);
        }}
        block
      >
        Thêm câu hỏi mới
      </Button>
    </div>
  );

  const handleStyleChange = () => {
    setShowStyleDrawer((prev) => !prev);
    setShowSettingsDrawer(false);
  };

  const handleSettingsChange = () => {
    setShowSettingsDrawer((prev) => !prev);
    setShowStyleDrawer(false);
  };

  // Add effect to adjust body padding when in config mode
  useEffect(() => {
    if (showConfigMode) {
      document.body.classList.add('config-mode-active');
    } else {
      document.body.classList.remove('config-mode-active');
    }

    return () => {
      document.body.classList.remove('config-mode-active');
    };
  }, [showConfigMode]);

  return (
    <div
      className={`question-list-view ${
        showConfigMode ? 'config-mode-view' : ''
      }`}
    >
      {showConfigMode && onSaveQuestions && (
        <SaveControls
          position="top"
          onSave={onSaveQuestions}
          onStyleChange={handleStyleChange}
          onSettingsChange={handleSettingsChange}
        />
      )}

      <div className="question-list-content">
        {!disableCreateQuestion && <AddQuestionButton position={0} />}

        {questions.map((question, index) => {
          const QuestionComponent = mapQuestionComponents.get(
            question.type
          )?.component;
          const componentId = HashHelper.computeHash([question.id]);
          return (
            <React.Fragment key={question.id}>
              <Card
                id={question.id}
                className={`question-list-item ${
                  currentQuestionId === question.id ? 'active' : ''
                }`}
                onClick={() => {
                  onSelectQuestion(question.id);
                }}
              >
                <div className="question-list-header">
                  <Space>
                    {question.isCompleted ? (
                      <FlagOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <QuestionCircleOutlined style={{ color: '#52c41a' }} />
                    )}
                    <Text strong>Câu hỏi {index + 1}</Text>
                  </Space>
                </div>

                <div
                  className={`question-list-content ${
                    showConfigMode ? 'config-mode' : ''
                  }`}
                >
                  {QuestionComponent && (
                    <Suspense fallback={<LoadingScreen />}>
                      <QuestionComponent
                        id={componentId}
                        question={question}
                        onComplete={() => {}}
                        showFeedback={false}
                        configMode={
                          currentQuestionId === question.id && showConfigMode
                        }
                        disabled={false}
                        hideSaveButton={true}
                      />
                    </Suspense>
                  )}
                </div>
              </Card>
              {!disableCreateQuestion && (
                <AddQuestionButton position={index + 1} />
              )}
            </React.Fragment>
          );
        })}

        {questions.length === 0 && (
          <div className="empty-question-list">
            <Text type="secondary">
              Chưa có câu hỏi nào. Hãy thêm câu hỏi đầu tiên!
            </Text>
          </div>
        )}
      </div>

      {/* Style Drawer */}
      <Drawer
        title="Thiết lập giao diện"
        placement="right"
        onClose={() => setShowStyleDrawer(false)}
        open={showStyleDrawer}
        width={350}
      >
        <div className="style-settings">
          <Text>Các tùy chỉnh giao diện sẽ được hiển thị ở đây</Text>
        </div>
      </Drawer>

      {/* Settings Drawer */}
      <Drawer
        title="Cài đặt"
        placement="right"
        onClose={() => setShowSettingsDrawer(false)}
        open={showSettingsDrawer}
        width={350}
      >
        <div className="practice-settings">
          <Text>Các tùy chỉnh bài luyện tập sẽ được hiển thị ở đây</Text>
        </div>
      </Drawer>
    </div>
  );
};

export default QuestionListView;

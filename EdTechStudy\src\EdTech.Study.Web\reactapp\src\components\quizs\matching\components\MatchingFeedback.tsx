import React from 'react';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { MatchingFeedbackProps } from '../../../../interfaces/quizs/mapping.interface';

const MatchingFeedback: React.FC<MatchingFeedbackProps> = ({
  isChecking,
  isCompleted,
  correctCount,
  totalCount,
  showFeedback,
}) => {
  if (!showFeedback) return null;

  if (isChecking && isCompleted) {
    return (
      <div className="matching-feedback success">
        <CheckOutlined /> Chúc mừng! Bạn đã hoàn thành bài tập này.
      </div>
    );
  }

  if (isChecking && !isCompleted && correctCount > 0) {
    return (
      <div className="matching-feedback warning">
        <CheckOutlined /> Bạn đã ghép đúng {correctCount} cặp. Tiếp tục cố gắng!
      </div>
    );
  }

  if (isChecking && !isCompleted && correctCount === 0) {
    return (
      <div className="matching-feedback error">
        <CloseOutlined /> <PERSON><PERSON> một số câu trả lời chưa chính xác. Hãy thử lại!
      </div>
    );
  }

  return null;
};

export default MatchingFeedback;

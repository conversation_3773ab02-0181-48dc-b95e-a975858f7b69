import React from 'react';
import 'rc-tree/assets/index.css';
import '../RcTreeSidebar.css';
import { SidebarHeader, SidebarContent, SidebarFooter } from './components';
import { useTreeOperations, useDragDrop, useSidebarActions } from './hooks';
import { LessonSidebarMenuProps } from './types';

const LessonSidebarWithRcTree: React.FC<LessonSidebarMenuProps> = ({
  items,
  title = 'Content',
  root,
  onSelect,
  isEditing = false,
  onFullscreen,
}) => {
  // Sidebar actions hook
  const { handleSave } = useSidebarActions();

  // Tree operations hook
  const {
    expandedKeys,
    selectedKey,
    editingItemKey,
    editingItemValue,
    autoExpandParent,
    convertedItems,
    selectNode,
    isShowSelectComponent,
    handleExpand,
    handleStartEdit,
    handleSaveEdit,
    handleInputChange,
    handleKeyDown,
    handleAddNode,
    handleAddRootNode,
    handleComponentSelect,
    setIsShowSelectComponent,
    setExpandedKeys,
    setAutoExpandParent,
    handleTreeSelect,
  } = useTreeOperations(items, root, isEditing, onSelect);

  // Drag and drop hook
  const { handleDrop } = useDragDrop(
    root,
    editingItemKey,
    expandedKeys,
    setExpandedKeys,
    setAutoExpandParent
  );

  return (
    <div className="rc-tree-sidebar">
      <SidebarHeader
        title={title}
        isEditing={isEditing}
        onFullscreen={onFullscreen}
      />

      <SidebarContent
        convertedItems={convertedItems}
        selectedKey={selectedKey}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        isEditing={isEditing}
        editingItemKey={editingItemKey}
        editingItemValue={editingItemValue}
        isShowSelectComponent={isShowSelectComponent}
        selectNode={selectNode}
        handleExpand={handleExpand}
        handleTreeSelect={handleTreeSelect}
        handleStartEdit={handleStartEdit}
        handleAddNode={handleAddNode}
        handleInputChange={handleInputChange}
        handleKeyDown={handleKeyDown}
        handleSaveEdit={handleSaveEdit}
        handleAddRootNode={handleAddRootNode}
        handleComponentSelect={handleComponentSelect}
        setIsShowSelectComponent={setIsShowSelectComponent}
        handleDrop={handleDrop}
      />

      {isEditing && <SidebarFooter onSave={handleSave} />}
    </div>
  );
};

export default LessonSidebarWithRcTree;

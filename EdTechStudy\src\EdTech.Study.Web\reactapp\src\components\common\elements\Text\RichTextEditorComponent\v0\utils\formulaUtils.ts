/**
 * Utility functions for formula handling in the RichTextEditor
 */

import { typesetMathJax } from './mathJaxUtils';
import { insertHtmlAtCursor } from './editorUtils';

/**
 * Add a formula button to the toolbar
 * @param toolbarItems Toolbar items array
 * @param id Component ID
 * @param saveSelectionFn Function to save the current selection
 * @param setIsFormulaDialogOpen Function to open the formula dialog
 */
export const addFormulaButton = (
  id: string,
  saveSelectionFn: () => void,
  setIsFormulaDialogOpen: (isOpen: boolean) => void
): void => {
  console.log('addFormulaButton called with id:', id);

  // Variable to store the toolbar element
  let editorToolbar: Element | null = null;

  try {
    // Target the specific editor's toolbar using the component's ID
    const editorId = `rich-text-editor-${id || 'default'}`;
    // Escape any special characters in the ID for the querySelector
    const escapedEditorId = editorId.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, "\\$&");

    console.log('Looking for toolbar with selector:', `#${escapedEditorId} .e-toolbar-items`);

    // Try different selectors if the specific one doesn't work
    editorToolbar = document.querySelector(`#${escapedEditorId} .e-toolbar-items`);

    if (!editorToolbar) {
      console.log('Toolbar not found with specific ID, trying generic selector');
      // Try a more generic selector as fallback
      editorToolbar = document.querySelector('.e-richtexteditor .e-toolbar-items');
    }

    if (!editorToolbar) {
      console.log('Trying alternative selectors');
      // Try other possible selectors
      const alternativeSelectors = [
        '.e-toolbar-container .e-toolbar-items',
        '.e-rte-toolbar .e-toolbar-items',
        '.e-toolbar .e-toolbar-items',
        '.e-toolbar-container',
        '.e-toolbar'
      ];

      for (const selector of alternativeSelectors) {
        const element = document.querySelector(selector);
        if (element) {
          console.log(`Found toolbar with selector: ${selector}`);
          editorToolbar = element;
          break;
        }
      }
    }

    if (!editorToolbar) {
      console.log('Toolbar still not found, will retry after delay');
      // Retry after a short delay if toolbar not found
      setTimeout(() =>
        addFormulaButton(id, saveSelectionFn, setIsFormulaDialogOpen),
        200
      );
      return;
    }
  } catch (error) {
    console.error('Error finding toolbar:', error);
    // Retry after a short delay if there was an error
    setTimeout(() =>
      addFormulaButton(id, saveSelectionFn, setIsFormulaDialogOpen),
      200
    );
    return;
  }

  console.log('Toolbar found:', editorToolbar);

  // Skip if formula button already exists
  if (document.getElementById(`formula-btn-custom-${id}`)) {
    return;
  }

  try {
    // Check if formula button already exists
    const existingButton = document.getElementById(`formula-btn-custom-${id}`);
    if (existingButton) {
      console.log('Formula button already exists, skipping creation');
      return;
    }

    console.log('Creating formula button element');

    // Create a custom formula button
    const formulaButton = document.createElement('button');
    formulaButton.id = `formula-btn-custom-${id}`;
    formulaButton.className = 'e-tbar-btn e-btn';
    formulaButton.title = 'Công thức toán học';
    formulaButton.style.fontWeight = 'bold';
    formulaButton.innerHTML =
      '<span class="e-btn-icon e-icons e-formula-icon"></span>';

    // Create a wrapper div with appropriate classes
    const buttonWrapper = document.createElement('div');
    buttonWrapper.className = 'e-toolbar-item e-custom-item';
    buttonWrapper.appendChild(formulaButton);

    // Find position to insert (before ClearFormat button or at the end)
    let clearFormatItem: Element | null = null;

    if (editorToolbar.children) {
      // Convert HTMLCollection to Array and find the ClearFormat button
      clearFormatItem = Array.from(editorToolbar.children).find((item: Element) => {
        return item.textContent && item.textContent.includes('ClearFormat');
      }) || null;
    }

    console.log('ClearFormat item found:', !!clearFormatItem);

    // Insert at appropriate position
    if (clearFormatItem) {
      console.log('Inserting before ClearFormat button');
      editorToolbar.insertBefore(buttonWrapper, clearFormatItem);
    } else {
      console.log('Appending to the end of toolbar');
      editorToolbar.appendChild(buttonWrapper);
    }

    console.log('Formula button created and inserted');

    // Add custom CSS for formula icon if needed
    if (!document.getElementById('formula-icon-style')) {
      console.log('Adding formula icon CSS');
      const style = document.createElement('style');
      style.id = 'formula-icon-style';
      style.textContent = `
        .e-formula-icon:before {
          content: "∑";
          font-weight: bold;
          font-size: 18px;
          color: #333;
        }
        .e-formula-icon:hover:before {
          color: #1890ff;
        }
        /* Additional styles to ensure visibility */
        .e-toolbar-item .e-tbar-btn .e-formula-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          vertical-align: middle;
        }
        /* Make the button more visible */
        #formula-btn-custom-${id} {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 28px;
          min-height: 28px;
          padding: 4px;
          margin: 2px;
          border: 1px solid transparent;
        }
        #formula-btn-custom-${id}:hover {
          background-color: #f0f0f0;
          border-color: #d9d9d9;
        }
      `;
      document.head.appendChild(style);
      console.log('Formula icon CSS added');
    }

    // Add event listener to the formula button
    formulaButton.addEventListener('click', (e) => {
      e.preventDefault();
      saveSelectionFn();
      setIsFormulaDialogOpen(true);
    });
  } catch (error) {
    console.error('Error adding formula button:', error);
  }
};

/**
 * Set up click handler for formula elements in the editor
 * @param toolbarItems Toolbar items array
 * @param saveSelectionFn Function to save the current selection
 * @param setCurrentFormula Function to set the current formula
 * @param setIsFormulaDialogOpen Function to open the formula dialog
 * @returns Cleanup function
 */
export const setupFormulaClickHandler = (
  saveSelectionFn: () => void,
  setCurrentFormula: (formula: string) => void,
  setIsFormulaDialogOpen: (isOpen: boolean) => void
): (() => void) => {
  const editorContent = document.querySelector(
    '.e-richtexteditor .e-content'
  );

  if (!editorContent) {
    return () => {};
  }

  // Remove any existing click handlers to prevent duplicates
  const oldHandler = (editorContent as any)._formulaClickHandler;
  if (oldHandler) {
    editorContent.removeEventListener('click', oldHandler);
  }

  // Create a new click handler
  const formulaClickHandler = (e: Event) => {
    const mouseEvent = e as MouseEvent;
    const target = mouseEvent.target as HTMLElement;

    // Check if the clicked element is a formula or contains a formula
    const formulaElement =
      target.closest('.mathjax-formula') ||
      target.closest('.katex-formula') ||
      target.closest('.math-latex') ||
      target.closest('span[data-latex]');

    if (formulaElement) {
      // Save the current selection
      saveSelectionFn();

      // Get the formula LaTeX from the data-latex attribute
      const latex = formulaElement.getAttribute('data-latex');
      if (latex) {
        try {
          // Set the current formula and open the dialog
          const decodedLatex = decodeURIComponent(latex);
          setCurrentFormula(decodedLatex);
          setIsFormulaDialogOpen(true);
        } catch (error) {
          // Try to use the raw latex if decoding fails
          setCurrentFormula(latex);
          setIsFormulaDialogOpen(true);
        }
      } else {
        // Try to extract LaTeX from the element's content as a fallback
        const content = formulaElement.textContent || '';
        if (content.includes('\\(') && content.includes('\\)')) {
          const extractedLatex = content.substring(
            content.indexOf('\\(') + 2,
            content.lastIndexOf('\\)')
          );
          if (extractedLatex) {
            setCurrentFormula(extractedLatex);
            setIsFormulaDialogOpen(true);
          }
        }
      }
    }
  };

  // Store the handler reference for later removal
  (editorContent as any)._formulaClickHandler = formulaClickHandler;

  // Add click event listener to the editor content
  editorContent.addEventListener(
    'click',
    formulaClickHandler as EventListener
  );

  // Return a cleanup function
  return () => {
    if (editorContent) {
      editorContent.removeEventListener(
        'click',
        formulaClickHandler as EventListener
      );
    }
  };
};

/**
 * Insert a formula into the editor
 * @param formula LaTeX formula to insert
 * @param editorRef Reference to the editor component
 * @param savedRange Previously saved selection range
 * @param restoreSelectionFn Function to restore the selection
 * @param setIsFormulaDialogOpen Function to close the formula dialog
 */
export const insertFormula = (
  formula: string,
  editorRef: any,
  savedRange: Range | null,
  restoreSelectionFn: () => void,
  setIsFormulaDialogOpen: (isOpen: boolean) => void
): void => {
  // Validate formula
  if (!formula || formula.trim() === '') {
    return;
  }

  // Restore the saved selection
  restoreSelectionFn();

  if (!editorRef) {
    return;
  }

  // Create a unique ID for this formula to help with editing later
  const formulaId = `formula-${Date.now()}`;

  // Use mathjax-formula class for consistency with the detection code
  const formulaHtml = `<span class="mathjax-formula" id="${formulaId}" data-latex="${encodeURIComponent(
    formula
  )}">\\(${formula}\\)</span>&nbsp;`;

  // Ensure the editor is focused before inserting
  if (typeof editorRef.focus === 'function') {
    editorRef.focus();
  }

  // Small delay to ensure focus is complete
  setTimeout(() => {
    // Insert the formula HTML
    const insertSuccess = insertHtmlAtCursor(formulaHtml, editorRef, savedRange);

    if (insertSuccess) {
      // Typeset MathJax formulas after a short delay
      setTimeout(() => {
        typesetMathJax();

        // Force a second typesetting after a longer delay to catch any missed formulas
        setTimeout(typesetMathJax, 500);
      }, 200);

      // Close the formula dialog
      setIsFormulaDialogOpen(false);
    } else {
      alert('Không thể chèn công thức. Vui lòng thử lại.');
    }
  }, 100);
};

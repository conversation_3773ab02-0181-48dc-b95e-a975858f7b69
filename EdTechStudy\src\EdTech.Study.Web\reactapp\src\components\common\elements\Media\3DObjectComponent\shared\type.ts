export interface Model3DItem extends MediaItem {
  originalUrl?: string;
  /** Format of the 3D model */
  format?: 'glb' | 'obj' | 'fbx';
}

import { ITextProps } from '../../../../../core/title/CoreTitle';
import { MediaComponentProps, MediaItem } from '../../shared/type';

export interface Model3DComponentProps extends MediaComponentProps{
  titleProps?: ITextProps;

  medias?: Model3DItem[];
  
  format?: 'glb' | 'obj';

  autoRotate?: boolean;

  rotationSpeed?: number;

  width?: number | string;

  height?: number | string;

  description?: string;

  displayMode?: 'horizontal' | 'vertical';

  instructionsContent?: {
    objective: string;
    steps: string[];
    notes?: string;
  };
}
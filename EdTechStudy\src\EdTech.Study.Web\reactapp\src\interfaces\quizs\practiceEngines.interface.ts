import { Question } from './questionBase';

export interface PracticeEnginesProps {
  id: string;
  showConfig?: boolean;
  questions: Question[];
  showProgressBar?: boolean;
  disableCreateQuestion?: boolean;
  onCreateQuestion?: (question: Question, index?: number) => any;
  onComplete?: (results: PracticeResult) => void;
  onRefresh?: () => void;
  onShowMenu?: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void;
}

export interface PracticeEnginesRef {
  changeMode: (value: string) => any;
  goToQuestion: (id: string) => any;
  forceReset: () => void;
}

export interface PracticeResult {
  totalQuestions: number;
  correctQuestions: number;
  score: number;
  completedQuestions: {
    [key: string]: {
      complete: boolean;
      userSelect?: any;
      isCorrect?: boolean;
    };
  };
}

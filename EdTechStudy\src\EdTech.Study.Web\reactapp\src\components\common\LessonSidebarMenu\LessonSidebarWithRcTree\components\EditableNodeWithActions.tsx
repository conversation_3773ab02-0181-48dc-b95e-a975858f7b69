import React from 'react';
import { Tooltip } from 'antd';
import { DownOutlined, RightOutlined, MenuOutlined } from '@ant-design/icons';
import { TreeMenuItem } from '../types';
import { ETypeEdTechComponent } from '../../../../../enums/AppEnums';
import TreeNodeActions from './TreeNodeActions';
import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';

interface EditableNodeWithActionsProps {
  nodeData: TreeMenuItem;
  level: number;
  isExpanded: boolean;
  hasChildren: boolean;
  handleExpandClick: (e: React.MouseEvent) => void;
  onAdd: (e: React.MouseEvent) => void;
  onEdit: (e: React.MouseEvent) => void;
  canAddChildren: boolean;
}

const EditableNodeWithActions: React.FC<EditableNodeWithActionsProps> = ({
  nodeData,
  level,
  isExpanded,
  hasChildren,
  handleExpandClick,
  onAdd,
  onEdit,
  canAddChildren,
}) => {
  const paddingStyle = {
    paddingLeft: `${level * 16}px`,
  };

  return (
    <div
      className={`rc-tree-node-content ${
        nodeData.type === ETypeEdTechComponent.LAYOUT
          ? `tailwind-font-roboto tailwind-font-semibold tailwind-text-[#908588]`
          : ''
      }`}
      style={paddingStyle}
    >
      <div className="content-drag-drop tailwind-pr-2">
        <MenuOutlined />
      </div>
      <Tooltip title={nodeData.title}>
        <span className="node-title">
          {hasChildren && (
            <span className="expand-icon" onClick={handleExpandClick}>
              {isExpanded ? <DownOutlined /> : <RightOutlined />}
            </span>
          )}
          {nodeData.title}
        </span>
      </Tooltip>
      <TreeNodeActions
        nodeData={
          {
            id: nodeData.key,
            path: nodeData.path || '',
            title: nodeData.title || '',
            type: nodeData.type,
            name: nodeData.name,
          } as IEdTechRenderTreeData
        }
        canAddChildren={canAddChildren}
        onAdd={onAdd}
        onEdit={onEdit}
      />
    </div>
  );
};

export default EditableNodeWithActions;

export interface ILessonData<T = any> {
  Name: string;
  Title: string;
  DataProps: T;
}

export interface ISubItem {
  key: string;
  label: string;
  type:
    | 'lesson-name'
    | 'subject-objectives'
    | 'lecture'
    | 'game'
    | 'practice'
    | 'configuration';
}

export interface ILessonCommonComponentProps {
  LessonContent?: ILessonData[];
  LessonMiniGame?: ILessonData[];
  LessonQuiz?: ILessonData[];
  isHideSidebar?: boolean;
  subitems?: ISubItem[];
}

/* Styles for component highlighting */
.engine-highlight-hover {
  border: 1px solid var(--edtt-color-secondary-base);
  border-radius: 4px;
  transition: transform 0.25s cubic-bezier(0.25, 0.1, 0.25, 1),
    box-shadow 0.25s cubic-bezier(0.25, 0.1, 0.25, 1),
    background-color 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform, box-shadow;
}

/* Style cho component được active */
.engine-active-content {
  position: relative;
  border: 1px solid var(--edtt-color-primary-base);
  border-radius: 4px;
  /* Only round the bottom corners */
  background-color: var(--edtt-color-bg-default) !important;
  border-radius: 5px 0px 5px 5px;
  z-index: 5;
  will-change: transform, box-shadow;
  /* No top border to connect with header */
}

[data-name="LessonCardComponent"]>div>.engine-active-content {
  margin-top: 30px;
}

/* Style cho header mới theo design */
.engine-simple-header {
  position: absolute;
  top: -30px;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: var(--edtt-color-bg-default);
  z-index: 10;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border: 1px solid var(--edtt-color-primary-base);
  border-bottom: none;
}

.engine-title {
  font-weight: 500;
  font-size: 14px;
  color: var(--edtt-color-text-default);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.engine-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

/* Legacy styles for backward compatibility */
.engine-label, .engine-toolbar {
  display: none;
}

/* Hiệu ứng transition */
.engine-highlight-hover,
.engine-highlight-parent,
.engine-active-content {
  will-change: transform, box-shadow;
  transition: transform 0.25s cubic-bezier(0.25, 0.1, 0.25, 1),
    box-shadow 0.25s cubic-bezier(0.25, 0.1, 0.25, 1),
    background-color 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.engine-active-content:hover {
  transform: translateY(-5px);
}
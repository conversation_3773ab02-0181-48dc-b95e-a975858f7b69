import { Guid } from 'guid-typescript';
import { ShortAnswerQuestion } from '../../../interfaces/quizs/shortAnswer.interface';

export const createShortAnswerQuestionTemplate = (
  title: string = '<PERSON>âu hỏi trả lời ngắn'
): ShortAnswerQuestion => {
  const id = Guid.create().toString();
  return {
    id,
    type: 'shortanswer',
    title,
    question: 'Điền câu trả lời ngắn gọn của bạn vào ô dưới đây:',
    correctAnswer: 'Đáp án mẫu',
    explanation: '<PERSON><PERSON><PERSON><PERSON> thích cho đáp án',
    points: 1,
    caseSensitive: false,
    allowPartialMatch: true,
    maxLength: 200,
  };
};

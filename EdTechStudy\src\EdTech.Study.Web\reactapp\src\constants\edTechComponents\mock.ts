import FractionDemoAddition from '../../components/mocks/FractionDemo/FractionDemoAddition';
import FractionDemoDivision from '../../components/mocks/FractionDemo/FractionDemoDivision';
import FractionDemoMultiplication from '../../components/mocks/FractionDemo/FractionDemoMultiplication';
import FractionDemoOperation from '../../components/mocks/FractionDemo/FractionDemoOperation';
import FractionDemoSubtraction from '../../components/mocks/FractionDemo/FractionDemoSubtraction';
import {
  fractionSchemaAddition,
  fractionSchemaSubtraction,
  fractionSchemaMultiplication,
  fractionSchemaDivision,
  fractionSchemaOperation,
  convertFractionDemoVersion,
} from '../../components/mocks/FractionDemo/FractionDemoUtils';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const MOCK_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'FractionDemo',
    title: 'Phép tính với phân số',
    components: [
      {
        version: 'Phép tính',
        component: FractionDemoOperation,
        schema: fractionSchemaOperation,
      },
      {
        version: 'Phép cộng',
        component: FractionDemoAddition,
        schema: fractionSchemaAddition,
        description: `Phép cộng phân số`,
      },
      {
        version: 'Phép trừ',
        component: FractionDemoSubtraction,
        schema: fractionSchemaSubtraction,
        description: `Phép trừ phân số`,
      },
      {
        version: 'Phép nhân',
        component: FractionDemoMultiplication,
        schema: fractionSchemaMultiplication,
        description: `Phép nhân phân số`,
      },
      {
        version: 'Phép chia',
        component: FractionDemoDivision,
        schema: fractionSchemaDivision,
        description: `Phép chia phân số`,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Toán học', 'Phân số', 'Phép tính'],
    description:
      'Component cho phép nhập một phân số và chọn loại phép tính (cộng, trừ, nhân, chia) để thực hiện. Mỗi phiên bản tập trung vào một phép tính.',
    schema: fractionSchemaOperation,
    convertVersion: convertFractionDemoVersion,
  },
];

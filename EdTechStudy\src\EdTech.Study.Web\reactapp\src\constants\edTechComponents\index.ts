import { IEdTechComponent } from '../../interfaces/AppComponents';
// Mini Games

// Import schemas from components
import { CONTENT_ED_TECH_COMPONENT } from './content';
import { PRESENTATION_ED_TECH_COMPONENT } from './presentation';
import { LAYOUT_ED_TECH_COMPONENT } from './layout';
import { COMMON_ED_TECH_COMPONENT } from './common';
import { SIMULATOR_ED_TECH_COMPONENT } from './simulator';
import { STRUCTURE_ED_TECH_COMPONENT } from './structure';
import { MINI_GAME_ED_TECH_COMPONENT } from './miniGame';
import { PRACTICES_ED_TECH_COMPONENT } from './practices';
import { MOCK_ED_TECH_COMPONENT } from './mock';
export const ED_TECH_COMPONENT: IEdTechComponent[] = [
  ...COMMON_ED_TECH_COMPONENT,
  ...LAYOUT_ED_TECH_COMPONENT,
  ...PRESENTATION_ED_TECH_COMPONENT,
  ...CONTENT_ED_TECH_COMPONENT,
  ...STRUCTURE_ED_TECH_COMPONENT,
  ...MINI_GAME_ED_TECH_COMPONENT,
  ...SIMULATOR_ED_TECH_COMPONENT,
  ...PRACTICES_ED_TECH_COMPONENT,
  ...MOCK_ED_TECH_COMPONENT,
];

import { useRef, useEffect, useCallback } from 'react';

interface DragResizeOptions {
  onChange: (delta: number) => void;
  direction: 'horizontal' | 'vertical';
}

export function useDragResize({ onChange, direction }: DragResizeOptions) {
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const isDraggingRef = useRef(false);
  const startPosRef = useRef(0);

  useEffect(() => {
    overlayRef.current = document.createElement('div');
    overlayRef.current.style.position = 'fixed';
    overlayRef.current.style.top = '0';
    overlayRef.current.style.left = '0';
    overlayRef.current.style.right = '0';
    overlayRef.current.style.bottom = '0';
    overlayRef.current.style.zIndex = '1000';
    overlayRef.current.style.cursor =
      direction === 'horizontal' ? 'col-resize' : 'row-resize';
    overlayRef.current.style.display = 'none';
    document.body.appendChild(overlayRef.current);

    return () => {
      if (overlayRef.current) {
        document.body.removeChild(overlayRef.current);
      }
    };
  }, [direction]);

  const startDrag = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (!overlayRef.current) return;

      isDraggingRef.current = true;
      startPosRef.current = direction === 'horizontal' ? e.clientX : e.clientY;
      document.body.style.cursor =
        direction === 'horizontal' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';
      overlayRef.current.style.display = 'block';

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDraggingRef.current) return;
        const currentPos = direction === 'horizontal' ? e.clientX : e.clientY;
        const delta = currentPos - startPosRef.current;
        onChange(delta);
      };

      const handleMouseUp = () => {
        if (!isDraggingRef.current || !overlayRef.current) return;
        isDraggingRef.current = false;
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        overlayRef.current.style.display = 'none';
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    [onChange, direction]
  );

  return { startDrag };
}

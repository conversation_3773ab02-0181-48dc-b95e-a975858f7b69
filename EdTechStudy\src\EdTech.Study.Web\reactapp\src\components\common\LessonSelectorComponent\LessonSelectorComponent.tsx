import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON>dal, Button, Typography, Tabs } from 'antd';
import {
  CloseOutlined,
  AppstoreOutlined,
  BlockOutlined,
  AppstoreAddOutlined,
} from '@ant-design/icons';
import { ETypeEdTechComponent } from '../../../enums/AppEnums';
import {
  IEdTechComponent,
  IEdTechRenderProps,
  IEdTechRenderTreeData,
} from '../../../interfaces/AppComponents';
import { typeIcons } from './utils/typeIcons';
import ComponentGrid from './components/ComponentGrid';
import StructureGrid from './components/StructureGrid';
import SelectedComponents from './components/SelectedComponents';
import SearchAndFilter from './components/SearchAndFilter';
import { getLocalizedType } from '../../../utils/localizationUtils';
import { connect } from 'react-redux';
import {
  addItems,
  addOrUpdateTreeStructure,
  removeItem,
} from '../../../store/slices/AppSlices/EdTechRenderDataSlice';
import {
  IEdTechUpdateStructurePayload,
  NodeWithItemsPayload,
} from '../../../store/slices/utils/createTreeDataSlice';
import { Modal as AntModal } from 'antd';
import { useGridStructure } from '../../../hooks/grid/useGridStructure';
import { ED_TECH_COMPONENT } from '../../../constants/edTechComponents';

const { Title, Text } = Typography;

// Constants for tag filtering
const ALL_TAB_VALUE = 'all';
const STRUCTURE_TAB_VALUE = 'STRUCTURE';

export interface ILessonSelectorComponentMapDispatchToProps {
  addItems: (payload: NodeWithItemsPayload) => void;
  addOrUpdateTreeStructure: (payload: IEdTechUpdateStructurePayload) => void;
  removeItem: (target: IEdTechRenderTreeData) => void;
}

interface ILessonSelectorComponentProps
  extends ILessonSelectorComponentMapDispatchToProps {
  onSelectComponent?: (components: IEdTechComponent[]) => void;
  hideButton?: boolean;
  isOpen?: boolean;
  onClose?: () => void;
  parentNode: IEdTechRenderTreeData | undefined;
}

const LessonSelectorComponent: React.FC<ILessonSelectorComponentProps> = ({
  hideButton = false,
  isOpen,
  onClose = () => {},
  onSelectComponent,
  parentNode: selectNode,
}) => {
  const gridStructure = useGridStructure();
  const components = ED_TECH_COMPONENT.filter(
    (x) => x.name !== 'LessonRowComponent' && x.name !== 'LessonColumnComponent'
  );
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const [selectedComponents, setSelectedComponents] = useState<
    IEdTechComponent[]
  >([]);
  const [activeTab, setActiveTab] = useState<string>(ALL_TAB_VALUE);

  useEffect(() => {
    if (isOpen !== undefined) {
      setIsModalVisible(isOpen);
    }
  }, [isOpen]);

  const componentTypes = useMemo(() => {
    const types = components.map((comp) => comp.type.toString());
    return Array.from(new Set(types));
  }, [components]);

  const tags = useMemo(() => {
    const allTags = components
      .flatMap((comp) => comp.tags || [])
      .filter(Boolean);
    return Array.from(new Set(allTags));
  }, [components]);

  const filteredComponents = useMemo(() => {
    return components.filter((component) => {
      const matchesType =
        activeTab === ALL_TAB_VALUE || component.type.toString() === activeTab;
      const matchesSearch =
        searchTerm.trim() === '' ||
        component.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (component.description
          ? component.description
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          : false);

      // Nếu không chọn tag nào, tức là chọn tất cả
      const matchesTags =
        filterTags.length === 0 ||
        (component.tags &&
          filterTags.every((tag) => component.tags?.includes(tag)));

      return matchesType && matchesSearch && matchesTags;
    });
  }, [components, searchTerm, filterTags, activeTab]);

  const handleOpenModal = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
    setSearchTerm('');
    setFilterTags([]);
    setSelectedComponents([]);
    onClose();
  };

  const handleSelectComponent = async (component: IEdTechComponent) => {
    // Check if component is STRUCTURE type
    const isStructureType = component.type === ETypeEdTechComponent.STRUCTURE;

    // Check if there are already selected components
    const hasSelectedComponents = selectedComponents.length > 0;

    // Check if there are already STRUCTURE type components selected
    const hasStructureType = selectedComponents.some(
      (c) => c.type === ETypeEdTechComponent.STRUCTURE
    );

    // If trying to select a STRUCTURE type when other types are selected
    if (isStructureType && hasSelectedComponents && !hasStructureType) {
      const confirmed = await new Promise<boolean>((resolve) => {
        AntModal.confirm({
          title: 'Thay đổi cấu trúc',
          content:
            'Bạn đang chọn một cấu trúc mới. Điều này sẽ thay thế các engine đã chọn. Bạn có chắc chắn?',
          okText: 'Đồng ý',
          cancelText: 'Hủy',
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });

      if (confirmed) {
        // Clear all selected components and add the new STRUCTURE component
        setSelectedComponents([component]);
        return;
      }
      return;
    }

    // If trying to select a non-STRUCTURE type when STRUCTURE type is selected
    if (!isStructureType && hasStructureType) {
      AntModal.warning({
        title: 'Không thể chọn',
        content: 'Không thể chọn engine thường khi đã chọn engine cấu trúc.',
      });
      return;
    }

    setSelectedComponents((prev) =>
      prev.some((c) => c.name === component.name)
        ? prev.filter((c) => c.name !== component.name)
        : [...prev, component]
    );
  };

  const handleRemoveComponent = (componentName: string) => {
    setSelectedComponents((prev) =>
      prev.filter((c) => c.name !== componentName)
    );
  };

  const handleSave = () => {
    // Thoát nếu không có thành phần nào được chọn
    if (!selectedComponents.length) return;

    const firstComponent = selectedComponents[0];

    // Trường hợp 1: Thêm cấu trúc đặc biệt
    if (
      firstComponent.type === ETypeEdTechComponent.STRUCTURE &&
      firstComponent.structure
    ) {
      // Sử dụng phương thức đặc biệt dành cho cấu trúc đã định nghĩa sẵn
      gridStructure.updateSpecialStructure(undefined, firstComponent.structure);
    }
    // Các trường hợp còn lại: Sử dụng updateGridStructure
    else if (selectNode) {
      // Sử dụng phương thức updateGridStructure tự động xác định loại cấu trúc
      gridStructure.updateGridStructure(
        selectNode as IEdTechRenderProps,
        selectedComponents
      );
    }

    // Thông báo lựa chọn thành phần và đóng modal
    onSelectComponent?.(selectedComponents);
    handleCloseModal();
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const modalFooter = (
    <div className="tailwind-flex tailwind-items-center tailwind-justify-between">
      <Text className="tailwind-text-sm tailwind-text-gray-500">
        Tìm thấy {filteredComponents.length} component
        {filterTags.length > 0
          ? ` trong danh mục "${filterTags.join(', ')}" (AND)`
          : ''}
      </Text>
      <div className="tailwind-flex tailwind-gap-3">
        <Button
          type="primary"
          onClick={handleSave}
          disabled={selectedComponents.length === 0}
          className="!tailwind-bg-blue-600 hover:!tailwind-bg-blue-700 tailwind-border-none tailwind-shadow-sm tailwind-font-medium"
        >
          Lưu{' '}
          {selectedComponents.length > 0 && `(${selectedComponents.length})`}
        </Button>
        <Button
          onClick={handleCloseModal}
          icon={<CloseOutlined />}
          className="hover:tailwind-bg-gray-50"
        >
          Đóng
        </Button>
      </div>
    </div>
  );

  const tabItems = [
    {
      label: (
        <span>
          <AppstoreOutlined /> Tất cả
        </span>
      ),
      key: ALL_TAB_VALUE,
      children: (
        <ComponentGrid
          components={filteredComponents}
          selectedComponents={selectedComponents}
          onSelectComponent={handleSelectComponent}
        />
      ),
    },
    {
      label: (
        <span>
          <BlockOutlined /> Cấu trúc
        </span>
      ),
      key: STRUCTURE_TAB_VALUE,
      children: (
        <StructureGrid
          components={filteredComponents.filter(
            (comp) => comp.type === ETypeEdTechComponent.STRUCTURE
          )}
          selectedComponents={selectedComponents}
          onSelectComponent={handleSelectComponent}
        />
      ),
    },
    ...componentTypes
      .filter(
        (type) =>
          type !== ETypeEdTechComponent.STRUCTURE.toString() &&
          type !== ETypeEdTechComponent.PRESENTATION.toString()
      )
      .map((type) => ({
        label: (
          <span>
            {React.createElement(typeIcons[type] || AppstoreOutlined)}{' '}
            {getLocalizedType(type)}
          </span>
        ),
        key: type,
        children: (
          <ComponentGrid
            components={filteredComponents}
            selectedComponents={selectedComponents}
            onSelectComponent={handleSelectComponent}
          />
        ),
      })),
  ];
  return (
    <>
      {!hideButton && (
        <Button
          type="primary"
          onClick={handleOpenModal}
          icon={<AppstoreAddOutlined />}
          size="large"
        >
          <span className="tailwind-text-base tailwind-font-medium">
            Thêm phần tử
          </span>
        </Button>
      )}
      <Modal
        title={
          <div className="tailwind-flex tailwind-flex-col">
            <Title level={4} className="!tailwind-text-white tailwind-m-0">
              Thêm engine vào bài giảng
            </Title>
            <Text className="!tailwind-text-blue-100 tailwind-mt-1 tailwind-opacity-90">
              Chọn một hoặc <strong>nhiều engine </strong> tương tác để thêm vào
              bài giảng.{' '}
              <span className="tailwind-underline">
                Bạn có thể chọn từ nhiều tab khác nhau.
              </span>
            </Text>
          </div>
        }
        open={isModalVisible}
        onCancel={handleCloseModal}
        footer={modalFooter}
        width="85%"
        className="tailwind-top-10"
        closable
        maskClosable={false}
        destroyOnHidden={true}
        closeIcon={<CloseOutlined className="!tailwind-text-white" />}
        style={{ top: 20 }}
        styles={{
          body: {
            padding: 0,
            maxHeight: 'calc(90vh - 100px)',
            overflow: 'hidden',
          },
          header: {
            backgroundColor: '#1890ff',
            padding: '16px 24px',
            color: 'white',
            borderBottom: 'none',
            borderRadius: '8px 8px 0 0',
          },
          content: {
            padding: 0,
          },
          wrapper: {
            padding: 0,
          },
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.45)',
          },
          footer: {
            borderTop: '1px solid #f0f0f0',
            padding: '12px 24px',
          },
        }}
      >
        <div className="tailwind-p-6">
          <SelectedComponents
            selectedComponents={selectedComponents}
            onRemoveComponent={handleRemoveComponent}
            onClearAll={() => setSelectedComponents([])}
          />
          <div className="tailwind-mt-4">
            <SearchAndFilter
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              tags={tags}
              selectedTags={filterTags}
              onTagsChange={setFilterTags}
            />
          </div>
          <div className="tailwind-mt-4">
            <Tabs
              items={tabItems}
              activeKey={activeTab}
              onChange={handleTabChange}
              className="component-tabs"
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

const mapDispatchToProps = (
  dispatch: any
): ILessonSelectorComponentMapDispatchToProps => ({
  addItems: (payload) => {
    dispatch(addItems(payload));
  },
  addOrUpdateTreeStructure: (payload) => {
    dispatch(addOrUpdateTreeStructure(payload));
  },
  removeItem: (target) => {
    dispatch(removeItem({ targetNode: target }));
  },
});

export default connect(null, mapDispatchToProps)(LessonSelectorComponent);

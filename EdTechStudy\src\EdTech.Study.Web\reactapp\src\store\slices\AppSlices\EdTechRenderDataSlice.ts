import {
  createTreeDataSlice,
  ITreeDataState,
  TreeUtils,
} from '../utils/createTreeDataSlice';

// Constants
const SLICE_NAME = 'ED_TECH_RENDER_TREE_DATA';

// Create the slice using the factory function
export const EdTechRenderTreeDataSlice = createTreeDataSlice(SLICE_NAME);

// Re-export the state interface and types
export type IEdTechRenderTreeDataState = ITreeDataState;
export { TreeUtils };

// Export actions
export const {
  initData,
  addItems,
  updateItems,
  addOrUpdateItems,
  addOrUpdateTreeStructure,
  removeItem,
  clearData,
  setLoading,
  setError,
} = EdTechRenderTreeDataSlice.actions;

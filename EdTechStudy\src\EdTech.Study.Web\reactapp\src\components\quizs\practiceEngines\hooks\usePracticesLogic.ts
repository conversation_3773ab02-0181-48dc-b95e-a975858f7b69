import { lazy, useEffect, useState } from 'react';
import {
  registerQuestionComponents,
  QuestionComponentBaseProps,
} from '../../../../interfaces/quizs/questionBase';
import practiceLocalization from '../../localization';

const FillBlanksComponent = lazy(() =>
  import('../../fillblanks').then((module) => ({
    default: module.FillBlanksComponent,
  }))
);
const MatchingComponent = lazy(() =>
  import('../../matching').then((module) => ({
    default: module.MatchingComponent,
  }))
);
const MultiSelectQuizComponent = lazy(() =>
  import('../../multiselect').then((module) => ({
    default: module.MultiSelectQuizComponent,
  }))
);
const QuizComponent = lazy(() =>
  import('../../quiz').then((module) => ({ default: module.QuizComponent }))
);
const ShortAnswerComponent = lazy(() =>
  import('../../shortanswer').then((module) => ({
    default: module.ShortAnswerComponent,
  }))
);

import {
  QuestionTemplateFactory,
  createQuizQuestionTemplate,
  createMatchingQuestionTemplate,
  createFillBlanksQuestionTemplate,
  createMultiSelectQuestionTemplate,
  createShortAnswerQuestionTemplate,
} from '../questionTemplates';

export interface PracticesLogicProps {
  //
}

export interface PracticesLogicResult {
  //
}

const usePracticesLogic = (
  props?: PracticesLogicProps
): PracticesLogicResult => {
  const [_, setTrigger] = useState<Boolean>(false);
  useEffect(() => {
    // For creating new instances
    const factory = QuestionTemplateFactory.getInstance();
    factory.register(practiceLocalization.quiz, createQuizQuestionTemplate); // prettier-ignore
    factory.register(practiceLocalization.matching, createMatchingQuestionTemplate); // prettier-ignore
    factory.register(practiceLocalization.fillblanks, createFillBlanksQuestionTemplate); // prettier-ignore
    factory.register(practiceLocalization.multiselect, createMultiSelectQuestionTemplate); // prettier-ignore
    factory.register(practiceLocalization.shortanswer, createShortAnswerQuestionTemplate); // prettier-ignore

    // For rendering Components
    registerQuestionComponents(practiceLocalization.quiz, {
      name: practiceLocalization.quiz,
      description: practiceLocalization.quiz_description,
      component: QuizComponent as React.LazyExoticComponent<
        React.FC<QuestionComponentBaseProps>
      >,
    });
    registerQuestionComponents(practiceLocalization.matching, {
      name: practiceLocalization.matching,
      description: practiceLocalization.matching_description,
      component: MatchingComponent as React.LazyExoticComponent<
        React.FC<QuestionComponentBaseProps>
      >,
    });
    registerQuestionComponents(practiceLocalization.fillblanks, {
      name: practiceLocalization.fillblanks,
      description: practiceLocalization.fillblanks_description,
      component: FillBlanksComponent as React.LazyExoticComponent<
        React.FC<QuestionComponentBaseProps>
      >,
    });
    registerQuestionComponents(practiceLocalization.multiselect, {
      name: practiceLocalization.multiselect,
      description: practiceLocalization.multiselect_description,
      component: MultiSelectQuizComponent as React.LazyExoticComponent<
        React.FC<QuestionComponentBaseProps>
      >,
    });
    registerQuestionComponents(practiceLocalization.shortanswer, {
      name: practiceLocalization.shortanswer,
      description: practiceLocalization.shortanswer_description,
      component: ShortAnswerComponent as React.LazyExoticComponent<
        React.FC<QuestionComponentBaseProps>
      >,
    });
    setTrigger(true);
  }, []);

  return {};
};

export default usePracticesLogic;

import React, { useState, useEffect, memo, useMemo } from 'react';
import '../MiniGameResponsive.css';
import {
  MillionaireGameConfig,
  defaultMillionaireGameConfig,
} from './MillionaireGameConfig';
import { MillionaireGameComponent } from './MillionaireGameComponent';
import MillionaireGameModal from './MillionaireGameModal';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { getQuestionsForGame } from '../../common/QuestionComponent/QuestionBankConfig';
import { EngineContainer } from '../../common/engines';
import { ITextProps } from '../../core/title/CoreTitle';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

// Extend MillionaireGameProps to include titleProps
interface MillionaireGameProps extends MillionaireGameConfig {}

export const millionaireSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    questions: z
      .array(
        z.object({
          question: z.string(),
          options: z.array(z.string()),
          correctAnswer: z.number(),
          difficulty: z.enum(['easy', 'medium', 'hard']),
        })
      )
      .optional(),
    lifelines: z
      .object({
        fiftyFifty: z.boolean().optional(),
        audience: z.boolean().optional(),
        phone: z.boolean().optional(),
      })
      .optional(),
  },
});

const MillionaireGame: React.FC<IEdTechRenderProps<MillionaireGameProps>> = (
  props
) => {
  const { params, addOrUpdateParamComponent, path } = props;
  // State for game configuration
  const [gameConfig, setGameConfig] = useState<MillionaireGameProps>({
    ...defaultMillionaireGameConfig,
    ...params,
  });

  // State for fullscreen and config modal
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);

  // Update configuration when params change (from Redux)
  useEffect(() => {
    let newConfig = {
      ...defaultMillionaireGameConfig,
    };
    if (params) {
      newConfig = { ...params };
    }
    // Kiểm tra xem có câu hỏi trong params không
    const hasQuestions = newConfig.questions && newConfig.questions.length > 0;
    // Đảm bảo danh sách câu hỏi được giữ nguyên từ params nếu có
    if (!hasQuestions) {
      // Nếu không có câu hỏi trong params, sử dụng câu hỏi mặc định
      newConfig.questions = getQuestionsForGame(15);
    }
    setGameConfig(newConfig);
  }, [params]);

  // Handle game config changes
  const handleConfigSubmit = (config: MillionaireGameConfig) => {
    if (addOrUpdateParamComponent) {
      // Preserve titleProps when updating config
      const updatedConfig = {
        ...config,
        titleProps: gameConfig.titleProps,
      };
      addOrUpdateParamComponent(updatedConfig);
      setGameConfig(updatedConfig);
    }

    // Close the modal
    setIsConfigModalOpen(false);
  };

  // Handle title updates
  const handleTitleUpdate = (titleProps: Partial<ITextProps>) => {
    if (addOrUpdateParamComponent) {
      const updatedConfig = {
        ...gameConfig,
        titleProps: {
          ...gameConfig.titleProps,
          ...titleProps,
        },
      };
      addOrUpdateParamComponent(updatedConfig);
      setGameConfig(updatedConfig);
    }
  };

  // Handle fullscreen state changes
  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  // Create text props for the title
  const titleProps = gameConfig.titleProps || {
    text: 'Ai Là Triệu Phú',
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Memoize the structured instruction props
  const instructionsContent = useMemo(
    () => ({
      objective:
        'Trả lời chính xác các câu hỏi để leo lên các mốc tiền thưởng và trở thành triệu phú!',
      steps: [
        'Mỗi câu hỏi có 4 đáp án A, B, C, D, chỉ một đáp án đúng',
        `Thời gian giới hạn cho mỗi câu hỏi là ${gameConfig.timeLimit} giây`,
        'Trả lời đúng để nhận tiền thưởng và tiếp tục chơi',
        'Trả lời sai sẽ kết thúc trò chơi và bạn chỉ nhận được tiền thưởng ở mốc an toàn gần nhất',
        'Các mốc an toàn giúp bạn đảm bảo tiền thưởng khi trả lời sai',
      ],
      notes:
        'Bạn có 3 quyền trợ giúp: 50:50 (loại bỏ hai đáp án sai), Khán giả (hiển thị tỷ lệ bình chọn), và Gọi điện (nhận gợi ý từ người thân).',
      isFullscreen: isFullscreen,
    }),
    [gameConfig.timeLimit, isFullscreen]
  );

  // Determine if configuration is allowed, default to true
  const allowConfiguration =
    params?.allowConfiguration !== undefined ? params.allowConfiguration : true;

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      allowConfiguration={allowConfiguration}
      onTitleUpdate={handleTitleUpdate}
      onFullscreenChange={handleFullscreenChange}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      id={path}
      mainComponent={
        <MillionaireGameComponent
          config={gameConfig}
          isFullscreen={isFullscreen}
        />
      }
      configModal={
        <MillionaireGameModal
          isOpen={isConfigModalOpen}
          onClose={() => setIsConfigModalOpen(false)}
          onSubmit={handleConfigSubmit}
          defaultConfig={gameConfig}
        />
      }
      instructionsContent={instructionsContent}
    />
  );
};

export default memo(withEdComponentParams(MillionaireGame));

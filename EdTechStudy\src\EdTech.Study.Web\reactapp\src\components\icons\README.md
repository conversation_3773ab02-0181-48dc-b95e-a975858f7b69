# Hướng Dẫn Sử Dụng Icon với Tailwind CSS

Tài liệu này hướng dẫn cách sử dụng các icon trong dự án EdTech Study với Tailwind CSS. Các icon được định nghĩa trực tiếp dưới dạng SVG components trong file IconRegister.tsx.

## Tổng quan

Hệ thống icon trong EdTech project đã được cập nhật để sử dụng các SVG components trực tiếp, cho phép:

1. Nh<PERSON>t quán với phần còn lại của dự án
2. <PERSON><PERSON> dàng tùy chỉnh kích thước, màu sắc và các thuộc tính khác
3. Tự động thích ứng với theme của ứng dụng
4. Truyền các props SVG tiêu chuẩn trực tiếp vào icon

## Cách sử dụng

### Sử dụng cơ bản

```jsx
import { HomeOutlineIcon } from '@/components/icons/IconIndex';

// Icon sẽ sử dụng màu mặc định (currentColor)
<HomeOutlineIcon width={24} height={24} />
```

### Tùy chỉnh kích thước

```jsx
import { HomeOutlineIcon } from '@/components/icons/IconIndex';

// Đặt kích thước trực tiếp qua props
<HomeOutlineIcon width={24} height={24} />  // 24px
<HomeOutlineIcon width={32} height={32} />  // 32px
<HomeOutlineIcon width={40} height={40} />  // 40px

// Hoặc sử dụng các lớp Tailwind để đặt kích thước
<HomeOutlineIcon className="tailwind-w-6 tailwind-h-6" />  // 24px
<HomeOutlineIcon className="tailwind-w-8 tailwind-h-8" />  // 32px
<HomeOutlineIcon className="tailwind-w-10 tailwind-h-10" /> // 40px
```

### Tùy chỉnh màu sắc

```jsx
import { HomeOutlineIcon } from '@/components/icons/IconIndex';

// Đặt màu trực tiếp qua prop fill
<HomeOutlineIcon width={24} height={24} fill="red" />

// Sử dụng màu chính từ theme với Tailwind
<HomeOutlineIcon className="tailwind-text-primary" width={24} height={24} />

// Sử dụng các màu cụ thể với Tailwind
<HomeOutlineIcon className="tailwind-text-red-500" width={24} height={24} />
<HomeOutlineIcon className="tailwind-text-blue-600" width={24} height={24} />

// Sử dụng màu theo ngữ cảnh với Tailwind
<HomeOutlineIcon className="tailwind-text-success" width={24} height={24} /> // Màu thành công
<HomeOutlineIcon className="tailwind-text-error" width={24} height={24} />   // Màu lỗi
<HomeOutlineIcon className="tailwind-text-warning" width={24} height={24} /> // Màu cảnh báo
```

### Kết hợp nhiều lớp Tailwind

```jsx
import { DeleteIcon } from '@/components/icons/IconIndex';

// Kết hợp kích thước, màu sắc và hiệu ứng hover
<DeleteIcon className="
  tailwind-w-5
  tailwind-h-5
  tailwind-text-red-500
  hover:tailwind-text-red-700
  tailwind-transition-colors
  tailwind-duration-200
" />
```

### Sử dụng trong các nút

```jsx
import { Button } from 'antd';
import { DeleteIcon } from '@/components/icons/IconIndex';

// Icon trong nút với màu tùy chỉnh
<Button
  type="text"
  icon={<DeleteIcon className="tailwind-text-red-500 tailwind-w-4 tailwind-h-4" />}
  onClick={handleDelete}
>
  Xóa
</Button>
```

## Các lớp Tailwind hữu ích cho Icon

### Kích thước

```jsx
// Kích thước tiêu chuẩn
tailwind-w-4 tailwind-h-4   // 16px
tailwind-w-5 tailwind-h-5   // 20px
tailwind-w-6 tailwind-h-6   // 24px
tailwind-w-8 tailwind-h-8   // 32px
tailwind-w-10 tailwind-h-10 // 40px
tailwind-w-12 tailwind-h-12 // 48px

// Kích thước tùy chỉnh
tailwind-w-[18px] tailwind-h-[18px]
```

### Màu sắc

```jsx
// Màu chính từ theme
tailwind-text-primary
tailwind-text-secondary
tailwind-text-tertiary

// Các biến thể màu chính
tailwind-text-primary-100 đến tailwind-text-primary-700

// Màu văn bản
tailwind-text-default       // Màu văn bản mặc định
tailwind-text-secondary     // Màu văn bản thứ cấp
tailwind-text-disabled      // Màu văn bản bị vô hiệu hóa

// Màu trạng thái
tailwind-text-success
tailwind-text-error
tailwind-text-warning
tailwind-text-info
```

### Hiệu ứng và chuyển tiếp

```jsx
// Hiệu ứng hover
hover:tailwind-text-primary-600
hover:tailwind-text-red-700

// Chuyển tiếp mượt mà
tailwind-transition-colors
tailwind-duration-200

// Xoay
tailwind-rotate-45
tailwind-rotate-90
tailwind-rotate-180
```

## Ví dụ thực tế

### Icon xóa trong bảng

```jsx
import { DeleteIcon } from '@/components/icons/IconIndex';

<DeleteIcon
  className="
    tailwind-w-5
    tailwind-h-5
    tailwind-text-red-500
    hover:tailwind-text-red-700
    tailwind-transition-colors
    tailwind-cursor-pointer
  "
  onClick={handleDelete}
/>
```

### Icon trong thanh điều hướng

```jsx
import { HomeIcon, SettingsIcon, UserIcon } from '@/components/icons/IconIndex';

// Icon trang chủ (đang active)
<HomeIcon className="tailwind-w-6 tailwind-h-6 tailwind-text-primary" />

// Icon cài đặt (không active)
<SettingsIcon className="tailwind-w-6 tailwind-h-6 tailwind-text-default hover:tailwind-text-primary tailwind-transition-colors" />

// Icon người dùng (không active)
<UserIcon className="tailwind-w-6 tailwind-h-6 tailwind-text-default hover:tailwind-text-primary tailwind-transition-colors" />
```

### Icon với badge

```jsx
import { BellIcon } from '@/components/icons/IconIndex';

<div className="tailwind-relative">
  <BellIcon className="tailwind-w-6 tailwind-h-6 tailwind-text-default" />
  <span className="
    tailwind-absolute
    tailwind-top-0
    tailwind-right-0
    tailwind-w-2
    tailwind-h-2
    tailwind-bg-red-500
    tailwind-rounded-full
  "></span>
</div>
```

## Các lưu ý quan trọng

1. **Sử dụng các props SVG tiêu chuẩn** như `width`, `height`, `fill`, `stroke` để tùy chỉnh icon.

2. **Sử dụng các biến màu theo ngữ cảnh** (như `tailwind-text-primary`) thay vì các mã màu cụ thể để đảm bảo tính nhất quán với theme.

3. **Kết hợp với các hiệu ứng hover** để tạo trải nghiệm người dùng tốt hơn.

4. **Sử dụng các lớp chuyển tiếp** (`tailwind-transition-*`) để có hiệu ứng mượt mà.

5. **Đảm bảo kích thước icon phù hợp** với ngữ cảnh sử dụng (nút, bảng, thanh điều hướng, v.v.).

## Ví dụ chuyển đổi từ cũ sang mới

### Trước (sử dụng IconDefinition):

```jsx
import { DeleteIcon } from '@/components/icons/IconIndex';

<DeleteIcon
  size={16}
  color="red"
  className="svg-icon-theme-text"
/>
```

### Sau (sử dụng SVG components trực tiếp):

```jsx
import { DeleteOutlineIcon } from '@/components/icons/IconIndex';

<DeleteOutlineIcon
  width={16}
  height={16}
  fill="red"
  className="svg-icon-theme-text"
/>
```

## Tham khảo

- [Tài liệu Tailwind CSS](https://tailwindcss.com/docs)
- [Biến theme trong dự án](../../../styles/theme-variables.css)
- [Cấu hình Tailwind](../../../tailwind.config.js)

# Mô phỏng Rơi Tự Do (Freefall Simulator)

Một mô-đun mô phỏng rơi tự do tương tác dành cho học sinh Vật lý lớp 10. Mô-đun này cho phép người dùng khám phá các quy luật chuyển động rơi tự do với nhiều tùy chỉnh khác nhau.

## Tính năng chính

- Mô phỏng trực quan chuyển động rơi tự do trong các môi trường khác nhau (<PERSON><PERSON><PERSON><PERSON>, Mặt <PERSON>r<PERSON>ng, Sao Hỏa)
- Hiển thị thời gian thực các vector vận tốc, gia tốc và lực
- Tùy chỉnh các thông số như chiều cao ban đầu, vận tốc ban đầu, môi trường
- Thêm/bỏ lực cản không khí để so sánh sự khác biệt
- <PERSON><PERSON> thị theo thời gian hiển thị vị trí, vận tốc và gia tốc
- Bảng dữ liệu chi tiết theo thời gian
- Tùy chỉnh vật thể (khối lượng, kích thước, màu sắc)
- Tùy chỉnh môi trường (gia tốc trọng trường, mật độ không khí, hệ số cản)

## Cài đặt

Để tích hợp mô-đun Mô phỏng Rơi Tự Do vào dự án React của bạn, hãy làm theo các bước sau:

1. Sao chép các file sau vào thư mục src/components/simulators/freefall của dự án:

   - FreefallSimulatorConfig.ts
   - FreefallUtils.ts
   - FreefallSimulator.tsx
   - FreefallSimulatorContainer.tsx
   - FreefallSimulatorConfigModal.tsx

2. Cài đặt các thư viện phụ thuộc cần thiết:

```bash
npm install antd recharts @ant-design/icons
```

3. Import và sử dụng trong component của bạn:

```jsx
import FreefallSimulatorContainer from './components/simulators/freefall/FreefallSimulatorContainer';

const MyComponent = () => {
  return (
    <div>
      <h1>Mô phỏng Vật lý</h1>
      <FreefallSimulatorContainer
        initialConfig={{
          title: 'Mô phỏng Rơi Tự Do',
          initialHeight: 100,
          initialVelocity: 0,
        }}
        onSimulationComplete={(result) => console.log('Kết quả:', result)}
      />
    </div>
  );
};
```

## Hướng dẫn sử dụng

### Cấu hình cơ bản

Bạn có thể cấu hình nhanh mô phỏng với các thông số chính:

1. **Chiều cao ban đầu**: Đặt độ cao ban đầu của vật (đơn vị: mét)
2. **Vận tốc ban đầu**: Đặt vận tốc ban đầu của vật (dương là hướng lên, âm là hướng xuống)
3. **Môi trường**: Chọn môi trường mô phỏng (Trái Đất, Mặt Trăng, Sao Hỏa, Tùy chỉnh)

### Điều khiển mô phỏng

- **Bắt đầu/Tạm dừng**: Bắt đầu hoặc tạm dừng mô phỏng
- **Khởi tạo lại**: Đặt lại mô phỏng về trạng thái ban đầu
- **Bước tiếp theo**: Tiến một bước thời gian trong mô phỏng
- **Tốc độ mô phỏng**: Điều chỉnh tốc độ mô phỏng (0.1x - 3x)

### Cấu hình nâng cao

Nhấp vào nút "Cấu hình nâng cao" để mở modal cấu hình chi tiết:

1. **Thông tin cơ bản**: Tiêu đề, mô tả, chiều cao, vận tốc, thời gian mô phỏng
2. **Môi trường**: Thêm/xóa/chỉnh sửa các môi trường với các thông số khác nhau
3. **Vật thể**: Thêm/xóa/chỉnh sửa các vật thể với khối lượng, kích thước và màu sắc khác nhau
4. **Tùy chọn hiển thị**: Bật/tắt các tùy chọn hiển thị như lưới, vector, đồ thị, bảng dữ liệu

## Kiến thức lý thuyết

### Công thức chuyển động rơi tự do

- **Vị trí**: h = h₀ - gt²/2
- **Vận tốc**: v = v₀ + gt
- **Thời gian rơi**: t = √(2h/g) (khi v₀ = 0)
- **Vận tốc khi chạm đất**: v = √(2gh) (khi v₀ = 0)

### Lực cản không khí

Công thức lực cản không khí: F_d = 0.5 _ ρ _ v² _ C_d _ A

Trong đó:

- ρ: Mật độ không khí (kg/m³)
- v: Vận tốc (m/s)
- C_d: Hệ số cản
- A: Diện tích tiếp xúc (m²)

## Ứng dụng trong dạy học

Mô phỏng Rơi Tự Do có thể được sử dụng trong các tình huống dạy học sau:

1. **Giới thiệu chuyển động rơi tự do**: Minh họa trực quan chuyển động rơi tự do và các đặc điểm của nó.
2. **So sánh môi trường khác nhau**: Cho học sinh khám phá sự khác biệt của chuyển động rơi tự do trên Trái Đất, Mặt Trăng và Sao Hỏa.
3. **Hiệu ứng của lực cản không khí**: Chứng minh sự khác biệt giữa mô hình lý tưởng (không có lực cản) và thực tế (có lực cản).
4. **Phân tích dữ liệu**: Cho học sinh phân tích đồ thị và dữ liệu số để hiểu rõ hơn về mối quan hệ giữa vị trí, vận tốc và gia tốc.
5. **Nghiên cứu về vận tốc tới hạn**: Khám phá khái niệm vận tốc tới hạn khi có lực cản không khí.

## Cấu trúc code

- **FreefallSimulatorConfig.ts**: Định nghĩa các interfaces và cấu hình mặc định
- **FreefallUtils.ts**: Các hàm tiện ích và tính toán vật lý
- **FreefallSimulator.tsx**: Component chính xử lý mô phỏng và hiển thị
- **FreefallSimulatorContainer.tsx**: Container bao gồm cài đặt nhanh và wrapper
- **FreefallSimulatorConfigModal.tsx**: Modal cấu hình nâng cao

## Tùy chỉnh và mở rộng

Mô phỏng Rơi Tự Do được thiết kế để dễ dàng mở rộng và tùy chỉnh. Bạn có thể:

1. Thêm các loại vật thể mới với hình dạng và đặc tính khác nhau
2. Thêm các môi trường mới với các thông số vật lý khác nhau
3. Thêm các loại biểu đồ và phân tích dữ liệu
4. Kết hợp với các mô phỏng khác như chuyển động ném ngang, ném xiên

---

Được phát triển như một phần của dự án EdTech Pro - Nền tảng học tập tương tác cho giáo dục phổ thông.

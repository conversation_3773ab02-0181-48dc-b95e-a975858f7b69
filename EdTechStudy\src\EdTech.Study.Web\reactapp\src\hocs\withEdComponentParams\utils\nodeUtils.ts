import { IEdTechRenderTreeData } from '../../../interfaces/AppComponents';

/**
 * Tìm node theo id hoặc path trong cây dữ liệu
 * @param root Node gốc
 * @param targetId Id cần tìm
 * @returns Node tìm thấy hoặc null nếu không tìm thấy
 */
export const findNodeById = (
  root: IEdTechRenderTreeData,
  targetId: string
): IEdTechRenderTreeData | null => {
  // Kiểm tra node gốc
  if (root.id === targetId || root.path === targetId) {
    return root;
  }

  // Kiểm tra các node con
  if (root.subItems && root.subItems.length > 0) {
    for (const item of root.subItems) {
      if (item.id === targetId || item.path === targetId) {
        return item;
      }

      // Tìm kiếm đệ quy trong các node con
      if (item.subItems && item.subItems.length > 0) {
        const found = findNodeById(item, targetId);
        if (found) return found;
      }
    }
  }
  return null;
};

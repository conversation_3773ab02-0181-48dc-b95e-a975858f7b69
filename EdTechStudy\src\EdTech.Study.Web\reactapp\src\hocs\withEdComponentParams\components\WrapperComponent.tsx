import React from 'react';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import EngineContent from './EngineContent';
import '../styles/component-highlight.css';
/**
 * Higher-Order Component (HOC) bọc các component với tính năng engine
 */
const WrapperComponent = (Component: React.ComponentType<any>) => {
  // Component được bọc
  const WrappedComponent = (props: IEdTechRenderProps) => {
    const { id, name, type, path, isEditing } = props;
    if (!isEditing)
      return (
        <div id={id} data-type={type} data-name={name} data-path={path}>
          <Component {...props} />
        </div>
      );

    return (
      <div
        id={id}
        data-type={type}
        data-name={name}
        data-path={path}
        className="tailwind-relative tailwind-w-full"
      >
        <EngineContent props={props}>
          <Component {...props} />
        </EngineContent>
      </div>
    );
  };
  // Đặt tên hiển thị cho component để debug dễ dàng hơn
  WrappedComponent.displayName = `WithEngine(${
    Component.displayName || Component.name || 'Component'
  })`;

  return WrappedComponent;
};

export default WrapperComponent;

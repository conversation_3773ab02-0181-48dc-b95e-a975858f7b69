.single-question-engine {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.single-question-header {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.single-question-content {
  flex: 1;
  padding: 16px;
  max-height: calc(100% - 100px);
}

.single-question-container {
  margin-bottom: 16px;
}

.question-not-found {
  padding: 24px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #999;
}

/* Ensure form elements don't overflow */
.single-question-content input,
.single-question-content textarea,
.single-question-content select {
  max-width: 100%;
}

/* Style for editor mode vs preview mode */
.single-question-engine.preview-mode .single-question-content {
  background-color: #fff;
  border: none;
}

.single-question-engine:not(.preview-mode) .single-question-content {
  background-color: #fafafa;
  border-radius: 4px;
}

/* Override some practice engine styling that might affect this component */
.single-question-engine .practice-navigation {
  display: none;
}

.single-question-engine .practice-progress {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .single-question-header {
    flex-direction: column;
    gap: 8px;
  }

  .single-question-content {
    padding: 12px;
  }
}

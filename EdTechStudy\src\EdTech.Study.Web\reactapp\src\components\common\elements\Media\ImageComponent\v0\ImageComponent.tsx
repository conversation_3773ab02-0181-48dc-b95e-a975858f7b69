// ImageComponent.tsx
import React, { useState, useRef, useEffect } from 'react';
import { Button, Tooltip, message, Select, Input, Slider } from 'antd';
import { EngineContainer } from '../../../../engines';
import '../../MediaComponentStyles.css';
import type { UploadFile } from 'antd/es/upload/interface';
import { IEdTechRenderProps } from '../../../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { ITextProps } from '../../../../../core/title/CoreTitle';
import { ImageComponentProps, ImageItem } from '../shared/types';
import { defaultProps } from '../shared/constants';
import BasicImageViewer, { BasicImageViewerRef } from './BasicImageViewer';
import {
  MediaFileUploaderContainer,
  UploadedFile,
} from '../../shared/MediaFileUploader';
import { DragDropMediaWrapper } from '../../shared';
import tempFileApi from '../../../../../../api/tempFileApi';
import {
  processImageUrl,
  getFileNameWithoutExtension,
  getFileExtension,
} from '../../shared/utils';
import {
  ArrowDownloadIcon,
  CheckmarkOutlineIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DeleteIcon,
  DismissOutlineIcon,
  EditIcon,
  LinkOutlinedIcon,
  RotateLeftOutlinedIcon,
  RotateRightOutlinedIcon,
  ZoomInOutlinedIcon,
  ZoomOutOutlinedIcon,
} from '../../../../../icons/IconIndex';

const ImageComponent: React.FC<IEdTechRenderProps<ImageComponentProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Merge with default props
  const config: ImageComponentProps = { ...defaultProps, ...params };

  // Create refs early
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const imageViewerRef = useRef<BasicImageViewerRef>(null);
  // State for image viewer
  const [rotation, setRotation] = useState<number>(0);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [imageWidth, setImageWidth] = useState<number>(0);
  const [tempUrl, setTempUrl] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [editingImageName, setEditingImageName] = useState<number | null>(null);
  const [editingImageNameValue, setEditingImageNameValue] =
    useState<string>('');
  const [zoomLevel, setZoomLevel] = useState<number>(100); // 100% is the default zoom level
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false); // State for tracking fullscreen mode
  // Handle fullscreen state changes
  const handleFullscreenChange = (fullscreen: boolean) => {
    // Update fullscreen state
    setIsFullscreen(fullscreen);

    // When entering fullscreen, reset zoom and ensure proper display
    if (fullscreen && imageViewerRef.current) {
      // Reset zoom to 100% for better viewing in fullscreen
      setZoomLevel(100);
      imageViewerRef.current.reset();

      // Get engine container width for proper sizing
      const engineWidth =
        containerRef.current?.parentElement?.offsetWidth || 800;

      // Update component with fullscreen dimensions if needed
      // Only update if the width has changed significantly
      if (
        addOrUpdateParamComponent &&
        (typeof config.width !== 'number' ||
          Math.abs(config.width - engineWidth) > 5)
      ) {
        addOrUpdateParamComponent({
          ...config,
          width: engineWidth,
        });
      }
    }
  };

  // Handle title updates
  const handleTitleUpdate = (titleProps: Partial<ITextProps>) => {
    if (addOrUpdateParamComponent) {
      const updatedConfig = {
        ...config,
        titleProps: {
          ...config.titleProps,
          ...titleProps,
        },
      };
      addOrUpdateParamComponent(updatedConfig);
    }
  };

  useEffect(() => {
    // Avoid dimension initialization if we're in the middle of a deletion
    // This helps prevent update loops
    let isInitializing = true;

    const initializeDimensions = () => {
      if (!isInitializing) return;

      if (containerRef.current) {
        // Force immediate update of dimensions when component mounts
        if (!config.medias || config.medias.length === 0) {
          if (isEditing && addOrUpdateParamComponent) {
            // Store current config to avoid unnecessary updates
            const currentWidth = config.width;
            const currentHeight = config.height;

            // Only update if dimensions actually changed
            if (currentWidth !== '100%' || currentHeight !== 'auto') {
              addOrUpdateParamComponent({
                ...config,
                width: '100%', // Use percentage for responsive width
                height: 'auto', // Let height be determined by aspect ratio
              });
            }
          }
        }
      }
    };

    // Try to initialize immediately
    initializeDimensions();

    // Set flag to false after initial run
    isInitializing = false;

    // Add window resize listener for responsive behavior
    const handleResize = () => {
      // Don't initialize on resize if we have no videos
      if (config.medias && config.medias.length > 0) {
        initializeDimensions();
      }
    };

    window.addEventListener('resize', handleResize);

    const timeoutId = setTimeout(() => {
      // Only run delayed initialization if we have videos
      if (config.medias && config.medias.length > 0) {
        initializeDimensions();
      }
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [config.medias, isEditing, addOrUpdateParamComponent, config]);

  // Ensure we have an images array
  useEffect(() => {
    // If we have a legacy imageUrl but no images array, convert it
    if (
      config.mediaUrl &&
      config.blobKey &&
      config.blobContext &&
      (!config.medias || config.medias.length === 0)
    ) {
      // Only update if we need to convert from legacy format
      addOrUpdateParamComponent({
        ...config,
        medias: [
          {
            mediaUrl: config.mediaUrl,
            name: config.name || 'Hình ảnh',
            mediaType: config.mediaType || 'upload',
            blobKey: config.blobKey,
            blobContext: config.blobContext,
          },
        ],
      });
    }
  }, []);

  // Initialize fileList from images array
  useEffect(() => {
    if (config.medias && config.medias.length > 0 && fileList.length === 0) {
      const newFileList = config.medias.map((img, index) => {
        const fileName = img.mediaUrl.split('/').pop() || `image-${index + 1}`;
        return {
          uid: `-${index + 1}`,
          name: fileName,
          status: 'done' as const,
          url: img.mediaUrl,
        };
      });
      setFileList(newFileList);
    }
  }, []);

  // Handle image rotation
  const handleRotateLeft = () => {
    setRotation((prev) => prev - 90);
  };

  const handleRotateRight = () => {
    setRotation((prev) => prev + 90);
  };

  // Handle zoom level change from slider
  const handleZoomChange = (value: number) => {
    setZoomLevel(value);
    if (imageViewerRef.current) {
      imageViewerRef.current.setZoom(value);
    }
  };

  // Handle zoom level change from BasicImageViewer
  const handleZoomUpdate = (value: number) => {
    setZoomLevel(value);
  };

  // Handle image download
  const handleDownload = () => {
    // If we have images array, download the currently selected image
    if (
      config.medias &&
      config.medias.length > 0 &&
      selectedIndex < config.medias.length
    ) {
      const currentImage = config.medias[selectedIndex];
      const link = document.createElement('a');
      link.href = currentImage.mediaUrl;
      link.download =
        currentImage.mediaUrl.split('/').pop() || `image-${selectedIndex + 1}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    // Legacy support for single imageUrl
    else if (config.mediaUrl) {
      const link = document.createElement('a');
      link.href = config.mediaUrl;
      link.download = config.mediaUrl.split('/').pop() || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Handle deletion of a specific image
  const handleDeleteImage = async (index?: number) => {
    // If no index provided, delete all images
    if (index === undefined && config.medias && config.medias.length > 0) {
      // Try to delete all uploaded images
      for (const img of config.medias) {
        if (img.mediaUrl && !img.mediaUrl.startsWith('data:') && img.blobKey) {
          try {
            await tempFileApi.Delete(img.blobKey);
          } catch (error) {
            // Continue with UI deletion even if server deletion fails
          }
        }
      }

      setFileList([]);
      setTempUrl('');
      setRotation(0);
      setSelectedIndex(0);
      addOrUpdateParamComponent({
        ...config,
        medias: [],
      });
      return;
    }

    // Delete a specific image
    if (config.medias && config.medias.length > 0 && index !== undefined) {
      const imageToDelete = config.medias[index];

      // Try to delete from server if it's an uploaded image
      try {
        await tempFileApi.Delete(imageToDelete.blobKey);
      } catch (error) {
        // Continue with UI deletion even if server deletion fails
      }

      // Remove from fileList
      const newFileList = [...fileList];
      newFileList.splice(index, 1);
      setFileList(newFileList);

      // Remove from images array
      const newImages = [...config.medias];
      newImages.splice(index, 1);

      // Calculate new selected index
      let newSelectedIndex = 0;

      if (newImages.length > 0) {
        // If we're deleting the last image and it's currently selected
        if (index === config.medias.length - 1 && index === selectedIndex) {
          // Select the new last image
          newSelectedIndex = newImages.length - 1;
        }
        // If we're deleting an image before the currently selected one
        else if (index < selectedIndex) {
          // Shift the selection one position back
          newSelectedIndex = selectedIndex - 1;
        }
        // If we're deleting the currently selected image but not the last one
        else if (index === selectedIndex) {
          // Keep the same position (which will now point to the next image)
          newSelectedIndex = Math.min(selectedIndex, newImages.length - 1);
        }
        // If we're deleting an image after the currently selected one
        else {
          // Keep the current selection
          newSelectedIndex = selectedIndex;
        }
      }

      // Update component
      addOrUpdateParamComponent({
        ...config,
        medias: newImages,
      });

      // Update selected index in state
      setSelectedIndex(newSelectedIndex);
    }
  };

  // Handle URL submission
  const handleUrlSubmit = () => {
    if (!tempUrl) {
      message.error('Vui lòng nhập URL hình ảnh');
      return;
    }

    if (!tempUrl.match(/^(http|https):\/\/.+/)) {
      message.error(
        'URL không hợp lệ. URL phải bắt đầu bằng http:// hoặc https://'
      );
      return;
    }

    // Process the URL using our utility function
    const { fullUrl } = processImageUrl(tempUrl);

    // Create a new image item with type 'embed'
    const newImage: ImageItem = {
      mediaUrl: fullUrl,
      name: 'Hình ảnh từ URL',
      mediaType: 'embed' as const,
      blobContext: '',
      blobKey: '',
    };

    // Add to images array
    const newImages = config.medias ? [...config.medias, newImage] : [newImage];

    // Lấy chiều rộng của engine container
    const engineWidth = containerRef.current?.parentElement?.offsetWidth || 800;

    // Check if we need to update the width
    const needsWidthUpdate =
      typeof config.width !== 'number' ||
      Math.abs(config.width - engineWidth) > 5;

    // Update component với chiều rộng vừa với engine
    addOrUpdateParamComponent({
      ...config,
      medias: newImages,
      width: needsWidthUpdate ? engineWidth : config.width,
      height: 'auto', // Let height be determined by content
    });

    // Clear URL input
    setTempUrl('');
  };

  // Handle navigation between images
  const handlePrevImage = () => {
    if (!config.medias || config.medias.length <= 1) return;

    const newIndex =
      selectedIndex > 0 ? selectedIndex - 1 : config.medias.length - 1;
    setSelectedIndex(newIndex);
  };

  const handleNextImage = () => {
    if (!config.medias || config.medias.length <= 1) return;

    const newIndex =
      selectedIndex < config.medias.length - 1 ? selectedIndex + 1 : 0;
    setSelectedIndex(newIndex);
  };

  // Handle selecting a specific image
  const handleSelectImage = (index: number) => {
    if (!config.medias || index >= config.medias.length) return;

    setSelectedIndex(index);
  };

  // Using utility functions from shared/utils.ts

  // Handle editing image name
  const handleEditImageName = (index: number) => {
    if (!config.medias || index >= config.medias.length) return;

    const currentName = config.medias[index].name || '';
    setEditingImageName(index);
    // Extract filename without extension and set it as the editing value
    const nameWithoutExtension = getFileNameWithoutExtension(currentName);
    console.log('Editing image name:', {
      index,
      currentName,
      nameWithoutExtension,
    });
    setEditingImageNameValue(nameWithoutExtension);
  };

  // Handle saving image name
  const handleSaveImageName = () => {
    if (
      editingImageName === null ||
      !config.medias ||
      editingImageName >= config.medias.length
    )
      return;

    // Get the current name from the config
    const currentName = config.medias[editingImageName].name || '';
    // Extract the extension from the current name
    const extension = getFileExtension(currentName);
    // Add the extension to the edited name
    const newName = editingImageNameValue + (extension ? '.' + extension : '');

    console.log('Saving image name:', {
      currentName,
      editingValue: editingImageNameValue,
      extension,
      newName,
    });

    // Create a new array with the updated name
    const newImages = [...config.medias];
    newImages[editingImageName] = {
      ...newImages[editingImageName],
      name: newName,
    };

    // Update the component with the new array
    addOrUpdateParamComponent({
      ...config,
      medias: newImages,
    });

    // Reset editing state
    setEditingImageName(null);
    setEditingImageNameValue('');
  };

  // Handle cancel editing image name
  const handleCancelEditImageName = () => {
    setEditingImageName(null);
    setEditingImageNameValue('');
  };

  // Update display settings
  const updateImageSettings = (updates: Partial<ImageComponentProps>) => {
    // Get current width (use parent width if no width is set)
    if (containerRef.current) {
      // Get the engine container width
      const engineWidth =
        containerRef.current.parentElement?.offsetWidth || 800;
      const width = imageWidth > 0 ? imageWidth : engineWidth;

      // Only update state if the width has changed significantly
      if (Math.abs(imageWidth - width) > 5) {
        setImageWidth(width);
      }

      // Apply dimensions to container immediately
      containerRef.current.style.width = `${engineWidth}px`;
      containerRef.current.style.transition = 'width 0.3s ease';

      // Apply to all image containers
      const imageContainers =
        containerRef.current.querySelectorAll('.image-container');
      imageContainers.forEach((container) => {
        // Ensure container takes full size
        (container as HTMLElement).style.width = '100%';
        (container as HTMLElement).style.height = '100%';
      });

      // Force a reflow to ensure changes are applied immediately
      containerRef.current.offsetHeight;

      // Check if we need to update the component configuration
      const needsWidthUpdate =
        typeof config.width !== 'number' ||
        Math.abs(config.width - engineWidth) > 5;

      // Only update if there are actual changes to make
      if (needsWidthUpdate || Object.keys(updates).length > 0) {
        addOrUpdateParamComponent({
          ...config,
          ...updates,
          width: engineWidth,
          height: 'auto', // Let height be determined by content
        });
      }
    } else if (Object.keys(updates).length > 0) {
      // For other updates, just update the component configuration
      addOrUpdateParamComponent({
        ...config,
        ...updates,
      });
    }
  };

  // Render the image viewer controls for both horizontal and vertical modes
  const renderImageControls = (imageIndex?: number) => {
    // Lấy thông tin ảnh hiện tại
    const currentImage =
      imageIndex !== undefined && config.medias && config.medias.length > 0
        ? config.medias[imageIndex]
        : config.medias && config.medias.length > 0
        ? config.medias[selectedIndex]
        : null;

    return (
      <div className="image-controls-container tailwind-w-full tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
        {/* Hiển thị tên ảnh ở bên trái */}
        <div className="tailwind-flex tailwind-items-center">
          {currentImage && (
            <>
              {editingImageName ===
              (imageIndex !== undefined ? imageIndex : selectedIndex) ? (
                <div className="tailwind-flex tailwind-items-center">
                  <Input
                    value={editingImageNameValue}
                    onChange={(e) => setEditingImageNameValue(e.target.value)}
                    onPressEnter={handleSaveImageName}
                    autoFocus
                    style={{ width: '200px' }}
                    suffix={
                      <div className="tailwind-flex tailwind-items-center">
                        <Button
                          type="text"
                          icon={
                            <CheckmarkOutlineIcon
                              height={14}
                              width={14}
                              style={{ color: '#52c41a' }}
                            />
                          }
                          onClick={handleSaveImageName}
                          className="tailwind-flex-shrink-0"
                          style={{
                            border: 'none',
                            padding: 0,
                            marginRight: '4px',
                          }}
                        />
                        <Button
                          type="text"
                          icon={<DismissOutlineIcon height={14} width={14} />}
                          onClick={handleCancelEditImageName}
                          className="tailwind-flex-shrink-0"
                          style={{ border: 'none', padding: 0 }}
                        />
                      </div>
                    }
                  />
                </div>
              ) : (
                <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
                  <span
                    className="tailwind-text-gray-700 tailwind-font-medium tailwind-text-sm tailwind-truncate"
                    style={{ maxWidth: '200px' }}
                  >
                    {getFileNameWithoutExtension(
                      currentImage.name || 'Hình ảnh'
                    )}
                  </span>
                  {isEditing && (
                    <Tooltip title="Chỉnh sửa tên ảnh">
                      <Button
                        icon={<EditIcon height={14} width={14} />}
                        onClick={() =>
                          handleEditImageName(
                            imageIndex !== undefined
                              ? imageIndex
                              : selectedIndex
                          )
                        }
                        className="tailwind-flex-shrink-0"
                        style={{
                          width: '20px',
                          height: '20px',
                          minWidth: '20px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          padding: 0,
                          backgroundColor: 'transparent',
                          border: 'none',
                          color: '#666',
                        }}
                      />
                    </Tooltip>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* Các nút điều khiển ở bên phải */}
        <div className="tailwind-flex tailwind-flex-wrap tailwind-items-center tailwind-justify-end tailwind-gap-x-1">
          {/* Zoom controls */}
          <Tooltip title="Thu nhỏ">
            <Button
              icon={<ZoomOutOutlinedIcon />}
              onClick={() => {
                if (
                  imageViewerRef.current &&
                  (imageIndex === undefined || imageIndex === 0)
                ) {
                  imageViewerRef.current.zoomOut();
                  // Update zoom level state when using the button
                  if (imageViewerRef.current) {
                    setZoomLevel(imageViewerRef.current.getZoom());
                  }
                }
              }}
              className="tailwind-flex-shrink-0"
              style={{
                width: '26px',
                height: '26px',
                minWidth: '26px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: 0,
                backgroundColor: 'white',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
              }}
            />
          </Tooltip>

          {/* Zoom slider */}
          <Slider
            className="tailwind-w-24 tailwind-mx-1"
            min={50}
            max={300}
            step={25}
            value={zoomLevel}
            onChange={handleZoomChange}
            tooltip={{ formatter: (value) => `${value}%` }}
            styles={{
              track: { backgroundColor: 'var(--edtt-color-primary)' },
              handle: { borderColor: 'var(--edtt-color-primary)' },
            }}
          />

          <Tooltip title="Phóng to">
            <Button
              icon={<ZoomInOutlinedIcon />}
              onClick={() => {
                if (
                  imageViewerRef.current &&
                  (imageIndex === undefined || imageIndex === 0)
                ) {
                  imageViewerRef.current.zoomIn();
                  // Update zoom level state when using the button
                  if (imageViewerRef.current) {
                    setZoomLevel(imageViewerRef.current.getZoom());
                  }
                }
              }}
              className="tailwind-flex-shrink-0"
              style={{
                width: '26px',
                height: '26px',
                minWidth: '26px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: 0,
                backgroundColor: 'white',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
              }}
            />
          </Tooltip>

          {/* Rotation controls */}
          <Tooltip title="Xoay trái">
            <Button
              icon={<RotateLeftOutlinedIcon />}
              onClick={
                imageIndex === undefined || imageIndex === 0
                  ? handleRotateLeft
                  : undefined
              }
              className="tailwind-flex-shrink-0 tailwind-ml-1"
              style={{
                width: '26px',
                height: '26px',
                minWidth: '26px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: 0,
                backgroundColor: 'white',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
              }}
            />
          </Tooltip>

          <Tooltip title="Xoay phải">
            <Button
              icon={<RotateRightOutlinedIcon />}
              onClick={
                imageIndex === undefined || imageIndex === 0
                  ? handleRotateRight
                  : undefined
              }
              className="tailwind-flex-shrink-0"
              style={{
                width: '26px',
                height: '26px',
                minWidth: '26px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: 0,
                backgroundColor: 'white',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
              }}
            />
          </Tooltip>

          {/* Download control */}
          <Tooltip title="Tải về">
            <Button
              icon={<ArrowDownloadIcon />}
              onClick={handleDownload}
              disabled={!config.medias || config.medias.length === 0}
              className="tailwind-flex-shrink-0 tailwind-ml-1"
              style={{
                width: '26px',
                height: '26px',
                minWidth: '26px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: 0,
                backgroundColor: 'white',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
              }}
            />
          </Tooltip>

          {/* Delete control */}
          {isEditing && (
            <Tooltip title="Xóa ảnh">
              <Button
                danger
                icon={<DeleteIcon />}
                onClick={(e) => {
                  if (e) e.stopPropagation();
                  handleDeleteImage(
                    imageIndex !== undefined ? imageIndex : selectedIndex
                  );
                }}
                className="tailwind-flex-shrink-0"
                style={{
                  width: '26px',
                  height: '26px',
                  minWidth: '26px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: 0,
                  backgroundColor: 'white',
                  color: '#ff4d4f',
                  border: '1px solid #ff4d4f',
                  borderRadius: '4px',
                }}
              />
            </Tooltip>
          )}
        </div>
      </div>
    );
  };

  // Render image settings section
  const renderImageSettings = () => {
    return (
      <div className="tailwind-w-full tailwind-mb-4 tailwind-bg-gray-100 tailwind-p-4 tailwind-rounded-lg">
        <div className="tailwind-flex tailwind-flex-wrap tailwind-items-center">
          {/* Display Mode Setting */}
          <div className="tailwind-flex tailwind-items-center">
            <label className="tailwind-text-sm tailwind-font-medium tailwind-text-gray-700 tailwind-mr-3 tailwind-whitespace-nowrap">
              Chế độ hiển thị
            </label>
            <Select
              value={config.displayMode}
              onChange={(value) =>
                updateImageSettings({
                  displayMode: value as 'horizontal' | 'vertical',
                })
              }
              size="middle"
              popupMatchSelectWidth={false}
              style={{ width: '220px' }}
              rootClassName="ant-select-bordered"
            >
              <Select.Option value="horizontal">Trượt ngang</Select.Option>
              <Select.Option value="vertical">Xếp dọc</Select.Option>
            </Select>
          </div>
        </div>
      </div>
    );
  };

  // Render image thumbnails for gallery navigation (horizontal mode)
  const renderImageThumbnails = () => {
    if (!config.medias || config.medias.length <= 1) return null;

    // Only show thumbnails in horizontal mode
    if (config.displayMode === 'vertical') return null;

    // Larger thumbnail size for better visibility
    const thumbnailSize = 60;

    return (
      <div
        className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-p-3 tailwind-overflow-x-auto"
        style={{ maxWidth: '100%' }}
      >
        {config.medias.map((image, index) => (
          <div
            key={index}
            className={`tailwind-cursor-pointer tailwind-rounded-md tailwind-overflow-hidden tailwind-transition-all tailwind-duration-200 ${
              index === selectedIndex
                ? 'tailwind-shadow-md'
                : 'hover:tailwind-shadow-md'
            }`}
            onClick={() => handleSelectImage(index)}
            style={{
              width: `${thumbnailSize}px`,
              height: `${thumbnailSize}px`,
              minWidth: `${thumbnailSize}px`,
              maxWidth: `${thumbnailSize}px`,
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f5f5f5',
              position: 'relative',
              margin: '4px',
              borderRadius: '4px',
              border:
                index === selectedIndex
                  ? '2px solid var(--edtt-color-primary)'
                  : '1px solid var(--edtt-color-gray-200)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            }}
          >
            <img
              src={image.mediaUrl}
              alt={image.name || `Hình ảnh ${index + 1}`}
              className="tailwind-w-full tailwind-h-full tailwind-object-cover"
            />
          </div>
        ))}
      </div>
    );
  };

  // Render all images vertically (vertical mode)
  const renderVerticalImages = () => {
    // Kiểm tra nếu không có ảnh hoặc không phải chế độ dọc
    if (
      !config.medias ||
      config.medias.length === 0 ||
      config.displayMode !== 'vertical'
    )
      return null;

    // Hiển thị tất cả ảnh theo chiều dọc
    return (
      <div className="vertical-images-container tailwind-w-full tailwind-mt-3 tailwind-max-w-full tailwind-mx-auto">
        <div className="tailwind-flex tailwind-flex-col tailwind-gap-6 tailwind-items-center">
          {config.medias.map((image, index) => (
            <div
              key={index}
              className={`vertical-image-container tailwind-w-full tailwind-relative ${
                isEditing ? 'tailwind-mb-6' : 'tailwind-mb-4'
              }`}
            >
              {/* Image controls for each image in vertical mode */}
              {renderImageControls(index)}

              {/* Use the same grid layout as horizontal mode for consistent sizing */}
              <div className="tailwind-w-full tailwind-grid tailwind-grid-cols-[60px_1fr_60px] tailwind-gap-2 tailwind-items-center">
                {/* Left empty space for consistent layout */}
                <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                  {/* Empty div for consistent spacing */}
                </div>

                {/* Center image container - Same as horizontal mode */}
                <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-overflow-hidden">
                  <div
                    className={`tailwind-w-full tailwind-flex tailwind-justify-center media-container image-container tailwind-overflow-hidden tailwind-h-auto tailwind-p-0 tailwind-box-border tailwind-items-center tailwind-max-w-full ${
                      isEditing ? 'editing' : ''
                    }`}
                    style={{
                      height: isFullscreen ? '70vh' : '400px', // Adjust height based on fullscreen state
                      transition: 'height 0.3s ease',
                    }}
                  >
                    <div
                      className="tailwind-w-full tailwind-h-full tailwind-overflow-hidden image-resize-container"
                      ref={index === 0 ? imageContainerRef : null}
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100%',
                      }}
                    >
                      <BasicImageViewer
                        ref={index === 0 ? imageViewerRef : undefined}
                        imageUrl={image.mediaUrl}
                        alt={image.name || `Hình ảnh ${index + 1}`}
                        imageRef={index === 0 ? imageRef : undefined}
                        isEditing={isEditing}
                        rotation={index === 0 ? rotation : 0}
                        showControls={false}
                        zoomLevel={index === 0 ? zoomLevel : 100}
                        onZoomChange={
                          index === 0 ? handleZoomUpdate : undefined
                        }
                        isFullscreen={isFullscreen}
                      />
                    </div>
                  </div>
                </div>

                {/* Right empty space for consistent layout */}
                <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                  {/* Empty div for consistent spacing */}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Xử lý khi upload thành công
  const handleUploadSuccess = (files: UploadedFile[]) => {
    if (!files || files.length === 0) return;

    // Tạo mảng các đối tượng hình ảnh mới từ các file đã upload
    const newUploadedImages = files.map((item) => {
      // Process the URL using our utility function
      const { fullUrl } = processImageUrl(item.url);

      return {
        mediaUrl: fullUrl,
        name: item.name || 'Hình ảnh',
        mediaType: 'upload' as const,
        blobKey: item.blobKey || '',
        blobContext: item.blobContext || '',
      } as ImageItem;
    });

    // Kết hợp với hình ảnh hiện có
    const updatedImages = config.medias
      ? [...config.medias, ...newUploadedImages]
      : newUploadedImages;

    // Lấy chiều rộng của engine container
    const engineWidth = containerRef.current?.parentElement?.offsetWidth || 800;

    // Check if we need to update the width
    const needsWidthUpdate =
      typeof config.width !== 'number' ||
      Math.abs(config.width - engineWidth) > 5;

    // Cập nhật component với hình ảnh mới và kích thước phù hợp
    addOrUpdateParamComponent({
      ...config,
      medias: updatedImages,
      width: needsWidthUpdate ? engineWidth : config.width,
      height: 'auto', // Let height be determined by content
    });
  };

  // Render upload button for images
  const renderUploadButton = () => {
    return (
      <MediaFileUploaderContainer
        mediaType="image"
        onUploadSuccess={handleUploadSuccess}
        allowDrop={false}
        multiple={true}
        onUploadComplete={() => setFileList([])}
      />
    );
  };

  // Render drag and drop area for video upload
  const renderDragDropArea = () => {
    return (
      <MediaFileUploaderContainer
        mediaType="image"
        onUploadSuccess={handleUploadSuccess}
        allowDrop={true}
        dropAreaHeight="100%"
        dropAreaWidth="100%"
        className={isEditing ? 'editing' : ''}
        style={{
          backgroundColor: 'var(--edtt-color-primary-100)',
          width:
            typeof config.width === 'number' ? `${config.width}px` : '100%',
          maxWidth: '100%',
        }}
        onUploadComplete={() => setFileList([])}
        isEditing={isEditing}
        containerRef={containerRef}
      />
    );
  };

  // Render the main component content
  const renderMainComponent = () => {
    // Get the current image to display
    const currentImage =
      config.medias && config.medias.length > 0
        ? config.medias[selectedIndex]
        : null;

    // Check if we should use vertical display mode
    const useVerticalMode =
      config.displayMode === 'vertical' &&
      config.medias &&
      config.medias.length > 0;

    return (
      <div
        className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full"
        style={{
          padding: isFullscreen ? '20px' : '0', // Add padding in fullscreen mode
          maxWidth: isFullscreen ? '1200px' : '100%', // Limit width in fullscreen for better viewing
          margin: isFullscreen ? '0 auto' : '0', // Center in fullscreen mode
        }}
      >
        {/* URL Input and Upload buttons - Always visible in edit mode */}
        {isEditing && (
          <div className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-between tailwind-gap-2 tailwind-mb-2">
            <div
              className="tailwind-flex tailwind-items-center tailwind-gap-2"
              style={{ width: '50%' }}
            >
              <Input
                placeholder="Nhập URL ảnh (https://...)"
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                onPressEnter={handleUrlSubmit}
                prefix={<LinkOutlinedIcon className="tailwind-text-primary" />}
                style={{ width: '100%' }}
                size="middle"
              />
              <Button
                onClick={handleUrlSubmit}
                className="tailwind-flex-shrink-0"
                style={{
                  backgroundColor: 'var(--edtt-color-primary)',
                  color: 'var(--edtt-color-white)',
                  border: 'none',
                  borderRadius: '4px',
                }}
              >
                Thêm ảnh
              </Button>
            </div>
            {config.medias && config.medias.length > 0 && (
              <Button
                danger
                icon={<DeleteIcon />}
                onClick={() => handleDeleteImage()}
                size="middle"
                style={{
                  backgroundColor: 'var(--edtt-color-status-error)',
                  color: 'var(--edtt-color-white)',
                  border: 'none',
                  borderRadius: '4px',
                }}
              >
                Xóa tất cả hình ảnh
              </Button>
            )}
          </div>
        )}

        {/* Image Settings - Only show in edit mode */}
        {isEditing &&
          config.medias &&
          config.medias.length > 0 &&
          renderImageSettings()}

        {isEditing && config.medias && config.medias.length > 0 && (
          <>
            {renderUploadButton()}
            <p className="tailwind-mt-4 tailwind-text-sm tailwind-text-center tailwind-italic tailwind-text-gray-400">
              Hoặc kéo & thả để thêm ảnh
            </p>
          </>
        )}
        {/* For vertical mode, render all images stacked */}
        {useVerticalMode ? (
          <div
            ref={containerRef}
            className="tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full"
            style={{
              width:
                typeof config.width === 'number' ? `${config.width}px` : '100%',
              height: 'auto', // Auto height to accommodate all images
              maxWidth: '100%', // Ensure it doesn't overflow its parent
              minWidth: '95%', // Ensure minimum width is set
            }}
          >
            {/* Render all images vertically */}
            {renderVerticalImages()}
          </div>
        ) : /* Horizontal mode (current behavior) */
        currentImage ? (
          <>
            {renderImageControls()}
            <div
              ref={containerRef}
              className="tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-p-0"
              style={{
                width:
                  typeof config.width === 'number'
                    ? `${config.width}px`
                    : '100%',
                maxWidth: '100%', // Ensure it doesn't exceed parent width
              }}
            >
              {/* Video container with 3-column layout - Similar to VideoComponent */}
              <div className="tailwind-w-full tailwind-grid tailwind-grid-cols-[60px_1fr_60px] tailwind-gap-2 tailwind-items-center tailwind-mb-3">
                {/* Left navigation button section */}
                <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                  {config.displayMode === 'horizontal' &&
                    config.medias &&
                    config.medias.length > 1 && (
                      <Button
                        icon={<ChevronLeftIcon />}
                        onClick={handlePrevImage}
                        disabled={selectedIndex === 0}
                        className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-border-0 tailwind-text-white tailwind-bg-gray-500 hover:tailwind-bg-gray-600 tailwind-w-[44px] tailwind-h-[44px] tailwind-rounded-[4px] tailwind-text-xl tailwind-shadow-md"
                      />
                    )}
                </div>

                {/* Center image container */}
                <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-overflow-hidden">
                  <DragDropMediaWrapper
                    mediaType="image"
                    isEditing={isEditing}
                    onUploadSuccess={handleUploadSuccess}
                    className={`tailwind-w-full tailwind-flex tailwind-justify-center media-container image-container tailwind-overflow-hidden tailwind-h-auto tailwind-p-0 tailwind-box-border tailwind-items-center tailwind-max-w-full ${
                      isEditing ? 'editing' : ''
                    }`}
                    style={{
                      height: isFullscreen ? '70vh' : '400px', // Adjust height based on fullscreen state
                      transition: 'height 0.3s ease',
                    }}
                  >
                    <div
                      ref={imageContainerRef}
                      className="tailwind-w-full tailwind-h-full tailwind-overflow-hidden image-resize-container"
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100%',
                      }}
                    >
                      <BasicImageViewer
                        ref={imageViewerRef}
                        imageUrl={currentImage.mediaUrl}
                        alt={currentImage.name || 'Hình ảnh'}
                        imageRef={imageRef}
                        isEditing={isEditing}
                        rotation={rotation}
                        onRotateLeft={handleRotateLeft}
                        onRotateRight={handleRotateRight}
                        onDownload={handleDownload}
                        showControls={false}
                        zoomLevel={zoomLevel}
                        onZoomChange={handleZoomUpdate}
                        isFullscreen={isFullscreen}
                      />
                    </div>
                  </DragDropMediaWrapper>
                </div>

                {/* Right navigation button section */}
                <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                  {config.displayMode === 'horizontal' &&
                    config.medias &&
                    config.medias.length > 1 && (
                      <Button
                        icon={<ChevronRightIcon />}
                        onClick={handleNextImage}
                        disabled={selectedIndex === config.medias.length - 1}
                        className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-border-0 tailwind-text-white tailwind-bg-gray-500 hover:tailwind-bg-gray-600 tailwind-w-[44px] tailwind-h-[44px] tailwind-rounded-[4px] tailwind-text-xl tailwind-shadow-md"
                      />
                    )}
                </div>
              </div>

              {/* Add fixed margin between image and thumbnails */}
              <div className="tailwind-w-full tailwind-mt-[10px]">
                {renderImageThumbnails()}
              </div>
            </div>
          </>
        ) : isEditing ? (
          <div
            ref={containerRef}
            className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-min-h-[200px] media-file-uploader-container media-container editing`}
            style={{
              width:
                typeof config.width === 'number' ? `${config.width}px` : '100%',
              maxWidth: '100%',
            }}
          >
            {renderDragDropArea()}
          </div>
        ) : (
          <></>
        )}
      </div>
    );
  };

  return (
    <EngineContainer
      node={props}
      mainComponent={renderMainComponent()}
      allowConfiguration={false}
      showFullscreenButton={true}
      id={props.path}
      titleProps={config.titleProps}
      onTitleUpdate={handleTitleUpdate}
      onFullscreenChange={handleFullscreenChange}
    />
  );
};

export default withEdComponentParams(ImageComponent);

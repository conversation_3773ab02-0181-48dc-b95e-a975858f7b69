import {
  Question,
  DifficultyLevel,
  getQuestionsForGame,
  QuestionResult,
} from '../../common/QuestionComponent/QuestionBankConfig';
import { ITextProps } from '../../core/title/CoreTitle';

// Định nghĩa kiểu dữ liệu cho cấu hình game
export interface QuizCardGameConfig {
  description?: string; // Mô tả trò chơi
  titleProps: ITextProps; // Thuộc tính định dạng cho tiêu đề
  questions: Question[]; // Danh sách câu hỏi
  timeLimit?: number; // Giới hạn thời gian (giây) cho mỗi câu hỏi, không bắt buộc
  difficultyLevel: DifficultyLevel; // Độ khó của game
  correctAnswerScore: number; // Điểm thưởng cho câu trả lời đúng
  incorrectAnswerPenalty?: number; // Điểm bị trừ cho câu trả lời sai (tùy chọn)
  shuffleQuestions?: boolean; // C<PERSON> xáo trộn câu hỏi hay không
  allowSkip?: boolean; // Cho phép bỏ qua câu hỏi hay không
  requireConfirmation?: boolean; // Yêu cầu xác nhận trước khi nộp câu trả lời
  showExplanation?: boolean; // Hiển thị giải thích sau mỗi câu trả lời
  maxQuestions?: number; // Số lượng câu hỏi tối đa (có thể ít hơn tổng số câu trong danh sách)
  allowConfiguration?: boolean; // Cho phép hiển thị nút cấu hình, mặc định là true
  theme?: {
    // Tùy chọn giao diện
    primaryColor?: string; // Màu chủ đạo
    secondaryColor?: string; // Màu phụ
    backgroundColor?: string; // Màu nền
    cardImage?: string; // Hình ảnh thẻ (đường dẫn)
    animationSpeed?: 'slow' | 'normal' | 'fast'; // Tốc độ hiệu ứng
    cardColors?: string[]; // Màu sắc cho các thẻ
    cardSize?: 'small' | 'medium' | 'large'; // Kích cỡ thẻ
    cardAspectRatio?: string; // Tỷ lệ khung hình thẻ (ví dụ: '1/1', '2/3', '3/4')
    displayMode?: 'simple' | 'highlight'; // Chế độ hiển thị: đơn giản hoặc nổi bật
    gridColumns?: number; // Số cột hiển thị thẻ
  };
}

// Kiểu dữ liệu tổng kết cho toàn bộ game
export interface GameSummary {
  totalScore: number;
  correctAnswers: number;
  totalQuestions: number;
  accuracy: number; // Tỷ lệ phần trăm đúng
  averageTimePerQuestion?: number;
  totalTimeTaken?: number;
  results: QuestionResult[];
}

// Cấu hình mặc định cho game
export const defaultQuizCardGameConfig: QuizCardGameConfig = {
  description:
    'Chọn từng thẻ và trả lời các câu hỏi để kiếm điểm. Trả lời đúng sẽ lật thẻ và nhận điểm thưởng!',
  titleProps: {
    text: 'Lật thẻ câu hỏi',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  questions: getQuestionsForGame(15),
  difficultyLevel: 'medium',
  correctAnswerScore: 10,
  incorrectAnswerPenalty: 0,
  shuffleQuestions: false,
  allowSkip: false,
  requireConfirmation: true,
  showExplanation: true,
  timeLimit: 30,
  allowConfiguration: true,
  theme: {
    animationSpeed: 'normal',
    cardSize: 'medium',
    cardAspectRatio: '2/3',
    displayMode: 'highlight',
    gridColumns: 5,
    cardColors: [
      'tailwind-bg-blue-500',
      'tailwind-bg-purple-600',
      'tailwind-bg-green-500',
      'tailwind-bg-rose-500',
      'tailwind-bg-amber-500',
      'tailwind-bg-cyan-500',
      'tailwind-bg-violet-600',
      'tailwind-bg-lime-500',
      'tailwind-bg-pink-500',
      'tailwind-bg-indigo-500',
      'tailwind-bg-red-500',
      'tailwind-bg-teal-500',
    ],
  },
};

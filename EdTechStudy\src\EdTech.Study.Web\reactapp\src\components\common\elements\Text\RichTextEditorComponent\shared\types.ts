
// types.ts
import { ITextProps } from '../../../../../core/title/CoreTitle';

/**
 * Interface definition for the RichTextEditor props
 * This interface defines all configurable properties for the rich text editor component
 */
export interface RichTextEditorProps {
  /** Title properties for the component */
  titleProps?: ITextProps;

  /** The HTML content of the editor */
  content?: string;

  /** Default placeholder text when editor is empty */
  placeholder?: string;

  /** Maximum length of content in characters */
  maxLength?: number;

  /** Primary color for editor UI elements (toolbar, buttons, borders) */
  primaryColor?: string;

  /** Custom toolbar items configuration */
  toolbar?: {
    /** Array of toolbar items to display */
    items?: string[];
  };

  /** Configuration for file uploads */
  uploadConfig?: {
    /** API endpoint for file uploads */
    apiUrl?: string;
    /** Maximum file size in bytes */
    maxFileSize?: number;
    /** Allowed file types (MIME types) */
    allowedTypes?: string[];
  };

  /** Configuration for video embedding */
  videoConfig?: {
    /** API endpoint for video uploads */
    saveUrl?: string;
    /** Path to save uploaded videos */
    path?: string;
    /** Maximum video file size in bytes */
    maxFileSize?: number;
    /** Default width for inserted videos */
    width?: string;
    /** Default height for inserted videos */
    height?: string;
    /** Whether to allow video resizing */
    allowResize?: boolean;
    /** Default layout option for videos (Inline or Break) */
    layoutOption?: 'Inline' | 'Break';
  };

  /** Configuration for audio embedding */
  audioConfig?: {
    /** API endpoint for audio uploads */
    saveUrl?: string;
    /** Path to save uploaded audio */
    path?: string;
    /** Maximum audio file size in bytes */
    maxFileSize?: number;
    /** Whether to allow audio resizing */
    allowResize?: boolean;
    /** Default layout option for audio (Inline or Break) */
    layoutOption?: 'Inline' | 'Break';
  };
  /** Master switch to enable/disable all collaboration features */
  enableCollaboration?: boolean;

  /** Enable commenting functionality (requires enableCollaboration) */
  enableComments?: boolean;

  /** Enable track changes functionality (requires enableCollaboration) */
  enableTrackChanges?: boolean;

  /** Enable revision history functionality (requires enableCollaboration) */
  enableRevisionHistory?: boolean;
}

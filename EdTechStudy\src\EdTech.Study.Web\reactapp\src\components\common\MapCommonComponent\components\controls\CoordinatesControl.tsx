import React, { useState, useEffect } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import { ControlPosition } from '../../MapUtils';

interface CoordinatesControlProps {
  position?: ControlPosition;
  decimals?: number;
  styles?: React.CSSProperties;
}

/**
 * Component hiển thị tọa độ con trỏ chuột trên bản đồ
 */
const CoordinatesControl: React.FC<CoordinatesControlProps> = ({
  position = 'bottomleft',
  decimals = 6,
  styles = {},
}) => {
  const map = useMap();
  const [coordinates, setCoordinates] = useState<{
    lat: number;
    lng: number;
  } | null>(null);

  // Cập nhật tọa độ khi di chuyển chuột
  useEffect(() => {
    const updateCoordinates = (e: L.LeafletMouseEvent) => {
      setCoordinates({
        lat: e.latlng.lat,
        lng: e.latlng.lng,
      });
    };

    map.on('mousemove', updateCoordinates);

    return () => {
      map.off('mousemove', updateCoordinates);
    };
  }, [map]);

  // Tạo control và thêm vào bản đồ
  useEffect(() => {
    const CoordinatesInfo = L.Control.extend({
      options: {
        position: position as L.ControlPosition,
      },

      onAdd: function () {
        const container = L.DomUtil.create(
          'div',
          'leaflet-control-coordinates leaflet-control'
        );

        // Style cho container
        const defaultStyles = {
          background: 'white',
          padding: '5px 8px',
          fontSize: '12px',
          borderRadius: '4px',
          boxShadow: '0 1px 5px rgba(0,0,0,0.4)',
          margin: '0 0 10px 10px',
          color: '#333',
          fontFamily: 'Arial, sans-serif',
          clear: 'none',
        };

        // Áp dụng style
        Object.assign(container.style, defaultStyles, styles);

        return container;
      },
    });

    const coordinatesControl = new CoordinatesInfo();
    coordinatesControl.addTo(map);

    return () => {
      if (coordinatesControl) {
        coordinatesControl.remove();
      }
    };
  }, [map, position, styles]);

  // Cập nhật nội dung hiển thị
  useEffect(() => {
    if (!coordinates) return;

    const container = document.querySelector('.leaflet-control-coordinates');
    if (container) {
      container.textContent = `Vĩ độ: ${coordinates.lat.toFixed(
        decimals
      )} | Kinh độ: ${coordinates.lng.toFixed(decimals)}`;
    }
  }, [coordinates, decimals]);

  return null;
};

export default CoordinatesControl;

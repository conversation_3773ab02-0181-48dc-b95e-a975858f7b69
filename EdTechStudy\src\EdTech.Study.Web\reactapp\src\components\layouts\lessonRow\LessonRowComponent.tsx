import { Button, Space, Tooltip } from 'antd';
import { useState, useEffect, useRef, useCallback, useMemo, memo } from 'react';
import { useDispatch } from 'react-redux';
import {
  EEngineInteractionMode,
  ETypeEdTechComponent,
} from '../../../enums/AppEnums';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import {
  IEdTechRenderProps,
  IEdTechRenderTreeData,
} from '../../../interfaces/AppComponents';
import { updateItems } from '../../../store/slices/AppSlices/EdTechRenderDataSlice';
import LessonRenderComponent from '../../common/LessonRenderComponent/LessonRenderComponent';
import Splitter from './Splitter';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';
import { nanoid } from 'nanoid';
import { isEmpty } from '@tsp/utils';

interface LessonRowConfig {
  columnWidths?: number[]; // Store column widths as percentages
}

// Zod schema for component validation
export const lessonRowSchema = createComponentSchema({
  paramsSchema: {
    columnWidths: z.array(z.number()).optional(),
  },
});

const LessonRowComponent: React.FC<IEdTechRenderProps<LessonRowConfig>> = (
  props
) => {
  const {
    isEditing,
    subItems = [],
    path,
    engineState,
    params,
    addOrUpdateParamComponent,
  } = props;
  const [columnHeights, setColumnHeights] = useState<{ [key: string]: number }>(
    {}
  );
  const [columnWidths, setColumnWidths] = useState<number[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const dispatch = useDispatch();

  // Refs for latest prop values to avoid stale closures
  const isEditingRef = useRef(isEditing);
  const addOrUpdateParamComponentRef = useRef(addOrUpdateParamComponent);
  const classRow =
    isEditing && engineState?.interactionMode === EEngineInteractionMode.DEFAULT
      ? 'tailwind-border tailwind-border-solid tailwind-border-default'
      : '';

  // Update refs when props change
  useEffect(() => {
    isEditingRef.current = isEditing;
    addOrUpdateParamComponentRef.current = addOrUpdateParamComponent;
  }, [isEditing, addOrUpdateParamComponent]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Get columns with subitems (used for width calculation in preview mode)
  // This is used to calculate the correct column widths when some columns have no subitems
  // Example: If we have 3 columns with widths [30%, 40%, 30%] and the middle one has no subitems,
  // we'll recalculate to [50%, 0%, 50%] for the columns
  const columnsWithSubitems = useMemo(() => {
    return subItems.map((item) => !isEmpty(item.subItems));
  }, [subItems]);
  // Initialize column widths
  useEffect(() => {
    if (!isEditing) {
      // In preview mode, if a column has no subitems, set its width to 0% and redistribute the remaining width
      if (subItems.length === 0) {
        setColumnWidths([]);
        return;
      }

      // Count columns with subitems
      const columnsWithSubitemsCount =
        columnsWithSubitems.filter(Boolean).length;

      if (columnsWithSubitemsCount === 0) {
        // If no columns have subitems, just set all to 0
        setColumnWidths(Array(subItems.length).fill(0));
        return;
      }

      if (
        params?.columnWidths &&
        params.columnWidths.length === subItems.length
      ) {
        // If we have saved column widths, we need to recalculate
        const originalWidths = [...params.columnWidths];
        const newWidths: number[] = new Array(subItems.length).fill(0);
        let totalVisibleWidth = 0;

        // Calculate total width of columns with subitems
        subItems.forEach((item, index) => {
          if (!isEmpty(item.subItems)) {
            totalVisibleWidth += originalWidths[index];
          }
        });

        // Normalize the widths - columns with subitems get proportional width, others get 0%
        if (totalVisibleWidth > 0) {
          subItems.forEach((item, index) => {
            if (!isEmpty(item.subItems)) {
              // Normalize to maintain proportions but sum to 100%
              newWidths[index] =
                (originalWidths[index] / totalVisibleWidth) * 100;
            } else {
              // Columns without subitems get 0% width
              newWidths[index] = 0;
            }
          });
          setColumnWidths(newWidths);
        } else {
          // Fallback to equal distribution if something went wrong
          const equalWidth = 100 / columnsWithSubitemsCount;
          const newWidths = subItems.map((item) =>
            !isEmpty(item.subItems) ? equalWidth : 0
          );
          setColumnWidths(newWidths);
        }
      } else {
        // If no saved widths, distribute evenly among columns with subitems
        const equalWidth = 100 / columnsWithSubitemsCount;
        const newWidths = subItems.map((item) =>
          !isEmpty(item.subItems) ? equalWidth : 0
        );
        setColumnWidths(newWidths);
      }
      return;
    }

    // In editing mode, use the original logic
    if (subItems.length > 0) {
      if (
        params?.columnWidths &&
        params.columnWidths.length === subItems.length
      ) {
        // Use saved column widths if available
        setColumnWidths(params.columnWidths);
      } else {
        // Otherwise distribute evenly
        const equalWidth = 100 / subItems.length;
        setColumnWidths(Array(subItems.length).fill(equalWidth));
      }
    } else {
      setColumnWidths([]);
    }
  }, [subItems.length, params?.columnWidths, isEditing, columnsWithSubitems]);

  // Update params when column widths change
  useEffect(() => {
    if (columnWidths.length > 0 && isEditing) {
      debouncedUpdateParams(columnWidths);
    }
  }, [columnWidths, isEditing]);

  // Debounced param updates
  const debouncedUpdateParams = useCallback((widths: number[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isEditingRef.current && addOrUpdateParamComponentRef.current) {
        addOrUpdateParamComponentRef.current({
          columnWidths: widths,
        });
      }
    }, 300);
  }, []);

  // Handle column width resize
  const handleColumnWidthsChange = useCallback(
    (newWidths: number[]) => {
      if (!isEditing) return;
      setColumnWidths(newWidths);
    },
    [isEditing]
  );

  // Calculate row height based on tallest column
  const rowHeight = useMemo(() => {
    if (Object.keys(columnHeights).length === 0) return 'auto';
    return Math.max(...Object.values(columnHeights));
  }, [columnHeights]);

  return (
    <div className="lesson-row-wrapper">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div
          className={`lesson-row-container ${
            isEditing ? 'editing' : ''
          } tailwind-flex`}
          ref={containerRef}
          style={{ height: rowHeight === 'auto' ? 'auto' : `${rowHeight}px` }}
        >
          <div className="tailwind-w-full">
            <Splitter
              isEditing={isEditing}
              initialSizes={columnWidths}
              onResize={handleColumnWidthsChange}
            >
              {subItems.map((item) => {
                // In preview mode, we still render all columns
                // Columns without subitems will have 0% width as calculated in columnWidths
                return (
                  <div
                    key={item.id}
                    className={`tailwind-h-full tailwind-bg-default ${classRow}`}
                  >
                    <LessonRenderComponent data={item} />
                  </div>
                );
              })}
            </Splitter>
          </div>
        </div>
      </Space>
    </div>
  );
};

export default memo(withEdComponentParams(LessonRowComponent));

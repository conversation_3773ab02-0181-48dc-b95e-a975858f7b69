import { z } from 'zod';
import { ED_TECH_COMPONENT } from '../../constants/edTechComponents';
import {
  IEdTechRenderParam,
  IEdTechRenderTreeData,
} from '../../interfaces/AppComponents';
import {
  baseComponentSchema,
  extractParamsSchema,
} from '../schema/createComponentSchema';

export interface IErrorValidateComponent {
  componentName: string;
  componentId: string;
  path: string;
  errorMessage: string;
  errorCode: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: IErrorValidateComponent[];
}

/**
 * Hàm validate toàn bộ dữ liệu
 * @param jsonData Dữ liệu cần validate
 * @returns Kết quả validation
 */
export const validateData = (jsonData: {
  edTechRenderTreeData: IEdTechRenderTreeData;
  edComponentParams: IEdTechRenderParam[] | undefined;
}): ValidationResult => {
  const validationErrors: IErrorValidateComponent[] = [];

  try {
    //validate jsonData với schemaJsonDataApp
    const schemaJsonDataApp = z.object({
      edComponentParams: z.array(z.any()).optional(),
      edTechRenderTreeData: z.any().optional(),
    });
    schemaJsonDataApp.parse(jsonData);

    const { edTechRenderTreeData, edComponentParams } = jsonData;

    const paramsMap = new Map<string, IEdTechRenderParam>();
    edComponentParams?.forEach((param) => {
      paramsMap.set(param.id, param);
    });

    // Validate từng component trong cây
    const validateNode = (node: IEdTechRenderTreeData) => {
      // Thêm params từ edComponentParams
      const param = paramsMap.get(node.id);
      const nodeWithParams = {
        ...node,
        params: param?.params,
      };

      // Tìm schema từ ED_TECH_COMPONENT
      const component = ED_TECH_COMPONENT.find(
        (comp) => comp.name === node.name
      );

      // nếu có version thì tìm schema theo version, không có thì tìm schema mặc định
      const currentSchema = node.version
        ? component?.components?.find((x) => x.version === node.version)?.schema
        : component?.schema;

      if (currentSchema) {
        // Nếu có schema thì validate
        try {
          currentSchema.parse(nodeWithParams);
        } catch (error) {
          if (error instanceof z.ZodError) {
            console.log(error);
            error.errors.forEach((err) => {
              validationErrors.push({
                componentName: node.name,
                componentId: node.id,
                path: err.path.join('.'),
                errorMessage: err.message,
                errorCode: err.code,
              });
            });
          }
        }
      } else {
        // Nếu không có schema thì chỉ validate cấu trúc cơ bản
        try {
          baseComponentSchema.parse(nodeWithParams);
        } catch (error) {
          if (error instanceof z.ZodError) {
            error.errors.forEach((err) => {
              validationErrors.push({
                componentName: node.name,
                componentId: node.id,
                path: err.path.join('.'),
                errorMessage: err.message,
                errorCode: err.code,
              });
            });
          }
        }
      }

      // Validate subItems nếu có
      if (node.subItems) {
        node.subItems.forEach(validateNode);
      }
    };

    validateNode(edTechRenderTreeData);
  } catch (error) {
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        validationErrors.push({
          componentName: 'Unknown',
          componentId: 'Unknown',
          path: err.path.join('.'),
          errorMessage: err.message,
          errorCode: err.code,
        });
      });
    } else {
      validationErrors.push({
        componentName: 'System',
        componentId: 'Unknown',
        path: '',
        errorMessage: `Đã xảy ra lỗi không xác định khi kiểm tra dữ liệu: ${error}`,
        errorCode: 'UNKNOWN_ERROR',
      });
    }
  }

  return {
    isValid: validationErrors.length === 0,
    errors: validationErrors,
  };
};

/**
 * Hàm validate chỉ phần params của component
 * @param schema Schema của component
 * @param params Dữ liệu params
 */
export const validateParams = (schema: any, params: any) => {
  try {
    const paramsSchema = extractParamsSchema(schema);
    return paramsSchema.parse({
      params,
    });
  } catch (error) {
    throw error;
  }
};

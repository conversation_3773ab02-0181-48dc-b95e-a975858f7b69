import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Button, message, Space, Input } from 'antd';
import {
  CheckCircleOutlined,
  TrophyOutlined,
  SettingOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  Question,
} from '../../../interfaces/quizs/questionBase';
import { FireworksComponent } from '../quiz';
import { createFillBlanksQuestionTemplate } from '../practiceEngines/questionTemplates';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { Guid } from 'guid-typescript';
import { FillBlanksComponent } from '.';
import {
  FillBlanksQuestion,
  FillBlanksAnswer,
} from '../../../interfaces/quizs/fillblanks.interfaces';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

const { Title, Text } = Typography;

export interface ISimpleFillBlanksWrapperProps {
  id: string;
  question?: FillBlanksQuestion;
  showFireworks?: boolean;
  showTitle?: boolean;
  congratsMessage?: string;
  startButtonText?: string; // Text for the start button
  startScreenTitle?: string; // Title text for start screen
  startScreenDescription?: string; // Description text for start screen
}

export const simpleFillBlanksSchema = createComponentSchema({
  paramsSchema: {
    question: z.string().optional(),
    answers: z.array(z.string()).optional(),
    caseSensitive: z.boolean().optional(),
    showFeedback: z.boolean().optional(),
  },
});

const SimpleFillBlanksWrapper: React.FC<
  IEdTechRenderProps<ISimpleFillBlanksWrapperProps>
> = (props) => {
  const {
    params = {} as ISimpleFillBlanksWrapperProps,
    path,
    order,
    addOrUpdateParamComponent,
  } = props;

  // Extract values from params with defaults
  const {
    id = `fillblanks-${Date.now()}`,
    question: initialQuestion,
    showFireworks = true,
    showTitle = true,
    congratsMessage = 'Chúc mừng bạn đã hoàn thành câu hỏi!',
    startButtonText = 'Bắt đầu',
    startScreenTitle = 'Bạn đã sẵn sàng?',
    startScreenDescription = 'Nhấn nút bắt đầu để làm bài.',
  } = params;

  // Create a default question if none is provided
  const [question, setQuestion] = useState<FillBlanksQuestion>(
    initialQuestion || createFillBlanksQuestionTemplate('Câu hỏi điền từ')
  );
  const [configMode, setConfigMode] = useState<boolean>(false);
  const [completed, setCompleted] = useState(false);
  const [showCongrats, setShowCongrats] = useState(false);
  const [showFireworksEffect, setShowFireworksEffect] = useState(false);

  // Start screen state
  const [started, setStarted] = useState(false);
  const firstCreate = useRef<number>(0);

  const handleUpdateParams = (values: { [key: string]: any }) => {
    addOrUpdateParamComponent({
      id,
      question,
      showFireworks,
      showTitle,
      congratsMessage,
      startButtonText,
      startScreenDescription,
      startScreenTitle,
      ...values,
    });
  };

  // Start the quiz
  const handleStart = () => {
    setStarted(true);
    setConfigMode(false);
  };

  // Handle question completion
  const handleComplete = (
    questionId: string,
    userSelect: FillBlanksAnswer | undefined
  ) => {
    setCompleted(true);

    // Update the question status
    const updatedQuestion = {
      ...question,
      isCompleted: true,
      status: 'incorrect',
      userSelect,
    };

    setQuestion(updatedQuestion);

    // Save the updated question state
    handleUpdateParams({ question: updatedQuestion });

    // Show congratulations if correct
    // if (correct) {
    //   setShowCongrats(true);
    //   if (showFireworks) {
    //     setShowFireworksEffect(true);
    //     setTimeout(() => {
    //       setShowFireworksEffect(false);
    //     }, 2000);
    //   }
    // }
  };

  // Reset the quiz
  const handleReset = () => {
    setCompleted(false);
    setShowCongrats(false);

    // Return to start screen
    setStarted(false);

    // Reset question status
    const resetQuestion = {
      ...question,
      isCompleted: false,
      status: undefined,
      userSelect: undefined,
    };

    setQuestion(resetQuestion);

    // Save the reset state
    handleUpdateParams({ question: resetQuestion });
  };

  // Update local state when props change
  useEffect(() => {
    if (initialQuestion) {
      setQuestion(initialQuestion);
      setCompleted(!!initialQuestion.isCompleted);
      setShowCongrats(initialQuestion.status === 'correct');

      // If question was already completed, show in completed state
      if (initialQuestion.isCompleted) {
        setStarted(true);
      }
    }
  }, [initialQuestion]);

  useEffect(() => {
    if (firstCreate.current < 1 && !!!props.params?.question) {
      setConfigMode(true);
      setStarted(true);
      firstCreate.current = firstCreate.current + 1;
    }
  }, [props.params?.question]);

  // Render start screen
  const renderStartScreen = () => {
    return (
      <Card
        className="start-card"
        style={{
          textAlign: 'center',
          padding: '24px',
        }}
      >
        <div className="flex justify-end">
          <Button
            type="default"
            onClick={() => {
              setConfigMode(true);
              setStarted(true);
            }}
          >
            <SettingOutlined />
          </Button>
        </div>
        <Title level={3}>{startScreenTitle}</Title>
        <Text
          style={{ fontSize: '16px', display: 'block', marginBottom: '24px' }}
        >
          {startScreenDescription}
        </Text>

        <Button
          type="primary"
          size="large"
          onClick={handleStart}
          className="flex-1 bg-blue-500 hover:bg-blue-600"
        >
          {startButtonText}
        </Button>
      </Card>
    );
  };

  function handleChangeQuestion(update: Question) {
    setConfigMode(false);
    setStarted(false);
    setQuestion(update as FillBlanksQuestion);
    handleUpdateParams({
      id: id,
      question: update as FillBlanksQuestion,
    });
  }

  function handleDeleteQuestion(id: string) {
    throw new Error('Function not implemented.');
  }

  // Main render
  return (
    <PracticeEngineContext.Provider
      value={{
        handleChangePosition: () => {},
        handleChangeQuestion: handleChangeQuestion,
        handleDeleteQuestion: handleDeleteQuestion,
        handleToggleFullscreen: () => {},
        isFullscreen: false,
      }}
    >
      <div
        id={id}
        className="simple-fillblanks-wrapper bg-white rounded-lg shadow-md p-5 max-w-6xl mx-auto"
        style={{ position: 'relative' }}
      >
        {showTitle && (
          <Title level={4} style={{ marginBottom: '16px' }}>
            {question.title}
          </Title>
        )}

        {!started ? (
          renderStartScreen()
        ) : (
          <>
            {showCongrats ? (
              <Card
                className="congrats-card"
                style={{
                  textAlign: 'center',
                  padding: '24px',
                  backgroundColor: '#f6ffed',
                  borderColor: '#b7eb8f',
                }}
              >
                <TrophyOutlined
                  style={{
                    fontSize: '48px',
                    color: '#52c41a',
                    marginBottom: '16px',
                  }}
                />
                <Title level={3} style={{ color: '#52c41a' }}>
                  <CheckCircleOutlined /> {congratsMessage}
                </Title>
                <Text
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    marginBottom: '24px',
                  }}
                >
                  Bạn đã trả lời chính xác câu hỏi. Tiếp tục cố gắng nhé!
                </Text>
                <Button
                  type="primary"
                  onClick={handleReset}
                  className="flex-1 bg-blue-500 hover:bg-blue-600"
                >
                  Làm lại
                </Button>
              </Card>
            ) : (
              <>
                <Space className="flex mb-2 justify-between">
                  <Button
                    type="default"
                    onClick={() => {
                      handleReset();
                    }}
                  >
                    <LeftOutlined />
                  </Button>
                </Space>
                <FillBlanksComponent
                  id={id + '-fill-blank-core'}
                  question={question}
                  onComplete={handleComplete}
                  showFeedback={true}
                  allowManyTimes={true}
                  configMode={configMode}
                />
              </>
            )}
          </>
        )}

        {/* Fireworks effect when answered correctly */}
        <FireworksComponent show={showFireworksEffect} />
      </div>
    </PracticeEngineContext.Provider>
  );
};

export default withEdComponentParams(SimpleFillBlanksWrapper);

/**
 * LessonConfigDB - Module for storing lesson configurations in IndexedDB
 * Tách lưu trữ thành 2 bảng riêng biệt:
 * 1. edComponentParamsStore - Lưu trữ thông tin component params
 * 2. edTechRenderTreeDataStore - Lưu trữ thông tin render tree data
 */

var LessonConfigDB = (function () {
  let self = this;
  const DB_NAME = 'LessonConfigDatabase';
  const DB_VERSION = 2; // Tăng version để trigger onupgradeneeded
  const PARAMS_STORE = 'edComponentParamsStore';
  const TREE_STORE = 'edTechRenderTreeDataStore';
  this.db = null;
  let dbInitPromise = null; // Promise theo dõi quá trình khởi tạo DB

  /**
   * Initialize the database
   * @returns {Promise} Promise that resolves when the database is ready
   */
  const init = () => {
    if (dbInitPromise) {
      return dbInitPromise; // T<PERSON><PERSON> về promise đang chờ nếu đã bắt đầu khởi tạo
    }

    dbInitPromise = new Promise((resolve, reject) => {
      if (self.db) {
        resolve(self.db);
        return;
      }

      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = (event) => {
        reject(`Database error: ${event.target.error}`);
      };

      request.onsuccess = (event) => {
        self.db = event.target.result;
        resolve(self.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        const transaction = event.target.transaction;

        // Tạo hoặc sử dụng object store cho edComponentParams
        let paramsStore;
        if (!db.objectStoreNames.contains(PARAMS_STORE)) {
          paramsStore = db.createObjectStore(PARAMS_STORE, {
            keyPath: 'id',
          });
          paramsStore.createIndex('timestamp', 'timestamp', {
            unique: false,
          });
        } else {
          paramsStore = transaction.objectStore(PARAMS_STORE);
        }

        // Tạo hoặc sử dụng object store cho edTechRenderTreeData
        let treeStore;
        if (!db.objectStoreNames.contains(TREE_STORE)) {
          treeStore = db.createObjectStore(TREE_STORE, {
            keyPath: 'id',
          });
          treeStore.createIndex('timestamp', 'timestamp', {
            unique: false,
          });
        } else {
          treeStore = transaction.objectStore(TREE_STORE);
        }

        // Di chuyển dữ liệu từ phiên bản cũ sang phiên bản mới nếu cần
        if (
          event.oldVersion < 2 &&
          db.objectStoreNames.contains('lessonConfigs')
        ) {
          const oldStore = transaction.objectStore('lessonConfigs');
          const getAllRequest = oldStore.getAll();

          getAllRequest.onsuccess = () => {
            const allData = getAllRequest.result;

            // Di chuyển dữ liệu sang các bảng mới
            allData.forEach((item) => {
              if (item.config) {
                // Lưu edComponentParams
                if (item.config.edComponentParams) {
                  paramsStore.put({
                    id: item.id,
                    data: item.config.edComponentParams,
                    timestamp: item.timestamp || Date.now(),
                  });
                }

                // Lưu edTechRenderTreeData
                if (item.config.edTechRenderTreeData) {
                  treeStore.put({
                    id: item.id,
                    data: item.config.edTechRenderTreeData,
                    timestamp: item.timestamp || Date.now(),
                  });
                }
              }
            });

            // Có thể xóa bảng cũ sau khi di chuyển xong
            // db.deleteObjectStore('lessonConfigs');
          };
        }
      };
    });

    return dbInitPromise;
  };

  /**
   * Đảm bảo database đã được khởi tạo trước khi thực hiện các thao tác
   * @returns {Promise} Promise that resolves when the database is ready
   */
  const ensureDbReady = () => {
    if (self.db) {
      return Promise.resolve(self.db);
    }
    return init();
  };

  /**
   * Lưu edComponentParams vào bảng riêng
   * @param {string} id - Lesson identifier
   * @param {Array} edComponentParams - Component parameters
   * @returns {Promise} Promise that resolves when the operation completes
   */
  const saveEdComponentParams = async (id, edComponentParams) => {
    return await ensureDbReady().then(() => {
      return new Promise((resolve, reject) => {
        try {
          // Kiểm tra và làm sạch dữ liệu
          if (edComponentParams === undefined || edComponentParams === null) {
            edComponentParams = [];
          }

          let cleanedParams;
          try {
            cleanedParams = JSON.parse(JSON.stringify(edComponentParams));
          } catch (error) {
            console.error('Failed to clean params:', error);
            cleanedParams = [];
          }

          const transaction = self.db.transaction([PARAMS_STORE], 'readwrite');
          const store = transaction.objectStore(PARAMS_STORE);

          const paramsData = {
            id: id,
            data: cleanedParams,
            timestamp: Date.now(),
          };

          const request = store.put(paramsData);

          request.onsuccess = () => {
            resolve(id);
          };

          request.onerror = (event) => {
            console.error('Error saving params:', event.target.error);
            reject(`Error saving edComponentParams: ${event.target.error}`);
          };
        } catch (error) {
          console.error('Error in saveEdComponentParams:', error);
          reject(`Error in saveEdComponentParams: ${error.message || error}`);
        }
      });
    });
  };

  /**
   * Lưu edTechRenderTreeData vào bảng riêng
   * @param {string} id - Lesson identifier
   * @param {Object} edTechRenderTreeData - Render tree data
   * @returns {Promise} Promise that resolves when the operation completes
   */
  const saveEdTechRenderTreeData = async (id, edTechRenderTreeData) => {
    return await ensureDbReady().then(() => {
      return new Promise((resolve, reject) => {
        try {
          // Kiểm tra và làm sạch dữ liệu
          if (
            edTechRenderTreeData === undefined ||
            edTechRenderTreeData === null
          ) {
            edTechRenderTreeData = {};
          }

          let cleanedTreeData;
          try {
            cleanedTreeData = JSON.parse(JSON.stringify(edTechRenderTreeData));
          } catch (error) {
            console.error('Failed to clean tree data:', error);
            cleanedTreeData = {};
          }

          const transaction = self.db.transaction([TREE_STORE], 'readwrite');
          const store = transaction.objectStore(TREE_STORE);

          const treeData = {
            id: id,
            data: cleanedTreeData,
            timestamp: Date.now(),
          };

          const request = store.put(treeData);

          request.onsuccess = () => {
            resolve(id);
          };

          request.onerror = (event) => {
            console.error('Error saving tree data:', event.target.error);
            reject(`Error saving edTechRenderTreeData: ${event.target.error}`);
          };
        } catch (error) {
          console.error('Error in saveEdTechRenderTreeData:', error);
          reject(
            `Error in saveEdTechRenderTreeData: ${error.message || error}`
          );
        }
      });
    });
  };

  /**
   * Lấy edComponentParams theo id
   * @param {string} id - Lesson identifier
   * @returns {Promise} Promise that resolves with the component parameters or empty array if not found
   */
  const getEdComponentParams = (id) => {
    return ensureDbReady().then(() => {
      return new Promise((resolve, reject) => {
        const transaction = self.db.transaction([PARAMS_STORE], 'readonly');
        const store = transaction.objectStore(PARAMS_STORE);
        const request = store.get(id);

        request.onsuccess = () => {
          const result = request.result;
          resolve(result ? result.data : []);
        };

        request.onerror = (event) => {
          reject(`Error retrieving edComponentParams: ${event.target.error}`);
        };
      });
    });
  };

  /**
   * Lấy edTechRenderTreeData theo id
   * @param {string} id - Lesson identifier
   * @returns {Promise} Promise that resolves with the render tree data or empty object if not found
   */
  const getEdTechRenderTreeData = (id) => {
    return ensureDbReady().then(() => {
      return new Promise((resolve, reject) => {
        const transaction = self.db.transaction([TREE_STORE], 'readonly');
        const store = transaction.objectStore(TREE_STORE);
        const request = store.get(id);

        request.onsuccess = () => {
          const result = request.result;
          resolve(result ? result.data : {});
        };

        request.onerror = (event) => {
          reject(
            `Error retrieving edTechRenderTreeData: ${event.target.error}`
          );
        };
      });
    });
  };

  /**
   * Lấy cấu hình đầy đủ của bài học (kết hợp từ 2 bảng)
   * @param {string} id - Lesson identifier
   * @returns {Promise} Promise that resolves with the complete lesson config or null if not found
   */
  const getConfig = (id) => {
    return ensureDbReady().then(() => {
      return Promise.all([
        new Promise((resolve) => {
          const transaction = self.db.transaction([PARAMS_STORE], 'readonly');
          const store = transaction.objectStore(PARAMS_STORE);
          const request = store.get(id);

          request.onsuccess = () => {
            resolve(request.result); // Trả về kết quả gốc, có thể là undefined
          };

          request.onerror = () => {
            resolve(undefined);
          };
        }),
        new Promise((resolve) => {
          const transaction = self.db.transaction([TREE_STORE], 'readonly');
          const store = transaction.objectStore(TREE_STORE);
          const request = store.get(id);

          request.onsuccess = () => {
            resolve(request.result); // Trả về kết quả gốc, có thể là undefined
          };

          request.onerror = () => {
            resolve(undefined);
          };
        }),
      ]).then(([paramsRecord, treeRecord]) => {
        // Kiểm tra nếu cả hai bảng đều không có dữ liệu
        if (!paramsRecord && !treeRecord) {
          return null;
        }

        // Lấy timestamp từ bản ghi mới nhất
        const timestamp = Math.max(
          paramsRecord ? paramsRecord.timestamp : 0,
          treeRecord ? treeRecord.timestamp : 0
        );

        return {
          id: id,
          config: {
            id: id,
            edComponentParams: paramsRecord ? paramsRecord.data : [],
            edTechRenderTreeData: treeRecord ? treeRecord.data : {},
          },
          timestamp: timestamp,
        };
      });
    });
  };

  /**
   * Lấy danh sách tất cả các id bài học
   * @returns {Promise} Promise that resolves with array of lesson ids
   */
  const getAllLessonIds = () => {
    return ensureDbReady().then(() => {
      return new Promise((resolve, reject) => {
        const transaction = self.db.transaction(
          [PARAMS_STORE, TREE_STORE],
          'readonly'
        );
        const paramsStore = transaction.objectStore(PARAMS_STORE);
        const treeStore = transaction.objectStore(TREE_STORE);

        const paramsRequest = paramsStore.getAllKeys();
        const treeRequest = treeStore.getAllKeys();

        let paramsIds = [];
        let treeIds = [];

        paramsRequest.onsuccess = () => {
          paramsIds = paramsRequest.result;
          checkComplete();
        };

        treeRequest.onsuccess = () => {
          treeIds = treeRequest.result;
          checkComplete();
        };

        let completedRequests = 0;
        function checkComplete() {
          completedRequests++;
          if (completedRequests === 2) {
            // Kết hợp và loại bỏ trùng lặp
            const allIds = [...new Set([...paramsIds, ...treeIds])];
            resolve(allIds);
          }
        }

        transaction.onerror = (event) => {
          reject(`Error retrieving lesson ids: ${event.target.error}`);
        };
      });
    });
  };

  /**
   * Lấy tất cả cấu hình bài học
   * @returns {Promise} Promise that resolves with array of all lesson configurations
   */
  const getAllConfigs = () => {
    return getAllLessonIds().then((ids) => {
      const promises = ids.map((id) => getConfig(id));
      return Promise.all(promises);
    });
  };

  /**
   * Xóa cấu hình bài học theo id
   * @param {string} id - Lesson identifier
   * @returns {Promise} Promise that resolves when both stores are updated
   */
  const deleteConfig = (id) => {
    return ensureDbReady().then(() => {
      return Promise.all([
        new Promise((resolve, reject) => {
          const transaction = self.db.transaction([PARAMS_STORE], 'readwrite');
          const store = transaction.objectStore(PARAMS_STORE);
          const request = store.delete(id);

          request.onsuccess = () => resolve(true);
          request.onerror = (event) =>
            reject(`Error deleting from params store: ${event.target.error}`);
        }),
        new Promise((resolve, reject) => {
          const transaction = self.db.transaction([TREE_STORE], 'readwrite');
          const store = transaction.objectStore(TREE_STORE);
          const request = store.delete(id);

          request.onsuccess = () => resolve(true);
          request.onerror = (event) =>
            reject(`Error deleting from tree store: ${event.target.error}`);
        }),
      ]).then(() => true);
    });
  };

  /**
   * Xóa tất cả dữ liệu
   * @returns {Promise} Promise that resolves when all data is cleared
   */
  const clearAllConfigs = () => {
    return ensureDbReady().then(() => {
      return Promise.all([
        new Promise((resolve, reject) => {
          const transaction = self.db.transaction([PARAMS_STORE], 'readwrite');
          const store = transaction.objectStore(PARAMS_STORE);
          const request = store.clear();

          request.onsuccess = () => resolve(true);
          request.onerror = (event) =>
            reject(`Error clearing params store: ${event.target.error}`);
        }),
        new Promise((resolve, reject) => {
          const transaction = self.db.transaction([TREE_STORE], 'readwrite');
          const store = transaction.objectStore(TREE_STORE);
          const request = store.clear();

          request.onsuccess = () => resolve(true);
          request.onerror = (event) =>
            reject(`Error clearing tree store: ${event.target.error}`);
        }),
      ]).then(() => true);
    });
  };

  /**
   * Create a standardized lesson ID from grade, subject and lesson number
   * @param {string} grade - The grade level (e.g. "Lớp 6")
   * @param {string} subject - The subject (e.g. "môn toán")
   * @param {string} lesson - The lesson identifier (e.g. "bài 1")
   * @returns {string} A formatted lesson ID
   */
  const createId = (grade, subject, lesson) => {
    // Remove special characters and normalize spaces
    const normalizeStr = (str) => {
      return str
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics (accents)
        .replace(/[đĐ]/g, (match) => (match === 'đ' ? 'd' : 'D')) // Replace Vietnamese characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/[^\w-]/g, '') // Remove remaining special characters
        .toLowerCase();
    };

    const gradeId = normalizeStr(grade).replace(/lop-/g, 'grade-');
    const subjectId = normalizeStr(subject).replace(/mon-/g, 'subject-');
    const lessonId = normalizeStr(lesson).replace(/bai-/g, 'lesson-');

    return `${gradeId}_${subjectId}_${lessonId}`;
  };

  /**
   * Cập nhật edComponentParams
   * @param {string} id - Lesson identifier
   * @param {Array} edComponentParams - New component parameters
   * @returns {Promise} Promise that resolves when the update completes
   */
  const updateEdComponentParams = async (id, edComponentParams) => {
    return await saveEdComponentParams(id, edComponentParams);
  };

  /**
   * Cập nhật edTechRenderTreeData
   * @param {string} id - Lesson identifier
   * @param {Object} edTechRenderTreeData - New render tree data
   * @returns {Promise} Promise that resolves when the update completes
   */
  const updateEdTechRenderTreeData = async (id, edTechRenderTreeData) => {
    return await saveEdTechRenderTreeData(id, edTechRenderTreeData);
  };

  /**
   * Cập nhật cả cấu hình (cả 2 phần)
   * @param {string} id - Lesson identifier
   * @param {Object} config - Complete configuration object
   * @returns {Promise} Promise that resolves when the update completes
   */
  const saveConfig = (id, config) => {
    if (!config) {
      config = { edComponentParams: [], edTechRenderTreeData: {} };
    }

    return Promise.all([
      saveEdComponentParams(id, config.edComponentParams || []),
      saveEdTechRenderTreeData(id, config.edTechRenderTreeData || {}),
    ]).then(() => id);
  };

  /**
   * Cập nhật đồng thời cả edComponentParams và edTechRenderTreeData
   * @param {string} id - Lesson identifier
   * @param {Array} edComponentParams - New component parameters
   * @param {Object} edTechRenderTreeData - New render tree data
   * @returns {Promise} Promise that resolves when both updates complete
   */
  const updateFullConfig = (id, edComponentParams, edTechRenderTreeData) => {
    return Promise.all([
      saveEdComponentParams(id, edComponentParams),
      saveEdTechRenderTreeData(id, edTechRenderTreeData),
    ]).then(() => id);
  };

  return {
    init,
    saveConfig,
    getConfig,
    getAllConfigs,
    deleteConfig,
    clearAllConfigs,
    createId,
    updateEdComponentParams,
    updateEdTechRenderTreeData,
    updateFullConfig,
    // Hàm mới bổ sung
    saveEdComponentParams,
    saveEdTechRenderTreeData,
    getEdComponentParams,
    getEdTechRenderTreeData,
    getAllLessonIds,
  };
})();
// Module sẽ được gán cho biến toàn cục LessonConfigDB
/**
 * Initialize the database and perform sample operations
 */
async function initAndUseDatabase() {
  try {
    // Initialize the database
    await LessonConfigDB.init();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error:', error);
  }
}

initAndUseDatabase();

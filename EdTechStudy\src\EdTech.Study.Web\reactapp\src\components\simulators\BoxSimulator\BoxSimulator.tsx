import { memo, useState, useEffect, ReactNode } from 'react';
import { <PERSON>, Slider, Button, ColorPicker, Space, Divider } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import BoxSimulatorComponent, { ShapeType } from './BoxSimulatorComponent';
import { EngineContainer } from '../../common/engines';
import { ITextProps } from '../../core/title/CoreTitle';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { useDispatch, useSelector } from 'react-redux';
import { updateTitleAndSync } from '../../../utils/titleSyncUtils';
import { EdTechRootState } from '../../../store/store';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

export interface BoxSimulatorProps {
  leftShapeType: ShapeType;
  rightShapeType: ShapeType;
  leftShapeColor: string;
  rightShapeColor: string;
  rotationSpeed: number;
  isAnimating: boolean;
  title?: string;
}

export const boxSimulatorSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    dimensions: z
      .object({
        width: z.number(),
        height: z.number(),
        depth: z.number(),
      })
      .optional(),
    material: z
      .object({
        color: z.string().optional(),
        texture: z.string().optional(),
      })
      .optional(),
    showMeasurements: z.boolean().optional(),
  },
});

function BoxSimulator(props: IEdTechRenderProps<BoxSimulatorProps>) {
  const {
    params = {
      isAnimating: true,
      leftShapeType: 'box',
      rightShapeType: 'box',
      leftShapeColor: '#ff7700',
      rightShapeColor: '#2288dd',
      rotationSpeed: 1,
      title: 'Vật chuyển động',
    },
  } = props;

  // State cho modal cấu hình
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);

  // State cho cấu hình hình dạng đã áp dụng
  const [leftShapeType, setLeftShapeType] = useState<ShapeType>('box');
  const [rightShapeType, setRightShapeType] = useState<ShapeType>('box');
  const [leftShapeColor, setLeftShapeColor] = useState<string>('#ff7700');
  const [rightShapeColor, setRightShapeColor] = useState<string>('#2288dd');
  const [rotationSpeed, setRotationSpeed] = useState<number>(1);

  // State tạm thời cho cấu hình đang chỉnh sửa trong modal
  const [tempLeftShapeType, setTempLeftShapeType] = useState<ShapeType>('box');
  const [tempRightShapeType, setTempRightShapeType] =
    useState<ShapeType>('box');
  const [tempLeftShapeColor, setTempLeftShapeColor] =
    useState<string>('#ff7700');
  const [tempRightShapeColor, setTempRightShapeColor] =
    useState<string>('#2288dd');
  const [tempRotationSpeed, setTempRotationSpeed] = useState<number>(1);

  // State cho điều khiển chuyển động
  const [isAnimating, setIsAnimating] = useState<boolean>(true);

  // Hàm xử lý xác nhận cấu hình
  const handleConfigSubmit = () => {
    // Áp dụng các cấu hình tạm thời vào state chính
    setLeftShapeType(tempLeftShapeType);
    setRightShapeType(tempRightShapeType);
    setLeftShapeColor(tempLeftShapeColor);
    setRightShapeColor(tempRightShapeColor);
    setRotationSpeed(tempRotationSpeed);
    setIsConfigModalOpen(false);

    // Cập nhật lại các giá trị trong params
    props.addOrUpdateParamComponent({
      leftShapeType: tempLeftShapeType,
      rightShapeType: tempRightShapeType,
      leftShapeColor: tempLeftShapeColor,
      rightShapeColor: tempRightShapeColor,
      rotationSpeed: tempRotationSpeed,
      isAnimating: isAnimating,
      title: params.title,
    });
  };

  // Bắt đầu/tạm dừng chuyển động
  const toggleAnimation = () => {
    const newAnimatingState = !isAnimating;
    setIsAnimating(newAnimatingState);

    // Cập nhật trạng thái animation trong params
    props.addOrUpdateParamComponent({
      ...params,
      isAnimating: newAnimatingState,
    });
  };

  // Danh sách hình dạng hỗ trợ
  const shapeOptions = [
    { value: 'box', label: 'Hình hộp' },
    { value: 'semiSphere', label: 'Hình bán nguyệt' },
    { value: 'cone', label: 'Hình nón' },
    { value: 'torus', label: 'Hình xuyến' },
  ];

  useEffect(() => {
    // Cập nhật các giá trị từ params vào state
    if (params) {
      setLeftShapeType(params.leftShapeType || 'box');
      setRightShapeType(params.rightShapeType || 'box');
      setLeftShapeColor(params.leftShapeColor || '#ff7700');
      setRightShapeColor(params.rightShapeColor || '#2288dd');
      setRotationSpeed(params.rotationSpeed || 1);
      setIsAnimating(
        params.isAnimating !== undefined ? params.isAnimating : true
      );
    }
    // Cập nhật các giá trị tạm thời khi params thay đổi
    setTempLeftShapeType(params.leftShapeType || 'box');
    setTempRightShapeType(params.rightShapeType || 'box');
    setTempLeftShapeColor(params.leftShapeColor || '#ff7700');
    setTempRightShapeColor(params.rightShapeColor || '#2288dd');
    setTempRotationSpeed(params.rotationSpeed || 1);
  }, [
    params?.leftShapeType,
    params?.rightShapeType,
    params?.leftShapeColor,
    params?.rightShapeColor,
    params?.rotationSpeed,
    params?.isAnimating,
  ]);

  // Title props for EngineContainer
  const titleProps: ITextProps = {
    text: params.title || 'Vật chuyển động',
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Instructions content using structured approach
  const instructionsContent = {
    objective:
      'Mô phỏng hình khối 3D cho phép bạn quan sát các hình dạng khác nhau trong không gian ba chiều.',
    steps: [
      'Điều chỉnh loại hình dạng cho các đối tượng',
      'Thay đổi màu sắc của từng đối tượng riêng biệt',
      'Điều chỉnh tốc độ xoay để quan sát nhanh hoặc chậm hơn',
      'Sử dụng chuột để xoay, phóng to và di chuyển góc nhìn',
    ],
    notes:
      "Bạn có thể tạm dừng hoặc tiếp tục chuyển động bằng cách nhấn nút 'Tạm dừng' hoặc 'Bắt đầu'.",
    isFullscreen: isAnimating,
  };

  // Box control panel
  const controlPanel: ReactNode = (
    <div className="tailwind-flex tailwind-justify-center tailwind-mb-4">
      <Button
        type="primary"
        icon={isAnimating ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
        onClick={toggleAnimation}
        size="large"
      >
        {isAnimating ? 'Tạm dừng' : 'Bắt đầu'}
      </Button>
    </div>
  );

  // Main component
  const mainComponent: ReactNode = (
    <div>
      {controlPanel}
      <BoxSimulatorComponent
        leftShapeType={leftShapeType}
        rightShapeType={rightShapeType}
        leftShapeColor={leftShapeColor}
        rightShapeColor={rightShapeColor}
        rotationSpeed={rotationSpeed}
        isAnimating={isAnimating}
      />
    </div>
  );

  // Config modal
  const configModal: ReactNode = (
    <ModalAntdCustom
      title="Cấu hình mô phỏng"
      open={isConfigModalOpen}
      onOk={handleConfigSubmit}
      onCancel={() => setIsConfigModalOpen(false)}
      width={600}
    >
      <div className="tailwind-space-y-6">
        <div>
          <h3 className="tailwind-text-lg tailwind-font-medium tailwind-mb-3">
            Tốc độ chuyển động
          </h3>
          <Slider
            min={0.1}
            max={5}
            step={0.1}
            value={tempRotationSpeed}
            onChange={setTempRotationSpeed}
            tooltip={{ formatter: (value) => `${value}x` }}
          />
        </div>

        <Divider />

        <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-6">
          <div>
            <h3 className="tailwind-text-lg tailwind-font-medium tailwind-mb-3">
              Vật bên trái
            </h3>
            <Space direction="vertical" className="tailwind-w-full">
              <div>
                <p className="tailwind-mb-1">Loại hình dạng:</p>
                <Select
                  className="tailwind-w-full"
                  value={tempLeftShapeType}
                  onChange={setTempLeftShapeType}
                  options={shapeOptions}
                />
              </div>
              <div>
                <p className="tailwind-mb-1">Màu sắc:</p>
                <ColorPicker
                  value={tempLeftShapeColor}
                  onChange={(color) =>
                    setTempLeftShapeColor(color.toHexString())
                  }
                  showText
                />
              </div>
            </Space>
          </div>

          <div>
            <h3 className="tailwind-text-lg tailwind-font-medium tailwind-mb-3">
              Vật bên phải
            </h3>
            <Space direction="vertical" className="tailwind-w-full">
              <div>
                <p className="tailwind-mb-1">Loại hình dạng:</p>
                <Select
                  className="tailwind-w-full"
                  value={tempRightShapeType}
                  onChange={setTempRightShapeType}
                  options={shapeOptions}
                />
              </div>
              <div>
                <p className="tailwind-mb-1">Màu sắc:</p>
                <ColorPicker
                  value={tempRightShapeColor}
                  onChange={(color) =>
                    setTempRightShapeColor(color.toHexString())
                  }
                  showText
                />
              </div>
            </Space>
          </div>
        </div>
      </div>
    </ModalAntdCustom>
  );

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Custom title update handler for BoxSimulator
  const handleBoxTitleUpdate = (updates: Partial<ITextProps>) => {
    // Use the utility function to update title and sync with menu
    updateTitleAndSync(
      props.id,
      updates,
      params,
      renderTreeData,
      dispatch,
      props.addOrUpdateParamComponent
    );
  };

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      allowConfiguration={true}
      mainComponent={mainComponent}
      configModal={configModal}
      instructionsContent={instructionsContent}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      onTitleUpdate={handleBoxTitleUpdate}
      id={props.id}
    />
  );
}

export default memo(withEdComponentParams(BoxSimulator));

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Button,
  message,
  Progress,
  Space,
  Input,
  Switch,
  Row,
  Col,
  Tooltip,
  Divider,
} from 'antd';
import {
  CheckCircleOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  LeftOutlined,
  FieldTimeOutlined,
  FormOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  Question,
} from '../../../interfaces/quizs/questionBase';
import ShortAnswerComponent from './ShortAnswerComponent';
import { FireworksComponent } from '../quiz';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { Guid } from 'guid-typescript';
import {
  ShortAnswerAnswer,
  ShortAnswerQuestion,
} from '../../../interfaces/quizs/shortAnswer.interface';
import { createShortAnswerQuestionTemplate } from './shortAnswerTemplates';

const { Title, Text } = Typography;

export interface IShortAnswerWrapperProps {
  id: string;
  question?: ShortAnswerQuestion;
  showFireworks?: boolean;
  showTitle?: boolean;
  congratsMessage?: string;
  timeLimit?: number;
  showTimer?: boolean;
  autoSubmitOnTimeEnd?: boolean;
  countDirection?: 'up' | 'down';
  timerPosition?: 'top' | 'bottom';
  startButtonText?: string;
  startScreenTitle?: string;
  startScreenDescription?: string;
  showTimeCounter?: boolean;
  timeSpent?: number;
}

/**
 * A wrapper component for short answer questions that provides timer functionality,
 * configuration mode, and congratulatory messages.
 */
const ShortAnswerWrapper: React.FC<
  IEdTechRenderProps<IShortAnswerWrapperProps>
> = (props) => {
  const { params = {} as IShortAnswerWrapperProps, addOrUpdateParamComponent } =
    props;

  // ============================
  // Extract props with defaults
  // ============================
  const {
    id = `shortanswer-${Date.now()}`,
    question: initialQuestion,
    showFireworks = true,
    showTitle = true,
    congratsMessage = 'Chúc mừng bạn đã hoàn thành câu hỏi!',
    timeLimit: initialTimeLimit = 0,
    showTimer = false,
    autoSubmitOnTimeEnd = true,
    countDirection = 'down',
    timerPosition = 'top',
    startButtonText = 'Bắt đầu',
    startScreenTitle = 'Bạn đã sẵn sàng?',
    startScreenDescription = 'Nhấn nút bắt đầu để làm bài.',
    showTimeCounter: initialShowTimeCounter = true,
    timeSpent: initialTimeSpent = 0,
  } = params;

  // ============================
  // State Management
  // ============================

  // Question state
  const [question, setQuestion] = useState<ShortAnswerQuestion>(
    initialQuestion || createShortAnswerQuestionTemplate('Câu hỏi trả lời ngắn')
  );
  const [configMode, setConfigMode] = useState<boolean>(false);
  const [completed, setCompleted] = useState(false);
  const [showCongrats, setShowCongrats] = useState(false);
  const [showFireworksEffect, setShowFireworksEffect] = useState(false);
  const [started, setStarted] = useState(false);
  const firstRender = useRef<boolean>(true);

  // Timer state
  const [timeLimit, setTimeLimit] = useState<number>(initialTimeLimit);
  const [timeRemaining, setTimeRemaining] = useState(initialTimeLimit);
  const [timerActive, setTimerActive] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Time counter state
  const [timeSpent, setTimeSpent] = useState<number>(initialTimeSpent);
  const [showTimeCounter, setShowTimeCounter] = useState<boolean>(
    initialShowTimeCounter
  );
  const [timeCounterActive, setTimeCounterActive] = useState<boolean>(false);
  const timeCounterRef = useRef<NodeJS.Timeout | null>(null);

  // ============================
  // Helper Functions
  // ============================

  /**
   * Updates parameters in the parent component
   */
  const handleUpdateParams = (values: Partial<IShortAnswerWrapperProps>) => {
    addOrUpdateParamComponent({
      id,
      question,
      showFireworks,
      showTimer,
      showTitle,
      congratsMessage,
      timeLimit,
      autoSubmitOnTimeEnd,
      countDirection,
      startButtonText,
      startScreenDescription,
      startScreenTitle,
      timerPosition,
      showTimeCounter,
      timeSpent,
      ...values,
    });
  };

  /**
   * Formats seconds into MM:SS display format
   */
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  /**
   * Calculates percentage for timer progress bar
   */
  const calculateTimerPercentage = (): number => {
    if (timeLimit <= 0) return 0;
    return countDirection === 'down'
      ? (timeRemaining / timeLimit) * 100
      : (timeRemaining / timeLimit) * 100;
  };

  /**
   * Determines the status color for timer
   */
  const getTimerStatus = (): 'success' | 'normal' | 'exception' | 'active' => {
    if (countDirection === 'down') {
      if (timeRemaining <= timeLimit * 0.25) return 'exception';
      if (timeRemaining <= timeLimit * 0.5) return 'normal';
      return 'success';
    } else {
      if (timeRemaining >= timeLimit * 0.75) return 'exception';
      if (timeRemaining >= timeLimit * 0.5) return 'normal';
      return 'success';
    }
  };

  // ============================
  // Core Functionality
  // ============================

  /**
   * Starts the quiz and associated timers
   */
  const handleStart = () => {
    setStarted(true);
    setConfigMode(false);

    // Start timers after state update
    setTimeout(() => {
      if (timeLimit > 0 && showTimer) {
        startTimer();
      }
      startTimeCounter();
    }, 50);
  };

  /**
   * Handles question completion
   */
  const handleComplete = (questionId: string, answer?: ShortAnswerAnswer) => {
    // Stop all timers
    stopTimer();
    stopTimeCounter();
    setCompleted(true);

    // Update question with results
    const updatedQuestion = {
      ...question,
      isCompleted: true,
      status: 'incorrect',
      userAnswer: answer,
    };

    setQuestion(updatedQuestion);
    handleUpdateParams({
      question: updatedQuestion,
      timeSpent,
    });

    // Show congratulations if correct
    // if (correct) {
    //   setShowCongrats(true);
    //   if (showFireworks) {
    //     setShowFireworksEffect(true);
    //     setTimeout(() => setShowFireworksEffect(false), 2000);
    //   }
    // }
  };

  /**
   * Resets the quiz to initial state
   */
  const handleReset = () => {
    setCompleted(false);
    setShowCongrats(false);
    setStarted(false);

    // Reset question state
    const resetQuestion = {
      ...question,
      isCompleted: false,
      status: undefined,
      userAnswer: undefined,
    };

    setQuestion(resetQuestion);
    resetTimer();
    resetTimeCounter();

    handleUpdateParams({
      question: resetQuestion,
      timeSpent: 0,
    });
  };

  /**
   * Auto-submits when time runs out
   */
  const handleTimeUp = () => {
    handleComplete(question.id, {
      id: Guid.create().toString(),
      text: '',
      isCorrect: false,
    });
    message.warning('Hết thời gian làm bài!');
  };

  /**
   * Handler for PracticeEngine to change the question
   */
  const handleChangeQuestion = (update: Question) => {
    setConfigMode(false);
    setStarted(false);
    setQuestion(update as ShortAnswerQuestion);
    handleUpdateParams({
      question: update as ShortAnswerQuestion,
    });
  };

  /**
   * Handler for PracticeEngine to delete the question
   */
  const handleDeleteQuestion = (_id: string) => {
    message.info('Xóa câu hỏi này sẽ được xử lý bởi component cha');
  };

  // ============================
  // Timer Management
  // ============================

  /**
   * Starts the constraint timer
   */
  const startTimer = () => {
    if (timeLimit <= 0 || timerActive) return;

    setTimerActive(true);
    setTimeRemaining(countDirection === 'down' ? timeLimit : 0);

    // Clear any existing interval
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Set up interval
    timerRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        const newValue = countDirection === 'down' ? prev - 1 : prev + 1;

        // Handle completion for countdown
        if (countDirection === 'down' && newValue <= 0) {
          stopTimer();
          if (autoSubmitOnTimeEnd && !completed) {
            handleTimeUp();
          }
          return 0;
        }

        // Handle completion for countup
        if (countDirection === 'up' && timeLimit > 0 && newValue >= timeLimit) {
          stopTimer();
          if (autoSubmitOnTimeEnd && !completed) {
            handleTimeUp();
          }
          return timeLimit;
        }

        return newValue;
      });
    }, 1000);
  };

  /**
   * Stops the constraint timer
   */
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setTimerActive(false);
    }
  };

  /**
   * Resets the constraint timer
   */
  const resetTimer = () => {
    stopTimer();
    setTimeRemaining(countDirection === 'down' ? timeLimit : 0);
  };

  /**
   * Starts the time counter
   */
  const startTimeCounter = () => {
    if (timeCounterActive) return;

    setTimeCounterActive(true);

    // Clear any existing interval
    if (timeCounterRef.current) {
      clearInterval(timeCounterRef.current);
      timeCounterRef.current = null;
    }

    // Set up interval
    timeCounterRef.current = setInterval(() => {
      setTimeSpent((prev) => prev + 1);
    }, 1000);
  };

  /**
   * Stops the time counter
   */
  const stopTimeCounter = () => {
    if (timeCounterRef.current) {
      clearInterval(timeCounterRef.current);
      timeCounterRef.current = null;
      setTimeCounterActive(false);
    }
  };

  /**
   * Resets the time counter
   */
  const resetTimeCounter = () => {
    stopTimeCounter();
    setTimeSpent(0);
  };

  // ============================
  // UI Rendering
  // ============================

  /**
   * Renders the constraint timer
   */
  const renderTimer = () => {
    if (!showTimer || timeLimit <= 0) return null;

    return (
      <div className="constraint-timer" style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ClockCircleOutlined style={{ marginRight: '8px' }} />
          <span style={{ marginRight: '16px' }}>
            Thời gian: {formatTime(timeRemaining)}
          </span>
          <Progress
            percent={calculateTimerPercentage()}
            status={getTimerStatus()}
            showInfo={false}
            style={{ flex: 1 }}
          />
        </div>
      </div>
    );
  };

  /**
   * Renders the time spent counter
   */
  const renderTimeCounter = () => {
    if (!showTimeCounter) return null;

    return (
      <div className="time-counter" style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FieldTimeOutlined style={{ marginRight: '8px' }} />
          <span>Thời gian làm bài: {formatTime(timeSpent)}</span>
        </div>
      </div>
    );
  };

  /**
   * Renders the start screen
   */
  const renderStartScreen = () => {
    return (
      <Card
        className="start-card"
        style={{
          textAlign: 'center',
          padding: '24px',
        }}
      >
        <div className="flex justify-end">
          <Button
            type="default"
            onClick={() => {
              setConfigMode(true);
              setStarted(true);
            }}
          >
            <SettingOutlined />
          </Button>
        </div>

        <FormOutlined
          style={{ fontSize: '64px', color: '#08979c', margin: '16px 0' }}
        />

        <Title level={3}>{startScreenTitle}</Title>
        <Text
          style={{ fontSize: '16px', display: 'block', marginBottom: '24px' }}
        >
          {startScreenDescription}
        </Text>

        {timeLimit > 0 && showTimer && (
          <Text
            style={{ display: 'block', marginBottom: '16px', color: '#1677ff' }}
          >
            <ClockCircleOutlined style={{ marginRight: '8px' }} />
            Thời gian làm bài: {formatTime(timeLimit)}
          </Text>
        )}

        <Button
          type="primary"
          size="large"
          onClick={handleStart}
          className="flex-1 bg-blue-500 hover:bg-blue-600"
          icon={<FormOutlined />}
        >
          {startButtonText}
        </Button>
      </Card>
    );
  };

  /**
   * Renders the congratulations screen
   */
  const renderCongratsScreen = () => {
    return (
      <Card
        className="congrats-card"
        style={{
          textAlign: 'center',
          padding: '24px',
          backgroundColor: '#f6ffed',
          borderColor: '#b7eb8f',
        }}
      >
        <TrophyOutlined
          style={{
            fontSize: '48px',
            color: '#52c41a',
            marginBottom: '16px',
          }}
        />
        <Title level={3} style={{ color: '#52c41a' }}>
          <CheckCircleOutlined /> {congratsMessage}
        </Title>
        <Text
          style={{
            fontSize: '16px',
            display: 'block',
            marginBottom: '24px',
          }}
        >
          Bạn đã trả lời chính xác câu hỏi. Tiếp tục cố gắng nhé!
        </Text>

        {/* Show time spent info */}
        {showTimeCounter && (
          <div style={{ marginBottom: '24px' }}>
            <Divider>Thông tin làm bài</Divider>
            <Text>
              <FieldTimeOutlined style={{ marginRight: '8px' }} />
              Thời gian hoàn thành: {formatTime(timeSpent)}
            </Text>
          </div>
        )}

        <Button
          type="primary"
          onClick={handleReset}
          className="flex-1 bg-blue-500 hover:bg-blue-600"
        >
          Làm lại
        </Button>
      </Card>
    );
  };

  /**
   * Renders the configuration controls
   */
  const renderConfigControls = () => {
    if (!configMode) return null;

    return (
      <Space
        direction="vertical"
        style={{ width: '100%', marginBottom: '16px' }}
      >
        <Card size="small" title="Cấu hình câu hỏi">
          <Row gutter={16} align="middle">
            <Col span={8}>
              <div style={{ marginBottom: '8px' }}>
                <Text strong>Thời gian giới hạn:</Text>
              </div>
              <Input
                type="number"
                value={timeLimit}
                onChange={(e) => {
                  const value = Number.parseInt(e.target.value);
                  setTimeLimit(value || 0);
                  setTimeRemaining(value || 0);
                  handleUpdateParams({
                    timeLimit: value || 0,
                  });
                }}
                addonAfter="giây"
                placeholder="0 = không giới hạn"
              />
            </Col>

            <Col span={8}>
              <div style={{ marginBottom: '8px' }}>
                <Text strong>Hiển thị thời gian:</Text>
              </div>
              <Switch
                checked={showTimeCounter}
                onChange={(checked) => {
                  setShowTimeCounter(checked);
                  handleUpdateParams({
                    showTimeCounter: checked,
                  });
                }}
              />
              <Text style={{ marginLeft: '8px' }}>
                {showTimeCounter ? 'Hiện' : 'Ẩn'}
              </Text>
            </Col>

            <Col span={8}>
              <div style={{ marginBottom: '8px' }}>
                <Text strong>Hiệu ứng chúc mừng:</Text>
              </div>
              <Switch
                checked={showFireworks}
                onChange={(checked) => {
                  handleUpdateParams({
                    showFireworks: checked,
                  });
                }}
              />
              <Text style={{ marginLeft: '8px' }}>
                {showFireworks ? 'Hiện' : 'Ẩn'}
              </Text>
            </Col>
          </Row>
        </Card>
      </Space>
    );
  };

  // ============================
  // Effect Hooks
  // ============================

  // Initialize from props
  useEffect(() => {
    if (initialQuestion) {
      setQuestion(initialQuestion);
      setCompleted(!!initialQuestion.isCompleted);
      setShowCongrats(initialQuestion.status === 'correct');

      if (initialQuestion.isCompleted) {
        setStarted(true);
      }
    }
  }, [initialQuestion]);

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      stopTimer();
      stopTimeCounter();
    };
  }, []);

  // Handle timer state based on quiz state
  useEffect(() => {
    if (completed) {
      stopTimer();
      stopTimeCounter();
    } else if (started && !configMode) {
      // Restart timers if already started but not in config mode
      if (timeLimit > 0 && showTimer && !timerActive) {
        startTimer();
      }

      if (!timeCounterActive) {
        startTimeCounter();
      }
    }
  }, [started, completed, configMode, timeLimit, showTimer]);

  // Set config mode on first render if no question is provided
  useEffect(() => {
    if (firstRender.current && !props.params?.question) {
      setConfigMode(true);
      setStarted(true);
      firstRender.current = false;
    }
  }, [props.params?.question]);

  // ============================
  // Main Render
  // ============================
  return (
    <PracticeEngineContext.Provider
      value={{
        handleChangePosition: () => {},
        handleChangeQuestion: handleChangeQuestion,
        handleDeleteQuestion: handleDeleteQuestion,
      }}
    >
      <div
        id={id}
        className="shortanswer-wrapper bg-white rounded-lg shadow-md p-5 max-w-6xl mx-auto"
        style={{ position: 'relative' }}
      >
        {showTitle && (
          <Title level={4} style={{ marginBottom: '16px' }}>
            {question.title}
          </Title>
        )}

        {!started ? (
          renderStartScreen()
        ) : (
          <>
            {timerPosition === 'top' && renderTimer()}
            {showTimeCounter && renderTimeCounter()}
            {renderConfigControls()}

            {showCongrats ? (
              renderCongratsScreen()
            ) : (
              <>
                <Space className="flex mb-2 justify-between">
                  {!configMode && (
                    <Button
                      type="default"
                      onClick={() => {
                        setConfigMode(true);
                      }}
                    >
                      <SettingOutlined />
                    </Button>
                  )}
                  <Button type="default" onClick={handleReset}>
                    <LeftOutlined />
                  </Button>
                </Space>

                <ShortAnswerComponent
                  id={Guid.create().toString()}
                  question={question}
                  onComplete={handleComplete}
                  showFeedback={true}
                  allowManyTimes={true}
                  configMode={configMode}
                  disabled={timeRemaining === 0 && timeLimit > 0}
                />
              </>
            )}

            {timerPosition === 'bottom' && renderTimer()}
          </>
        )}

        <FireworksComponent show={showFireworksEffect} />
      </div>
    </PracticeEngineContext.Provider>
  );
};

export default withEdComponentParams(ShortAnswerWrapper);

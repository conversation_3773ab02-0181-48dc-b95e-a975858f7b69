// TrigonometricUtils.ts
import { SpecialAngle, TrigonometricConfig } from './TrigonometricConfig';

/**
 * Convert degrees to radians
 * @param degrees Angle in degrees
 * @returns Angle in radians
 */
export const degreesToRadians = (degrees: number): number => {
  return (degrees * Math.PI) / 180;
};

/**
 * Convert radians to degrees
 * @param radians Angle in radians
 * @returns Angle in degrees
 */
export const radiansToDegrees = (radians: number): number => {
  return (radians * 180) / Math.PI;
};

/**
 * Normalize angle to be between 0 and 360 degrees
 * @param angle Angle in degrees
 * @returns Normalized angle in degrees (0-360)
 */
export const normalizeAngle = (angle: number): number => {
  // Get the angle within 0-360 range
  return ((angle % 360) + 360) % 360;
};

/**
 * Round to specific decimal places and handle special cases
 * @param value Value to round
 * @param precision Number of decimal places
 * @returns Rounded value
 */
export const roundValue = (value: number, precision: number = 4): number => {
  if (Math.abs(value) < 0.0001) return 0;
  return parseFloat(value.toFixed(precision));
};

/**
 * Find nearest special angle if within threshold
 * @param angle Current angle in degrees
 * @param specialAngles Array of special angles
 * @param threshold Threshold in degrees for snapping
 * @returns Snapped angle or original angle
 */
export const findNearestSpecialAngle = (
  angle: number,
  specialAngles: SpecialAngle[],
  threshold: number
): number => {
  const normalizedAngle = normalizeAngle(angle);

  // Find the closest special angle
  for (const specialAngle of specialAngles) {
    if (Math.abs(normalizedAngle - specialAngle.angle) <= threshold) {
      return specialAngle.angle;
    }
  }

  return angle;
};

/**
 * Check if an angle is a special angle
 * @param angle Angle in degrees
 * @param specialAngles Array of special angles
 * @returns Whether the angle is special
 */
// export const isSpecialAngle = (
//   angle: number,
//   specialAngles: SpecialAngle[]
// ): boolean => {
//   return specialAngles.some((specialAngle) => specialAngle.angle === angle);
// };

/**
 * Get exact representation of trigonometric value for special angles
 * @param value Numeric value
 * @param angle Angle in degrees
 * @param specialAngles Array of special angles
 * @returns String representation of the value
 */
export const getExactRepresentation = (
  value: number,
  angle: number,
  specialAngles: SpecialAngle[]
): string => {
  // Normalize angle to 0-360 range
  const normalizedAngle = normalizeAngle(angle);

  // Find the special angle
  const specialAngle = specialAngles.find((sa) => sa.angle === normalizedAngle);

  if (specialAngle) {
    // Determine which trigonometric function this value represents
    const sinValue = Math.sin(degreesToRadians(normalizedAngle));
    const cosValue = Math.cos(degreesToRadians(normalizedAngle));
    const tanValue = Math.tan(degreesToRadians(normalizedAngle));
    let cotValue = 0;

    if (Math.abs(sinValue) > 0.0001) {
      cotValue = cosValue / sinValue;
    }

    // Match the value to determine which trig function it is
    if (Math.abs(value - sinValue) < 0.001) {
      return specialAngle.sin;
    } else if (Math.abs(value - cosValue) < 0.001) {
      return specialAngle.cos;
    } else if (
      Math.abs(value - tanValue) < 0.001 ||
      (Math.abs(cosValue) < 0.001 && value === Infinity)
    ) {
      return specialAngle.tan;
    } else if (
      Math.abs(value - cotValue) < 0.001 ||
      (Math.abs(sinValue) < 0.001 && value === Infinity)
    ) {
      return specialAngle.cot;
    }
  }

  // If not a special case or couldn't determine the trig function,
  // return the rounded numeric value
  return roundValue(value).toString();
};

/**
 * Calculate all trigonometric values for an angle
 * @param angle Angle in degrees
 * @param config Trigonometric configuration
 * @returns Object with all trigonometric values
 */
// export const calculateTrigonometricValues = (
//   angle: number,
//   config: TrigonometricConfig
// ) => {
//   const { precision, specialAngles, displayOptions } = config;
//   const angleInRadians = degreesToRadians(angle);

//   // Calculate basic trigonometric values
//   const sinValue = Math.sin(angleInRadians);
//   const cosValue = Math.cos(angleInRadians);

//   // Calculate derived values with safeguards for division by zero
//   let tanValue: number | string = sinValue / cosValue;
//   if (!isFinite(tanValue)) tanValue = 'undefined';

//   let cotValue: number | string = cosValue / sinValue;
//   if (!isFinite(cotValue)) cotValue = 'undefined';

//   // Format the values based on configuration
//   const isSpecial = isSpecialAngle(normalizeAngle(angle));

//   // If we should use exact values and this is a special angle, use the exact representation
//   if (displayOptions.showExactValues && isSpecial) {
//     return {
//       sin: getExactRepresentation(sinValue, angle, specialAngles),
//       cos: getExactRepresentation(cosValue, angle, specialAngles),
//       tan:
//         typeof tanValue === 'number'
//           ? getExactRepresentation(tanValue, angle, specialAngles)
//           : tanValue,
//       cot:
//         typeof cotValue === 'number'
//           ? getExactRepresentation(cotValue, angle, specialAngles)
//           : cotValue,
//       sinValue,
//       cosValue,
//       tanValue: typeof tanValue === 'number' ? tanValue : NaN,
//       cotValue: typeof cotValue === 'number' ? cotValue : NaN,
//     };
//   }

//   // Otherwise, use the numeric values rounded to the specified precision
//   return {
//     sin:
//       typeof sinValue === 'number'
//         ? roundValue(sinValue, precision).toString()
//         : sinValue,
//     cos:
//       typeof cosValue === 'number'
//         ? roundValue(cosValue, precision).toString()
//         : cosValue,
//     tan:
//       typeof tanValue === 'number'
//         ? roundValue(tanValue, precision).toString()
//         : tanValue,
//     cot:
//       typeof cotValue === 'number'
//         ? roundValue(cotValue, precision).toString()
//         : cotValue,
//     sinValue,
//     cosValue,
//     tanValue: typeof tanValue === 'number' ? tanValue : NaN,
//     cotValue: typeof cotValue === 'number' ? cotValue : NaN,
//   };
// };

/**
 * Get the point on the unit circle for a given angle
 * @param angle Angle in degrees
 * @param radius Circle radius
 * @returns Coordinates of the point {x, y}
 */
export const getPointOnCircle = (angle: number, radius: number) => {
  const angleInRadians = degreesToRadians(angle);
  return {
    x: radius * Math.cos(angleInRadians),
    y: radius * Math.sin(angleInRadians),
  };
};

/**
 * Check if an angle is in allowed quadrants
 * @param angle Angle in degrees
 * @param enabledQuadrants Configuration for enabled quadrants
 * @returns Whether the angle is in allowed quadrants
 */
export const isAngleInAllowedQuadrants = (
  angle: number,
  enabledQuadrants: {
    first: boolean;
    second: boolean;
    third: boolean;
    fourth: boolean;
  }
): boolean => {
  const normalizedAngle = normalizeAngle(angle);

  // Exact 360 degrees should be treated as 0 degrees (first quadrant)
  if (normalizedAngle === 360) {
    return enabledQuadrants.first;
  }

  // First quadrant: 0-90 degrees
  if (normalizedAngle >= 0 && normalizedAngle <= 90) {
    return enabledQuadrants.first;
  }

  // Second quadrant: 90-180 degrees
  if (normalizedAngle > 90 && normalizedAngle <= 180) {
    return enabledQuadrants.second;
  }

  // Third quadrant: 180-270 degrees
  if (normalizedAngle > 180 && normalizedAngle <= 270) {
    return enabledQuadrants.third;
  }

  // Fourth quadrant: 270-360 degrees
  if (normalizedAngle > 270 && normalizedAngle < 360) {
    return enabledQuadrants.fourth;
  }

  // Should never reach here if normalizeAngle works correctly
  console.error('Unexpected angle value:', normalizedAngle);
  return false;
};

/**
 * Get translation strings based on language
 * @param language Language code ('en' or 'vi')
 * @returns Object with translated strings
 */
export const getTranslations = (language: 'en' | 'vi') => {
  const translations = {
    en: {
      title: 'Trigonometric Values Visualizer',
      angle: 'Angle',
      sin: 'sin',
      cos: 'cos',
      tan: 'tan',
      cot: 'cot',
      degrees: 'degrees',
      radians: 'radians',
      showUnitCircle: 'Show unit circle',
      specialAngles: 'Special angles',
      basicTheory: 'Basic Theory',
      definition: 'Definition of Trigonometric Values',
      theoryText:
        'In mathematics, trigonometric values of an angle are defined by the coordinates of a point M on the unit circle. If α is an angle and M(x, y) is the point on the unit circle corresponding to the angle α, then:',
      sinDef: 'sin α = y (y-coordinate of point M)',
      cosDef: 'cos α = x (x-coordinate of point M)',
      tanDef: 'tan α = y/x = sin α / cos α (defined when cos α ≠ 0)',
      cotDef: 'cot α = x/y = cos α / sin α (defined when sin α ≠ 0)',
      specialAnglesTable: 'Table of Special Angle Values',
      undefined: 'undefined',
      complementaryAngles: 'Complementary Angles (α + β = 90°)',
      supplementaryAngles: 'Supplementary Angles (α + β = 180°)',
      point: 'Point',
      apply: 'Apply',
      cancel: 'Cancel',
      settings: 'Settings',
      showInstructions: 'Show instructions',
      hideInstructions: 'Hide instructions',
      advancedConfig: 'Advanced configuration',
      instructions: 'Instructions',
      instructionsText:
        'Use this simulator to explore trigonometric values of angles. You can:',
      instruction1: 'Drag the red point on the circle to change the angle',
      instruction2:
        'Use the presets to select common angles like 30°, 45°, 60°, etc.',
      instruction3: 'Enter a specific angle in the input field',
      instruction4:
        'Toggle the visibility of different elements using the settings',
      instruction5:
        'See the exact values for special angles like 30°, 45°, 60°, etc.',
      quickSettings: 'Quick Settings',
      angleValue: 'Angle value',
    },
    vi: {
      title: 'Giá trị lượng giác của một góc',
      angle: 'Góc',
      sin: 'sin',
      cos: 'cos',
      tan: 'tan',
      cot: 'cot',
      degrees: 'độ',
      radians: 'radian',
      showUnitCircle: 'Hiện đường tròn đơn vị',
      specialAngles: 'Góc đặc biệt',
      basicTheory: 'Lý thuyết',
      definition: 'Định nghĩa giá trị lượng giác của một góc',
      theoryText:
        'Trong toán học, giá trị lượng giác của một góc được xác định bởi tọa độ của một điểm M trên đường tròn đơn vị. Nếu α là góc và M(x, y) là điểm trên đường tròn đơn vị tương ứng với góc α, thì:',
      sinDef: 'sin α = y (tung độ của điểm M)',
      cosDef: 'cos α = x (hoành độ của điểm M)',
      tanDef: 'tan α = y/x = sin α / cos α (xác định khi cos α ≠ 0)',
      cotDef: 'cot α = x/y = cos α / sin α (xác định khi sin α ≠ 0)',
      specialAnglesTable: 'Bảng giá trị lượng giác đặc biệt',
      undefined: 'không xác định',
      complementaryAngles: 'Hai góc phụ nhau (α + β = 90°)',
      supplementaryAngles: 'Hai góc bù nhau (α + β = 180°)',
      point: 'Điểm',
      apply: 'Áp dụng',
      cancel: 'Hủy',
      settings: 'Cài đặt',
      showInstructions: 'Hiện hướng dẫn',
      hideInstructions: 'Ẩn hướng dẫn',
      advancedConfig: 'Cấu hình nâng cao',
      instructions: 'Hướng dẫn sử dụng',
      instructionsText:
        'Sử dụng mô phỏng này để khám phá giá trị lượng giác của các góc. Bạn có thể:',
      instruction1: 'Kéo điểm đỏ trên đường tròn để thay đổi góc',
      instruction2:
        'Sử dụng các nút đặt sẵn để chọn các góc phổ biến như 30°, 45°, 60°, v.v.',
      instruction3: 'Nhập một góc cụ thể vào ô nhập liệu',
      instruction4:
        'Bật/tắt hiển thị các thành phần khác nhau bằng cách sử dụng cài đặt',
      instruction5:
        'Xem giá trị chính xác cho các góc đặc biệt như 30°, 45°, 60°, v.v.',
      quickSettings: 'Cài đặt nhanh',
      angleValue: 'Giá trị góc',
    },
  };

  return translations[language];
};
export const specialAngleFractions: Record<
  number,
  { sin: string; cos: string; tan: string; cot: string }
> = {
  0: { sin: '0', cos: '1', tan: '0', cot: 'Không xác định' },
  30: { sin: '1/2', cos: '√3/2', tan: '√3/3', cot: '√3' },
  45: { sin: '√2/2', cos: '√2/2', tan: '1', cot: '1' },
  60: { sin: '√3/2', cos: '1/2', tan: '√3', cot: '√3/3' },
  90: { sin: '1', cos: '0', tan: 'Không xác định', cot: '0' },
  120: { sin: '√3/2', cos: '-1/2', tan: '-√3', cot: '-√3/3' },
  135: { sin: '√2/2', cos: '-√2/2', tan: '-1', cot: '-1' },
  150: { sin: '1/2', cos: '-√3/2', tan: '-√3/3', cot: '-√3' },
  180: { sin: '0', cos: '-1', tan: '0', cot: 'Không xác định' },
  210: { sin: '-1/2', cos: '-√3/2', tan: '√3/3', cot: '√3' },
  225: { sin: '-√2/2', cos: '-√2/2', tan: '1', cot: '1' },
  240: { sin: '-√3/2', cos: '-1/2', tan: '√3', cot: '√3/3' },
  270: { sin: '-1', cos: '0', tan: 'Không xác định', cot: '0' },
  300: { sin: '-√3/2', cos: '1/2', tan: '-√3', cot: '-√3/3' },
  315: { sin: '-√2/2', cos: '√2/2', tan: '-1', cot: '-1' },
  330: { sin: '-1/2', cos: '√3/2', tan: '-√3/3', cot: '-√3' },
  360: { sin: '0', cos: '1', tan: '0', cot: 'Không xác định' },
};
// Hàm kiểm tra góc có phải là góc đặc biệt không
export const isSpecialAngle = (angle: number): boolean => {
  return angle in specialAngleFractions;
};

// Cập nhật calculateTrigonometricValues để trả về phân số cho góc đặc biệt
export const calculateTrigonometricValues = (
  angle: number,
  config: TrigonometricConfig
) => {
  const normalizedAngle = normalizeAngle(angle);
  const angleInRadians = degreesToRadians(normalizedAngle);
  const sinValue = Math.sin(angleInRadians);
  const cosValue = Math.cos(angleInRadians);
  let tanValue: number | string = sinValue / cosValue;
  let cotValue: number | string = cosValue / sinValue;

  if (!isFinite(tanValue as number)) tanValue = 'Không xác định';
  if (!isFinite(cotValue as number)) cotValue = 'Không xác định';
  // Nếu là góc đặc biệt, trả về giá trị phân số
  if (isSpecialAngle(normalizedAngle)) {
    const fractions = specialAngleFractions[normalizedAngle];
    return {
      sin: fractions.sin,
      cos: fractions.cos,
      tan: fractions.tan,
      cot: fractions.cot,
      sinValue,
      cosValue,
      tanValue: typeof tanValue === 'number' ? tanValue : NaN,
      cotValue: typeof cotValue === 'number' ? cotValue : NaN,
    };
  }

  // Nếu không, trả về số thập phân
  const { precision } = config;
  return {
    sin: roundValue(sinValue, precision).toString(),
    cos: roundValue(cosValue, precision).toString(),
    tan:
      typeof tanValue === 'number'
        ? roundValue(tanValue, precision).toString()
        : tanValue,
    cot:
      typeof cotValue === 'number'
        ? roundValue(cotValue, precision).toString()
        : cotValue,
    sinValue,
    cosValue,
    tanValue: typeof tanValue === 'number' ? tanValue : NaN,
    cotValue: typeof cotValue === 'number' ? cotValue : NaN,
  };
};

import React, { useContext, useEffect, useState } from 'react';
import {
  Card,
  Checkbox,
  Button,
  Space,
  message,
  Typography,
  Input,
  Form,
  Tooltip,
  PopconfirmProps,
} from 'antd';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import {
  CheckOutlined,
  CloseOutlined,
  PlusOutlined,
  DeleteFilled,
  QuestionOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  Question,
} from '../../../interfaces/quizs/questionBase';
import '../quiz/QuizAnimations.css';
import {
  MultiSelectAnswer,
  MultiSelectQuestion,
  MultiSelectQuizComponentProps,
} from '../../../interfaces/quizs/multiSelectQuiz.interface';
import './MultiSelectStyles.css';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import { quizLocalization } from '../localization';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import DOMPurify from 'dompurify';
import { DeleteIcon } from '../../icons/IconIndex';
const { Text } = Typography;
const { TextArea } = Input;

// Define modules for the rich text editor
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image'],
    ['clean'],
  ],
};

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
];

const MultiSelectQuizComponent: React.FC<MultiSelectQuizComponentProps> = ({
  question,
  onComplete,
  showFeedback = true,
  configMode = false,
  disabled = false,
  hideSaveButton = false,
  hideDeleteButton = false,
}) => {
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const {
    handleChangeQuestion,
    handleDeleteQuestion: externalHandleDeleteQuestion,
  } = useContext(PracticeEngineContext);

  // Config mode states
  const [editedQuestion, setEditedQuestion] = useState<MultiSelectQuestion>({
    ...question,
    answers: question.answers ?? [],
  });

  const [form] = Form.useForm();
  const { handleUpdateQuestion } = useUpdateQuestion();

  const handleAnswerSelect = (answerId: string) => {
    if (!isSubmitted && !disabled) {
      const newSelectedAnswers = selectedAnswers.includes(answerId)
        ? selectedAnswers.filter((id) => id !== answerId)
        : [...selectedAnswers, answerId];
      setSelectedAnswers(newSelectedAnswers);

      let answers = question.answers.filter((que) =>
        newSelectedAnswers.includes(que.id)
      );

      setEditedQuestion((prev) => ({
        ...prev,
        userSelect: answers,
      }));
      // Store answers without auto-submitting
      if (onComplete) {
        onComplete(question.id, answers);
      }
    }
  };

  // Reset the question
  const handleReset = () => {
    if (question.id) {
      setSelectedAnswers([]);
      setIsSubmitted(false);
      setIsCorrect(false);
      setEditedQuestion({ ...question });
    }
    if (!disabled) {
      setSelectedAnswers(question.userSelect?.map((p) => p.id) || []);
      setIsSubmitted(false);
      setIsCorrect(false);
    }
  };

  // Reset state when user changes question
  useEffect(() => {
    handleReset();
  }, [question.id]);

  // Config mode functions
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuestion = { ...editedQuestion, title: e.target.value };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const handleQuestionTextChange = (content: string) => {
    const newQuestion = {
      ...editedQuestion,
      question: content,
      description: content,
    };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const handleExplanationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newQuestion = { ...editedQuestion, explanation: e.target.value };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const handleOptionTextChange = (id: string, newText: string) => {
    const newAnswers = editedQuestion.answers?.map((answer: any) =>
      answer.id === id ? { ...answer, text: newText } : answer
    );
    const newQuestion = { ...editedQuestion, answers: newAnswers };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const handleCorrectAnswerChange = (id: string) => {
    const newAnswers = editedQuestion.answers?.map((answer: any) => {
      if (answer.id === id) {
        return { ...answer, isCorrect: !answer.isCorrect };
      }
      return answer;
    });
    const newQuestion = { ...editedQuestion, answers: newAnswers };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const addNewOption = () => {
    const newId = `${editedQuestion.id}-${String.fromCharCode(
      65 + editedQuestion.answers.length
    )}`;
    const newAnswers = [
      ...editedQuestion.answers,
      {
        id: newId,
        text: 'Tùy chọn mới',
        isCorrect: false,
        order: editedQuestion.answers.length,
      },
    ];
    const newQuestion = { ...editedQuestion, answers: newAnswers };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const removeOption = (id: string) => {
    if (editedQuestion.answers.length <= 2) {
      message.warning('Phải có ít nhất 2 tùy chọn!');
      return;
    }

    const newAnswers = editedQuestion.answers.filter(
      (answer: any) => answer.id !== id
    );
    const newQuestion = { ...editedQuestion, answers: newAnswers };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  const handlePointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const points = parseInt(e.target.value) || 0;
    const newQuestion = { ...editedQuestion, points };
    setEditedQuestion(newQuestion);
    handleUpdateQuestion(newQuestion as Question);
  };

  // Handle save configuration
  const handleSaveConfig = () => {
    // Validate the question before saving
    const hasTitle = editedQuestion.title.trim() !== '';
    const hasQuestionText = editedQuestion.question.trim() !== '';
    const hasAtLeastTwoOptions = editedQuestion.answers.length >= 2;
    const hasCorrectAnswer = editedQuestion.answers.some(
      (answer: any) => answer.isCorrect
    );
    const allOptionsHaveText = editedQuestion.answers.every(
      (answer: any) => answer.text.trim() !== ''
    );

    if (!hasTitle) {
      message.error('Tiêu đề không được để trống!');
      return;
    }

    if (!hasQuestionText) {
      message.error('Nội dung câu hỏi không được để trống!');
      return;
    }

    if (!hasAtLeastTwoOptions) {
      message.error('Phải có ít nhất 2 tùy chọn đáp án!');
      return;
    }

    if (!hasCorrectAnswer) {
      message.error('Phải chọn ít nhất 1 đáp án đúng!');
      return;
    }

    if (!allOptionsHaveText) {
      message.error('Tất cả các tùy chọn phải có nội dung!');
      return;
    }

    handleChangeQuestion(editedQuestion);
    handleUpdateQuestion(editedQuestion as Question);
    message.success('Lưu cấu hình thành công!');
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.id);
  };

  // Render config mode
  if (configMode) {
    return (
      <Card
        title="Chỉnh sửa câu hỏi nhiều lựa chọn"
        className="quiz-card"
        extra={
          <>
            {!hideSaveButton && (
              <Button
                type="primary"
                onClick={handleSaveConfig}
                className="flex-1 bg-blue-500 hover:bg-blue-600"
                icon={<CheckOutlined />}
              >
                Lưu cấu hình
              </Button>
            )}
          </>
        }
      >
        <Form form={form} layout="vertical">
          <Form.Item label="Tiêu đề" required>
            <Input
              value={editedQuestion.title}
              onChange={handleTitleChange}
              placeholder="Nhập tiêu đề câu hỏi"
            />
          </Form.Item>

          <Form.Item label="Nội dung câu hỏi" required>
            <ReactQuill
              theme="snow"
              value={editedQuestion.question}
              onChange={handleQuestionTextChange}
              placeholder="Nhập nội dung câu hỏi"
              modules={modules}
              formats={formats}
            />
          </Form.Item>

          <Form.Item label="Điểm">
            <Input
              type="number"
              value={editedQuestion.points || 1}
              onChange={handlePointsChange}
              min={1}
              style={{ width: '100px' }}
            />
          </Form.Item>

          <Form.Item label="Các tùy chọn đáp án (chọn đáp án đúng)" required>
            <Space direction="vertical" style={{ width: '100%' }}>
              {editedQuestion.answers?.map((answer: any, index: number) => (
                <div
                  key={answer.id}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '10px',
                  }}
                >
                  <Checkbox
                    checked={answer.isCorrect}
                    onChange={() => handleCorrectAnswerChange(answer.id)}
                  />
                  <Input
                    value={answer.text}
                    onChange={(e) =>
                      handleOptionTextChange(answer.id, e.target.value)
                    }
                    autoFocus={true}
                    style={{ margin: '0 10px', flex: 1 }}
                    placeholder={`Tùy chọn ${index + 1}`}
                  />
                  <Button
                    type="text"
                    danger
                    icon={<DeleteIcon />}
                    onClick={() => removeOption(answer.id)}
                  />
                </div>
              ))}
              <Button
                type="dashed"
                onClick={addNewOption}
                icon={<PlusOutlined />}
                style={{ width: '100%' }}
              >
                Thêm tùy chọn
              </Button>
            </Space>
          </Form.Item>

          <Form.Item label="Giải thích đáp án">
            <TextArea
              rows={3}
              value={editedQuestion.explanation || ''}
              onChange={handleExplanationChange}
              placeholder="Nhập giải thích cho đáp án đúng"
            />
          </Form.Item>

          <Space>
            {!hideSaveButton && (
              <Button type="primary" onClick={handleSaveConfig}>
                {quizLocalization.buttons.saveChanges}
              </Button>
            )}
            {!hideDeleteButton && (
              <PopconfirmAntdCustom
                title={quizLocalization.buttons.deleteQuestion.confirmTitle}
                onConfirm={deleteConfirm}
                onCancel={() => {}}
                okText={quizLocalization.buttons.deleteQuestion.yes}
                cancelText={quizLocalization.buttons.deleteQuestion.no}
              >
                <Button danger icon={<DeleteFilled />}>
                  {quizLocalization.buttons.deleteQuestion.button}
                </Button>
              </PopconfirmAntdCustom>
            )}
          </Space>
        </Form>
      </Card>
    );
  }

  // Render normal quiz mode
  return (
    <Card
      title={question.title}
      className="multi-select-card"
      extra={
        <>
          <Tooltip title="Hướng dẫn">
            <Button
              icon={<QuestionOutlined />}
              type="text"
              onClick={() => message.info('Chọn tất cả các đáp án đúng.')}
            />
          </Tooltip>
        </>
      }
    >
      <div className="multi-select-question">
        <div
          style={{
            color: '#1a1a1a',
          }}
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(question.question),
          }}
        />
      </div>

      <div className="multi-select-options">
        {question.answers?.map((option: MultiSelectAnswer) => (
          <div
            key={option.id}
            className={`option-card ${
              isSubmitted
                ? option.isCorrect
                  ? 'correct'
                  : selectedAnswers.includes(option.id)
                  ? 'incorrect'
                  : ''
                : selectedAnswers.includes(option.id)
                ? 'selected'
                : ''
            }`}
            onClick={() =>
              !disabled && !isSubmitted && handleAnswerSelect(option.id)
            }
          >
            <div className="option-content">
              <Checkbox
                checked={selectedAnswers.includes(option.id)}
                disabled={isSubmitted || disabled}
                onChange={(e) => {
                  e.stopPropagation();
                  handleAnswerSelect(option.id);
                }}
              />
              <span
                className="option-text"
                dangerouslySetInnerHTML={{
                  __html: option.text,
                }}
              ></span>
            </div>
            {isSubmitted && (
              <div className="option-feedback">
                {option.isCorrect ? (
                  <CheckOutlined className="feedback-icon correct" />
                ) : selectedAnswers.includes(option.id) ? (
                  <CloseOutlined className="feedback-icon incorrect" />
                ) : null}
              </div>
            )}
          </div>
        ))}
      </div>

      {isSubmitted && question.explanation && showFeedback && (
        <div
          className={`mt-4 p-3 rounded-lg ${
            isCorrect
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}
        >
          <Text strong>{isCorrect ? 'Giải thích:' : 'Đáp án đúng:'} </Text>
          <Text>{question.explanation}</Text>
        </div>
      )}
    </Card>
  );
};

export default MultiSelectQuizComponent;

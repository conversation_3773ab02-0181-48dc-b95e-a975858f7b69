import React from 'react';
import ThemeProvider from './ThemeProvider';
import AntdThemeProvider from './AntdThemeProvider';
import { ThemeProviderProps } from './ThemeProvider.interfaces';

interface CombinedThemeProviderProps extends ThemeProviderProps {}

const CombinedThemeProvider: React.FC<CombinedThemeProviderProps> = ({
  children,
  defaultTheme,
}) => {
  return (
    <ThemeProvider defaultTheme={defaultTheme}>
      <AntdThemeProvider>{children}</AntdThemeProvider>
    </ThemeProvider>
  );
};

export default CombinedThemeProvider;

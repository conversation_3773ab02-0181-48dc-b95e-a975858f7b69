/**
 * <PERSON><PERSON><PERSON> đường dẫn của cấp cha gần nhất từ một đường dẫn cụ thể
 * @param currentPath Đường dẫn hiện tại
 * @returns Đường dẫn của cấp cha gần nhất hoặc null nếu không có cha
 */
export const getImmediateParentPath = (currentPath: string): string | null => {
  const parts = currentPath.split('/');
  if (parts.length > 1) {
    parts.pop();
    return parts.join('/');
  }
  return null;
};

import React, { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import { ControlPosition } from '../../MapUtils';

interface ScaleControlProps {
  position?: ControlPosition;
  imperial?: boolean;
  maxWidth?: number;
  marginBottom?: number;
}

/**
 * Scale control hiển thị thanh tỉ lệ trên bản đồ
 */
const ScaleControl: React.FC<ScaleControlProps> = ({
  position = 'bottomright',
  imperial = false,
  maxWidth = 120,
  marginBottom = 30,
}) => {
  const map = useMap();

  useEffect(() => {
    const scale = L.control.scale({
      imperial: imperial,
      position: position as L.ControlPosition,
      maxWidth: maxWidth,
    });
    scale.addTo(map);

    // Điều chỉnh vị trí của thanh tỉ lệ nếu nằm ở góc dưới bên phải
    if (position === 'bottomright') {
      setTimeout(() => {
        const scaleElement = document.querySelector('.leaflet-control-scale');
        if (scaleElement) {
          // @ts-ignore
          scaleElement.style.marginBottom = `${marginBottom}px`;
          // @ts-ignore
          scaleElement.style.marginRight = '0';
        }
      }, 100);
    }

    return () => {
      scale.remove();
    };
  }, [map, position, imperial, maxWidth, marginBottom]);

  return null;
};

export default ScaleControl;

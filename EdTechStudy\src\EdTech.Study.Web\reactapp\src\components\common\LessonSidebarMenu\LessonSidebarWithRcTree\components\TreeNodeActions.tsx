import React from 'react';
import { But<PERSON>, Tooltip } from 'antd';
import { AddOulinedIcon, EditIcon } from '../../../../icons/IconRegister';
import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';
import DeleteEngineButton from '../../../engines/components/DeleteEngineButton';

interface TreeNodeActionsProps {
  canAddChildren: boolean;
  nodeData: IEdTechRenderTreeData;
  onAdd: (e: React.MouseEvent) => void;
  onEdit: (e: React.MouseEvent) => void;
}

const TreeNodeActions: React.FC<TreeNodeActionsProps> = ({
  canAddChildren,
  nodeData,
  onAdd,
  onEdit,
}) => {
  return (
    <div className="node-actions">
      {canAddChildren && (
        <Tooltip title="Thêm">
          <Button
            type="text"
            icon={<AddOulinedIcon />}
            size="small"
            className="action-button add-button"
            onClick={onAdd}
          />
        </Tooltip>
      )}
      <Tooltip title="Sửa">
        <Button
          type="text"
          icon={<EditIcon />}
          size="small"
          className="action-button edit-button"
          onClick={onEdit}
        />
      </Tooltip>
      <DeleteEngineButton
        node={nodeData}
        buttonStyle={{ border: 'none', background: 'none', boxShadow: 'none' }}
      />
    </div>
  );
};

export default TreeNodeActions;

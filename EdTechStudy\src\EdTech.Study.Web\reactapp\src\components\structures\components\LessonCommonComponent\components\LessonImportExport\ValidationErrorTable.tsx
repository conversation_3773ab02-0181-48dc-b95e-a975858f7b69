import React from 'react';
import { Table, Typography, Alert, Space } from 'antd';
import { WarningOutlined } from '@ant-design/icons';
import { IErrorValidateComponent } from '../../../../../../utils/validation/validateSchema';

const { Text } = Typography;

interface ValidationErrorTableProps {
  errors: IErrorValidateComponent[];
}

const ValidationErrorTable: React.FC<ValidationErrorTableProps> = ({
  errors,
}) => {
  // Cấu hình cột cho bảng lỗi
  const errorColumns = [
    {
      title: 'Component',
      dataIndex: 'componentName',
      key: 'componentName',
      width: 150,
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: 'ID',
      dataIndex: 'componentId',
      key: 'componentId',
      width: 100,
    },
    {
      title: 'Path',
      dataIndex: 'path',
      key: 'path',
      width: 150,
      render: (text: string) => <Text code>{text}</Text>,
    },
    {
      title: 'Mã lỗi',
      dataIndex: 'errorCode',
      key: 'errorCode',
      width: 120,
      render: (text: string) => (
        <Text type="danger" code>
          {text}
        </Text>
      ),
    },
    {
      title: 'Thông báo lỗi',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      render: (text: string) => <Text type="danger">{text}</Text>,
    },
  ];

  if (errors.length === 0) return null;

  return (
    <Space
      direction="vertical"
      size="middle"
      className="tailwind-w-full tailwind-mt-6"
    >
      <Alert
        message={
          <Space>
            <WarningOutlined className="tailwind-text-red-500" />
            <Text>Đã phát hiện {errors.length} lỗi validation</Text>
          </Space>
        }
        type="error"
        showIcon={false}
      />
      <Table
        columns={errorColumns}
        dataSource={errors}
        rowKey={(record) => `${record.componentId}-${record.errorCode}`}
        pagination={false}
        scroll={{ y: 300 }}
        size="small"
        bordered
      />
    </Space>
  );
};

export default ValidationErrorTable;

import { axiosClient } from '../services/axiosClient';

class DefaultLessonConfigApi {
  /**
   * Save the default lesson configuration
   * @param config The configuration to save as default
   * @returns Promise with the result of the operation
   */
  public async saveDefaultConfig(param: {
    Key: string;
    Data: {
      edComponentParams: any;
      edTechRenderTreeData: any;
    };
  }) {
    try {
      const response = await axiosClient({
        method: 'post',
        url: `api/default-lesson-config/save/${param.Key}`,
        data: {
          ...param,
          Data: JSON.stringify(param.Data),
        },
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error) {
      console.error('Error saving default lesson configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export default new DefaultLessonConfigApi();

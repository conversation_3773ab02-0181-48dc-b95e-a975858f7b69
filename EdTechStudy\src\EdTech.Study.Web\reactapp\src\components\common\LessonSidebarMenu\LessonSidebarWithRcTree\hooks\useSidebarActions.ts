import { useCallback } from 'react';
import { Modal, message } from 'antd';
import { useDispatch } from 'react-redux';
import { applyTempToData } from '../../../../../store/slices/utils/createTreeDataSlice';

export const useSidebarActions = () => {
  const dispatch = useDispatch();

  const handleSave = useCallback(() => {
    Modal.confirm({
      title: 'Xác nhận lưu',
      content: 'Bạn có chắc chắn muốn lưu cấu hình này không?',
      okText: 'Lưu',
      okType: 'primary',
      cancelText: 'Hủy',
      onOk: () => {
        dispatch(applyTempToData());
        message.success('Lưu cấu hình thành công');
      },
    });
  }, [dispatch]);

  return {
    handleSave,
  };
};

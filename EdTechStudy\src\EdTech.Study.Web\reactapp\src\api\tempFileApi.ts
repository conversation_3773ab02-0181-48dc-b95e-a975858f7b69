import { Guid } from 'guid-typescript';
import { axiosClient } from '../services/axiosClient';

class TempFileApi {
  // public async Upload(formData: FormData, config?: any) {
  //   return axiosClient({
  //     method: 'post',
  //     url: `/api/edtech-temp-files/`,
  //     data: formData,
  //     ...config,
  //   });
  // }

  public async UploadMultiple(
    sessionIdKey: string,
    formData: FormData,
    config?: any
  ) {
    //get SessionId from localstorage. If there's no sessionId, create by Guid
    const sessionId =
      localStorage.getItem(sessionIdKey) || Guid.create().toString();
    if (!localStorage.getItem(sessionIdKey)) {
      localStorage.setItem(sessionIdKey, sessionId);
    }
    return axiosClient({
      method: 'post',
      url: `/api/edtech-temp-files/multiple/${sessionId}`,
      data: formData,
      ...config,
    });
  }

  public async Delete(key: string) {
    return axiosClient({
      method: 'delete',
      url: `/api/edtech-temp-files/${key}`,
    });
  }

  public async GetFile(key: string) {
    return axiosClient({
      method: 'get',
      url: `/api/edtech-temp-files/${key}`,
      responseType: 'blob',
    });
  }
}

export default new TempFileApi();

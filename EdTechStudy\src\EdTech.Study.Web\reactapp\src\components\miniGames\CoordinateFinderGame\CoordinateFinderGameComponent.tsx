import React, { useState, useEffect, useRef } from 'react';
import 'leaflet/dist/leaflet.css';
import '../MiniGameResponsive.css';
import {
  CoordinateFinderGameConfig,
  GameResult,
  GameSummary,
  defaultCoordinateFinderGameConfig,
} from './CoordinateFinderGameConfig';
import {
  calculateDistance,
  createCustomIcon,
  MapPosition,
} from '../../common/MapCommonComponent/MapUtils';
import MapComponent from '../../common/MapCommonComponent/components/MapComponent';
import {
  Card,
  Button,
  Space,
  Typography,
  Table,
  Tag,
  Alert,
  Badge,
  Row,
  Col,
} from 'antd';
import {
  CheckOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  BulbOutlined,
  ReloadOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CompassOutlined,
} from '@ant-design/icons';
import { applyMapInteractionStyles } from '../../common/MapCommonComponent/MapUtils';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

const { Title, Text, Paragraph } = Typography;

// Kiểu dữ liệu cho các props của component
interface CoordinateFinderGameComponentProps {
  config?: CoordinateFinderGameConfig;
  onGameComplete?: (summary: GameSummary) => void;
  onScoreChange?: (newScore: number) => void;
  onQuestionChange?: (index: number) => void;
  onGameResultsChange?: (results: GameResult[]) => void;
  onGameStatusChange?: (
    status: 'ready' | 'playing' | 'complete',
    timeLeft?: number
  ) => void;
  isFullscreen?: boolean;
  allowConfiguration?: boolean;
}

// Zod schema for component validation
export const coordinateFinderGameSchema = createComponentSchema({
  paramsSchema: {
    id: z.string(),
    title: z.string(),
    description: z.string().optional(),
    gridSize: z.object({
      width: z.number().min(1).max(20),
      height: z.number().min(1).max(20),
    }),
    targetPoints: z
      .array(
        z.object({
          x: z.number().min(0),
          y: z.number().min(0),
          label: z.string().optional(),
          color: z.string().optional(),
        })
      )
      .min(1),
    showGrid: z.boolean().optional(),
    showCoordinates: z.boolean().optional(),
    showLabels: z.boolean().optional(),
    allowMultipleSelections: z.boolean().optional(),
    timeLimit: z.number().optional(),
    maxAttempts: z.number().optional(),
    difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
    onComplete: z.function().optional(),
    onError: z.function().optional(),
    onReset: z.function().optional(),
  },
});

const CoordinateFinderGameComponent: React.FC<
  CoordinateFinderGameComponentProps
> = ({
  config = defaultCoordinateFinderGameConfig,
  onGameComplete,
  onScoreChange,
  onQuestionChange,
  onGameResultsChange,
  onGameStatusChange,
  isFullscreen = false,
}) => {
  // States
  const [currentCoordinateIndex, setCurrentCoordinateIndex] =
    useState<number>(0);
  const [userGuess, setUserGuess] = useState<MapPosition | null>(null);
  const [showAnswer, setShowAnswer] = useState<boolean>(false);
  const [gameResults, setGameResults] = useState<GameResult[]>([]);
  const [score, setScore] = useState<number>(0);
  const [hintsUsed, setHintsUsed] = useState<number>(0);
  const [timeLeft, setTimeLeft] = useState<number | undefined>(
    config?.timeLimit
  );
  const [gameStatus, setGameStatus] = useState<
    'ready' | 'playing' | 'complete'
  >('ready');
  const [inputLat, setInputLat] = useState<string>('');
  const [inputLng, setInputLng] = useState<string>('');
  const [distance, setDistance] = useState<number | null>(null);
  const [message, setMessage] = useState<string>('');

  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Lấy tọa độ bí ẩn hiện tại
  const currentCoordinate = config.coordinates?.[currentCoordinateIndex] || {
    name: 'Không có địa điểm',
    latitude: 0,
    longitude: 0,
  };

  // Theo dõi thay đổi currentCoordinateIndex và thông báo cho component cha
  useEffect(() => {
    if (onQuestionChange) {
      onQuestionChange(currentCoordinateIndex);
    }
  }, [currentCoordinateIndex, onQuestionChange]);

  // Khởi động bộ đếm thời gian nếu có giới hạn thời gian
  useEffect(() => {
    if (config?.timeLimit && gameStatus === 'playing') {
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev && prev > 0) {
            return prev - 1;
          } else {
            // Hết thời gian
            clearInterval(timerRef.current as ReturnType<typeof setInterval>);
            handleSubmitGuess();
            return 0;
          }
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [currentCoordinateIndex, config?.timeLimit, gameStatus]);

  // Xử lý bắt đầu game
  const handleStartGame = () => {
    setGameStatus('playing');
    setTimeLeft(config.timeLimit);
    setMessage('Trò chơi đã bắt đầu! Hãy tìm vị trí trên bản đồ.');
  };

  // Xử lý khi game kết thúc
  useEffect(() => {
    if (gameStatus === 'complete' && onGameComplete) {
      // Tính tổng kết game
      const summary: GameSummary = {
        totalScore: score,
        correctAnswers: gameResults.filter((result) => result.correct).length,
        totalQuestions: config?.coordinates?.length || 0,
        accuracy:
          (gameResults.filter((result) => result.correct).length /
            (config?.coordinates?.length || 1)) *
          100,
        averageDistance:
          gameResults.reduce((sum, result) => sum + (result.distance || 0), 0) /
          gameResults.length,
        totalHintsUsed: gameResults.reduce(
          (sum, result) => sum + result.hintsUsed,
          0
        ),
        totalTimeTaken: config?.timeLimit
          ? config.timeLimit * (config?.coordinates?.length || 0) -
            (timeLeft || 0)
          : undefined,
        results: gameResults,
      };
      onGameComplete(summary);
    }
  }, [
    gameStatus,
    onGameComplete,
    score,
    gameResults,
    config?.coordinates?.length,
    config?.timeLimit,
    timeLeft,
  ]);

  // Xử lý khi điểm thay đổi
  useEffect(() => {
    if (onScoreChange) {
      onScoreChange(score);
    }
  }, [score, onScoreChange]);

  // Xử lý khi người dùng click vào bản đồ
  const handleMapClick = (position: MapPosition) => {
    if (gameStatus === 'playing') {
      setUserGuess(position);
      setInputLat(position.lat.toFixed(6));
      setInputLng(position.lng.toFixed(6));
    }
  };

  // Xử lý khi người dùng tìm kiếm và chọn địa điểm
  const handleLocationFound = (position: MapPosition) => {
    if (gameStatus === 'playing') {
      setUserGuess(position);
      setInputLat(position.lat.toFixed(6));
      setInputLng(position.lng.toFixed(6));
    }
  };

  // Xử lý khi người dùng gửi đáp án
  const handleSubmitGuess = () => {
    if (!userGuess && (!inputLat || !inputLng)) {
      setMessage('Vui lòng chọn vị trí trên bản đồ hoặc nhập tọa độ.');
      return;
    }

    // Sử dụng tọa độ đã nhập nếu không có userGuess
    const guessLocation = userGuess || {
      lat: parseFloat(inputLat),
      lng: parseFloat(inputLng),
    };
    // Tính khoảng cách giữa đáp án và tọa độ bí ẩn
    const dist = calculateDistance(guessLocation, {
      lat: currentCoordinate.latitude,
      lng: currentCoordinate.longitude,
    });
    setDistance(dist);

    // Kiểm tra xem đáp án có đúng không (trong ngưỡng)
    const correct = dist <= (config?.proximityThreshold || 10);

    // Tính điểm
    const resultScore = correct
      ? (config?.correctAnswerScore || 10) -
        hintsUsed * (config?.hintPenalty || 0)
      : 0;

    // Cập nhật tổng điểm
    setScore((prev) => prev + resultScore);

    // Thêm kết quả vào danh sách
    const result: GameResult = {
      coordinate: currentCoordinate,
      userGuess: guessLocation,
      distance: dist,
      correct,
      hintsUsed,
      score: resultScore,
      timeTaken: config.timeLimit
        ? config.timeLimit - (timeLeft || 0)
        : undefined,
    };
    setGameResults((prev) => [...prev, result]);

    // Hiển thị vị trí đúng
    setShowAnswer(true);

    // Hiển thị thông báo kết quả
    if (correct) {
      setMessage(
        `Chính xác! Bạn đã tìm thấy "${
          currentCoordinate.name
        }". Khoảng cách: ${dist.toFixed(2)} km. Điểm: +${resultScore}`
      );
    } else {
      setMessage(
        `Chưa chính xác. "${
          currentCoordinate.name
        }" cách vị trí bạn chọn ${dist.toFixed(2)} km.`
      );
    }

    // Chuyển sang câu hỏi tiếp theo sau 3 giây
    setTimeout(() => {
      if (currentCoordinateIndex < config.coordinates.length - 1) {
        setCurrentCoordinateIndex((prev) => prev + 1);
        setUserGuess(null);
        setShowAnswer(false);
        setHintsUsed(0);
        setDistance(null);
        setInputLat('');
        setInputLng('');
        setMessage('');

        // Reset thời gian nếu có
        if (config.timeLimit) {
          setTimeLeft(config.timeLimit);
        }
      } else {
        // Kết thúc game
        handleGameEnd();
      }
    }, 3000);
  };

  // Xử lý khi người dùng sử dụng gợi ý
  const handleUseHint = () => {
    if (currentCoordinate.hint) {
      setHintsUsed((prev) => prev + 1);
      setMessage(`Gợi ý: ${currentCoordinate.hint}`);
    } else {
      setMessage('Không có gợi ý cho vị trí này.');
    }
  };

  // Xử lý reset game
  const handleResetGame = () => {
    // Reset lại các trạng thái
    setCurrentCoordinateIndex(0);
    setUserGuess(null);
    setShowAnswer(false);
    setGameResults([]);
    setScore(0);
    setHintsUsed(0);
    setTimeLeft(config.timeLimit);
    setInputLat('');
    setInputLng('');
    setDistance(null);
    setMessage('');

    // Bắt đầu ngay trò chơi mới
    setGameStatus('playing');
  };

  // Custom CSS for map controls in fullscreen mode
  useEffect(() => {
    applyMapInteractionStyles(
      isFullscreen,
      'fullscreen-map-styles-coordinate-finder'
    );
  }, [isFullscreen]);

  // Thêm style chung cho tất cả các nút
  useEffect(() => {
    // Thêm CSS toàn cục cho các nút
    const globalStyleEl = document.createElement('style');
    globalStyleEl.innerHTML = `
      .coordinate-finder-game button {
        cursor: pointer !important;
      }
      .ant-btn {
        cursor: pointer !important;
      }
    `;
    globalStyleEl.id = 'coordinate-finder-game-global-styles';
    document.head.appendChild(globalStyleEl);

    return () => {
      const existingGlobalStyle = document.getElementById(
        'coordinate-finder-game-global-styles'
      );
      if (existingGlobalStyle) {
        document.head.removeChild(existingGlobalStyle);
      }
    };
  }, []);

  // Tùy chỉnh icon cho marker dựa trên trạng thái fullscreen
  const createMarkerIcon = (color: string) => {
    return createCustomIcon(color, isFullscreen ? 'large' : 'medium');
  };

  // Chuẩn bị markers cho bản đồ
  const mapMarkers = [];

  // Thêm marker cho vị trí người dùng đã chọn
  if (userGuess) {
    mapMarkers.push({
      position: userGuess,
      title: 'Vị trí bạn đã chọn',
      icon: createMarkerIcon('blue'),
    });
  }

  // Thêm marker cho vị trí đúng nếu đã submit
  if (showAnswer) {
    mapMarkers.push({
      position: {
        lat: currentCoordinate.latitude,
        lng: currentCoordinate.longitude,
      },
      title: currentCoordinate.name,
      icon: createMarkerIcon('green'),
    });
  }

  // Thông báo thay đổi kết quả game cho component cha
  useEffect(() => {
    if (onGameResultsChange) {
      onGameResultsChange(gameResults);
    }
  }, [gameResults, onGameResultsChange]);

  // Thông báo thay đổi trạng thái game và thời gian còn lại cho component cha
  useEffect(() => {
    if (onGameStatusChange) {
      onGameStatusChange(gameStatus, timeLeft);
    }
  }, [gameStatus, timeLeft, onGameStatusChange]);

  // Đảm bảo config hợp lệ
  useEffect(() => {
    // Không tự động áp dụng cấu hình khi game đã kết thúc
    // Chỉ cập nhật giá trị timeLimit để hiển thị đúng thông tin
    if (gameStatus === 'ready') {
      setTimeLeft(config?.timeLimit);
    }
  }, [config, gameStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  // Xử lý khi game kết thúc
  const handleGameEnd = () => {
    // Kết thúc game
    setGameStatus('complete');
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setMessage(`Trò chơi kết thúc! Tổng điểm của bạn: ${score}`);
  };

  const getTitleClass = () => {
    return isFullscreen ? 'tailwind-text-xl' : 'tailwind-text-base';
  };

  const getButtonClass = () => {
    return isFullscreen
      ? 'tailwind-text-base tailwind-py-2 tailwind-px-6'
      : 'tailwind-text-sm tailwind-py-1 tailwind-px-4';
  };

  return (
    <Card
      className="tailwind-w-full tailwind-max-w-full tailwind-mx-auto coordinate-finder-game mini-game-container"
      styles={{ body: { padding: isFullscreen ? '12px' : '12px' } }}
    >
      <div className="tailwind-mb-2 tailwind-flex tailwind-flex-wrap tailwind-justify-end tailwind-items-center tailwind-gap-1">
        <Tag
          color="blue"
          className={`tailwind-py-0 tailwind-px-2 tailwind-m-0 ${
            isFullscreen ? 'tailwind-text-base' : ''
          }`}
        >
          <TrophyOutlined className="tailwind-mr-1" /> Điểm: {score}
        </Tag>
      </div>

      {gameStatus === 'playing' && (
        <Card
          className="tailwind-mb-2"
          style={{
            background: '#f0f7ff',
            boxShadow: isFullscreen ? '0 4px 6px rgba(0, 0, 0, 0.1)' : '',
          }}
          styles={{
            body: { padding: isFullscreen ? '10px 16px' : '8px 12px' },
          }}
        >
          <div className="tailwind-flex tailwind-flex-wrap tailwind-items-center tailwind-justify-between tailwind-mb-0">
            <div className="tailwind-flex tailwind-items-center">
              <EnvironmentOutlined
                className={`tailwind-mr-1 tailwind-text-indigo-800 ${
                  isFullscreen ? 'tailwind-text-xl' : ''
                }`}
              />
              <Text
                strong
                className={`tailwind-text-indigo-800 ${
                  isFullscreen ? 'tailwind-text-lg' : 'tailwind-text-sm'
                }`}
              >
                Câu hỏi {currentCoordinateIndex + 1}/
                {config?.coordinates?.length || 0}:
              </Text>
            </div>

            {config.timeLimit && (
              <Tag
                color={
                  timeLeft && timeLeft < 10
                    ? 'error'
                    : timeLeft && timeLeft < 20
                    ? 'warning'
                    : 'success'
                }
                className={`tailwind-py-0 tailwind-px-2 tailwind-m-0 ${
                  isFullscreen ? 'tailwind-text-base' : ''
                }`}
              >
                <ClockCircleOutlined className="tailwind-mr-1" /> {timeLeft}s
              </Tag>
            )}
          </div>

          <div
            className="tailwind-bg-white tailwind-rounded-md tailwind-shadow-sm tailwind-border tailwind-border-indigo-100 tailwind-mt-1"
            style={{ padding: isFullscreen ? '12px 16px' : '8px' }}
          >
            <Text
              strong
              className={`tailwind-block tailwind-text-center tailwind-text-indigo-900 ${
                isFullscreen ? 'tailwind-text-2xl' : 'tailwind-text-sm'
              }`}
            >
              {currentCoordinate.name}
            </Text>
          </div>
        </Card>
      )}

      {gameStatus === 'ready' && (
        <Card
          className="tailwind-mb-3"
          style={{ background: '#f0f7ff' }}
          styles={{ body: { padding: isFullscreen ? '12px' : '10px' } }}
        >
          <Title
            level={4}
            className={`tailwind-text-center tailwind-mb-2 tailwind-text-indigo-800 ${getTitleClass()}`}
          >
            Sẵn sàng để bắt đầu!
          </Title>

          <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-mb-3">
            <div className="tailwind-flex tailwind-flex-wrap tailwind-justify-between tailwind-items-center tailwind-w-full tailwind-gap-1 tailwind-px-2 tailwind-mb-1">
              <div className="tailwind-inline-flex tailwind-items-center tailwind-gap-1">
                <EnvironmentOutlined className="tailwind-text-indigo-800" />
                <Text
                  strong
                  className={
                    isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
                  }
                >
                  {config.coordinates.length} địa điểm
                </Text>
              </div>

              <div className="tailwind-inline-flex tailwind-items-center tailwind-gap-1">
                <ClockCircleOutlined className="tailwind-text-indigo-800" />
                <Text
                  strong
                  className={
                    isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
                  }
                >
                  {config.timeLimit
                    ? `${config.timeLimit}s/câu`
                    : 'Không giới hạn'}
                </Text>
              </div>

              <div className="tailwind-inline-flex tailwind-items-center tailwind-gap-1">
                <TrophyOutlined className="tailwind-text-indigo-800" />
                <Text
                  strong
                  className={
                    isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
                  }
                >
                  {config.correctAnswerScore} điểm/câu đúng
                </Text>
              </div>

              <Tag
                color={
                  config.difficultyLevel === 'easy'
                    ? 'success'
                    : config.difficultyLevel === 'medium'
                    ? 'warning'
                    : 'error'
                }
                className={`tailwind-py-0 tailwind-px-2 tailwind-m-0 ${
                  isFullscreen ? 'tailwind-text-sm' : 'tailwind-text-xs'
                }`}
              >
                Độ khó:{' '}
                {config.difficultyLevel === 'easy'
                  ? 'Dễ'
                  : config.difficultyLevel === 'medium'
                  ? 'Trung bình'
                  : 'Khó'}
              </Tag>
            </div>
          </div>

          <Paragraph
            className={`tailwind-text-indigo-700 tailwind-mb-3 tailwind-text-center ${
              isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
            }`}
          >
            Tìm vị trí chính xác cho từng địa điểm trên bản đồ. Bạn cần click
            vào đúng vị trí với khoảng cách không quá{' '}
            <Text strong>{config.proximityThreshold}km</Text> để được tính là
            chính xác.
          </Paragraph>

          <div className="tailwind-text-center">
            <Button
              type="primary"
              size={isFullscreen ? 'large' : 'middle'}
              onClick={handleStartGame}
              icon={
                <ClockCircleOutlined
                  style={{ fontSize: isFullscreen ? '18px' : '14px' }}
                />
              }
              className={`${getButtonClass()} game-action-button`}
              style={{ cursor: 'pointer' }}
            >
              Bắt đầu trò chơi
            </Button>
          </div>
        </Card>
      )}

      {message && gameStatus !== 'ready' && (
        <Alert
          message={message}
          type={
            distance !== null && distance <= config.proximityThreshold
              ? 'success'
              : 'info'
          }
          showIcon
          className={`tailwind-mb-2 ${
            isFullscreen ? 'tailwind-text-base' : 'tailwind-text-sm'
          }`}
          style={{ padding: isFullscreen ? '6px 12px' : '4px 8px' }}
        />
      )}

      <div className="tailwind-relative tailwind-mb-14">
        <div
          className={`tailwind-mb-0 tailwind-border tailwind-rounded-lg tailwind-overflow-hidden tailwind-shadow-sm tailwind-transition-all tailwind-duration-500 ${
            isFullscreen ? 'mini-game-map fullscreen' : 'mini-game-map'
          }`}
        >
          <MapComponent
            initialCenter={config?.initialCenter}
            initialZoom={config?.initialZoom}
            markers={mapMarkers}
            onMapClick={gameStatus === 'playing' ? handleMapClick : undefined}
            scrollWheelZoom={config?.mapOptions?.scrollWheelZoom ?? true}
            doubleClickZoom={config?.mapOptions?.doubleClickZoom ?? true}
            className="tailwind-rounded-lg tailwind-overflow-hidden"
            height="100%"
            onLocationFound={
              gameStatus === 'playing' ? handleLocationFound : undefined
            }
            controls={{
              scale: {
                enabled: true,
                position: 'bottomright',
                maxWidth: isFullscreen ? 180 : 120,
              },
              zoom: {
                enabled: true,
                position: 'topright',
              },
            }}
          />
        </div>

        <div
          className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center game-controls-container tailwind-absolute tailwind-w-full mini-game-controls"
          style={{ bottom: '-60px', left: 0, minHeight: '60px' }}
        >
          {gameStatus === 'playing' && (
            <Space className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-w-full">
              <Button
                type="primary"
                onClick={handleSubmitGuess}
                disabled={!userGuess && (!inputLat || !inputLng)}
                icon={
                  <CheckOutlined
                    style={{ fontSize: isFullscreen ? '18px' : '14px' }}
                  />
                }
                size={isFullscreen ? 'large' : 'small'}
                className={`${getButtonClass()} game-action-button`}
                style={{ cursor: 'pointer' }}
              >
                Kiểm tra vị trí
              </Button>

              {currentCoordinate.hint && (
                <Button
                  type="default"
                  onClick={handleUseHint}
                  icon={
                    <BulbOutlined
                      style={{ fontSize: isFullscreen ? '18px' : '14px' }}
                    />
                  }
                  size={isFullscreen ? 'large' : 'small'}
                  className={`${getButtonClass()} game-action-button`}
                  style={{
                    backgroundColor: '#d97706',
                    color: 'white',
                    borderColor: '#b45309',
                    cursor: 'pointer',
                  }}
                >
                  Sử dụng gợi ý
                  {config?.hintPenalty ? (
                    <Badge
                      count={`-${config.hintPenalty}đ`}
                      style={{
                        backgroundColor: '#f59e0b',
                        color: '#78350f',
                        marginLeft: 4,
                        fontSize: isFullscreen ? '12px' : '10px',
                        padding: '0 4px',
                        height: isFullscreen ? '20px' : '16px',
                        lineHeight: isFullscreen ? '20px' : '16px',
                      }}
                    />
                  ) : null}
                </Button>
              )}
            </Space>
          )}

          {/* Removed duplicate 'Start New Game' button */}
        </div>
      </div>

      {gameStatus === 'complete' && (
        <Card
          className="tailwind-mt-4"
          title={
            <Title
              level={4}
              className={`tailwind-text-center tailwind-my-0 tailwind-text-indigo-700 ${
                isFullscreen ? 'tailwind-text-xl' : 'tailwind-text-base'
              }`}
            >
              Kết Quả Trò Chơi
            </Title>
          }
          style={{
            background: 'linear-gradient(to bottom right, white, #f9fafb)',
          }}
          styles={{ body: { padding: isFullscreen ? '16px' : '8px' } }}
        >
          <Row gutter={[16, 16]} className="tailwind-mb-4">
            <Col xs={24} md={8}>
              <Card className="tailwind-h-full tailwind-shadow-sm tailwind-bg-gradient-to-br tailwind-from-indigo-50 tailwind-to-blue-50 tailwind-border-indigo-100">
                <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-gap-2">
                  <div className="tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-text-indigo-700">
                    <TrophyOutlined className="tailwind-text-xl tailwind-text-yellow-500" />
                    <span className="tailwind-text-sm tailwind-uppercase tailwind-tracking-wider tailwind-font-bold">
                      Tổng Điểm
                    </span>
                  </div>

                  <div className="tailwind-text-4xl tailwind-font-bold tailwind-text-indigo-700">
                    {score}
                  </div>

                  <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-mt-2">
                    <Tag
                      color="blue"
                      className="tailwind-flex tailwind-items-center tailwind-gap-1"
                    >
                      <CheckOutlined />{' '}
                      {gameResults.filter((result) => result.correct).length}/
                      {config?.coordinates?.length || 0} đúng
                    </Tag>
                  </div>

                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleResetGame}
                    className="tailwind-mt-3 tailwind-bg-gradient-to-r tailwind-from-green-500 tailwind-to-emerald-600 hover:tailwind-from-green-600 hover:tailwind-to-emerald-700 tailwind-border-none tailwind-shadow-md"
                  >
                    Bắt đầu lại
                  </Button>
                </div>
              </Card>
            </Col>

            <Col xs={24} md={16}>
              <Card
                className="tailwind-h-full tailwind-shadow-sm tailwind-bg-gradient-to-r tailwind-from-blue-50 tailwind-to-indigo-50 tailwind-border-blue-100"
                title={
                  <div className="tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-text-indigo-700">
                    <EnvironmentOutlined />
                    <span>Kết quả tìm vị trí</span>
                  </div>
                }
                size="small"
              >
                <Table
                  dataSource={gameResults.map((result, index) => ({
                    ...result,
                    key: index,
                  }))}
                  bordered
                  size={isFullscreen ? 'middle' : 'small'}
                  pagination={false}
                  className="tailwind-shadow-sm"
                  columns={[
                    {
                      title: 'Vị trí',
                      dataIndex: ['coordinate', 'name'],
                      key: 'name',
                      render: (text) => (
                        <Text
                          strong
                          className="tailwind-flex tailwind-items-center tailwind-gap-1"
                        >
                          <EnvironmentOutlined className="tailwind-text-blue-500" />{' '}
                          {text}
                        </Text>
                      ),
                    },
                    {
                      title: 'Khoảng cách',
                      dataIndex: 'distance',
                      key: 'distance',
                      render: (dist) => (
                        <span className="tailwind-flex tailwind-items-center tailwind-gap-1">
                          <CompassOutlined className="tailwind-text-indigo-500" />{' '}
                          {dist?.toFixed(2)} km
                        </span>
                      ),
                    },
                    {
                      title: 'Kết quả',
                      dataIndex: 'correct',
                      key: 'correct',
                      render: (correct) =>
                        correct ? (
                          <Tag
                            color="success"
                            className="tailwind-flex tailwind-items-center tailwind-gap-1"
                          >
                            <CheckCircleOutlined /> Chính xác
                          </Tag>
                        ) : (
                          <Tag
                            color="error"
                            className="tailwind-flex tailwind-items-center tailwind-gap-1"
                          >
                            <CloseCircleOutlined /> Sai
                          </Tag>
                        ),
                    },
                    {
                      title: 'Điểm',
                      dataIndex: 'score',
                      key: 'score',
                      render: (score) => (
                        <Text
                          strong
                          className="tailwind-flex tailwind-items-center tailwind-gap-1"
                        >
                          <TrophyOutlined className="tailwind-text-yellow-500" />{' '}
                          {score}
                        </Text>
                      ),
                    },
                  ]}
                  rowClassName={(record) =>
                    record.correct
                      ? 'tailwind-bg-green-50 hover:tailwind-bg-green-100'
                      : 'tailwind-bg-red-50 hover:tailwind-bg-red-100'
                  }
                />
              </Card>
            </Col>
          </Row>
        </Card>
      )}
    </Card>
  );
};

export default CoordinateFinderGameComponent;

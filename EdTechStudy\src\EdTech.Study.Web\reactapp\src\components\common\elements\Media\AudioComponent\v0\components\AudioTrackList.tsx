import React, { useState, useEffect } from 'react';
import { But<PERSON> } from 'antd';
import { Trash2, Download, PlayCircle } from './IconAdapter';
import { getFileNameWithoutExtension } from '../../../shared/utils';
import { useAudio } from '../context/AudioContext';
import { AudioWaveIcon } from '../../../../../../icons/IconRegister';

export const AudioTrackList: React.FC = () => {
  const {
    config,
    selectedIndex,
    isEditing,
    handleSelectAudio,
    handleDownloadAudio,
    showDeleteConfirmation,
    audioPlayerRef,
  } = useAudio();

  // Local state to track playing status for UI updates
  const [isPlaying, setIsPlaying] = useState(false);

  // Listen to audio events to update playing status
  useEffect(() => {
    const audioElement = audioPlayerRef.current?.audio?.current;
    if (!audioElement) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    audioElement.addEventListener('play', handlePlay);
    audioElement.addEventListener('pause', handlePause);
    audioElement.addEventListener('ended', handleEnded);

    // Set initial state
    setIsPlaying(!audioElement.paused);

    return () => {
      audioElement.removeEventListener('play', handlePlay);
      audioElement.removeEventListener('pause', handlePause);
      audioElement.removeEventListener('ended', handleEnded);
    };
  }, [audioPlayerRef, selectedIndex]); // Re-run when audio changes

  if (!config.medias || config.medias.length === 0) return null;

  return (
    <div className="edtech-audio-tracklist">
      {config.medias.map((audio, index) => (
        <div
          key={index}
          className={`edtech-audio-track-item audio-item-container ${
            index === selectedIndex ? 'active' : ''
          }`}
          onClick={() => handleSelectAudio(index)}
        >
          <div className="edtech-audio-track-info">
            <span className="edtech-audio-track-icon">
              {index === selectedIndex && isPlaying ? (
                <AudioWaveIcon className="icon-play" />
              ) : (
                <PlayCircle className="icon-play" />
              )}
            </span>
            <span className="edtech-audio-track-name">
              {getFileNameWithoutExtension(audio.name || `Tên âm thanh.mp3`)}
            </span>
          </div>

          <div className="edtech-audio-track-actions">
            <Button
              type="text"
              icon={<Download size={16} />}
              onClick={(e) => {
                e.stopPropagation();
                handleDownloadAudio();
              }}
              className="edtech-audio-download-button"
              style={{ color: 'var(--edtt-color-text-secondary)' }}
            />
            {isEditing && (
              <Button
                type="text"
                danger
                icon={<Trash2 size={16} />}
                onClick={(e) => {
                  e.stopPropagation();
                  showDeleteConfirmation(index);
                }}
                className="edtech-audio-delete-button"
                style={{ color: 'var(--edtt-color-status-error)' }}
              />
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

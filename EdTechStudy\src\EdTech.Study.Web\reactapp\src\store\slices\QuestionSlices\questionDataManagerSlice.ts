import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import questionDraftApi, {
  QuestionDraftParams,
  QuestionType,
  QuestionStatus,
  QuestionDraft,
} from '../../../api/questionDraftApi';
import questionDraftAdapter from '../../../utils/adapters/questionDraftAdapter';
import { Subject } from '../../../components/quizs/storeManager/QuestionTableAdvancedSettings';
import { camelCaseKeys } from '../../../utils/parsers';

export interface QuestionDataManagerState {
  questionData: BaseQuestion[] | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  pagination: {
    current: number;
    pageSize: number;
  };
  filters: {
    type?: QuestionType | QuestionType[]; // Use enum type instead of string
    statusEntity?: QuestionStatus | QuestionStatus[]; // Use enum type instead of string
    subjectIds?: string[];
    searchText?: string;
  };
  subjects?: Subject[];
}

export const QUESTION_DATA_SLICE_NAME = 'questionDataManager';

// Async thunk for fetching questions using OData
export const fetchQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/fetchQuestions`,
  async (params: QuestionDraftParams, { rejectWithValue }) => {
    try {
      // Format sort parameters for OData query
      let formattedParams = { ...params };

      // Translate Ant Design sort order to OData format
      if (params.sortField && params.sortOrder) {
        formattedParams.sortField = params.sortField;
        // Transform sortOrder from Ant Design format to OData format
        formattedParams.sortOrder =
          params.sortOrder === 'ascend' ? 'asc' : 'desc';
      }

      const response = await questionDraftApi.getQuestions(formattedParams);

      if (response.success) {
        return {
          data: response.data,
          totalCount: response.total,
          pagination: {
            current: params.page || 1,
            pageSize: params.pageSize || 10,
          },
          filters: {
            type: params.type,
            statusEntity: params.status,
            subjectIds: params.subjectIds,
            searchText: params.searchText,
          },
        };
      } else {
        return rejectWithValue(response.error || 'Failed to fetch questions');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const getPaginationInfo = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/getPaginationInfo`,
  async () => {
    try {
      const response = await questionDraftApi.getPaginationInfo();

      if (response.success) {
        return {
          data: response.data,
        };
      }
    } catch (error) {}
  }
);

export const updateQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/updateQuestions`,
  async (questions: QuestionDraft[], { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.updateQuestions(questions);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update questions');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const deleteQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/deleteQuestions`,
  async (questionIds: string[], { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.deleteQuestions(questionIds);
      if (response.success) {
        return questionIds;
      } else {
        return rejectWithValue(response.error || 'Failed to delete questions');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

const questionDataManagerSlice = createSlice({
  name: QUESTION_DATA_SLICE_NAME,
  initialState: {
    questionData: null,
    loading: false,
    error: null,
    totalCount: 0,
    pagination: {
      current: 1,
      pageSize: 10,
    },
    filters: {
      type: undefined,
      statusEntity: undefined,
      subjectIds: undefined,
      searchText: undefined,
    },
  } as QuestionDataManagerState,
  reducers: {
    setQuestionData: (state, action) => {
      state.questionData = action.payload;
    },
    handleChangeQuestion: (state, action) => {
      const { id, question } = action.payload;
      const index = state.questionData?.findIndex((q) => q.id === id);
      if (index !== undefined && state.questionData && index !== -1) {
        state.questionData[index] = question;
      }
    },
    clearQuestionData: (state) => {
      state.questionData = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {
        type: undefined,
        statusEntity: undefined,
        subjectIds: undefined,
        searchText: undefined,
      };
    },
  },
  extraReducers: (builder) => {
    // Handle fetchQuestions
    builder
      .addCase(fetchQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestions.fulfilled, (state, action) => {
        state.loading = false;
        const adapter = questionDraftAdapter;
        state.questionData = adapter.toBaseQuestions(action.payload.data);
        state.pagination = action.payload.pagination;
        state.filters = action.payload.filters;
      })
      .addCase(fetchQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getPaginationInfo.fulfilled, (state, action) => {
        state.totalCount = action.payload?.data?.totalCount;
        if (Array.isArray(action.payload?.data?.subjects))
          state.subjects = action.payload?.data?.subjects?.map((sub: any) =>
            camelCaseKeys<Subject>(sub)
          );
      })
      .addCase(updateQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (Array.isArray(action.payload.data)) {
          state.questionData = action.payload.data;
        }
      })
      .addCase(updateQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Get the IDs of questions to remove
        const deletedIds = action.payload as string[];

        // Update the question data by filtering out deleted questions
        if (state.questionData) {
          state.questionData = state.questionData.filter(
            (question) => !deletedIds.includes(question.id)
          );
        }

        // Update the total count
        if (state.totalCount) {
          state.totalCount = Math.max(0, state.totalCount - deletedIds.length);
        }
      })
      .addCase(deleteQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setQuestionData,
  clearQuestionData,
  handleChangeQuestion,
  setFilters,
  resetFilters,
} = questionDataManagerSlice.actions;
export default questionDataManagerSlice;

import React, { memo, useState, useEffect, useMemo } from 'react';
import '../MiniGameResponsive.css';
import {
  TreasureHuntGameConfig,
  defaultTreasureHuntGameConfig,
} from './TreasureHuntGameConfig';
import TreasureHuntGameComponent from './TreasureHuntGameComponent';
import TreasureHuntGameModal from './TreasureHuntGameModal';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { EngineContainer } from '../../common/engines';
import { ITextProps } from '../../core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { updateTitleAndSync } from '../../../utils/titleSyncUtils';
import { EdTechRootState } from '../../../store/store';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

interface TreasureHuntGameProps extends TreasureHuntGameConfig {}

export const treasureHuntSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    mapUrl: z.string().optional(),
    clues: z
      .array(
        z.object({
          text: z.string(),
          location: z.object({
            lat: z.number(),
            lng: z.number(),
          }),
        })
      )
      .optional(),
    difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  },
});

const TreasureHuntGame: React.FC<IEdTechRenderProps<TreasureHuntGameProps>> = (
  props
) => {
  const { params, path, order, addOrUpdateParamComponent } = props;
  // State for game configuration
  const [gameConfig, setGameConfig] = useState<TreasureHuntGameConfig>({
    ...defaultTreasureHuntGameConfig,
    ...params,
  });

  // State for fullscreen and config modal
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState<boolean>(false);

  // Fix for map interactions in fullscreen mode
  useEffect(() => {
    if (isFullscreen) {
      const styleEl = document.createElement('style');
      styleEl.innerHTML = `
        .leaflet-container {
          z-index: 1 !important;
          pointer-events: auto !important;
        }
        .leaflet-map-pane {
          z-index: 2 !important;
          pointer-events: auto !important;
        }
        .leaflet-google-layer {
          z-index: 1 !important;
        }
        .leaflet-control-container {
          z-index: 400 !important;
          pointer-events: auto !important;
        }
        .leaflet-interactive {
          cursor: pointer !important;
          pointer-events: auto !important;
        }
        /* Ensure the fullscreen container doesn't block events */
        [data-fullscreen="true"] {
          pointer-events: auto !important;
        }
        /* Make sure cards don't block events to map */
        .ant-card .leaflet-container {
          pointer-events: auto !important;
        }
      `;
      styleEl.id = 'map-interaction-fix-styles';
      document.head.appendChild(styleEl);

      return () => {
        const existingStyle = document.getElementById(
          'map-interaction-fix-styles'
        );
        if (existingStyle) {
          document.head.removeChild(existingStyle);
        }
      };
    }
  }, [isFullscreen]);

  // Update configuration when params change (from Redux)
  useEffect(() => {
    if (params) {
      setGameConfig({
        ...defaultTreasureHuntGameConfig,
        ...params,
      });
    }
  }, [params]);

  // Handle game config changes
  const handleConfigSubmit = (config: TreasureHuntGameConfig) => {
    if (addOrUpdateParamComponent) {
      // Preserve titleProps when updating config
      const updatedConfig = {
        ...config,
        titleProps: gameConfig.titleProps,
      };
      addOrUpdateParamComponent(updatedConfig);
      setGameConfig(updatedConfig);
    }

    // Close modal
    setIsConfigModalOpen(false);
  };

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Custom title update handler that also updates local state
  const handleGameTitleUpdate = (titleProps: Partial<ITextProps>) => {
    // Update the title in the game config and local state
    const updatedConfig = updateTitleAndSync(
      path,
      titleProps,
      gameConfig,
      renderTreeData,
      dispatch,
      addOrUpdateParamComponent
    );

    // Update local state
    if (updatedConfig !== gameConfig) {
      setGameConfig(updatedConfig);
    }
  };

  // Handle fullscreen state changes
  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  // Create text props for the title
  const titleProps = gameConfig.titleProps || {
    text: 'Truy Tìm Kho Báu',
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Memoize the structured instruction props
  const instructionsContent = useMemo(
    () => ({
      objective:
        'Làm theo các hướng dẫn để tìm ra vị trí của kho báu trên bản đồ.',
      steps: [
        'Đọc hướng dẫn hiển thị trong khung màu xanh',
        'Đặt marker bằng cách click trực tiếp lên bản đồ để chọn vị trí theo hướng dẫn',
        'Kiểm tra vị trí bằng cách nhấn nút kiểm tra',
        'Nếu đúng, bạn sẽ được chuyển qua hướng dẫn tiếp theo',
        'Nếu khó khăn, bạn có thể sử dụng gợi ý (sẽ bị trừ điểm)',
        'Làm theo các hướng dẫn cho đến khi tìm thấy kho báu',
      ],
      notes: `Vị trí được coi là chính xác khi khoảng cách giữa vị trí bạn chọn và vị trí đúng không quá ${gameConfig.proximityThreshold} km.`,
      isFullscreen: isFullscreen,
    }),
    [gameConfig.proximityThreshold, isFullscreen]
  );

  // Determine if configuration is allowed, default to true
  const allowConfiguration =
    params?.allowConfiguration !== undefined ? params.allowConfiguration : true;

  // Main game component with status badges
  const mainGameComponent = (
    <>
      <TreasureHuntGameComponent
        config={gameConfig}
        isFullscreen={isFullscreen}
      />
    </>
  );

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      allowConfiguration={allowConfiguration}
      onTitleUpdate={handleGameTitleUpdate}
      onFullscreenChange={handleFullscreenChange}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      id={path}
      mainComponent={mainGameComponent}
      configModal={
        <TreasureHuntGameModal
          isOpen={isConfigModalOpen}
          onClose={() => setIsConfigModalOpen(false)}
          onSubmit={handleConfigSubmit}
          defaultConfig={gameConfig}
          path={path}
          order={order}
          addOrUpdateParamComponent={addOrUpdateParamComponent}
        />
      }
      instructionsContent={instructionsContent}
    />
  );
};

export default memo(withEdComponentParams(TreasureHuntGame));

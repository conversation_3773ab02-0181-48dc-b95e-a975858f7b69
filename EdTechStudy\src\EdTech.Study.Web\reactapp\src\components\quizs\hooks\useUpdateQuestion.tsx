import { useEffect, useState } from 'react';

import { useContext } from 'react';
import { BaseQuestion, PracticeEngineContext } from '../practiceEngines';

const useUpdateQuestion = () => {
  const { handleChangeQuestion } = useContext(PracticeEngineContext);
  const [stack, setStack] = useState<BaseQuestion | null>(null);

  const handleUpdateQuestion = (update: BaseQuestion) => {
    setStack(update);
  };

  useEffect(() => {
    let id: any = null;
    if (stack) {
      id = setTimeout(() => {
        handleChangeQuestion({ ...stack, userSelect: undefined });
      }, 0);
    }
    return () => {
      clearTimeout(id);
    };
  }, [stack]);

  return { handleUpdateQuestion };
};

export default useUpdateQuestion;

import React, { useEffect, useState } from 'react';
import { MatchingItemType } from '../../../../interfaces/quizs/questionBase';

interface ConnectionLinesProps {
  leftItems: MatchingItemType[];
  rightItems: MatchingItemType[];
  pairedItems: { [key: string]: { partnerId: string; color: string } };
  containerRef: React.RefObject<HTMLDivElement>;
}

interface LinePosition {
  fromId: string;
  toId: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  width: number;
  angle: number;
  color: string;
}

const ConnectionLines: React.FC<ConnectionLinesProps> = ({
  leftItems,
  rightItems,
  pairedItems,
  containerRef,
}) => {
  const [lines, setLines] = useState<LinePosition[]>([]);

  useEffect(() => {
    const calculateLines = () => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      const newLines: LinePosition[] = [];

      // Process only left items to avoid duplicates
      leftItems.forEach((leftItem) => {
        const pair = pairedItems[leftItem.id];
        if (pair) {
          // Find the DOM elements
          const leftElement = container.querySelector(
            `[data-item-id="${leftItem.id}"]`
          ) as HTMLElement;
          const rightElement = container.querySelector(
            `[data-item-id="${pair.partnerId}"]`
          ) as HTMLElement;

          if (leftElement && rightElement) {
            const leftRect = leftElement.getBoundingClientRect();
            const rightRect = rightElement.getBoundingClientRect();

            // Calculate positions relative to container
            // Start from the right edge of the left item
            const x1 = leftRect.right - containerRect.left;
            // Center vertically on the item
            const y1 = leftRect.top + leftRect.height / 2 - containerRect.top;

            // End at the left edge of the right item
            const x2 = rightRect.left - containerRect.left;
            // Center vertically on the item
            const y2 = rightRect.top + rightRect.height / 2 - containerRect.top;

            // Calculate line properties (length and angle)
            const width = Math.sqrt(
              Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2)
            );
            const angle = Math.atan2(y2 - y1, x2 - x1) * (180 / Math.PI);

            newLines.push({
              fromId: leftItem.id,
              toId: pair.partnerId,
              x1,
              y1,
              x2,
              y2,
              width,
              angle,
              color: pair.color,
            });
          }
        }
      });

      setLines(newLines);
    };

    // Calculate lines initially
    calculateLines();

    // Recalculate when pairs change
    const observer = new MutationObserver(calculateLines);
    if (containerRef.current) {
      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['data-item-id', 'style', 'class'],
      });
    }

    // Recalculate on window resize
    window.addEventListener('resize', calculateLines);

    return () => {
      window.removeEventListener('resize', calculateLines);
      observer.disconnect();
    };
  }, [leftItems, rightItems, pairedItems, containerRef]);

  return (
    <div className="connection-lines-container">
      {lines.map((line, index) => (
        <div
          key={`${line.fromId}-${line.toId}`}
          className="connection-line animate"
          style={{
            width: `${line.width}px`,
            backgroundColor: line.color,
            left: `${line.x1}px`,
            top: `${line.y1}px`,
            transform: `rotate(${line.angle}deg)`,
            transformOrigin: '0 0',
            height: '4px', // Thicker for better visibility
            boxShadow: '0 0 4px rgba(0, 0, 0, 0.3)', // Add a more visible shadow
          }}
        />
      ))}
    </div>
  );
};

export default ConnectionLines;

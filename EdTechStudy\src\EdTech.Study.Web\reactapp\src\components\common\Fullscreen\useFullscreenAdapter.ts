import { useCallback, useState, useRef } from 'react';
import { SizeType } from 'antd/lib/config-provider/SizeContext';
import { FullscreenContainerHandle } from './FullscreenContainer';

interface UseFullscreenAdapterOptions {
  /**
   * Hàm callback khi vào chế độ toàn màn hình
   */
  onEnterFullscreen?: () => void;

  /**
   * Hàm callback khi thoát chế độ toàn màn hình
   */
  onExitFullscreen?: () => void;

  /**
   * Trạng thái toàn màn hình ban đầu
   * @default false
   */
  initialFullscreen?: boolean;
}

/**
 * Hook được cải tiến giúp quản lý state fullscreen và cung cấp các phương thức hỗ trợ
 * cho việc thiết kế giao diện thích ứng theo trạng thái fullscreen
 *
 * @param {UseFullscreenAdapterOptions} options Cá<PERSON> tùy chọn cho adapter
 * @returns {Object} State và các hàm hỗ trợ
 */
const useFullscreenAdapter = (options: UseFullscreenAdapterOptions = {}) => {
  const {
    onEnterFullscreen,
    onExitFullscreen,
    initialFullscreen = false,
  } = options;

  // State và ref để quản lý chế độ toàn màn hình
  const [isFullscreen, setIsFullscreen] = useState<boolean>(initialFullscreen);
  const fullscreenRef = useRef<FullscreenContainerHandle>(null);

  // Xử lý khi chuyển chế độ toàn màn hình
  const handleToggleFullscreen = useCallback(() => {
    if (fullscreenRef.current) {
      fullscreenRef.current.toggleFullscreen();
      // Cập nhật state sau một khoảng thời gian ngắn để đảm bảo trạng thái đã thay đổi
      setTimeout(() => {
        if (fullscreenRef.current) {
          setIsFullscreen(fullscreenRef.current.isFullscreen);
        }
      }, 100);
    }
  }, []);

  // Xử lý khi vào chế độ toàn màn hình
  const handleEnterFullscreen = useCallback(() => {
    setIsFullscreen(true);
    if (onEnterFullscreen) {
      onEnterFullscreen();
    }
  }, [onEnterFullscreen]);

  // Xử lý khi thoát chế độ toàn màn hình
  const handleExitFullscreen = useCallback(() => {
    setIsFullscreen(false);
    if (onExitFullscreen) {
      onExitFullscreen();
    }
  }, [onExitFullscreen]);

  /**
   * Lấy giá trị kích thước phù hợp dựa trên trạng thái toàn màn hình
   * Hỗ trợ nhiều kiểu dữ liệu bao gồm số và chuỗi
   *
   * @template T Kiểu dữ liệu của giá trị kích thước (số hoặc chuỗi hoặc SizeType)
   * @param {T} normalSize Kích thước ở chế độ bình thường
   * @param {T} fullscreenSize Kích thước ở chế độ toàn màn hình
   * @returns {T} Kích thước phù hợp dựa trên trạng thái hiện tại
   */
  function getResponsiveSize<T>(normalSize: T, fullscreenSize: T): T {
    return isFullscreen ? fullscreenSize : normalSize;
  }

  /**
   * Hàm chuyên biệt cho các thuộc tính kích thước của Ant Design
   *
   * @param {SizeType} normalSize Kích thước ở chế độ bình thường ('small', 'middle', 'large')
   * @param {SizeType} fullscreenSize Kích thước ở chế độ toàn màn hình
   * @returns {SizeType} Kích thước phù hợp dựa trên trạng thái hiện tại
   */
  const getResponsiveSizeAnt = useCallback(
    (normalSize: SizeType, fullscreenSize: SizeType): SizeType => {
      return isFullscreen ? fullscreenSize : normalSize;
    },
    [isFullscreen]
  );

  /**
   * Lấy tên lớp CSS phù hợp dựa trên trạng thái toàn màn hình
   *
   * @param {string} normalClass Lớp CSS sử dụng ở chế độ bình thường
   * @param {string} fullscreenClass Lớp CSS sử dụng ở chế độ toàn màn hình
   * @returns {string} Tên lớp CSS phù hợp dựa trên trạng thái hiện tại
   */
  const getResponsiveClass = useCallback(
    (normalClass: string, fullscreenClass: string): string => {
      return isFullscreen ? fullscreenClass : normalClass;
    },
    [isFullscreen]
  );

  /**
   * Lấy kích thước font chữ với đơn vị phù hợp
   *
   * @param {number} normalSize Kích thước cơ bản khi không ở chế độ toàn màn hình
   * @param {number} fullscreenSize Kích thước khi ở chế độ toàn màn hình
   * @param {string} unit Đơn vị CSS sử dụng (px, rem, em, v.v.)
   * @returns {string} Giá trị font-size hoàn chỉnh với đơn vị
   */
  const getResponsiveFontSize = useCallback(
    (normalSize: number, fullscreenSize: number, unit = 'rem'): string => {
      const size = isFullscreen ? fullscreenSize : normalSize;
      return `${size}${unit}`;
    },
    [isFullscreen]
  );

  /**
   * Tạo đối tượng style với các thuộc tính phản hồi theo trạng thái toàn màn hình
   *
   * @param {Object} normalStyles Styles cho chế độ bình thường
   * @param {Object} fullscreenStyles Styles cho chế độ toàn màn hình
   * @returns {Object} Đối tượng style kết hợp phù hợp với trạng thái hiện tại
   */
  const getResponsiveStyles = useCallback(
    (
      normalStyles: React.CSSProperties,
      fullscreenStyles: React.CSSProperties
    ): React.CSSProperties => {
      return isFullscreen
        ? { ...normalStyles, ...fullscreenStyles }
        : normalStyles;
    },
    [isFullscreen]
  );

  // Trả về state và các hàm hỗ trợ
  return {
    // State và refs
    isFullscreen,
    fullscreenRef,

    // Các hàm xử lý trạng thái
    setIsFullscreen,
    handleToggleFullscreen,
    handleEnterFullscreen,
    handleExitFullscreen,

    // Các hàm hỗ trợ thiết kế responsive
    getResponsiveSize,
    getResponsiveSizeAnt,
    getResponsiveClass,
    getResponsiveFontSize,
    getResponsiveStyles,
  };
};

export default useFullscreenAdapter;

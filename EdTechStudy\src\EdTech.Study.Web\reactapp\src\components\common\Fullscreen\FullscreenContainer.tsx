import React, {
  forwardRef,
  ReactNode,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from 'react';
import {
  isFullscreenSupported,
  getFullscreenElement,
  requestFullscreen,
  exitFullscreen as exitFullscreenUtil,
  addFullscreenChangeListener,
  removeFullscreenChangeListener,
} from './fullscreenUtils';

export interface FullscreenContainerHandle {
  toggleFullscreen: () => void;
  enterFullscreen: () => void;
  exitFullscreen: () => void;
  isFullscreen: boolean;
}

interface FullscreenContainerProps {
  /**
   * Content to be rendered within the container
   */
  children: ReactNode;

  /**
   * Additional CSS classes to add to the container
   */
  className?: string;

  /**
   * Whether to expose fullscreen controls
   * @default false
   */
  exposeControls?: boolean;

  /**
   * CSS classes to apply when in fullscreen mode using CSS fallback
   */
  fullscreenClasses?: string;

  /**
   * Additional inline styles to apply to the container
   */
  style?: React.CSSProperties;

  /**
   * Test ID for automated testing
   */
  testId?: string;

  /**
   * Callback when entering fullscreen
   */
  onEnterFullscreen?: () => void;

  /**
   * Callback when exiting fullscreen
   */
  onExitFullscreen?: () => void;
}

/**
 * A container component that handles styling for fullscreen and normal modes
 * Supports both Fullscreen API and CSS-based fallback
 */
const FullscreenContainer = forwardRef<
  FullscreenContainerHandle,
  FullscreenContainerProps
>(
  (
    {
      children,
      className = '',
      fullscreenClasses = '',
      style,
      testId = 'fullscreen-container',
      onEnterFullscreen,
      onExitFullscreen,
    },
    ref
  ) => {
    // Create a ref for the container element
    const containerRef = useRef<HTMLDivElement>(null);

    // State to track fullscreen status
    const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

    // Check if browser supports Fullscreen API
    const isSupported = isFullscreenSupported();

    // State to track whether we're using CSS fallback
    const [usingCssFallback, setUsingCssFallback] = useState<boolean>(
      !isSupported
    );

    // Handle entering fullscreen
    const enterFullscreen = () => {
      if (isSupported && containerRef.current) {
        requestFullscreen(containerRef.current).catch((err: any) => {
          console.error(`Error entering fullscreen:`, err);

          // Fallback to CSS approach
          setUsingCssFallback(true);
          setIsFullscreen(true);
        });
      } else {
        setUsingCssFallback(true);
        setIsFullscreen(true);
      }

      if (onEnterFullscreen) {
        onEnterFullscreen();
      }
    };

    // Handle exiting fullscreen
    const exitFullscreen = () => {
      if (isSupported && getFullscreenElement()) {
        exitFullscreenUtil().catch((err: any) => {
          console.error(`Error exiting fullscreen:`, err);

          // Fallback to CSS approach
          setUsingCssFallback(true);
          setIsFullscreen(false);
        });
      } else if (usingCssFallback) {
        setIsFullscreen(false);
      }

      if (onExitFullscreen) {
        onExitFullscreen();
      }
    };

    // Toggle fullscreen state
    const toggleFullscreen = () => {
      if (isFullscreen) {
        exitFullscreen();
      } else {
        enterFullscreen();
      }
    };

    // Monitor fullscreen changes when using the Fullscreen API
    useEffect(() => {
      if (isSupported) {
        const handleFullscreenChange = () => {
          const isInFullscreen = !!getFullscreenElement();
          setIsFullscreen(isInFullscreen);

          if (isInFullscreen && onEnterFullscreen) {
            onEnterFullscreen();
          } else if (!isInFullscreen && onExitFullscreen) {
            onExitFullscreen();
          }
        };

        // Add event listeners for all vendor prefixed events
        addFullscreenChangeListener(handleFullscreenChange);

        return () => {
          removeFullscreenChangeListener(handleFullscreenChange);
        };
      }
    }, [isSupported, onEnterFullscreen, onExitFullscreen]);

    // Handle body overflow style when using CSS fallback
    useEffect(() => {
      if (usingCssFallback && isFullscreen) {
        document.body.style.overflow = 'hidden';
      } else if (usingCssFallback) {
        document.body.style.overflow = '';
      }

      return () => {
        if (usingCssFallback) {
          document.body.style.overflow = '';
        }
      };
    }, [usingCssFallback, isFullscreen]);

    // Expose fullscreen methods through ref
    useImperativeHandle(ref, () => ({
      toggleFullscreen,
      enterFullscreen,
      exitFullscreen,
      isFullscreen,
    }));

    // Apply classes based on fullscreen state
    const combinedClasses = `${className} ${
      isFullscreen
        ? fullscreenClasses + 'tailwind-p-0 tailwind-overflow-auto'
        : 'tailwind-p-0 tailwind-overflow-auto'
    }`;

    // Apply fullscreen styles including min-height: 100vh when in fullscreen mode
    const combinedStyles = {
      ...(style || {}),
      ...(isFullscreen ? { minHeight: '100vh' } : {}),
    };

    return (
      <div
        ref={containerRef}
        className={combinedClasses}
        style={combinedStyles}
        data-fullscreen={isFullscreen}
        data-testid={testId}
      >
        {children}
      </div>
    );
  }
);

FullscreenContainer.displayName = 'FullscreenContainer';

export default FullscreenContainer;

import { useCallback } from 'react';
import { message } from 'antd';
import { useDispatch } from 'react-redux';
import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';
import { updateItems } from '../../../../../store/slices/AppSlices/EdTechRenderDataSlice';
import { findNodeById } from '../../../../../utils/treeUtils';
import {
  findParentAndItems,
  updateChildPaths,
  updateItemOrders,
  calculateInsertIndex,
  findParentNode,
  buildPath,
  findNodeIndex,
  validateDragAndDrop,
} from '../utils/dragDropUtils';
import { deepCopy, canNodeHaveChildren } from '../utils/treeUtils';

/**
 * Custom hook for drag and drop functionality
 */
export const useDragDrop = (
  root?: IEdTechRenderTreeData,
  editingItemKey: string | null = null,
  expandedKeys: string[] = [],
  setExpandedKeys: (keys: string[]) => void = () => {},
  setAutoExpandParent: (value: boolean) => void = () => {}
) => {
  const dispatch = useDispatch();

  /**
   * Move an item in the tree
   */
  const moveItem = useCallback(
    (
      _dragIndex: number,
      hoverIndex: number,
      dragPath: string[],
      dropPath: string[],
      dropPosition: 'top' | 'bottom' | 'child' | null = null
    ) => {
      if (!root || !root.subItems) return;

      const treeDataCopy = deepCopy(root.subItems);

      const { parentNode: dragParentNode, items: dragItems } =
        findParentAndItems(treeDataCopy, dragPath);
      const { parentNode: dropParentNode, items: dropItems } =
        findParentAndItems(treeDataCopy, dropPath);

      const dragItemId = dragPath[dragPath.length - 1];
      const dropItemId = dropPath[dropPath.length - 1];
      const dragItem = findNodeById(treeDataCopy, dragItemId);
      const dropItem = findNodeById(treeDataCopy, dropItemId);

      if (!dragItem) {
        console.error('Không tìm thấy mục cần di chuyển:', dragItemId);
        return;
      }

      // Validate the drag and drop operation
      const validation = validateDragAndDrop(
        dragItem,
        dropItem,
        dropParentNode,
        dropPosition
      );

      if (!validation.isValid) {
        message.error(validation.errorMessage);
        return;
      }

      if (dropPosition === 'child') {
        handleMoveAsChild(treeDataCopy, dragItem, dragItems, dropPath);
      } else if (dragParentNode?.id === dropParentNode?.id) {
        handleMoveWithinSameParent(dragItem, dragItems, hoverIndex);
      } else {
        handleMoveBetweenParents(
          dragItem,
          dragItems,
          dropItems,
          dropParentNode || root,
          hoverIndex,
          dropPosition
        );
      }
    },
    [root, dispatch]
  );

  /**
   * Handle moving an item to become a child of another item
   */
  const handleMoveAsChild = useCallback(
    (
      treeDataCopy: IEdTechRenderTreeData[],
      dragItem: IEdTechRenderTreeData,
      dragItems: IEdTechRenderTreeData[],
      dropPath: string[]
    ): void => {
      const targetParentNode = findNodeById(
        treeDataCopy,
        dropPath[dropPath.length - 1]
      );

      if (!targetParentNode) {
        console.error('Không tìm thấy node cha đích');
        return;
      }

      if (!canNodeHaveChildren(targetParentNode)) {
        console.error('Node cha đích không thể có các node con');
        return;
      }

      // Validate the operation again to be sure
      const validation = validateDragAndDrop(
        dragItem,
        targetParentNode,
        null,
        'child'
      );

      if (!validation.isValid) {
        message.error(validation.errorMessage);
        return;
      }

      if (!targetParentNode.subItems) {
        targetParentNode.subItems = [];
      }

      // Remove the item from its original parent
      const newDragParentItems = dragItems.filter(
        (item) => item.id !== dragItem.id
      );

      // Update order of items in the original parent
      updateItemOrders(newDragParentItems);

      const oldPath = dragItem.path || dragItem.id;

      // Create a deep copy of the drag item to avoid reference issues
      const updatedDragItem = JSON.parse(JSON.stringify(dragItem));
      updatedDragItem.path = targetParentNode.path
        ? `${targetParentNode.path}/${dragItem.id}`
        : dragItem.id;
      updatedDragItem.order = targetParentNode.subItems.length;

      // Add the item to its new parent
      targetParentNode.subItems.push(updatedDragItem);

      // Update paths of all child items
      const childUpdates = updateChildPaths(
        updatedDragItem,
        updatedDragItem.path
      );

      // Create update objects for Redux
      const updatedItems: { path: string; data: any }[] = [];

      // Add updates for the original parent's children
      newDragParentItems.forEach((item) => {
        updatedItems.push({
          path: item.path || item.id,
          data: { order: item.order },
        });
      });

      // Add update for the dragged item
      updatedItems.push({
        path: oldPath,
        data: {
          path: updatedDragItem.path,
          order: updatedDragItem.order,
        },
      });

      // Add updates for all children
      childUpdates.forEach((update) => {
        updatedItems.push({
          path: update.oldPath,
          data: { path: update.newPath },
        });
      });

      // Dispatch the updates to Redux
      dispatch(updateItems({ items: updatedItems }));
    },
    [dispatch]
  );

  /**
   * Handle moving an item within the same parent
   */
  const handleMoveWithinSameParent = useCallback(
    (
      dragItem: IEdTechRenderTreeData,
      dragItems: IEdTechRenderTreeData[],
      hoverIndex: number
    ): void => {
      const updatedDragItem = JSON.parse(JSON.stringify(dragItem));

      // Remove the item from its original position
      const newItems = dragItems.filter((item) => item.id !== dragItem.id);

      // Calculate where to insert it
      const insertIndex = calculateInsertIndex(hoverIndex);

      // Insert the item at the new position
      newItems.splice(insertIndex, 0, updatedDragItem);

      // Update the order of all items in the parent
      updateItemOrders(newItems);

      // Create update objects for Redux
      const updatedItems = newItems.map((item) => ({
        path: item.path || item.id,
        data: { order: item.order },
      }));

      // Dispatch the updates to Redux
      dispatch(updateItems({ items: updatedItems }));
    },
    [dispatch]
  );

  /**
   * Handle moving an item between different parents
   */
  const handleMoveBetweenParents = useCallback(
    (
      dragItem: IEdTechRenderTreeData,
      dragItems: IEdTechRenderTreeData[],
      dropItems: IEdTechRenderTreeData[],
      dropParentNode: IEdTechRenderTreeData | null,
      hoverIndex: number,
      dropPosition: 'top' | 'bottom' | 'child' | null
    ): void => {
      // Validate the operation again to be sure
      const validation = validateDragAndDrop(
        dragItem,
        null,
        dropParentNode,
        dropPosition
      );

      if (!validation.isValid) {
        message.error(validation.errorMessage);
        return;
      }

      // Remove the item from its original parent
      const newDragParentItems = dragItems.filter(
        (item) => item.id !== dragItem.id
      );

      // Update order of items in the original parent
      updateItemOrders(newDragParentItems);

      // Create a deep copy of the drag item to avoid reference issues
      const updatedDragItem = JSON.parse(JSON.stringify(dragItem));

      const oldPath = dragItem.path || dragItem.id;

      // Update the path of the dragged item based on its new parent
      if (dropParentNode) {
        updatedDragItem.path = dropParentNode.path
          ? `${dropParentNode.path}/${dragItem.id}`
          : dragItem.id;
      } else {
        updatedDragItem.path = dragItem.id;
      }

      // Add the item to its new parent
      const newDropItems = [...dropItems];
      const insertIndex = calculateInsertIndex(hoverIndex);
      newDropItems.splice(insertIndex, 0, updatedDragItem);

      // Update the order of all items in the new parent
      updateItemOrders(newDropItems);

      // Update paths of all child items
      const childUpdates = updateChildPaths(
        updatedDragItem,
        updatedDragItem.path
      );

      // Create update objects for Redux
      const updatedItems: { path: string; data: any }[] = [];

      // Add updates for the original parent's children
      newDragParentItems.forEach((item) => {
        updatedItems.push({
          path: item.path || item.id,
          data: { order: item.order },
        });
      });

      // Add update for the dragged item
      updatedItems.push({
        path: oldPath,
        data: {
          path: updatedDragItem.path,
          order: updatedDragItem.order,
        },
      });

      // Add updates for all items in the new parent
      newDropItems.forEach((item) => {
        if (item.id !== dragItem.id) {
          updatedItems.push({
            path: item.path || item.id,
            data: { order: item.order },
          });
        }
      });

      // Add updates for all children of the dragged item
      childUpdates.forEach((update) => {
        updatedItems.push({
          path: update.oldPath,
          data: { path: update.newPath },
        });
      });

      // Dispatch the updates to Redux
      dispatch(updateItems({ items: updatedItems }));
    },
    [dispatch]
  );

  /**
   * Handle the drop event from the tree component
   */
  const handleDrop = useCallback(
    (info: any) => {
      const dropKey = info.node.key;
      const dragKey = info.dragNode.key;
      const dropPos = info.node.pos.split('-');
      const dropPositionRelative =
        info.dropPosition - Number(dropPos[dropPos.length - 1]);

      if (!root || !root.subItems) return;

      // Check if we're currently editing an item
      if (editingItemKey !== null) {
        message.error('Không thể kéo thả các mục khi đang chỉnh sửa');
        return;
      }

      // Check if we're trying to drop an item onto itself
      if (dragKey === dropKey) {
        message.error('Không thể thả một mục lên chính nó');
        return;
      }

      // Find the nodes involved in the operation
      const dragNode = findNodeById(root.subItems, dragKey);
      const dropNode = findNodeById(root.subItems, dropKey);
      if (!dragNode || !dropNode) {
        console.error('Không tìm thấy node kéo hoặc thả');
        return;
      }

      // Build paths to the nodes
      const dragPath = buildPath(root.subItems || [], dragKey);
      const dropPath = buildPath(root.subItems || [], dropKey);
      if (!dragPath || !dropPath) {
        console.error('Không thể xây dựng đường dẫn cho node kéo hoặc thả');
        return;
      }

      // Check if we're trying to move a parent into one of its children
      if (dropPath.includes(dragKey)) {
        message.error(
          'Không thể di chuyển một node cha vào bên trong node con của nó'
        );
        return;
      }

      let dropPositionType: 'top' | 'bottom' | 'child' | null = null;
      let hoverIndex = 0;

      // Determine the drop position and calculate the hover index
      if (info.dropToGap) {
        // Dropping before or after a node
        dropPositionType = dropPositionRelative === -1 ? 'top' : 'bottom';

        const dropParent =
          findParentNode(root.subItems || [], dropKey) ||
          (root as IEdTechRenderTreeData);

        const dropItems = dropParent
          ? dropParent.subItems || []
          : root.subItems || [];

        hoverIndex = findNodeIndex(dropItems, dropKey);

        if (hoverIndex === -1) {
          console.error('Không tìm thấy chỉ mục của node thả');
          return;
        }

        if (dropPositionType === 'bottom') {
          hoverIndex = Math.min(dropItems.length, hoverIndex + 1);
        }

        // Adjust hover index if the drag and drop items have the same parent
        const dragParent = findParentNode(root.subItems || [], dragKey);
        if (dragParent?.id === dropParent?.id && dragKey !== dropKey) {
          const dragIndex = findNodeIndex(dropItems, dragKey);
          if (dragIndex < hoverIndex) {
            hoverIndex -= 1;
          }
        }
      } else {
        // Dropping onto a node (as a child)
        dropPositionType = 'child';

        if (!canNodeHaveChildren(dropNode)) {
          message.error('Mục này không thể chứa các mục con');
          return;
        }

        hoverIndex = dropNode.subItems ? dropNode.subItems.length : 0;
      }

      // Find the parent of the drag node
      const dragParent = findParentNode(root.subItems || [], dragKey);
      const dragItems = dragParent
        ? dragParent.subItems || []
        : root.subItems || [];
      const dragIndex = findNodeIndex(dragItems, dragKey);

      // Save the expanded keys before making changes
      const currentExpandedKeys = [...expandedKeys];

      // Perform the move operation
      moveItem(dragIndex, hoverIndex, dragPath, dropPath, dropPositionType);

      // Add the drop key to expanded keys if we're dropping as a child and it's not already expanded
      if (
        dropPositionType === 'child' &&
        !currentExpandedKeys.includes(dropKey)
      ) {
        currentExpandedKeys.push(dropKey);
      }

      // Restore expanded keys after the drop
      setExpandedKeys(currentExpandedKeys);
      setAutoExpandParent(false);
    },
    [
      root,
      editingItemKey,
      expandedKeys,
      moveItem,
      setExpandedKeys,
      setAutoExpandParent,
    ]
  );

  return {
    handleDrop,
    moveItem,
  };
};

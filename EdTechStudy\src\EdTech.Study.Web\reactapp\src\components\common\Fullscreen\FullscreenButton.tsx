import React from 'react';
import { <PERSON><PERSON>, <PERSON>lt<PERSON>, ButtonProps } from 'antd';
import {
  FullScreenIcon,
  FullScreenMaximizeOutlinedIcon,
} from '../../icons/IconRegister';

interface FullscreenButtonProps
  extends Omit<ButtonProps, 'onClick' | 'icon' | 'style'> {
  /**
   * Custom style for the button
   */
  style?: React.CSSProperties;
  /**
   * Current fullscreen state
   */
  isFullscreen: boolean;

  /**
   * Function to toggle fullscreen state
   */
  toggleFullscreen: () => void;

  /**
   * Custom tooltip text for entering fullscreen
   * @default 'Toàn màn hình'
   */
  enterFullscreenText?: string;

  /**
   * Custom tooltip text for exiting fullscreen
   * @default 'Thoát toàn màn hình'
   */
  exitFullscreenText?: string;

  /**
   * Custom icon for enter fullscreen button (overrides default)
   */
  enterIcon?: React.ReactNode;

  /**
   * Custom icon for exit fullscreen button (overrides default)
   */
  exitIcon?: React.ReactNode;

  /**
   * Whether to disable tooltip
   * @default false
   */
  disableTooltip?: boolean;

  /**
   * Callback when button is clicked
   */
  onButtonClick?: () => void;

  /**
   * Test ID for automated testing
   */
  testId?: string;
}

/**
 * Reusable fullscreen toggle button component with Ant Design styling
 * Supports all standard Ant Design Button props
 */
const FullscreenButton: React.FC<FullscreenButtonProps> = ({
  isFullscreen,
  toggleFullscreen,
  enterFullscreenText = 'Toàn màn hình',
  exitFullscreenText = 'Thoát toàn màn hình',
  enterIcon,
  exitIcon,
  disableTooltip = false,
  onButtonClick,
  testId = 'fullscreen-button',
  shape = 'default',
  size = 'middle',
  className = '',
  style,
  ...buttonProps
}) => {
  // Determine which icon to display
  const icon = isFullscreen
    ? exitIcon || <FullScreenMaximizeOutlinedIcon />
    : enterIcon || <FullScreenIcon />;

  // Handle click event
  const handleClick = () => {
    toggleFullscreen();
    if (onButtonClick) {
      onButtonClick();
    }
  };

  // Get tooltip text based on fullscreen state
  const tooltipText = isFullscreen ? exitFullscreenText : enterFullscreenText;

  // Render button with or without tooltip
  const button = (
    <Button
      type="primary"
      shape={shape}
      size={size}
      icon={icon}
      onClick={handleClick}
      className={`${className}`}
      data-testid={testId}
      aria-label={tooltipText}
      style={style}
      {...buttonProps}
    />
  );

  // Return button with tooltip if tooltip is enabled
  return disableTooltip ? (
    button
  ) : (
    <Tooltip title={tooltipText}>{button}</Tooltip>
  );
};

export default FullscreenButton;

import { MapPosition } from '../../common/MapCommonComponent/MapUtils';
import { ITextProps } from '../../core/title/CoreTitle';

// Game configuration
export interface TreasureHuntGameConfig {
  titleProps: ITextProps; // Title properties for styling
  description?: string; // Game description
  initialPosition: MapPosition; // Starting position
  treasurePosition: MapPosition; // Treasure location
  clues: TreasureClue[]; // List of clues to find the treasure
  difficultyLevel: 'easy' | 'medium' | 'hard'; // Game difficulty
  timeLimit?: number; // Time limit (seconds)
  hintPenalty?: number; // Points deducted when using hints
  initialZoom: number; // Initial map zoom level
  proximityThreshold: number; // Distance (km) to count as found
  mapOptions?: any; // Additional map options
  allowConfiguration?: boolean; // Allow configuration changes
}

// Clue type definition
export interface TreasureClue {
  type: 'direction' | 'coordinates' | 'latitudinal' | 'longitudinal';
  value: string;
  distance?: number; // distance (km) or degrees
  direction?: 'N' | 'NE' | 'E' | 'SE' | 'S' | 'SW' | 'W' | 'NW';
  hint?: string;
}

// Result for each step
export interface StepResult {
  clue: TreasureClue;
  userPosition: MapPosition;
  distance: number;
  correct: boolean;
  hintsUsed: number;
}

// Overall game result
export interface TreasureHuntResult {
  found: boolean;
  totalSteps: number;
  stepsCompleted: number;
  hintsUsed: number;
  timeTaken?: number;
  finalDistance: number;
  stepResults: StepResult[];
  score: number;
}

export interface TreasureHuntGameProps {
  config: TreasureHuntGameConfig;
  onGameComplete?: (result: TreasureHuntResult) => void;
  onScoreChange?: (newScore: number) => void;
}

// Default game configuration
export const defaultTreasureHuntGameConfig: TreasureHuntGameConfig = {
  description:
    'Khám phá các địa điểm nổi tiếng ở Hà Nội qua các gợi ý hấp dẫn. Hãy theo dõi các chỉ dẫn để tìm ra kho báu bí ẩn!',
  titleProps: {
    text: 'Truy Tìm Kho Báu',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  initialPosition: { lat: 21.0285, lng: 105.8542 }, // Hồ Hoàn Kiếm, Hà Nội
  treasurePosition: { lat: 21.0379, lng: 105.8339 }, // Văn Miếu Quốc Tử Giám
  clues: [
    {
      type: 'direction',
      value: 'Đi về phía Tây 300m',
      direction: 'W',
      distance: 0.3,
      hint: 'Đây là con đường nổi tiếng với nhiều cửa hiệu cà phê và thời trang',
    },
    {
      type: 'direction',
      value: 'Tiếp tục đi về phía Tây Nam 500m',
      direction: 'SW',
      distance: 0.5,
      hint: 'Hướng đến một khu vực có nhiều trường đại học',
    },
    {
      type: 'coordinates',
      value: '21.0379,105.8339',
      hint: 'Đây là nơi tôn vinh học vấn và khoa bảng',
    },
  ],
  difficultyLevel: 'medium',
  timeLimit: 300,
  hintPenalty: 5,
  initialZoom: 15, // Zoom gần hơn để nhìn rõ Hà Nội
  proximityThreshold: 0.1, // Giảm ngưỡng xuống 100m để phù hợp với khu vực thành phố
  allowConfiguration: true,
  mapOptions: {
    scrollWheelZoom: true,
    doubleClickZoom: false,
  },
};

// Sample data sets
export const sampleTreasureHuntGameConfigs: Record<
  string,
  TreasureHuntGameConfig
> = {
  'hanoi-landmarks': {
    titleProps: {
      text: 'Khám Phá Hà Nội',
      fontSize: 24,
      align: 'left',
      bold: true,
    },
    description:
      'Hành trình khám phá các địa danh nổi tiếng tại Thủ đô nghìn năm văn hiến.',
    initialPosition: { lat: 21.0285, lng: 105.8542 }, // Hồ Hoàn Kiếm
    treasurePosition: { lat: 21.0482, lng: 105.8458 }, // Chùa Trấn Quốc
    clues: [
      {
        type: 'direction',
        value: 'Đi về phía Bắc 500m',
        direction: 'N',
        distance: 0.5,
        hint: 'Hướng tới khu phố cổ',
      },
      {
        type: 'direction',
        value: 'Rẽ về phía Tây 300m',
        direction: 'W',
        distance: 0.3,
        hint: 'Tiếp tục đi qua các con phố nhỏ',
      },
      {
        type: 'direction',
        value: 'Đi về phía Bắc 400m',
        direction: 'N',
        distance: 0.4,
        hint: 'Hướng tới Hồ Tây',
      },
      {
        type: 'direction',
        value: 'Đi dọc theo bờ hồ 600m',
        direction: 'NW',
        distance: 0.6,
        hint: 'Một ngôi chùa cổ đang chờ đón bạn',
      },
    ],
    difficultyLevel: 'medium',
    timeLimit: 400,
    hintPenalty: 5,
    initialZoom: 15,
    proximityThreshold: 0.1,
    mapOptions: {
      scrollWheelZoom: true,
      doubleClickZoom: false,
    },
  },
  'hanoi-museums': {
    titleProps: {
      text: 'Bảo Tàng Hà Nội',
      fontSize: 24,
      align: 'left',
      bold: true,
    },
    description:
      'Tìm hiểu lịch sử và văn hóa Hà Nội qua các bảo tàng nổi tiếng.',
    initialPosition: { lat: 21.0285, lng: 105.8542 }, // Hồ Hoàn Kiếm
    treasurePosition: { lat: 21.0358, lng: 105.8344 }, // Bảo tàng Lịch sử Quốc gia
    clues: [
      {
        type: 'direction',
        value: 'Đi về phía Tây 200m',
        direction: 'W',
        distance: 0.2,
        hint: 'Đi theo hướng nhà thờ lớn',
      },
      {
        type: 'direction',
        value: 'Rẽ về phía Nam 300m',
        direction: 'S',
        distance: 0.3,
        hint: 'Đi theo đường Tràng Tiền',
      },
      {
        type: 'direction',
        value: 'Rẽ phải và đi 250m',
        direction: 'W',
        distance: 0.25,
        hint: 'Nơi lưu giữ nhiều hiện vật lịch sử quý giá',
      },
    ],
    difficultyLevel: 'easy',
    timeLimit: 300,
    hintPenalty: 3,
    initialZoom: 16,
    proximityThreshold: 0.1,
    mapOptions: {
      scrollWheelZoom: true,
      doubleClickZoom: false,
    },
  },

  'old-quarter': {
    titleProps: {
      text: 'Khám Phá Phố Cổ',
      fontSize: 24,
      align: 'left',
      bold: true,
    },
    description: 'Dạo quanh 36 phố phường của khu phố cổ Hà Nội lịch sử.',
    initialPosition: { lat: 21.0285, lng: 105.8542 }, // Hồ Hoàn Kiếm
    treasurePosition: { lat: 21.0358, lng: 105.85 }, // Đền Bạch Mã
    clues: [
      {
        type: 'direction',
        value: 'Đi về phía Bắc 300m',
        direction: 'N',
        distance: 0.3,
        hint: 'Hướng tới khu phố cổ',
      },
      {
        type: 'direction',
        value: 'Rẽ phải và đi 200m',
        direction: 'E',
        distance: 0.2,
        hint: 'Đi vào con phố bán hàng thủ công mỹ nghệ',
      },
      {
        type: 'direction',
        value: 'Rẽ trái và đi 150m',
        direction: 'N',
        distance: 0.15,
        hint: 'Tìm ngôi đền cổ nhất Hà Nội',
      },
      {
        type: 'coordinates',
        value: '21.0358,105.8500',
        hint: 'Ngôi đền thờ thần bạch mã',
      },
    ],
    difficultyLevel: 'hard',
    timeLimit: 400,
    hintPenalty: 5,
    initialZoom: 16,
    proximityThreshold: 0.05,
    mapOptions: {
      scrollWheelZoom: true,
      doubleClickZoom: false,
    },
  },

  'vietnam-cities': {
    titleProps: {
      text: 'Các Thành Phố Việt Nam',
      fontSize: 24,
      align: 'left',
      bold: true,
    },
    description: 'Hành trình khám phá các thành phố lớn của Việt Nam.',
    initialPosition: { lat: 21.0285, lng: 105.8542 }, // Hanoi
    treasurePosition: { lat: 10.8231, lng: 106.6297 }, // Ho Chi Minh City
    clues: [
      {
        type: 'direction',
        value: 'Đi về phía Nam 300km',
        direction: 'S',
        distance: 300,
        hint: 'Đến thành phố Vinh',
      },
      {
        type: 'direction',
        value: 'Tiếp tục đi về phía Nam 300km',
        direction: 'S',
        distance: 300,
        hint: 'Đến thành phố Đà Nẵng',
      },
      {
        type: 'direction',
        value: 'Đi về phía Nam 400km',
        direction: 'S',
        distance: 400,
        hint: 'Đến thành phố Nha Trang',
      },
      {
        type: 'direction',
        value: 'Đi về phía Nam 300km',
        direction: 'S',
        distance: 300,
        hint: 'Hướng tới thành phố lớn nhất Việt Nam',
      },
    ],
    difficultyLevel: 'medium',
    timeLimit: 600,
    hintPenalty: 5,
    initialZoom: 6,
    proximityThreshold: 20,
    mapOptions: {
      scrollWheelZoom: true,
      doubleClickZoom: false,
    },
  },

  'world-capitals': {
    titleProps: {
      text: 'Thủ Đô Trên Thế Giới',
      fontSize: 24,
      align: 'left',
      bold: true,
    },
    description: 'Khám phá các thủ đô nổi tiếng trên khắp thế giới.',
    initialPosition: { lat: 21.0285, lng: 105.8542 }, // Hanoi
    treasurePosition: { lat: 55.7558, lng: 37.6173 }, // Moscow
    clues: [
      {
        type: 'direction',
        value: 'Di chuyển về phía Tây Bắc 3000km',
        direction: 'NW',
        distance: 3000,
        hint: 'Hướng tới Đông Âu',
      },
      {
        type: 'direction',
        value: 'Tiếp tục di chuyển về phía Tây Bắc 2000km',
        direction: 'NW',
        distance: 2000,
        hint: 'Đi qua biên giới các quốc gia Đông Âu',
      },
      {
        type: 'direction',
        value: 'Di chuyển về phía Đông Bắc 1000km',
        direction: 'NE',
        distance: 1000,
        hint: 'Hướng tới thủ đô lớn nhất Châu Âu',
      },
    ],
    difficultyLevel: 'hard',
    timeLimit: 500,
    hintPenalty: 10,
    initialZoom: 4,
    proximityThreshold: 50,
    mapOptions: {
      scrollWheelZoom: true,
      doubleClickZoom: false,
    },
  },
};

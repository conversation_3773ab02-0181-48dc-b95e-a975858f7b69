import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Space, Typography, Row, Col } from 'antd';
import {
  CheckSquareOutlined,
  EditOutlined,
  FileTextOutlined,
  FunctionOutlined,
  TagOutlined,
} from '@ant-design/icons';
import practiceLocalization from '../localization';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';

const { Title, Text } = Typography;

interface ModalSelectQuestionTypeProps {
  visible: boolean;
  onCancel: () => void;
  onSelectType: (type: string) => void;
}

const ModalSelectQuestionType: React.FC<ModalSelectQuestionTypeProps> = ({
  visible,
  onCancel,
  onSelectType,
}) => {
  return (
    <ModalAntdCustom
      title="Tạo câu hỏi"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text>H<PERSON>y lựa chọn loại câu hỏi:</Text>

        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col xs={24} sm={12} md={8}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              onClick={() => onSelectType(practiceLocalization.quiz)}
            >
              <FileTextOutlined
                style={{ fontSize: '48px', color: '#1890ff' }}
              />
              <Title level={4}>Trắc nghiệm 1 đáp án</Title>
              <Text>Câu hỏi có nhiều lựa chọn với 1 đáp án</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              onClick={() => onSelectType(practiceLocalization.matching)}
            >
              <FunctionOutlined
                style={{ fontSize: '48px', color: '#52c41a' }}
              />
              <Title level={4}>Câu hỏi nối</Title>
              <Text>Nối các thẻ phù hợp với đề bài</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              onClick={() => onSelectType(practiceLocalization.fillblanks)}
            >
              <TagOutlined style={{ fontSize: '48px', color: '#C0B29E' }} />
              <Title level={4}>Câu hỏi điền từ</Title>
              <Text>Điền từ phù hợp vào ô trống để hoàn thành câu trả lời</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              onClick={() => onSelectType(practiceLocalization.multiselect)}
            >
              <CheckSquareOutlined
                style={{ fontSize: '48px', color: '#722ED1' }}
              />
              <Title level={4}>Câu hỏi nhiều đáp án</Title>
              <Text>Chọn nhiều đáp án đúng cho một câu hỏi</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              hoverable
              style={{ textAlign: 'center', height: '100%' }}
              onClick={() => onSelectType(practiceLocalization.shortanswer)}
            >
              <EditOutlined style={{ fontSize: '48px', color: '#722ED1' }} />
              <Title level={4}>Câu hỏi trả lời ngắn</Title>
              <Text>Viết câu trả lời ngắn gọn</Text>
            </Card>
          </Col>
        </Row>

        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: '24px',
          }}
        >
          <Button onClick={onCancel}>Hủy</Button>
        </div>
      </Space>
    </ModalAntdCustom>
  );
};

export default ModalSelectQuestionType;

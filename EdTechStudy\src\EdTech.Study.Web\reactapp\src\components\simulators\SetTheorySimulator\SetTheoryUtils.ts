// SetTheoryUtils.ts
import { SetElement } from './SetTheoryConfig';

/**
 * Kiểm tra xem một phần tử có thuộc tập hợp hay không
 * @param element Phần tử cần kiểm tra
 * @param set Tập hợp cần kiểm tra
 * @returns true nếu phần tử thuộc tập hợp, false nếu không
 */
export const isMember = (element: SetElement, set: SetElement[]): boolean => {
  return set.some((el) => el.id === element.id);
};

/**
 * Kiểm tra xem một giá trị có thuộc tập hợp hay không
 * @param value Giá trị cần kiểm tra
 * @param set Tập hợp cần kiểm tra
 * @returns true nếu giá trị thuộc tập hợp, false nếu không
 */
export const isValueMember = (value: string, set: SetElement[]): boolean => {
  return set.some((el) => el.value === value);
};

/**
 * Kiểm tra xem tập hợp A có là tập con của tập hợp B hay không
 * @param setA Tập hợp A
 * @param setB Tập hợp B
 * @returns true nếu A là tập con của B, false nếu không
 */
export const isSubset = (setA: SetElement[], setB: SetElement[]): boolean => {
  return setA.every((elA) => setB.some((elB) => elB.id === elA.id));
};

/**
 * Tạo tập hợp hợp của hai tập hợp
 * @param setA Tập hợp A
 * @param setB Tập hợp B
 * @returns Tập hợp mới chứa tất cả phần tử của A và B
 */
export const union = (setA: SetElement[], setB: SetElement[]): SetElement[] => {
  const result = [...setA];

  setB.forEach((elB) => {
    if (!result.some((el) => el.id === elB.id)) {
      result.push(elB);
    }
  });

  return result;
};

/**
 * Tạo tập hợp giao của hai tập hợp
 * @param setA Tập hợp A
 * @param setB Tập hợp B
 * @returns Tập hợp mới chứa các phần tử vừa thuộc A vừa thuộc B
 */
export const intersection = (
  setA: SetElement[],
  setB: SetElement[]
): SetElement[] => {
  return setA.filter((elA) => setB.some((elB) => elB.id === elA.id));
};

/**
 * Tạo tập hợp hiệu A\B
 * @param setA Tập hợp A
 * @param setB Tập hợp B
 * @returns Tập hợp mới chứa các phần tử thuộc A nhưng không thuộc B
 */
export const difference = (
  setA: SetElement[],
  setB: SetElement[]
): SetElement[] => {
  return setA.filter((elA) => !setB.some((elB) => elB.id === elA.id));
};

/**
 * Tạo phần tử ngẫu nhiên
 * @param type Loại phần tử
 * @returns Phần tử mới với giá trị ngẫu nhiên
 */
export const createRandomElement = (
  type: 'number' | 'letter' | 'custom'
): SetElement => {
  let value = '';

  switch (type) {
    case 'number':
      value = Math.floor(Math.random() * 100).toString();
      break;
    case 'letter':
      value = String.fromCharCode(65 + Math.floor(Math.random() * 26));
      break;
    case 'custom':
      value = `Element ${Math.floor(Math.random() * 100)}`;
      break;
  }

  return {
    id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
    value,
    type,
  };
};

/**
 * Tạo chuỗi biểu diễn tập hợp
 * @param set Tập hợp cần biểu diễn
 * @returns Chuỗi biểu diễn tập hợp theo định dạng {a, b, c}
 */
export const setToString = (set: SetElement[]): string => {
  if (set.length === 0) return '∅'; // Empty set
  return `{${set.map((el) => el.value).join(', ')}}`;
};

/**
 * Kiểm tra tập hợp rỗng
 * @param set Tập hợp cần kiểm tra
 * @returns true nếu tập hợp rỗng, false nếu không
 */
export const isEmpty = (set: SetElement[]): boolean => {
  return set.length === 0;
};

/**
 * Tạo bản sao của tập hợp
 * @param set Tập hợp cần sao chép
 * @returns Bản sao của tập hợp
 */
export const cloneSet = (set: SetElement[]): SetElement[] => {
  return set.map((el) => ({ ...el }));
};

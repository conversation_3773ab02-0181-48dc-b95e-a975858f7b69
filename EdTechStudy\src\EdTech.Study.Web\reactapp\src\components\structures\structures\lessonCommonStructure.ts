import { nanoid } from 'nanoid';
import { ETypeEdTechComponent } from '../../../enums/AppEnums';
import { IEdTechRenderTreeStructureData } from '../../../interfaces/AppComponents';

const ED_TECH_COMPONENT_RENDER_ROOT_NAME = 'RootLessonComponent';
const ED_TECH_LESSON_TAB_COMPONENT_NAME = 'LessonCardComponent';

export const ED_TECH_COMPONENT_RENDER_ROOT = {
  id: ED_TECH_COMPONENT_RENDER_ROOT_NAME,
  name: ED_TECH_COMPONENT_RENDER_ROOT_NAME,
  title: '<PERSON>ài học gốc',
  type: ETypeEdTechComponent.STRUCTURE,
  order: 0,
  path: ED_TECH_COMPONENT_RENDER_ROOT_NAME,
  subItems: [],
  version: '1.0.0',
};

const getTypeFromName = (name: string): ETypeEdTechComponent => {
  switch (name) {
    case 'RootLessonComponent':
    case 'LessonCommonComponent':
      return ETypeEdTechComponent.STRUCTURE;
    case 'LessonCardComponent':
      return ETypeEdTechComponent.CONTENT;
    default:
      return ETypeEdTechComponent.LAYOUT;
  }
};

export const LESSON_COMMON_STRUCTURE_DATA =
  (): IEdTechRenderTreeStructureData => {
    const lessonCommonComponentId = nanoid(8);

    const lessonObjectivesId = nanoid(8);
    const lessonObjectives: IEdTechRenderTreeStructureData = {
      id: lessonObjectivesId,
      name: ED_TECH_LESSON_TAB_COMPONENT_NAME,
      title: 'Mục Tiêu Bài Học',
      type: getTypeFromName(ED_TECH_LESSON_TAB_COMPONENT_NAME),
      order: 1,
      path: '',
      version: '1.0.0',
    };

    // LessonContent
    const lessonContentId = nanoid(8);
    const lessonContent: IEdTechRenderTreeStructureData = {
      id: lessonContentId,
      name: ED_TECH_LESSON_TAB_COMPONENT_NAME,
      title: 'Nội Dung Bài Học',
      type: getTypeFromName(ED_TECH_LESSON_TAB_COMPONENT_NAME),
      order: 2,
      path: '',
      subItems: [],
      version: '1.0.0',
    };

    const lessonGamesId = nanoid(8);
    const lessonGames: IEdTechRenderTreeStructureData = {
      id: lessonGamesId,
      name: ED_TECH_LESSON_TAB_COMPONENT_NAME,
      title: 'Trò Chơi Bài Học',
      type: getTypeFromName(ED_TECH_LESSON_TAB_COMPONENT_NAME),
      order: 3,
      path: '',
      subItems: [],
      version: '1.0.0',
    };

    const lessonQuizId = nanoid(8);
    const LessonQuiz: IEdTechRenderTreeStructureData = {
      id: lessonQuizId,
      name: ED_TECH_LESSON_TAB_COMPONENT_NAME,
      title: 'Bài Kiểm Tra Nhỏ',
      type: getTypeFromName(ED_TECH_LESSON_TAB_COMPONENT_NAME),
      order: 4,
      path: '',
      version: '1.0.0',
    };

    const LESSON_COMMON_STRUCTURE: IEdTechRenderTreeStructureData = {
      id: lessonCommonComponentId,
      name: 'LessonCommonComponent',
      title: 'Bài học',
      type: ETypeEdTechComponent.STRUCTURE,
      order: 0,
      version: '1.0.0',
      path: lessonCommonComponentId,
      subItems: [
        {
          ...lessonObjectives,
          order: 0,
        },
        {
          ...lessonContent,
          order: 1,
        },
        {
          ...lessonGames,
          order: 2,
        },
        {
          ...LessonQuiz,
          order: 3,
        },
      ],
    };

    // Update paths for sub items
    if (LESSON_COMMON_STRUCTURE.subItems) {
      LESSON_COMMON_STRUCTURE.subItems.forEach((item) => {
        item.path = `${LESSON_COMMON_STRUCTURE.path}/${item.id}`;
      });
    }

    return LESSON_COMMON_STRUCTURE;
  };

// Default export using ED_TECH_COMPONENT_RENDER_ROOT values
export default LESSON_COMMON_STRUCTURE_DATA();

import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';
export const fractionSchemaAddition = createComponentSchema({
  paramsSchema: {
    numerator: z.number().default(1),
    denominator: z.number().default(2),
    showDecimal: z.boolean().default(true),
    operation: z.literal('add').default('add'),
  },
});

// Schema cho phép trừ
export const fractionSchemaSubtraction = createComponentSchema({
  paramsSchema: {
    numerator: z.number().default(1),
    denominator: z.number().default(2),
    showDecimal: z.boolean().default(true),
    operation: z.literal('subtract').default('subtract'),
  },
});

// Schema cho phép nhân
export const fractionSchemaMultiplication = createComponentSchema({
  paramsSchema: {
    numerator: z.number().default(1),
    denominator: z.number().default(2),
    showDecimal: z.boolean().default(true),
    operation: z.literal('multiply').default('multiply'),
  },
});

// Schema cho phép chia
export const fractionSchemaDivision = createComponentSchema({
  paramsSchema: {
    numerator: z.number().default(1),
    denominator: z.number().default(2),
    showDecimal: z.boolean().default(true),
    operation: z.literal('divide').default('divide'),
  },
});

// Schema cho phép tính
export const fractionSchemaOperation = createComponentSchema({
  paramsSchema: {
    numerator: z.number().default(1),
    denominator: z.number().default(2),
    operation: z
      .union([
        z.literal('add'),
        z.literal('subtract'),
        z.literal('multiply'),
        z.literal('divide'),
      ])
      .default('add'),
    showDecimal: z.boolean().default(true),
  },
});

export const convertFractionDemoVersion = (
  fromVersion: string,
  toVersion: string,
  data: any
): any => {
  // Tạo bản sao của data để tránh thay đổi trực tiếp tham số đầu vào
  const newData = { ...data };
  const showDecimal =
    newData.showDecimal !== undefined ? newData.showDecimal : true;

  switch (fromVersion) {
    case 'Phép cộng':
      switch (toVersion) {
        case 'Phép tính':
          return {
            ...newData,
            operation: 'add',
            showDecimal,
          };
        case 'Phép trừ':
          return {
            ...newData,
            operation: 'subtract',
            showDecimal,
          };
        case 'Phép nhân':
          return {
            ...newData,
            operation: 'multiply',
            showDecimal,
          };
        case 'Phép chia':
          return {
            ...newData,
            operation: 'divide',
            showDecimal,
          };
        default:
          return newData;
      }

    case 'Phép trừ':
      switch (toVersion) {
        case 'Phép tính':
          return {
            ...newData,
            operation: 'subtract',
            showDecimal,
          };
        case 'Phép cộng':
          return {
            ...newData,
            operation: 'add',
            showDecimal,
          };
        case 'Phép nhân':
          return {
            ...newData,
            operation: 'multiply',
            showDecimal,
          };
        case 'Phép chia':
          return {
            ...newData,
            operation: 'divide',
            showDecimal,
          };
        default:
          return newData;
      }

    // case 'Phép nhân':
    //   switch (toVersion) {
    //     case 'Phép tính':
    //       return {
    //         ...newData,
    //         operation: 'multiply',
    //         showDecimal,
    //       };
    //     case 'Phép cộng':
    //       return {
    //         ...newData,
    //         operation: 'add',
    //         showDecimal,
    //       };
    //     case 'Phép trừ':
    //       return {
    //         ...newData,
    //         operation: 'subtract',
    //         showDecimal,
    //       };
    //     case 'Phép chia':
    //       return {
    //         ...newData,
    //         operation: 'divide',
    //         showDecimal,
    //       };
    //     default:
    //       return newData;
    //   }

    // case 'Phép chia':
    //   switch (toVersion) {
    //     case 'Phép tính':
    //       return {
    //         ...newData,
    //         operation: 'divide',
    //         showDecimal,
    //       };
    //     case 'Phép cộng':
    //       return {
    //         ...newData,
    //         operation: 'add',
    //         showDecimal,
    //       };
    //     case 'Phép trừ':
    //       return {
    //         ...newData,
    //         operation: 'subtract',
    //         showDecimal,
    //       };
    //     case 'Phép nhân':
    //       return {
    //         ...newData,
    //         operation: 'multiply',
    //         showDecimal,
    //       };
    //     default:
    //       return newData;
    //   }

    case 'Phép tính':
      // Lấy loại phép tính hiện tại để xác định giá trị mặc định khi chuyển đổi

      switch (toVersion) {
        case 'Phép cộng':
          return {
            ...newData,
            operation: 'add',
            showDecimal,
          };
        case 'Phép trừ':
          return {
            ...newData,
            operation: 'subtract',
            showDecimal,
          };
        case 'Phép nhân':
          return {
            ...newData,
            operation: 'multiply',
            showDecimal,
          };
        case 'Phép chia':
          return {
            ...newData,
            operation: 'divide',
            showDecimal,
          };
        default:
          return newData;
      }

    default:
      // Nếu không khớp với bất kỳ phiên bản nào đã biết, trả về dữ liệu ban đầu
      return newData;
  }
};

import React from 'react';
import { Button, Tooltip, Typography } from 'antd';
import { FullScreenIcon } from '../../../../icons/IconRegister';

const { Title } = Typography;

interface SidebarHeaderProps {
  title: string;
  isEditing: boolean;
  onFullscreen?: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
  title,
  isEditing,
  onFullscreen,
}) => {
  return (
    <div className="sidebar-header">
      <div className="title-container">
        <Title level={5} className="sidebar-title">
          {title}
        </Title>
        {isEditing && (
          <>
            <div className="title-actions">
              {onFullscreen && (
                <Tooltip title="Fullscreen">
                  <Button
                    type="primary"
                    icon={<FullScreenIcon />}
                    size="small"
                    className="title-action-button fullscreen-button"
                    onClick={onFullscreen}
                  />
                </Tooltip>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SidebarHeader;

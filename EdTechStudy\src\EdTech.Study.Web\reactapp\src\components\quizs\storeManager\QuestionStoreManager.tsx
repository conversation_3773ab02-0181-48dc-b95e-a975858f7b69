import React, {
  useState,
  useCallback,
  useEffect,
  useMemo,
  CSSProperties,
  useRef,
} from 'react';
import {
  Table,
  Input,
  Button,
  Space,
  Tag,
  Typography,
  message,
  Badge,
  Tooltip,
  Card,
  Collapse,
  CollapseProps,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  EyeOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined,
  CopyOutlined,
  SettingOutlined,
  DeleteFilled,
  TagsOutlined,
  ProfileOutlined,
  CaretRightOutlined,
} from '@ant-design/icons';

import type {
  ColumnsType,
  FilterValue,
  SorterResult,
  TablePaginationConfig,
} from 'antd/es/table/interface';
import QuestionTableAdvancedSettings, {
  TableColumn,
  Subject,
} from './QuestionTableAdvancedSettings';
import {
  BaseQuestion,
  QuestionStatus,
  QuestionType,
} from '../../../interfaces/quizs/questionBase';
import './QuestionStoreManager.css';
import { DeleteIcon } from '../../icons/IconIndex';

const { Title } = Typography;
const { Search } = Input;

export interface IQuestionStoreManagerProps {
  id: string;
  displayMode: string;
  questions: BaseQuestion[];
  loading?: boolean;
  totalCount?: number;
  onEditQuestion?: (question: BaseQuestion) => void;
  onDeleteQuestions?: (questionIds: string[]) => void;
  onPreviewQuestion?: (question: BaseQuestion) => void;
  onExportQuestions?: () => void;
  onImportQuestions?: () => void;
  onTableChange?: (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
  ) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  subjects?: Subject[];
  onSubjectFilterChange?: (subjectIds: string[]) => void;
}

// Map for question type display names
const questionTypeMap: Record<string, string> = {
  quiz: 'Chọn 1 đáp án',
  multiselect: 'Chọn nhiều đáp án',
  matching: 'Nối',
  essay: 'Tự luận',
  fillblank: 'Điền ô trống',
  shortanswer: 'Trả lời ngắn',
};

// Map for type to color
const typeColorMap: Record<string, string> = {
  quiz: 'blue',
  multiselect: 'cyan',
  matching: 'green',
  essay: 'purple',
  fillblank: 'orange',
  shortanswer: 'magenta',
};

// Define all possible columns for the table
const allTableColumns: TableColumn[] = [
  { key: 'id', title: 'ID', visible: true, width: 100 },
  { key: 'type', title: 'Loại câu hỏi', visible: true, width: 150 },
  { key: 'title', title: 'Mô tả', visible: true, width: 150 },
  { key: 'preview', title: 'Nội dung', visible: true, width: 500 },
  { key: 'points', title: 'Độ khó', visible: false, width: 80 },
  { key: 'statusEntity', title: 'Trạng thái', visible: true, width: 120 },
  { key: 'tags', title: 'Thẻ', visible: true, width: 150 },
  { key: 'topics', title: 'Chủ đề', visible: true, width: 150 },
  { key: 'creationTime', title: 'Ngày tạo', visible: false, width: 150 },
  {
    key: 'lastModificationTime',
    title: 'Ngày cập nhật',
    visible: false,
    width: 150,
  },
  { key: 'subject', title: 'Môn học', visible: false, width: 150 },
  { key: 'action', title: 'Thao tác', visible: true, width: 100 },
];

// Default key for localStorage
const SETTINGS_STORAGE_KEY = 'question-table-settings';

const QuestionStoreManager: React.FC<IQuestionStoreManagerProps> = (props) => {
  const {
    id,
    displayMode,
    questions,
    loading = false,
    totalCount,
    subjects = [],
    onEditQuestion,
    onDeleteQuestions,
    onPreviewQuestion,
    onExportQuestions,
    onImportQuestions,
    onTableChange,
    onSearch,
    onRefresh,
    onSubjectFilterChange,
  } = props;

  // State for table filter, sort, and search
  const [searchText, setSearchText] = useState('');
  const [searchTextActive, setSearchTextActive] = useState('');
  const [filteredInfo, setFilteredInfo] = useState<
    Record<string, FilterValue | null>
  >({});
  const [sortedInfo, setSortedInfo] = useState<SorterResult<BaseQuestion>>({});
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} câu hỏi`,
  });

  // State for table row selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<BaseQuestion[]>([]);

  // State for advanced settings
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [tableColumns, setTableColumns] =
    useState<TableColumn[]>(allTableColumns);
  const [selectedSubjectIds, setSelectedSubjectIds] = useState<string[]>([]);
  // State for tag and topic search
  const [tagSearchText, setTagSearchText] = useState('');
  const [topicSearchText, setTopicSearchText] = useState('');

  // Load saved settings from localStorage on component mount
  // useEffect(() => {
  //   const savedSettings = localStorage.getItem(`${SETTINGS_STORAGE_KEY}-${id}`);
  //   if (savedSettings) {
  //     try {
  //       const { columns, pageSize, subjectIds } = JSON.parse(savedSettings);
  //       if (columns) setTableColumns(columns);
  //       if (pageSize) setPagination((prev) => ({ ...prev, pageSize }));
  //       if (subjectIds) setSelectedSubjectIds(subjectIds);
  //     } catch (e) {
  //       console.error('Error loading saved table settings:', e);
  //     }
  //   }
  // }, [id]);

  // Save settings to localStorage when they change
  // useEffect(() => {
  //   const settings = {
  //     columns: tableColumns,
  //     pageSize: pagination.pageSize,
  //     subjectIds: selectedSubjectIds,
  //   };
  //   localStorage.setItem(
  //     `${SETTINGS_STORAGE_KEY}-${id}`,
  //     JSON.stringify(settings)
  //   );
  // }, [tableColumns, pagination.pageSize, selectedSubjectIds, id]);

  // Update pagination total when questions or totalCount changes
  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: totalCount !== undefined ? totalCount : questions.length,
    }));
  }, [questions, totalCount]);

  // Delay search to avoid too many requests
  const handleSearchTextChange = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // Actually perform the search after typing stops
  useEffect(() => {
    const timer = setTimeout(() => {
      if (onSearch) {
        onSearch(searchTextActive);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTextActive, onSearch]);

  // Reset filters and sorters
  const handleReset = useCallback(() => {
    setFilteredInfo({});
    setSortedInfo({});
    setSearchText('');
    setTagSearchText('');
    setTopicSearchText('');
    setSelectedSubjectIds([]);
    // Clear selections when filters are reset
    setSelectedRowKeys([]);
    setSelectedRows([]);

    if (onSubjectFilterChange) {
      onSubjectFilterChange([]);
    }
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));

    // Notify parent component
    if (onSearch) {
      onSearch('');
    }
    if (onTableChange) {
      onTableChange({ current: 1, pageSize: pagination.pageSize }, {}, {});
    }
  }, [onSearch, onTableChange, pagination.pageSize, onSubjectFilterChange]);

  // Refresh data
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    } else if (onTableChange) {
      onTableChange(pagination, filteredInfo, sortedInfo);
    }
  }, [onRefresh, onTableChange, pagination, filteredInfo, sortedInfo]);

  // Function to copy ID to clipboard
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        message.success('ID đã được sao chép!');
      },
      (err) => {
        message.error('Không thể sao chép ID: ' + err);
      }
    );
  }, []);

  // Open and close advanced settings modal
  const showSettings = () => {
    setSettingsVisible(true);
  };

  const hideSettings = () => {
    setSettingsVisible(false);
  };

  // Handle change of visible columns
  const handleColumnsChange = (newColumns: TableColumn[]) => {
    setTableColumns(newColumns);
  };

  // Handle subject filter change
  const handleSubjectFilterChange = (subjectIds: string[]) => {
    setSelectedSubjectIds(subjectIds);
    if (onSubjectFilterChange) {
      onSubjectFilterChange(subjectIds);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    setPagination((prev) => ({
      ...prev,
      pageSize: newSize,
      current: 1, // Reset to first page when changing page size
    }));

    if (onTableChange) {
      onTableChange(
        { ...pagination, pageSize: newSize, current: 1 },
        filteredInfo,
        sortedInfo
      );
    }
  };

  // Generate question type filters
  const questionTypeFilters = [
    { text: 'Chọn 1 đáp án', value: QuestionType.SingleChoice },
    { text: 'Chọn nhiều đáp án', value: QuestionType.MultipleChoice },
    { text: 'Nối', value: QuestionType.Matching },
    { text: 'Tự luận', value: QuestionType.Essay },
    { text: 'Điền ô trống', value: QuestionType.Fillblank },
  ];

  // Function to shorten ID for display
  const shortenId = (id: string): string => {
    if (id.length <= 8) return id;
    return id.substring(0, 6) + '...';
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Generate subject filters from available subjects
  const subjectFilters = useMemo(() => {
    return subjects.map((subject) => ({
      text: subject.name,
      value: subject.id,
    }));
  }, [subjects]);

  const idFilterTimeout = useRef<any>(null);
  // Handle tag and topic search
  const handleTagSearch = (value: string) => {
    if (idFilterTimeout.current) {
      clearTimeout(idFilterTimeout.current);
    }
    idFilterTimeout.current = setTimeout(() => {
      setTagSearchText(value);
    }, 500);
  };

  const handleTopicSearch = (value: string) => {
    if (idFilterTimeout.current) {
      clearTimeout(idFilterTimeout.current);
    }
    idFilterTimeout.current = setTimeout(() => {
      setTopicSearchText(value);
    }, 500);
  };

  // Filter questions based on tag and topic search
  const filteredQuestions = useMemo(() => {
    if (!tagSearchText && !topicSearchText) return questions;

    return questions.filter((question) => {
      // Tag filtering
      const questionTags = Array.isArray(question.tags)
        ? question.tags
        : question.tags
        ? question.tags.split(',').map((tag) => tag.trim())
        : [];

      const tagMatch =
        !tagSearchText ||
        questionTags.some((tag) =>
          tag.toLowerCase().includes(tagSearchText.toLowerCase())
        );

      // Topic filtering
      const questionTopics: string[] = Array.isArray(question.metadata?.topics)
        ? question.metadata.topics
        : question.topics
        ? (question.topics as string).split(',').map((topic) => topic.trim())
        : [];

      const topicMatch =
        !topicSearchText ||
        questionTopics.some((topic) =>
          topic.toLowerCase().includes(topicSearchText.toLowerCase())
        );

      return tagMatch && topicMatch;
    });
  }, [questions, tagSearchText, topicSearchText]);

  // Table columns definition with correct field names
  const getTableColumns = useMemo((): ColumnsType<BaseQuestion> => {
    const visibleColumns = tableColumns.filter((col) => col.visible);

    return visibleColumns.map((column) => {
      switch (column.key) {
        case 'id':
          return {
            title: column.title,
            dataIndex: 'id',
            key: 'id',
            width: column.width,
            ellipsis: true,
            render: (id: string) => (
              <Tooltip title={`${id} (Nhấp để sao chép)`} placement="topLeft">
                <Button
                  type="text"
                  size="small"
                  onClick={() => copyToClipboard(id)}
                  style={{ padding: '0', fontFamily: 'monospace' }}
                  icon={
                    <CopyOutlined
                      style={{ fontSize: '12px', marginRight: '4px' }}
                    />
                  }
                >
                  {shortenId(id)}
                </Button>
              </Tooltip>
            ),
          };
        case 'type':
          return {
            title: column.title,
            dataIndex: 'type',
            key: 'type',
            width: column.width,
            filters: questionTypeFilters,
            filteredValue: filteredInfo.type || null,
            render: (type) => (
              <Tag color={typeColorMap[type] || 'default'}>
                {questionTypeMap[type] || type}
              </Tag>
            ),
          };
        case 'title':
          return {
            title: column.title,
            dataIndex: 'title',
            key: 'title',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'title' ? sortedInfo.order : null,
            ellipsis: true,
            render: (_, record) => {
              const preview = record.title;
              return (
                <Tooltip title={preview}>
                  {preview.length > 50
                    ? `${preview.substring(0, 50)}...`
                    : preview}
                </Tooltip>
              );
            },
          };
        case 'preview':
          return {
            title: column.title,
            key: 'preview',
            width: column.width,
            render: (_, record) => {
              const preview = record.description || '';
              return (
                <Tooltip
                  title={
                    <span
                      dangerouslySetInnerHTML={{
                        __html: preview,
                      }}
                    ></span>
                  }
                >
                  <span
                    dangerouslySetInnerHTML={{
                      __html:
                        preview.length > 50
                          ? `${preview.substring(0, 50)}...`
                          : preview,
                    }}
                  ></span>
                </Tooltip>
              );
            },
            ellipsis: true,
          };
        case 'points':
          return {
            title: column.title,
            dataIndex: 'points',
            key: 'points',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'points' ? sortedInfo.order : null,
          };
        case 'statusEntity':
          return {
            title: column.title,
            dataIndex: 'statusEntity',
            key: 'statusEntity',
            width: column.width,
            filters: [
              { text: 'Hoàn thành', value: QuestionStatus.Published },
              { text: 'Đã duyệt', value: QuestionStatus.Approved },
              { text: 'Đã gửi', value: QuestionStatus.Submitted },
              { text: 'Bản nháp', value: QuestionStatus.Draft },
              { text: 'Đã từ chối', value: QuestionStatus.Rejected },
            ],
            filteredValue: filteredInfo.statusEntity || null,
            render: (statusEntity) => {
              let color = 'default';
              let text = 'Bản nháp';

              if (statusEntity === QuestionStatus.Published) {
                color = 'success';
                text = 'Hoàn thành';
              } else if (statusEntity === QuestionStatus.Approved) {
                color = 'processing';
                text = 'Đã duyệt';
              } else if (statusEntity === QuestionStatus.Submitted) {
                color = 'warning';
                text = 'Đã gửi';
              } else if (statusEntity === QuestionStatus.Draft) {
                color = 'default';
                text = 'Bản nháp';
              } else if (statusEntity === QuestionStatus.Rejected) {
                color = 'error';
                text = 'Đã từ chối';
              }

              return <Tag color={color}>{text}</Tag>;
            },
          };
        case 'creationTime':
          return {
            title: column.title,
            dataIndex: 'creationTime',
            key: 'creationTime',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'creationTime' ? sortedInfo.order : null,
            render: (date) => formatDate(date),
          };
        case 'lastModificationTime':
          return {
            title: column.title,
            dataIndex: 'lastModificationTime',
            key: 'lastModificationTime',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'lastModificationTime'
                ? sortedInfo.order
                : null,
            render: (date) => formatDate(date),
          };
        case 'subject':
          return {
            title: column.title,
            key: 'subject',
            width: column.width,
            filters: subjectFilters,
            filteredValue: filteredInfo.subject || null,
            // Fixed subject column filters by customizing the onFilter function
            onFilter: (value, record) => {
              if (!record.subject) return false;
              return record.subject.id === value;
            },
            render: (_, record) => {
              if (!record.subject) return '-';
              return (
                <Tooltip title={record.subject.code}>
                  <Tag color="blue">{record.subject.name}</Tag>
                </Tooltip>
              );
            },
          };
        case 'action':
          return {
            title: column.title,
            key: 'action',
            width: column.width,
            fixed: 'right',
            render: (_, record) => (
              <Space size="small">
                <Tooltip title="Xem trước">
                  <Button
                    type="default"
                    color="primary"
                    icon={<EyeOutlined />}
                    onClick={() => onPreviewQuestion?.(record)}
                  />
                </Tooltip>
                <Tooltip title="Xóa">
                  <Button
                    type="default"
                    icon={<DeleteIcon />}
                    danger
                    onClick={() => onDeleteQuestions?.([record.id])}
                  />
                </Tooltip>
              </Space>
            ),
          };
        case 'tags':
          return {
            title: column.title,
            key: 'tags',
            width: column.width,
            render: (_, record) => (
              <span>
                {Array.isArray(record.tags)
                  ? record.tags.map((t) => <Tag key={t}>{t}</Tag>)
                  : record.tags}
              </span>
            ),
          };
        case 'topics':
          return {
            title: column.title,
            key: 'topics',
            width: column.width,
            render: (_, record) => (
              <span>
                {Array.isArray(record.topics)
                  ? record.topics.map((t) => <Tag key={t}>{t}</Tag>)
                  : record.topics}
              </span>
            ),
          };
        default:
          return {
            title: column.title,
            dataIndex: column.key,
            key: column.key,
            width: column.width,
          };
      }
    });
  }, [
    tableColumns,
    filteredInfo,
    sortedInfo,
    subjectFilters,
    copyToClipboard,
    onDeleteQuestions,
    onPreviewQuestion,
    questionTypeFilters,
  ]);

  const getAdvanceFilteingItems: () => CollapseProps['items'] = () => [
    {
      key: '1',
      label: 'Tìm kiếm nâng cao',
      children: (
        <Space
          // wrap
          direction="horizontal"
          size="middle"
          className="custom-space-item-full"
          style={{ marginBottom: '16px' }}
        >
          <div
            className="tailwind-flex tailwind-w-full"
            style={{
              width: '100%',
            }}
          >
            <TagsOutlined style={{ color: '#1890ff' }} />
            <Input
              placeholder="Tìm theo thẻ"
              allowClear
              onChange={(e) => handleTagSearch(e.target.value)}
              style={{ width: '100%' }}
            />
          </div>
          <div
            className="tailwind-flex tailwind-w-full"
            style={{
              width: '100%',
            }}
          >
            <ProfileOutlined style={{ color: '#1890ff' }} />
            <Input
              placeholder="Tìm theo chủ đề"
              allowClear
              onChange={(e) => handleTopicSearch(e.target.value)}
              style={{ width: '100%' }}
            />
          </div>
        </Space>
      ),
    },
  ];

  // Handle table change (for sorting, filtering, and pagination)
  const handleTableChange = (
    paginationConfig: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
  ) => {
    // Store the filter and sort info for local state management
    setFilteredInfo(filters);

    // Ensure sorter is properly handled for both single and multiple column sorting
    if (Array.isArray(sorter)) {
      setSortedInfo(sorter.length > 0 ? sorter[0] : {});
    } else {
      setSortedInfo(sorter);
    }

    setPagination(paginationConfig);

    // Pass the updated parameters to the parent component
    if (onTableChange) {
      onTableChange(paginationConfig, filters, sorter);
    }
  };

  // Determine if advanced filters are active
  const hasAdvancedFilters =
    selectedSubjectIds.length > 0 ||
    tableColumns.some((col) => col.key !== 'action' && !col.visible);

  // Handle row selection events
  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: BaseQuestion[]
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(selectedRows);
  };

  // Configure row selection options
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    fixed: true, // This will freeze the selection column
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  // Handle bulk delete action
  const handleBulkDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một câu hỏi để xóa');
      return;
    }

    // Convert the selected keys to string array for the delete function
    const questionIds = selectedRowKeys.map((key) => String(key));

    if (onDeleteQuestions) {
      onDeleteQuestions(questionIds);
      // Clear selection after delete
      setSelectedRowKeys([]);
      setSelectedRows([]);
    } else {
      message.info('Chức năng xóa nhiều câu hỏi chưa được cài đặt');
    }
  };

  return (
    <div
      className="question-store-manager"
      style={{
        width: '100%',
      }}
    >
      <Card
        title={
          <Title level={4}>
            Ngân hàng câu hỏi{' '}
            <small>
              <small>
                (
                {totalCount !== undefined
                  ? totalCount
                  : filteredQuestions.length}
                )
              </small>
            </small>
          </Title>
        }
        variant="borderless"
        style={{ width: '100%' }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div
            className="tailwind-flex tailwind-w-full"
            style={{
              marginBottom: 16,
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Space
              // wrap
              className="search-space"
              size={'middle'}
              style={{ width: '100%' }}
            >
              <Search
                style={{ width: '100%', minWidth: '300px' }}
                className="tailwind-w-full"
                placeholder="Tìm kiếm câu hỏi..."
                // value={searchText}
                // onChange={(e) => handleSearchTextChange(e.target.value)}
                onSearch={(value) => setSearchTextActive(value)}
                // suffix={<SearchOutlined />}
                allowClear
              />
              <Button icon={<FilterOutlined />} onClick={handleReset}>
                Xóa bộ lọc
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                Tải lại
              </Button>
              <Tooltip title="Cài đặt nâng cao">
                <Badge dot={hasAdvancedFilters}>
                  <Button icon={<SettingOutlined />} onClick={showSettings} />
                </Badge>
              </Tooltip>
              <Button icon={<ImportOutlined />} onClick={onImportQuestions}>
                Nhập
              </Button>
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={onExportQuestions}
              >
                Xuất
              </Button>
            </Space>
          </div>

          {/* Tag and Topic search filters */}
          <Collapse
            bordered={false}
            defaultActiveKey={['0']}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            items={getAdvanceFilteingItems()}
          />

          {/* Bulk actions toolbar - only visible when rows are selected */}
          {selectedRowKeys.length > 0 && (
            <div
              style={{
                padding: '8px 16px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>Đã chọn {selectedRowKeys.length} câu hỏi</span>
              <Space>
                <Button
                  type="primary"
                  danger
                  icon={<DeleteFilled />}
                  onClick={handleBulkDelete}
                >
                  Xóa đã chọn
                </Button>
                <Button
                  onClick={() => {
                    setSelectedRowKeys([]);
                    setSelectedRows([]);
                  }}
                >
                  Bỏ chọn
                </Button>
              </Space>
            </div>
          )}

          <Table
            rowSelection={rowSelection}
            columns={getTableColumns}
            dataSource={filteredQuestions}
            rowKey="id"
            onChange={handleTableChange}
            pagination={pagination}
            loading={loading}
            scroll={{ x: 'max-content' }}
            bordered
          />
        </Space>

        {/* Modal for advanced settings instead of sidebar */}
        <QuestionTableAdvancedSettings
          columns={tableColumns}
          onColumnsChange={handleColumnsChange}
          subjects={subjects || []}
          onSubjectFilterChange={handleSubjectFilterChange}
          selectedSubjectIds={selectedSubjectIds}
          onDrawerClose={hideSettings}
          visible={settingsVisible}
          pageSize={pagination.pageSize as number}
          onPageSizeChange={handlePageSizeChange}
        />
      </Card>
    </div>
  );
};

export default QuestionStoreManager;

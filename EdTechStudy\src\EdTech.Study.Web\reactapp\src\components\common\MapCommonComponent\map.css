/* Z-index management for map components and modals */

/* Base map container - lower z-index */
.leaflet-container {
  z-index: 10;
}

/* Map controls - higher than map but lower than modals */
.leaflet-control,
.leaflet-top,
.leaflet-bottom {
  z-index: 1000;
}

/* Default modal z-index */
.modal-overlay {
  z-index: 1500;
}

/* Modal content - highest z-index */
.modal-content {
  z-index: 2000;
}

/* Ensure search controls appear above map */
.map-search-control {
  z-index: 1200;
}

/* Animation classes for modals */
.modal-enter {
  opacity: 0;
  transform: scale(0.9);
}

.modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.modal-exit {
  opacity: 1;
}

.modal-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

/* Tailwind utility classes for z-index management */
.z-map {
  z-index: 10 !important;
}

.z-map-controls {
  z-index: 1000 !important;
}

.z-modal-overlay {
  z-index: 1500 !important;
}

.z-modal-content {
  z-index: 2000 !important;
}

/* Additional styles for map components */
.map-component-wrapper {
  position: relative;
}

/* Animation for map interaction indicators */
@keyframes pulsate {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.map-interaction-indicator {
  animation: pulsate 1.5s ease-in-out infinite;
}
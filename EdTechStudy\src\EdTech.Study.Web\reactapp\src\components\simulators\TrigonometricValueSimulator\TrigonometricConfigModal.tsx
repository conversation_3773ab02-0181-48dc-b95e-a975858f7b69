import React, { useState } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Tabs,
  Button,
  message,
  Select,
  Divider,
  Row,
  Col,
} from 'antd';
import { QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import {
  TrigonometricConfig,
  defaultTrigonometricConfig,
} from './TrigonometricConfig';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';

interface TrigonometricConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: TrigonometricConfig) => void;
  defaultConfig?: TrigonometricConfig;
}

const TrigonometricConfigModal: React.FC<TrigonometricConfigModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig = defaultTrigonometricConfig,
}) => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<TrigonometricConfig>(defaultConfig);

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const updatedConfig = deepMerge(config, values);
        onSubmit(updatedConfig);
        message.success('Cấu hình đã được lưu thành công');
        onClose();
      })
      .catch((errorInfo) => {
        console.log('Xác nhận thất bại:', errorInfo);
        message.error('Vui lòng kiểm tra thông tin cấu hình của bạn');
      });
  };

  const deepMerge = (prevObj: any, newValues: any) => {
    const result = { ...prevObj };
    Object.keys(newValues).forEach((key) => {
      if (
        typeof newValues[key] === 'object' &&
        newValues[key] !== null &&
        typeof result[key] === 'object' &&
        result[key] !== null
      ) {
        result[key] = deepMerge(result[key], newValues[key]);
      } else {
        result[key] = newValues[key];
      }
    });
    return result;
  };

  const tabItems = [
    {
      key: '1',
      label: 'Cấu hình cơ bản',
      children: (
        <>
          <Form.Item name="description" label="Mô tả">
            <Input.TextArea rows={3} placeholder="Mô tả chi tiết về mô phỏng" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="initialAngle"
                label={
                  <span>
                    Góc ban đầu (độ)
                    <InfoCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                  </span>
                }
                rules={[{ required: true, message: 'Vui lòng nhập góc' }]}
              >
                <InputNumber min={0} max={360} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="language"
                label="Ngôn ngữ hiển thị"
                rules={[{ required: true, message: 'Vui lòng chọn ngôn ngữ' }]}
              >
                <Select>
                  <Select.Option value="en">Tiếng Anh</Select.Option>
                  <Select.Option value="vi">Tiếng Việt</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="circleRadius"
                label="Bán kính vòng tròn (px)"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập bán kính vòng tròn',
                  },
                ]}
              >
                <InputNumber min={50} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="precision"
                label="Độ chính xác thập phân"
                rules={[
                  { required: true, message: 'Vui lòng nhập độ chính xác' },
                ]}
              >
                <InputNumber min={1} max={6} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="angleSnapThreshold"
            label={
              <span>
                Ngưỡng gắn góc (độ)
                <Tooltip title="Gắn vào các góc đặc biệt nếu nằm trong ngưỡng này">
                  <QuestionCircleOutlined className="tailwind-ml-1 tailwind-text-gray-400" />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: 'Vui lòng nhập ngưỡng' }]}
          >
            <InputNumber min={0} max={10} style={{ width: '100%' }} />
          </Form.Item>
        </>
      ),
    },
    {
      key: '2',
      label: 'Tùy chọn hiển thị',
      children: (
        <>
          <Divider>Hiển thị các thành phần</Divider>
          <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4">
            <Form.Item
              name={['displayOptions', 'showPropertiesPanel']}
              label="Hiển thị bảng thuộc tính"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showTheoryPanel']}
              label="Hiển thị lý thuyết"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </div>

          <Divider>Hiển thị chi tiết</Divider>
          <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4">
            <Form.Item
              name={['displayOptions', 'showUnitCircle']}
              label="Hiển thị vòng tròn đơn vị"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showGrid']}
              label="Hiển thị lưới"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showCoordinates']}
              label="Hiển thị tọa độ"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showAngleInRadians']}
              label="Hiển thị góc bằng radian"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showExactValues']}
              label="Hiển thị giá trị chính xác"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showLabels']}
              label="Hiển thị nhãn"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showSinCosLines']}
              label="Hiển thị đường Sin/Cos"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showTangentLine']}
              label="Hiển thị đường tiếp tuyến"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showCotangentLine']}
              label="Hiển thị đường cotang"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showTable']}
              label="Hiển thị bảng giá trị"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['displayOptions', 'showFormulas']}
              label="Hiển thị công thức"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </div>
        </>
      ),
    },
    {
      key: '3',
      label: 'Góc phần tư',
      children: (
        <>
          <Divider>Góc phần tư được kích hoạt</Divider>
          <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4">
            <Form.Item
              name={['enableQuadrants', 'first']}
              label="Phần tư thứ nhất (0° - 90°)"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['enableQuadrants', 'second']}
              label="Phần tư thứ hai (90° - 180°)"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['enableQuadrants', 'third']}
              label="Phần tư thứ ba (180° - 270°)"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name={['enableQuadrants', 'fourth']}
              label="Phần tư thứ tư (270° - 360°)"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </div>

          <div className="tailwind-mt-4 tailwind-p-3 tailwind-bg-yellow-50 tailwind-border tailwind-border-yellow-200 tailwind-rounded">
            <p className="tailwind-text-sm tailwind-text-yellow-700">
              <InfoCircleOutlined className="tailwind-mr-1" />
              Việc bật hoặc tắt các góc phần tư sẽ ảnh hưởng đến các góc có sẵn
              trong mô phỏng và bảng.
            </p>
          </div>
        </>
      ),
    },
  ];

  return (
    <ModalAntdCustom
      title="Cấu hình mô phỏng lượng giác"
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Hủy
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          Áp dụng
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultConfig}
        onValuesChange={(changedValues) => {
          setConfig((prevConfig) => deepMerge(prevConfig, changedValues));
        }}
      >
        <Tabs defaultActiveKey="1" items={tabItems} />
      </Form>
    </ModalAntdCustom>
  );
};

const Tooltip: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children,
}) => (
  <span className="tailwind-relative tailwind-group">
    {children}
    <div className="tailwind-absolute tailwind-bottom-full tailwind-mb-2 tailwind-left-1/2 tailwind-transform tailwind--translate-x-1/2 tailwind-hidden group-hover:tailwind-block tailwind-bg-gray-800 tailwind-text-white tailwind-text-xs tailwind-rounded tailwind-py-1 tailwind-px-2 tailwind-whitespace-nowrap">
      {title}
    </div>
  </span>
);

export default TrigonometricConfigModal;

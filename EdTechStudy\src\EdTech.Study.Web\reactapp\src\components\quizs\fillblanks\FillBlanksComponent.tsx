import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  message,
  Typography,
  Form,
  Switch,
  Divider,
  Tag,
  Collapse,
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  QuestionOutlined,
  PlusOutlined,
  DeleteFilled,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  Question,
} from '../../../interfaces/quizs/questionBase';
import './FillBlanksComponent.css';
import {
  FillBlanksComponentProps,
  FillBlanksQuestion,
  FillBlankItem,
  FillBlanksAnswer,
} from '../../../interfaces/quizs/fillblanks.interfaces';
import { Guid } from 'guid-typescript';
import practiceLocalization, { quizLocalization } from '../localization';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import TooltipAntdCustom from '../../customs/antd/TooltipAntdCustom';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import { DeleteIcon } from '../../icons/IconIndex';
const { Text } = Typography;
const { TextArea } = Input;

const FillBlanksComponent: React.FC<FillBlanksComponentProps> = ({
  question,
  onComplete,
  showFeedback = true,
  configMode = false,
  disabled = false,
  hideSaveButton = false,
}) => {
  const [userAnswers, setUserAnswers] = useState<{ [key: string]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [blankResults, setBlankResults] = useState<{ [key: string]: boolean }>({}); // prettier-ignore
  const { handleChangeQuestion, handleDeleteQuestion } = useContext(
    PracticeEngineContext
  );
  const contentQuestionRef = useRef<HTMLTextAreaElement>(null);
  const { handleUpdateQuestion } = useUpdateQuestion();
  const [focus, setFocus] = useState<string | null>(null);
  // Config mode states
  const [editedQuestion, setEditedQuestion] = useState<FillBlanksQuestion>({
    ...question,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    const initialAnswers: { [key: string]: string } = {};
    question.blanks.forEach((blank) => {
      initialAnswers[blank.id] = '';
    });
    setUserAnswers(initialAnswers);

    // Initialize user answers from saved state if available
    if (question.userSelect && question.userSelect.answers) {
      setUserAnswers(question.userSelect.answers);
      if (question.userSelect) {
        setIsSubmitted(false);
      }
    }
  }, [question]);

  const handleInputChange = (blankId: string, value: string) => {
    const newAnswers = { ...userAnswers, [blankId]: value };
    setUserAnswers(newAnswers);
    // Store answer without checking completion
    if (onComplete) {
      onComplete(question.id, {
        id: Guid.create().toString(),
        answers: newAnswers,
        isCorrect: false,
        order: question.answers?.length ?? 0,
      } as FillBlanksAnswer);
    }
  };

  // Reset the question
  const handleReset = () => {
    const resetAnswers: { [key: string]: string } = {};
    question.blanks.forEach((blank) => {
      resetAnswers[blank.id] = '';
    });
    setUserAnswers(question.userSelect?.answers ?? resetAnswers);
    setIsSubmitted(false);
    setIsCorrect(false);
    setBlankResults({});
  };

  // Function to render question with input fields
  const renderQuestion = () => {
    // Parse question text to identify blanks
    // We'll use simple parsing with ____ as blank placeholders
    const parts = question.question.split('____');

    if (parts.length <= 1) {
      return <div className="item-config">{question.question}</div>;
    }

    return (
      <div
        className="fill-blanks-question"
        onClick={() => setFocus('question')}
      >
        {parts.map((part, index) => {
          // Last part doesn't have a blank after it
          if (index === parts.length - 1) {
            return (
              <span key={`part-${index}`} className="item-config">
                {part}
              </span>
            );
          }

          const blank = question.blanks[index];
          if (!blank) return null;

          return (
            <React.Fragment key={`blank-fragment-${index}`}>
              <span className="item-config">{part}</span>
              <span className="blank-wrapper item-config">
                <Input
                  onClick={() => setFocus(`blank-${index}`)}
                  className={`fill-blanks-input ${
                    isSubmitted
                      ? blankResults[blank.id]
                        ? 'correct'
                        : 'incorrect'
                      : ''
                  }`}
                  value={userAnswers[blank.id] || ''}
                  onChange={(e) => handleInputChange(blank.id, e.target.value)}
                  disabled={isSubmitted || disabled}
                  placeholder="___________"
                  size="middle"
                />
                {isSubmitted && (
                  <span
                    className={`blank-feedback-icon ${
                      blankResults[blank.id] ? 'correct' : 'incorrect'
                    }`}
                  >
                    {blankResults[blank.id] ? (
                      <CheckOutlined />
                    ) : (
                      <CloseOutlined />
                    )}
                  </span>
                )}
              </span>
              {isSubmitted &&
                !blankResults[blank.id] &&
                question.showHints &&
                blank.hint && (
                  <div className="fill-blanks-hint">
                    <InfoCircleOutlined /> Gợi ý: {blank.hint}
                  </div>
                )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  // Configuration mode methods
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedQuestion = {
      ...editedQuestion,
      title: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as Question);
  };

  const handleQuestionTextChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const updatedQuestion = {
      ...editedQuestion,
      question: e.target.value,
      description: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as Question);
  };

  const handleInsertBlank = () => {
    const textArea = document.querySelector(
      '.fill-blanks-textarea'
    ) as HTMLTextAreaElement;
    let updatedQuestion: FillBlanksQuestion | null = null;
    if (textArea) {
      const start = textArea.selectionStart;
      const end = textArea.selectionEnd;
      const currentText = editedQuestion.question;
      const newText =
        currentText.substring(0, start) + '____' + currentText.substring(end);

      // Update the question text
      setEditedQuestion((prev) => {
        const update = {
          ...prev,
          question: newText,
          description: newText,
        };
        updatedQuestion = update;
        return update;
      });

      // Add a new blank if needed
      const blankCount = (newText.match(/____/g) || []).length;
      if (blankCount > editedQuestion.blanks.length) {
        const newBlank: FillBlankItem = {
          id: Guid.create().toString(),
          correctAnswer: '',
          alternativeAnswers: [],
          hint: '',
        };

        setEditedQuestion((prev) => {
          const update = {
            ...prev,
            blanks: [...prev.blanks, newBlank],
          };
          updatedQuestion = update;
          return update;
        });
      }
      // Set cursor position after the blank
      setTimeout(() => {
        textArea.focus();
        textArea.setSelectionRange(start + 4, start + 4);
      }, 0);

      if (updatedQuestion) {
        handleUpdateQuestion(updatedQuestion as Question);
      }
    }
  };

  const handleExplanationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const updatedQuestion = {
      ...editedQuestion,
      explanation: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as Question);
  };

  const handleCaseSensitiveChange = (checked: boolean) => {
    const updatedQuestion = {
      ...editedQuestion,
      caseSensitive: checked,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as Question);
  };

  const handleShowHintsChange = (checked: boolean) => {
    const updatedQuestion = {
      ...editedQuestion,
      showHints: checked,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as Question);
  };

  const handleBlankAnswerChange = (
    blankId: string,
    field: keyof FillBlankItem,
    value: string
  ) => {
    let updatedQuestion: FillBlanksQuestion | null = null;
    setEditedQuestion((prev) => {
      const updatedBlanks = prev.blanks.map((blank) => {
        if (blank.id === blankId) {
          return {
            ...blank,
            [field]: value,
          };
        }
        return blank;
      });
      const update = {
        ...prev,
        blanks: updatedBlanks,
      };
      updatedQuestion = update;
      return update;
    });

    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as Question);
    }
  };

  // Replace the existing implementation with a new one that handles comma-separated values
  const handleAlternativeStringChange = (blankId: string, value: string) => {
    let updatedQuestion: FillBlanksQuestion | null = null;
    setEditedQuestion((prev) => {
      const updatedBlanks = prev.blanks.map((blank) => {
        if (blank.id === blankId) {
          // Split by comma and trim each value
          const alternativeAnswers = value
            ? value.split(',').map((item) => item.trim())
            : [];
          return {
            ...blank,
            alternativeAnswers,
          };
        }
        return blank;
      });

      const update = {
        ...prev,
        blanks: updatedBlanks,
      };
      updatedQuestion = update;
      return update;
    });
    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as Question);
    }
  };

  const addNewBlank = () => {
    // Generate unique ID for new blank
    let updatedQuestion: FillBlanksQuestion | null = null;
    const newBlankId = Guid.create().toString();

    // Create new blank
    const newBlank: FillBlankItem = {
      id: newBlankId,
      correctAnswer: '',
      hint: '',
    };

    // Add blank to question
    setEditedQuestion((prev) => {
      // Add blank to array
      const updatedBlanks = [...prev.blanks, newBlank];

      // Add blank placeholder to question text if no blanks exist yet
      let updatedQuestionText = prev.question;
      if (!updatedQuestionText.includes('____')) {
        updatedQuestionText = 'Điền từ thích hợp vào chỗ trống: ____';
      } else {
        updatedQuestionText = updatedQuestionText + ' ____';
      }

      const update = {
        ...prev,
        blanks: updatedBlanks,
        question: updatedQuestionText,
        description: updatedQuestionText,
      };
      updatedQuestion = update;
      return update;
    });
    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as Question);
    }
  };

  const removeBlank = (blankId: string) => {
    let updatedQuestion: FillBlanksQuestion | null = null;
    setEditedQuestion((prev) => {
      // Get index of blank to remove
      const blankIndex = prev.blanks.findIndex((b) => b.id === blankId);

      if (blankIndex === -1) return prev;

      // Make a copy of blanks array without the removed blank
      const updatedBlanks = prev.blanks.filter((b) => b.id !== blankId);

      // We need to update question text to remove the corresponding blank placeholder
      // This is tricky since we need to identify which ____ to remove
      // Let's split by blank placeholders and remove the one at blankIndex
      const parts = prev.question.split('____');

      // If we have fewer parts than expected, don't modify the text
      if (parts.length <= blankIndex + 1) {
        return {
          ...prev,
          blanks: updatedBlanks,
        };
      }

      // Remove the blank placeholder by joining parts without the blank at blankIndex
      let updatedQuestionText = '';
      parts.forEach((part, index) => {
        updatedQuestionText += part;
        if (index < parts.length - 1 && index !== blankIndex) {
          updatedQuestionText += '____';
        }
      });

      const update = {
        ...prev,
        blanks: updatedBlanks,
        question: updatedQuestionText,
        description: updatedQuestionText,
      };
      updatedQuestion = update;
      return update;
    });

    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as Question);
    }
  };

  const handleSaveConfig = () => {
    // Validate the configuration
    const hasTitle = editedQuestion.title.trim() !== '';
    const hasQuestionText = editedQuestion.description?.trim() !== '';
    const hasAtLeastOneBlank = editedQuestion.blanks.length > 0;
    const allBlanksHaveAnswers = editedQuestion.blanks.every(
      (blank) => blank.correctAnswer.trim() !== ''
    );

    if (!hasTitle) {
      message.error('Tiêu đề không được để trống!');
      return;
    }

    if (!hasQuestionText) {
      message.error('Câu hỏi không được để trống!');
      return;
    }

    if (!hasAtLeastOneBlank) {
      message.error('Phải có ít nhất một ô trống!');
      return;
    }

    if (!allBlanksHaveAnswers) {
      message.error('Tất cả các ô trống phải có đáp án đúng!');
      return;
    }

    // Count blanks in question text
    const blankCount = (editedQuestion.question.match(/____/g) || []).length;
    if (blankCount !== editedQuestion.blanks.length) {
      message.error(
        `Số lượng ô trống trong câu hỏi (${blankCount}) phải bằng số lượng đáp án (${editedQuestion.blanks.length})!`
      );
      return;
    }
    editedQuestion.answers = [
      {
        id: Guid.create().toString(),
        isCorrect: true,
        answers: editedQuestion.blanks.reduce((acc, blank) => {
          acc[blank.id] = blank.correctAnswer;
          return acc;
        }, {} as { [blankId: string]: string }),
      },
    ];
    // Save configuration
    handleChangeQuestion(editedQuestion as Question);
    message.success('Lưu cấu hình thành công!');
  };

  const deleteConfirm = () => {
    handleDeleteQuestion(question.id);
  };

  const deleteCancel = () => {
    // Handle cancel action if needed
  };

  const collapseItems = [
    {
      key: '1',
      label: practiceLocalization['Advanced Options'],
      children: (
        <>
          <Form.Item label="Tùy chọn">
            <Space>
              <Switch
                checked={editedQuestion.caseSensitive}
                onChange={handleCaseSensitiveChange}
              />
              <span>Phân biệt chữ hoa/thường</span>
            </Space>
            <Divider type="vertical" />
            <Space>
              <Switch
                checked={editedQuestion.showHints}
                onChange={handleShowHintsChange}
              />
              <span>Hiển thị gợi ý</span>
            </Space>
          </Form.Item>
          <Form.Item label="Giải thích đáp án">
            <TextArea
              rows={3}
              value={editedQuestion.explanation || ''}
              onChange={handleExplanationChange}
              placeholder="Nhập giải thích cho đáp án"
            />
          </Form.Item>
        </>
      ),
    },
  ];

  useEffect(() => {
    handleReset();
  }, [question.id]);

  // region Config_mode
  if (configMode) {
    return (
      <Card>
        <Form form={form} className="form-config" layout="vertical">
          <Form.Item required>
            <Input
              variant="underlined"
              value={editedQuestion.title}
              onChange={handleTitleChange}
              placeholder="Nhập tiêu đề câu hỏi"
              autoFocus={focus === 'title'}
            />
          </Form.Item>

          <Form.Item
            label="Nội dung câu hỏi (sử dụng ____ để đánh dấu ô trống)"
            required
            tooltip="Mỗi dấu ____ sẽ được thay thế bằng một ô trống. Số lượng ô trống phải bằng số lượng đáp án."
          >
            <Space.Compact className="tailwind-relative tailwind-w-full">
              <TextArea
                ref={contentQuestionRef}
                className="fill-blanks-textarea"
                value={editedQuestion.question}
                onChange={handleQuestionTextChange}
                placeholder="Nhập nội dung câu hỏi"
                autoSize={{ minRows: 2 }}
                autoFocus={focus === 'question'}
              />
            </Space.Compact>
            <TooltipAntdCustom title="Chèn ô trống">
              <Button
                type="primary"
                variant="filled"
                hidden={focus !== 'question'}
                icon={<PlusOutlined />}
                onClick={handleInsertBlank}
                className={'tailwind-h-full tailwind-mt-2'}
              ></Button>
            </TooltipAntdCustom>
          </Form.Item>

          <Divider>Các ô trống</Divider>

          {editedQuestion.blanks.map((blank, index) => (
            <div key={blank.id} className="blank-config-item">
              <div className="blank-config-row">
                <Space align="baseline" style={{ marginBottom: '10px' }}>
                  <Tag color="blue">Ô trống {index + 1}</Tag>
                  <Button
                    type="text"
                    danger
                    icon={<DeleteIcon />}
                    onClick={() => removeBlank(blank.id)}
                  />
                </Space>
              </div>

              <Form.Item label="Đáp án đúng" required>
                <Input
                  variant="underlined"
                  value={blank.correctAnswer}
                  onChange={(e) =>
                    handleBlankAnswerChange(
                      blank.id,
                      'correctAnswer',
                      e.target.value
                    )
                  }
                  placeholder="Nhập đáp án đúng"
                />
              </Form.Item>

              {/* <Form.Item label="Gợi ý">
                <Input
                  variant="underlined"
                  value={blank.hint || ''}
                  onChange={(e) =>
                    handleBlankAnswerChange(blank.id, 'hint', e.target.value)
                  }
                  placeholder="Nhập gợi ý cho đáp án này"
                />
              </Form.Item> */}

              <Form.Item label="Các đáp án thay thế (phân cách bằng dấu phẩy ',')">
                <div className="blank-alternatives">
                  <Input
                    variant="underlined"
                    value={blank.alternativeAnswers?.join(', ') || ''}
                    onChange={(e) =>
                      handleAlternativeStringChange(blank.id, e.target.value)
                    }
                    placeholder="Nhập các đáp án thay thế, phân cách bằng dấu phẩy"
                  />
                </div>
              </Form.Item>
            </div>
          ))}

          <Form.Item>
            <Button type="dashed" onClick={addNewBlank} icon={<PlusOutlined />}>
              Thêm ô trống
            </Button>
          </Form.Item>

          {!hideSaveButton && (
            <Form.Item>
              <Button
                type="primary"
                onClick={handleSaveConfig}
                className="flex-1 bg-blue-500 hover:bg-blue-600"
                icon={<CheckOutlined />}
                block
              >
                Lưu cấu hình
              </Button>
            </Form.Item>
          )}

          <Collapse defaultActiveKey={[]} ghost items={collapseItems} />

          <PopconfirmAntdCustom
            title={quizLocalization.buttons.deleteQuestion.confirmTitle}
            onConfirm={deleteConfirm}
            onCancel={deleteCancel}
            okText={quizLocalization.buttons.deleteQuestion.yes}
            cancelText={quizLocalization.buttons.deleteQuestion.no}
          >
            <Button danger icon={<DeleteFilled />}>
              {quizLocalization.buttons.deleteQuestion.button}
            </Button>
          </PopconfirmAntdCustom>
        </Form>
      </Card>
    );
  }
  // endregion Config_mode

  // Render normal mode
  return (
    <Card
      title={
        <span className="item-config" onClick={() => setFocus('title')}>
          {question.title}
        </span>
      }
      className="fill-blanks-card"
      extra={
        <>
          <TooltipAntdCustom title="Hướng dẫn">
            <Button
              icon={<QuestionOutlined />}
              type="text"
              onClick={() => message.info('Điền từ thích hợp vào chỗ trống.')}
            />
          </TooltipAntdCustom>
        </>
      }
    >
      <div className="fill-blanks-question tailwind-text-base">
        <Text>{renderQuestion()}</Text>
      </div>

      {isSubmitted && question.explanation && showFeedback && (
        <div
          className={`fill-blanks-explanation ${
            isCorrect ? 'correct' : 'incorrect'
          }`}
          style={{
            marginTop: '1rem',
          }}
        >
          <Text strong>{isCorrect ? 'Giải thích:' : 'Đáp án đúng:'} </Text>
          <Text>{question.explanation}</Text>
        </div>
      )}
    </Card>
  );
};

export default FillBlanksComponent;

// VideoComponent.tsx
import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, Select, Switch, message, Tooltip, Modal } from 'antd';
import { EngineContainer } from '../../../../engines';
import '../../MediaComponentStyles.css';
import type { UploadFile } from 'antd/es/upload/interface';
import { IEdTechRenderProps } from '../../../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { VideoComponentProps, VideoItem } from '../shared/types';
import { defaultProps } from '../shared/constants';
import { ITextProps } from '../../../../../core/title/CoreTitle';

import BasicVideoViewer, { BasicVideoViewerRef } from './BasicVideoViewer';
import {
  MediaFileUploaderContainer,
  UploadedFile,
} from '../../shared/MediaFileUploader';
import { DragDropMediaWrapper } from '../../shared';
import tempFileApi from '../../../../../../api/tempFileApi';
import {
  processVideoUrl,
  extractYoutubeId,
  extractVimeoId,
  getFileNameWithoutExtension,
  getFileExtension,
} from '../../shared/utils';
import {
  ArrowDownloadIcon,
  CheckmarkOutlineIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DeleteIcon,
  DismissOutlineIcon,
  EditIcon,
  LinkOutlinedIcon,
  PlayCircleOutlinedIcon,
  VideoOutlinedIcon,
} from '../../../../../icons/IconIndex';

const { Option } = Select;

const VideoComponent: React.FC<IEdTechRenderProps<VideoComponentProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Merge with default props
  const config: VideoComponentProps = { ...defaultProps, ...params };

  // State for video player
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [tempUrl, setTempUrl] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);

  const [editingNameIndex, setEditingNameIndex] = useState<number | null>(null);
  const [tempVideoName, setTempVideoName] = useState<string>('');

  // State for confirmation modals
  const [deleteConfirmVisible, setDeleteConfirmVisible] =
    useState<boolean>(false);
  const [deleteAllConfirmVisible, setDeleteAllConfirmVisible] =
    useState<boolean>(false);
  const [videoToDeleteIndex, setVideoToDeleteIndex] = useState<number | null>(
    null
  );

  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<HTMLDivElement>(null);
  const videoViewerRef = useRef<BasicVideoViewerRef>(null);

  useEffect(() => {
    // If we have a legacy videoUrl but no videos array, convert it
    if (
      config.mediaUrl &&
      config.blobKey &&
      config.blobContext &&
      (!config.medias || config.medias.length === 0)
    ) {
      addOrUpdateParamComponent({
        ...config,
        medias: [
          {
            mediaUrl: config.mediaUrl,
            mediaType: config.mediaType,
            name: config.name || '',
            blobKey: config.blobKey,
            blobContext: config.blobContext,
          },
        ],
      });
    }
  }, [config.mediaUrl, config.medias]);

  // Initialize file list if we have videos for uploads
  useEffect(() => {
    if (config.medias && config.medias.length > 0 && fileList.length === 0) {
      const newFileList = config.medias
        .filter((video) => video.mediaType === 'upload')
        .map((video, index) => {
          const fileName =
            video.mediaUrl.split('/').pop() || `video-${index + 1}`;
          return {
            uid: `-${index + 1}`,
            name: fileName,
            status: 'done' as const,
            url: video.mediaUrl,
          };
        });

      if (newFileList.length > 0) {
        setFileList(newFileList);
      }
    }
  }, [config.medias]);

  // Effect to sync video state when the config changes
  useEffect(() => {
    if (videoRef.current && config.medias && config.medias.length > 0) {
      if (config.startTime && currentTime < config.startTime) {
        videoRef.current.currentTime = config.startTime;
      }
    }
  }, [config, currentTime]);

  useEffect(() => {
    // Avoid dimension initialization if we're in the middle of a deletion
    // This helps prevent update loops
    let isInitializing = true;

    const initializeDimensions = () => {
      if (!isInitializing) return;

      if (containerRef.current) {
        // Force immediate update of dimensions when component mounts
        if (!config.medias || config.medias.length === 0) {
          if (isEditing && addOrUpdateParamComponent) {
            // Store current config to avoid unnecessary updates
            const currentWidth = config.width;
            const currentHeight = config.height;

            // Only update if dimensions actually changed
            if (currentWidth !== '100%' || currentHeight !== 'auto') {
              addOrUpdateParamComponent({
                ...config,
                width: '100%', // Use percentage for responsive width
                height: 'auto', // Let height be determined by aspect ratio
              });
            }
          }
        }
      }
    };

    // Try to initialize immediately
    initializeDimensions();

    // Set flag to false after initial run
    isInitializing = false;

    // Add window resize listener for responsive behavior
    const handleResize = () => {
      // Don't initialize on resize if we have no videos
      if (config.medias && config.medias.length > 0) {
        initializeDimensions();
      }
    };

    window.addEventListener('resize', handleResize);

    const timeoutId = setTimeout(() => {
      // Only run delayed initialization if we have videos
      if (config.medias && config.medias.length > 0) {
        initializeDimensions();
      }
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [config.medias, isEditing, addOrUpdateParamComponent, config]);

  const showDeleteConfirmation = (index: number) => {
    setVideoToDeleteIndex(index);
    setDeleteConfirmVisible(true);
  };

  const showDeleteAllConfirmation = () => {
    setDeleteAllConfirmVisible(true);
  };

  const handleCancelDelete = () => {
    setDeleteConfirmVisible(false);
    setDeleteAllConfirmVisible(false);
    setVideoToDeleteIndex(null);
  };

  // Actual delete video implementation
  const performDeleteVideo = async (index?: number) => {
    try {
      // Prevent multiple delete operations from running simultaneously
      if (!config.medias || config.medias.length === 0) {
        message.info('Không có video để xóa');
        return;
      }

      // Case 1: Delete a specific video
      if (index !== undefined) {
        if (index < 0 || index >= config.medias.length) {
          message.error('Không thể xóa video: Chỉ mục không hợp lệ');
          return;
        }

        const videoToDelete = config.medias[index];

        // Delete from server if it's an uploaded video
        if (videoToDelete.mediaType === 'upload') {
          try {
            let url = videoToDelete.mediaUrl;
            if (!url.startsWith('data:') && videoToDelete.blobKey) {
              await tempFileApi.Delete(videoToDelete.blobKey);
            }
          } catch (error) {
            console.error('Error deleting video from server:', error);
          }
        }

        // Create a new videos array without the deleted video
        const newVideos = [...config.medias];
        newVideos.splice(index, 1);

        // Update fileList to match
        const newFileList = fileList.filter((_, i) => i !== index);
        setFileList(newFileList);

        // Get container width for consistent sizing
        const engineWidth =
          containerRef.current?.parentElement?.offsetWidth || 800;

        // If we deleted the last video, reset to empty state
        if (newVideos.length === 0) {
          // Apply dimensions to container for immediate visual feedback
          if (containerRef.current) {
            containerRef.current.style.width = `${engineWidth}px`;
            containerRef.current.style.height = '200px';
          }

          // First reset the selected index to 0
          setSelectedIndex(0);

          // Use setTimeout to ensure state updates are processed before updating component
          setTimeout(() => {
            // Reset component state with empty videos array
            addOrUpdateParamComponent({
              ...config,
              medias: [],
              width: engineWidth,
              height: 200,
            });

            message.success('Đã xóa video thành công');
          }, 0);
        } else {
          // First adjust selectedIndex if needed
          if (selectedIndex >= newVideos.length) {
            const newIndex = Math.max(0, newVideos.length - 1);
            setSelectedIndex(newIndex);
          }

          // Use setTimeout to ensure state updates are processed before updating component
          setTimeout(() => {
            addOrUpdateParamComponent({
              ...config,
              medias: newVideos,
            });

            message.success('Đã xóa video thành công');
          }, 0);
        }
      }
      // Case 2: Delete all videos
      else {
        const deletePromises = [];
        for (const video of config.medias) {
          if (video.mediaType === 'upload') {
            try {
              let url = video.mediaUrl;
              if (!url.startsWith('data:') && video.blobKey) {
                deletePromises.push(tempFileApi.Delete(video.blobKey));
              }
            } catch (error) {
              console.error('Error deleting video from server:', error);
            }
          }
        }

        // Wait for all delete operations to complete
        try {
          await Promise.allSettled(deletePromises);
        } catch (error) {
          console.error('Error in batch delete:', error);
        }

        // Reset UI state
        setFileList([]);
        setTempUrl('');

        // Get container width for consistent sizing
        const engineWidth =
          containerRef.current?.parentElement?.offsetWidth || 800;

        // Apply dimensions to container for immediate visual feedback
        if (containerRef.current) {
          containerRef.current.style.width = `${engineWidth}px`;
          containerRef.current.style.height = '200px';
        }

        // First reset the selected index to 0
        setSelectedIndex(0);

        // Use setTimeout to ensure state updates are processed before updating component
        setTimeout(() => {
          // Reset component state with empty videos array
          addOrUpdateParamComponent({
            ...config,
            medias: [],
            width: engineWidth,
            height: 200,
          });

          message.success('Đã xóa tất cả video thành công');
        }, 0);
      }
    } catch (error: any) {
      console.error('Error in performDeleteVideo:', error);
      message.error(
        `Lỗi khi xóa video: ${error.message || 'Lỗi không xác định'}`
      );
    } finally {
      // Reset confirmation states
      setDeleteConfirmVisible(false);
      setDeleteAllConfirmVisible(false);
      setVideoToDeleteIndex(null);
    }
  };

  // Public delete function that shows confirmation first
  const handleDeleteVideo = (index?: number) => {
    if (index !== undefined) {
      showDeleteConfirmation(index);
    } else if (config.medias && config.medias.length > 0) {
      showDeleteAllConfirmation();
    } else {
      message.info('Không có video để xóa');
    }
  };

  const handleUrlSubmit = () => {
    if (!tempUrl) {
      message.error('Vui lòng nhập URL video');
      return;
    }

    // Process the URL using our utility function
    const { fullUrl } = processVideoUrl(tempUrl);

    let videoId = '';
    if (tempUrl.includes('youtube.com') || tempUrl.includes('youtu.be')) {
      videoId = extractYoutubeId(tempUrl);
    } else if (tempUrl.includes('vimeo.com')) {
      videoId = extractVimeoId(tempUrl);
    }

    const newVideo: VideoItem = {
      mediaUrl: fullUrl,
      mediaType: 'embed' as const,
      name: 'Video từ URL',
      blobKey: videoId,
      blobContext: '',
    };

    const newVideos = config.medias ? [...config.medias, newVideo] : [newVideo];

    const engineWidth = containerRef.current?.parentElement?.offsetWidth || 800;

    const newHeight = engineWidth * (9 / 16);
    addOrUpdateParamComponent({
      ...config,
      medias: newVideos,
      width: engineWidth,
      height: newHeight,
    });

    setTempUrl('');
  };

  const handlePrevVideo = () => {
    if (!config.medias || config.medias.length <= 1) return;

    const newIndex =
      selectedIndex > 0 ? selectedIndex - 1 : config.medias.length - 1;
    setSelectedIndex(newIndex);
  };

  const handleNextVideo = () => {
    if (!config.medias || config.medias.length <= 1) return;

    const newIndex =
      selectedIndex < config.medias.length - 1 ? selectedIndex + 1 : 0;
    setSelectedIndex(newIndex);
  };

  // Handle selecting a specific video
  const handleSelectVideo = (index: number) => {
    if (!config.medias || index >= config.medias.length) return;

    setSelectedIndex(index);
  };

  // Start editing video name
  const startEditingVideoName = (index: number) => {
    if (!config.medias || index >= config.medias.length) return;

    // Get the current video name
    const video = config.medias[index];
    const currentName = video.name || '';

    // Extract filename without extension and set it as the editing value
    const nameWithoutExtension = getFileNameWithoutExtension(currentName);

    console.log('Editing video name:', {
      index,
      currentName,
      nameWithoutExtension,
    });

    setTempVideoName(nameWithoutExtension);
    setEditingNameIndex(index);
  };

  // Save edited video name
  const saveVideoName = (index: number) => {
    if (!config.medias || index >= config.medias.length || !tempVideoName)
      return;

    // Get the current video
    const video = config.medias[index];

    // Get the current name from the config
    const currentName = video.name || '';

    // Extract the extension from the current name
    const extension = getFileExtension(currentName);

    // Add the extension to the edited name
    const newName = tempVideoName + (extension ? '.' + extension : '');

    console.log('Saving video name:', {
      currentName,
      editingValue: tempVideoName,
      extension,
      newName,
    });

    // Create a new video object with the updated name property
    const updatedVideo = {
      ...video,
      name: newName,
    };

    // Update the videos array
    const updatedVideos = [...config.medias];
    updatedVideos[index] = updatedVideo;

    // Update component
    addOrUpdateParamComponent({
      ...config,
      medias: updatedVideos,
    });

    // Reset editing state
    setEditingNameIndex(null);
    setTempVideoName('');
  };

  // Cancel editing video name
  const cancelEditingVideoName = () => {
    setEditingNameIndex(null);
    setTempVideoName('');
  };

  // Handle toggle changes
  const handleToggleChange = (name: string, checked: boolean) => {
    addOrUpdateParamComponent({
      ...config,
      [name]: checked,
    });
  };

  // Handle input changes
  const handleInputChange = (name: string, value: any) => {
    // For all updates, just update the component configuration
    addOrUpdateParamComponent({
      ...config,
      [name]: value,
    });
  };

  // Render drag and drop area for video upload
  const renderDragDropArea = () => {
    return (
      <MediaFileUploaderContainer
        mediaType="video"
        onUploadSuccess={handleUploadSuccess}
        allowDrop={true}
        dropAreaHeight="100%"
        dropAreaWidth="100%"
        className={isEditing ? 'editing' : ''}
        onUploadComplete={() => setFileList([])}
        isEditing={isEditing}
        containerRef={containerRef}
      />
    );
  };

  // Render video thumbnails for navigation
  const renderVideoThumbnails = () => {
    try {
      // Only show thumbnails if we have multiple videos
      if (!config.medias || config.medias.length <= 1) {
        return null;
      }

      // Filter out any invalid videos (missing URLs)
      const validVideos = config.medias.filter(
        (video) => video && video.mediaUrl
      );

      if (validVideos.length <= 1) {
        return null;
      }

      // Larger thumbnail size for better visibility
      const thumbnailSize = 60;

      return (
        <div
          className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-p-3 tailwind-overflow-x-auto"
          style={{ maxWidth: '100%' }}
        >
          {validVideos.map((video) => {
            // Skip invalid videos
            if (!video || !video.mediaUrl) {
              return null;
            }

            // Determine video type and get appropriate thumbnail
            const isYouTube =
              video.mediaUrl &&
              (video.mediaUrl.includes('youtube.com') ||
                video.mediaUrl.includes('youtu.be'));

            const isVimeo =
              video.mediaUrl && video.mediaUrl.includes('vimeo.com');

            // Get thumbnail based on video type
            let thumbnailUrl = null;

            if (isYouTube && video.blobKey) {
              // YouTube thumbnail
              thumbnailUrl = `https://img.youtube.com/vi/${video.blobKey}/default.jpg`;
            } else if (isVimeo && video.blobKey) {
              // Vimeo thumbnail (using placeholder - actual Vimeo thumbnails require API)
              thumbnailUrl = `https://vumbnail.com/${video.blobKey}.jpg`;
            }

            // Find the actual index in the original array
            const originalIndex = config.medias
              ? config.medias.findIndex((v) => v === video)
              : -1;
            const isSelected = originalIndex === selectedIndex;

            return (
              <div
                key={originalIndex}
                className={`tailwind-cursor-pointer tailwind-rounded-md tailwind-overflow-hidden tailwind-transition-all tailwind-duration-200 ${
                  isSelected ? 'tailwind-shadow-md' : 'hover:tailwind-shadow-md'
                }`}
                onClick={() => handleSelectVideo(originalIndex)}
                style={{
                  width: `${thumbnailSize}px`,
                  height: `${thumbnailSize}px`,
                  minWidth: `${thumbnailSize}px`,
                  maxWidth: `${thumbnailSize}px`,
                  flexShrink: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5',
                  position: 'relative',
                  margin: '4px',
                  borderRadius: '4px',
                  border: isSelected
                    ? '2px solid #EA4C89'
                    : '1px solid #e0e0e0',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                }}
              >
                {thumbnailUrl ? (
                  <img
                    src={thumbnailUrl}
                    alt={`Video ${originalIndex + 1}`}
                    className="tailwind-w-full tailwind-h-full tailwind-object-cover"
                  />
                ) : (
                  <VideoOutlinedIcon
                    height={Math.floor(thumbnailSize * 0.4)}
                    width={Math.floor(thumbnailSize * 0.4)}
                    style={{
                      color: '#666',
                    }}
                  />
                )}
                <div className="tailwind-absolute tailwind-inset-0 tailwind-bg-black tailwind-bg-opacity-20 tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center">
                  <PlayCircleOutlinedIcon />
                  {/* Show a badge indicating video type */}
                  <span
                    className="tailwind-absolute tailwind-top-0 tailwind-right-0 tailwind-text-xs tailwind-px-1 tailwind-rounded-bl tailwind-text-white"
                    style={{
                      backgroundColor:
                        video.mediaType === 'upload' ? '#52c41a' : '#1890ff',
                      fontSize: '8px',
                    }}
                  >
                    {video.mediaType === 'upload' ? 'UP' : 'URL'}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      );
    } catch (error: any) {
      console.error('Error rendering video thumbnails:', error);
      return null;
    }
  };
  // Render video based on type
  const renderVideo = () => {
    try {
      if (!config.medias || config.medias.length === 0) {
        return null;
      }
      if (config.medias.length > 0) {
        const validIndex = Math.min(
          Math.max(0, selectedIndex),
          config.medias.length - 1
        );
        if (validIndex !== selectedIndex) {
          setSelectedIndex(validIndex);
        }
      } else {
        return null;
      }

      if (config.medias.length === 0) {
        return null;
      }

      const currentVideo = config.medias[selectedIndex];

      if (!currentVideo) {
        return null;
      }

      if (!currentVideo.mediaUrl) {
        return (
          <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-w-full tailwind-h-full tailwind-bg-gray-100 tailwind-text-gray-500 tailwind-p-4 tailwind-rounded">
            Video không hợp lệ hoặc không có URL
          </div>
        );
      }

      return (
        <div
          ref={playerRef}
          className="tailwind-relative tailwind-w-full video-player-container tailwind-overflow-hidden tailwind-shadow-none tailwind-aspect-video"
        >
          {/* Video element using BasicVideoViewer */}
          <div className="tailwind-w-full tailwind-h-full tailwind-overflow-hidden">
            <BasicVideoViewer
              ref={videoViewerRef}
              videoUrl={currentVideo.mediaUrl}
              videoType={currentVideo.mediaType || 'upload'}
              videoId={currentVideo.blobKey}
              videoRef={videoRef}
              isEditing={isEditing}
              controls={config.controls}
              autoPlay={config.autoPlay}
              loop={config.loop}
              startTime={config.startTime}
              endTime={config.endTime}
              onTimeUpdate={(time) => setCurrentTime(time)}
              isFullscreen={isFullscreen}
            />
          </div>
        </div>
      );
    } catch (error: any) {
      console.error('Error rendering video:', error);
      return (
        <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-w-full tailwind-h-full tailwind-bg-gray-100 tailwind-text-gray-500 tailwind-p-4 tailwind-rounded">
          Lỗi hiển thị video: {error.message || 'Lỗi không xác định'}
        </div>
      );
    }
  };

  // Hàm chung để hiển thị một video và các điều khiển của nó
  const renderVideoItem = (
    video: VideoItem,
    index: number,
    isVertical: boolean = false
  ) => {
    // Lấy tên video để hiển thị
    const videoFileName =
      video.name || video.mediaUrl.split('/').pop() || `video-${index + 1}`;
    const videoNameWithoutExtension =
      videoFileName.split('.').slice(0, -1).join('.') || videoFileName;

    return (
      <div
        key={index}
        className={`tailwind-w-full tailwind-relative ${
          isVertical ? (isEditing ? 'tailwind-mb-6' : 'tailwind-mb-4') : ''
        }`}
      >
        {/* Video name and controls row */}
        <div
          className={`tailwind-flex tailwind-justify-between tailwind-items-center tailwind-w-full tailwind-mb-2`}
        >
          {/* Video name with edit functionality */}
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            {editingNameIndex === index ? (
              <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
                <Input
                  value={tempVideoName}
                  onChange={(e) => setTempVideoName(e.target.value)}
                  size="small"
                  className="tailwind-max-w-[200px]"
                  autoFocus
                  onPressEnter={() => saveVideoName(index)}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<CheckmarkOutlineIcon height={14} width={14} />}
                  onClick={() => saveVideoName(index)}
                  style={{ color: '#52c41a' }}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<DismissOutlineIcon height={14} width={14} />}
                  onClick={cancelEditingVideoName}
                  style={{ color: '#ff4d4f' }}
                />
              </div>
            ) : (
              <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
                <span className="tailwind-text-sm tailwind-font-medium">
                  {videoNameWithoutExtension}
                </span>
                {isEditing && (
                  <Tooltip title="Chỉnh sửa tên video">
                    <Button
                      type="text"
                      icon={<EditIcon height={14} width={14} />}
                      onClick={() => startEditingVideoName(index)}
                      className="tailwind-text-gray-500 hover:tailwind-text-pink-500"
                      style={{
                        width: '26px',
                        height: '26px',
                        minWidth: '26px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 0,
                      }}
                    />
                  </Tooltip>
                )}
              </div>
            )}
          </div>

          {/* Control buttons */}
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            {isEditing && (
              <>
                <Tooltip title="Tải về">
                  <Button
                    type="text"
                    icon={<ArrowDownloadIcon />}
                    onClick={() => {
                      if (video.mediaUrl && video.mediaType === 'upload') {
                        const link = document.createElement('a');
                        link.href = video.mediaUrl;
                        link.download = videoFileName;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      } else {
                        message.info('Chỉ có thể tải xuống video đã tải lên');
                      }
                    }}
                    style={{
                      width: '26px',
                      height: '26px',
                      minWidth: '26px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 0,
                      backgroundColor: 'white',
                      border: '1px solid #d9d9d9',
                      borderRadius: '4px',
                    }}
                  />
                </Tooltip>
                <Tooltip title="Xoá video">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteIcon />}
                    onClick={(e) => {
                      if (e) e.stopPropagation();
                      handleDeleteVideo(index);
                    }}
                    style={{
                      width: '26px',
                      height: '26px',
                      minWidth: '26px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 0,
                      backgroundColor: 'white',
                      border: '1px solid #ff4d4f',
                      borderRadius: '4px',
                    }}
                  />
                </Tooltip>
              </>
            )}
          </div>
        </div>

        {isVertical && (
          // Use the same grid layout as horizontal mode for consistent sizing
          <div className="tailwind-w-full tailwind-grid tailwind-grid-cols-[60px_1fr_60px] tailwind-gap-2 tailwind-items-center">
            {/* Left empty space for consistent layout */}
            <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
              {/* Empty div for consistent spacing */}
            </div>

            {/* Center video container - Same as horizontal mode */}
            <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-overflow-hidden">
              <div
                className={`tailwind-w-full tailwind-flex tailwind-justify-center media-container video-container tailwind-overflow-hidden tailwind-h-auto tailwind-p-0 tailwind-box-border tailwind-items-center tailwind-max-w-full tailwind-aspect-video ${
                  isEditing ? 'editing' : ''
                }`}
              >
                <div className="tailwind-w-full tailwind-h-full tailwind-overflow-hidden">
                  <BasicVideoViewer
                    videoUrl={video.mediaUrl}
                    videoType={video.mediaType || 'upload'}
                    videoId={video.blobKey}
                    isEditing={isEditing}
                    controls={config.controls}
                    autoPlay={config.autoPlay}
                    loop={config.loop}
                    startTime={config.startTime}
                    endTime={config.endTime}
                    isFullscreen={isFullscreen}
                  />
                </div>
              </div>
            </div>

            {/* Right empty space for consistent layout */}
            <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
              {/* Empty div for consistent spacing */}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render video controls for navigation between videos
  const renderVideoControls = () => {
    try {
      // Check if we have videos to display
      if (!config.medias || !config.medias.length) {
        return null;
      }

      if (config.medias.length > 0) {
        const validIndex = Math.min(
          Math.max(0, selectedIndex),
          config.medias.length - 1
        );
        if (validIndex !== selectedIndex) {
          setSelectedIndex(validIndex);
        }
      } else {
        return null;
      }

      if (config.medias.length === 0) {
        return null;
      }

      const currentVideo = config.medias[selectedIndex];

      if (!currentVideo) {
        return null;
      }

      return (
        <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-gap-2">
          {renderVideoItem(currentVideo, selectedIndex, false)}
        </div>
      );
    } catch (error: any) {
      console.error('Error rendering video controls:', error);
      return null;
    }
  };

  // Render all videos vertically (vertical mode)
  const renderVerticalVideos = () => {
    try {
      // Check if we have videos and are in vertical mode
      if (
        !config.medias ||
        config.medias.length === 0 ||
        config.displayMode !== 'vertical'
      ) {
        return null;
      }

      const validVideos = config.medias.filter(
        (video) => video && video.mediaUrl
      );

      if (validVideos.length === 0) {
        return null;
      }

      return (
        <div className="vertical-images-container tailwind-w-full tailwind-mt-3 tailwind-max-w-full tailwind-mx-auto">
          <div className="tailwind-flex tailwind-flex-col tailwind-gap-6 tailwind-items-center">
            {validVideos.map((video, index) =>
              renderVideoItem(video, index, true)
            )}
          </div>
        </div>
      );
    } catch (error: any) {
      console.error('Error rendering vertical videos:', error);
      return null;
    }
  };

  // Xử lý khi upload thành công
  const handleUploadSuccess = (files: UploadedFile[]) => {
    // Tạo mảng các đối tượng video mới từ các file đã upload
    const newUploadedVideos = files.map((item) => {
      // Process the URL using our utility function
      const { fullUrl } = processVideoUrl(item.url);

      return {
        mediaUrl: fullUrl,
        mediaType: 'upload' as const,
        name: item.name || 'Video',
        blobKey: item.blobKey || '',
        blobContext: item.blobContext,
      } as VideoItem;
    });
    // Kết hợp với video hiện có
    const updatedVideos = config.medias
      ? [...config.medias, ...newUploadedVideos]
      : newUploadedVideos;

    // Get engine container width
    const engineWidth = containerRef.current?.parentElement?.offsetWidth || 800;

    // Calculate height based on 16:9 aspect ratio
    const newHeight = engineWidth * (9 / 16);

    // Cập nhật component với video mới
    addOrUpdateParamComponent({
      ...config,
      medias: updatedVideos,
      width: engineWidth,
      height: newHeight,
    });
  };

  // Render upload button for videos
  const renderUploadButton = () => {
    return (
      <MediaFileUploaderContainer
        mediaType="video"
        onUploadSuccess={handleUploadSuccess}
        allowDrop={false}
        onUploadComplete={() => setFileList([])}
      />
    );
  };

  const renderVideoSettings = () => {
    if (!isEditing) return null;

    // Safely check if we have videos
    const hasVideos = config.medias && config.medias.length > 0;
    if (!hasVideos) return null;

    // Get the current video to display - only if selectedIndex is valid
    const currentVideo =
      config.medias && selectedIndex < config.medias.length
        ? config.medias[selectedIndex]
        : null;

    if (!currentVideo) return null;

    return (
      <div className="tailwind-w-full tailwind-mb-4 tailwind-bg-gray-100 tailwind-p-4 tailwind-rounded-lg">
        <div className="tailwind-flex tailwind-flex-wrap tailwind-items-center">
          {/* Appearance settings */}
          <div className="media-config-section">
            {/* Video Settings - Only show when video is added */}
            <div className="tailwind-flex tailwind-flex-wrap tailwind-items-start tailwind-gap-x-6 tailwind-gap-y-3 tailwind-mb-4">
              <div className="media-config-switch-item tailwind-flex tailwind-items-center">
                <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                  Chế độ hiển thị:
                </span>
                <Select
                  className="tailwind-w-48 tailwind-max-w-full"
                  value={config.displayMode || 'horizontal'}
                  onChange={(value) => handleInputChange('displayMode', value)}
                  size="middle"
                  popupMatchSelectWidth={false}
                  style={{ minWidth: '180px' }}
                >
                  <Option value="horizontal">Trượt ngang</Option>
                  <Option value="vertical">Xếp dọc</Option>
                </Select>
              </div>
              <div className="media-config-switch-item tailwind-flex tailwind-items-center">
                <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                  Tự động phát
                </span>
                <Switch
                  checked={config.autoPlay}
                  onChange={(checked) =>
                    handleToggleChange('autoPlay', checked)
                  }
                />
              </div>
              <div className="media-config-switch-item tailwind-flex tailwind-items-center">
                <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                  Lặp video
                </span>
                <Switch
                  checked={config.loop}
                  onChange={(checked) => handleToggleChange('loop', checked)}
                />
              </div>
              <div className="media-config-switch-item tailwind-flex tailwind-items-center">
                <span className="tailwind-font-semibold tailwind-w-40 tailwind-whitespace-nowrap">
                  Hiển thị điều khiển
                </span>
                <Switch
                  checked={config.controls}
                  onChange={(checked) =>
                    handleToggleChange('controls', checked)
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render the main component content
  const renderMainComponent = () => {
    // Safely check if we have videos
    const hasVideos = config.medias && config.medias.length > 0;

    // Get the current video to display - only if we have videos and selectedIndex is valid
    const currentVideo =
      hasVideos && config.medias && selectedIndex < config.medias.length
        ? config.medias[selectedIndex]
        : null;

    // Check if we should use vertical display mode
    const useVerticalMode = config.displayMode === 'vertical' && hasVideos;

    return (
      <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-overflow-visible tailwind-pb-4">
        {isEditing && (
          <div className="tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-between tailwind-gap-2 tailwind-mb-2">
            <div
              className="tailwind-flex tailwind-items-center tailwind-gap-2"
              style={{ width: '50%' }}
            >
              <Input
                placeholder="Nhập URL video (hỗ trợ Youtube, Vimeo và URI video khác)"
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                onPressEnter={handleUrlSubmit}
                prefix={<LinkOutlinedIcon className="tailwind-text-primary" />}
                style={{ width: '100%' }}
                size="middle"
              />
              <Button
                onClick={handleUrlSubmit}
                className="tailwind-flex-shrink-0"
                style={{
                  backgroundColor: 'var(--edtt-color-primary)',
                  color: 'var(--edtt-color-white)',
                  border: 'none',
                  borderRadius: '4px',
                }}
              >
                Thêm video
              </Button>
            </div>
            {config.medias && config.medias.length > 0 && (
              <Button
                type="primary"
                danger
                icon={<DeleteIcon />}
                onClick={() => handleDeleteVideo()}
                size="middle"
                style={{
                  backgroundColor: 'var(--edtt-color-status-error)',
                  borderColor: 'var(--edtt-color-status-error)',
                }}
              >
                Xóa tất cả video
              </Button>
            )}
          </div>
        )}
        {isEditing &&
          config.medias &&
          config.medias.length > 0 &&
          renderVideoSettings()}

        {isEditing && config.medias && config.medias.length > 0 && (
          <>
            {renderUploadButton()}
            <p className="tailwind-mt-4 tailwind-text-sm tailwind-text-center tailwind-italic tailwind-text-gray-400">
              Hoặc kéo & thả để thêm video
            </p>
          </>
        )}
        {useVerticalMode ? (
          <div
            ref={containerRef}
            className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-mx-auto ${
              isEditing ? 'media-container editing' : ''
            }`}
          >
            {/* Render all videos vertically */}
            {renderVerticalVideos()}
          </div>
        ) : currentVideo ? (
          <div
            className={`tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-gap-[10px] ${
              isEditing ? 'media-container editing' : ''
            }`}
          >
            {renderVideoControls()}

            {/* Video container with 3-column layout */}
            <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-items-center">
              {/* Main video and navigation row - Using grid for 3 distinct sections */}
              <div className="tailwind-w-full tailwind-grid tailwind-grid-cols-[60px_1fr_60px] tailwind-gap-2 tailwind-items-center tailwind-mb-3">
                {/* Left navigation button section */}
                <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                  {config.displayMode === 'horizontal' &&
                    config.medias &&
                    config.medias.length > 1 && (
                      <Button
                        icon={<ChevronLeftIcon />}
                        onClick={handlePrevVideo}
                        disabled={selectedIndex === 0}
                        className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-border-0 tailwind-text-white tailwind-bg-gray-500 hover:tailwind-bg-gray-600 tailwind-w-[44px] tailwind-h-[44px] tailwind-rounded-[4px] tailwind-text-xl tailwind-shadow-md"
                      />
                    )}
                </div>

                {/* Center video container */}
                <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-overflow-hidden">
                  <DragDropMediaWrapper
                    mediaType="video"
                    isEditing={isEditing}
                    onUploadSuccess={handleUploadSuccess}
                    className={`tailwind-w-full tailwind-flex tailwind-justify-center media-container video-container tailwind-overflow-hidden tailwind-h-auto tailwind-p-0 tailwind-box-border tailwind-items-center tailwind-max-w-full tailwind-aspect-video ${
                      isEditing ? 'editing' : ''
                    }`}
                  >
                    <div
                      ref={containerRef}
                      className="tailwind-w-full tailwind-h-full tailwind-flex tailwind-justify-center tailwind-items-center"
                    >
                      {/* Video content */}
                      {renderVideo()}
                    </div>
                  </DragDropMediaWrapper>
                </div>

                {/* Right navigation button section */}
                <div className="tailwind-flex tailwind-items-center tailwind-justify-center">
                  {config.displayMode === 'horizontal' &&
                    config.medias &&
                    config.medias.length > 1 && (
                      <Button
                        icon={<ChevronRightIcon />}
                        onClick={handleNextVideo}
                        disabled={selectedIndex === config.medias.length - 1}
                        className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-border-0 tailwind-text-white tailwind-bg-gray-500 hover:tailwind-bg-gray-600 tailwind-w-[44px] tailwind-h-[44px] tailwind-rounded-[4px] tailwind-text-xl tailwind-shadow-md"
                      />
                    )}
                </div>
              </div>

              {/* Add fixed margin between video and thumbnails */}
              <div className="tailwind-w-full tailwind-mt-[10px]">
                {renderVideoThumbnails()}
              </div>
            </div>
          </div>
        ) : isEditing ? (
          <div
            ref={containerRef}
            className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-min-h-[200px] media-file-uploader-container media-container editing`}
          >
            {renderDragDropArea()}
          </div>
        ) : (
          <></>
        )}
      </div>
    );
  };

  // Handle title updates
  const handleTitleUpdate = (titleProps: Partial<ITextProps>) => {
    if (addOrUpdateParamComponent) {
      const updatedConfig = {
        ...config,
        titleProps: {
          ...config.titleProps,
          ...titleProps,
        },
      };
      addOrUpdateParamComponent(updatedConfig);
    }
  };

  // Render confirmation modals
  const renderConfirmationModals = () => {
    return (
      <>
        {/* Confirmation modal for deleting a specific video */}
        <Modal
          title="Xác nhận xóa video"
          open={deleteConfirmVisible}
          onOk={() =>
            videoToDeleteIndex !== null &&
            performDeleteVideo(videoToDeleteIndex)
          }
          onCancel={handleCancelDelete}
          okText="Xóa"
          cancelText="Hủy"
          okButtonProps={{ danger: true }}
        >
          <p>Bạn có chắc chắn muốn xóa video này không?</p>
          <p className="tailwind-text-gray-500 tailwind-text-sm">
            Hành động này không thể hoàn tác.
          </p>
        </Modal>

        {/* Confirmation modal for deleting all videos */}
        <Modal
          title="Xác nhận xóa tất cả video"
          open={deleteAllConfirmVisible}
          onOk={() => performDeleteVideo()}
          onCancel={handleCancelDelete}
          okText="Xóa tất cả"
          cancelText="Hủy"
          okButtonProps={{ danger: true }}
        >
          <p>
            Bạn có chắc chắn muốn xóa <strong>tất cả</strong> video không?
          </p>
          <p className="tailwind-text-gray-500 tailwind-text-sm">
            Hành động này không thể hoàn tác.
          </p>
        </Modal>
      </>
    );
  };

  // State for tracking fullscreen mode
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  // Handle fullscreen state changes
  const handleFullscreenChange = (fullscreen: boolean) => {
    // Update fullscreen state
    setIsFullscreen(fullscreen);

    // When entering fullscreen, ensure video controls are visible
    if (fullscreen && videoViewerRef.current) {
      // Ensure controls are visible in fullscreen mode
      if (config.medias && config.medias.length > 0) {
      }
    }
  };

  return (
    <>
      <EngineContainer
        node={props}
        mainComponent={renderMainComponent()}
        allowConfiguration={false}
        showFullscreenButton={true}
        id={props.path}
        titleProps={config.titleProps}
        onTitleUpdate={handleTitleUpdate}
        onFullscreenChange={handleFullscreenChange}
        className="tailwind-overflow-visible tailwind-pb-4" // Allow content to be visible and add padding
      />
      {renderConfirmationModals()}
    </>
  );
};
export default withEdComponentParams(VideoComponent);

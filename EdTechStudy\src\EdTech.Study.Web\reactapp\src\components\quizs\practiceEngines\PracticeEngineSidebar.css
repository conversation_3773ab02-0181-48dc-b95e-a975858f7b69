.practice-engine-sidebar {
  position: fixed;
  top: 0;
  right: -320px;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  z-index: 99999;
  display: flex;
  flex-direction: column;
}

.practice-engine-sidebar.visible {
  right: 0;
}

.practice-engine-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.practice-engine-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* Ensure FloatButton is always on top */
.ant-float-btn {
  z-index: 99999 !important;
}

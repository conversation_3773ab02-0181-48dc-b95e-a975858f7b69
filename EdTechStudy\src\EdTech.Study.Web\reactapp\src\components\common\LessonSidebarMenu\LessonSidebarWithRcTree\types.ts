import React from 'react';
import { ETypeEdTechComponent } from '../../../../enums/AppEnums';
import { IEdTechRenderTreeData } from '../../../../interfaces/AppComponents';

// Interface for menu items
export interface TreeMenuItem {
  key: string;
  title: string;
  icon?: React.ReactNode;
  children?: TreeMenuItem[];
  type?: ETypeEdTechComponent;
  isLeaf?: boolean;
  disableCheckbox?: boolean;
  name?: string;
  path?: string;
  pathMenu?: string; // Path constructed by parent pathMenu + current id
  pos?: string; // Add pos for level calculation
}

export interface LessonSidebarMenuProps {
  items: TreeMenuItem[];
  title?: string;
  onSelect?: (key: string) => void;
  isEditing?: boolean;
  root?: IEdTechRenderTreeData;
  onFullscreen?: () => void;
  onImport?: () => void;
  onExport?: () => void;
  onClose?: () => void;
}

# Hướng Dẫn Sử Dụng Biến CSS

## Tổng quan

Tài liệu này hướng dẫn cách sử dụng biến CSS trong dự án EdTech Study. Biến CSS được định nghĩa trong file `theme-variables.css` và kết hợp với Tailwind CSS qua file `tailwind.config.js`.

## C<PERSON>u trúc biến CSS

Tất cả biến CSS trong dự án đều theo quy ước đặt tên: `--edtt-[loại]-[mục đích]-[biến thể]`. Ví dụ:
- `--edtt-color-primary-base`: <PERSON><PERSON><PERSON> chính cơ bản
- `--edtt-color-text-default`: <PERSON><PERSON><PERSON> chữ mặc định

## Nguyên tắc định nghĩa biến CSS

1. **Luôn sử dụng biến màu cơ sở:**
   - <PERSON><PERSON><PERSON> biến màu cơ sở gồm các bảng màu ch<PERSON> (primary, secondary, tertiary, quaternary, grey, quinary, senary)
   - Mỗi bảng màu có các biến thể từ 100 đến 700
   - V<PERSON> dụ: `--edtt-color-primary-base`, `--edtt-color-primary-100`, `--edtt-color-primary-500`, v.v.
   - Quinary (cam) chỉ có trong theme learning, Senary (xám than) chỉ có trong theme dark

2. **Định nghĩa màu theo ngữ cảnh dựa trên màu cơ sở:**
   - Tất cả các biến màu theo ngữ cảnh phải dựa trên biến màu cơ sở
   - KHÔNG sử dụng giá trị màu trực tiếp (hex, rgb) trong biến theo ngữ cảnh
   - Ví dụ: `--edtt-color-text-primary: var(--edtt-color-primary-base);`

3. **Sử dụng biến ngữ cảnh trong thiết kế:**
   - Uu tiên sử dụng biến theo ngữ cảnh thay vì biến màu cơ sở
   - Ví dụ: dùng `--edtt-color-bg-default` thay vì `--edtt-color-white`
   - Ví dụ: dùng `--edtt-color-text-primary` thay vì `--edtt-color-primary-base`

Điều này giúp đảm bảo: khi bạn thay đổi màu cơ sở (thay đổi tại MỘT nơi), toàn bộ bảng màu hệ thống sẽ tự động cập nhật.

## Sử dụng biến màu đúng cách

### 1. Tại sao cần sử dụng biến màu?

Sử dụng biến màu đúng cách sẽ giúp:
- Chỉ cần thay đổi màu cơ sở ở 1 nơi, toàn bộ hệ thống sẽ tự động cập nhật
- Đảm bảo tính nhất quán trong toàn bộ giao diện
- Dễ dàng thay đổi theme mà không cần sửa lại code CSS/JSX

### 2. Cách sử dụng đúng

```css
/* TỐT - Dùng biến theo ngữ cảnh */
.card {
  background-color: var(--edtt-color-bg-card);         /* Dùng biến ngữ cảnh */
  color: var(--edtt-color-text-default);               /* Dùng biến ngữ cảnh */
  border: 1px solid var(--edtt-color-border-default);  /* Dùng biến ngữ cảnh */
}

.button-primary {
  background-color: var(--edtt-color-primary);          /* Dùng biến ngữ cảnh */
  color: var(--edtt-color-text-inverse);                /* Dùng biến ngữ cảnh */
}

/* KHÔNG TỐT - Dùng trực tiếp màu cơ sở hoặc mã hex */
.card {
  background-color: var(--edtt-color-white);           /* Không linh hoạt khi đổi theme */
  color: #000000;                                       /* Mã màu trực tiếp - không được */
  border: 1px solid var(--edtt-color-gray-200);        /* Dùng biến màu cơ sở thay vì biến ngữ cảnh */
}
```

### 3. Ví dụ tự định nghĩa biến mới đúng cách

```css
/* ĐÚNG - Dùng lại các biến màu cơ sở */
:root {
  /* Biến mới dùng biến màu cơ sở */
  --edtt-color-badge-info: var(--edtt-color-quaternary-200);       /* Màu nền badge */
  --edtt-color-badge-info-text: var(--edtt-color-quaternary-700);  /* Màu chữ badge */
}

/* SAI - Dùng mã màu trực tiếp */
:root {
  /* Biến mới dùng giá trị trực tiếp */
  --edtt-color-badge-info: #D7D4FB;       /* Không nên dùng mã hex trực tiếp */
  --edtt-color-badge-info-text: #0D0540;  /* Không nên dùng mã hex trực tiếp */
}
```

### 4. Cấu trúc theo tầng

Cấu trúc hệ thống biến màu gồm 3 tầng:

1. **Tầng màu cơ sở** (Base colors): Chứa mã màu cứng (hex, rgb)
   - Ví dụ: `--edtt-color-primary-base: #EA4C89;`
   - Bao gồm các bảng màu: primary, secondary, tertiary, quaternary, quinary (learning theme), senary (dark theme)

2. **Tầng màu theo ngữ cảnh** (Semantic colors): Dùng lại màu cơ sở
   - Ví dụ: `--edtt-color-text-primary: var(--edtt-color-primary-base);`
   - Bao gồm các nhóm: bg (nền), text (chữ), border (viền), shadow (đổ bóng)

3. **Tầng component**: Dùng lại màu theo ngữ cảnh
   - Ví dụ: `--edtt-button-primary-bg: var(--edtt-color-primary);`
   - Tùy chỉnh cho từng component cụ thể: button, card, input, v.v.

Với cấu trúc này, khi bạn đổi màu ở tầng cơ sở, toàn bộ hệ thống sẽ cập nhật.

## Cách sử dụng biến CSS

### Trong file CSS
```css
.component-cua-toi {
  background-color: var(--edtt-color-primary);
  color: var(--edtt-color-text-default);
  border: 1px solid var(--edtt-color-primary-300);
}
```

### Trong React Inline Styles
```jsx
const kieuNut = {
  backgroundColor: 'var(--edtt-color-primary)',
  color: 'var(--edtt-color-text-default)',
};

function NutCuaToi({ children }) {
  return <button style={kieuNut}>{children}</button>;
}
```

## Sử dụng Tailwind với biến Theme

Tailwind đã được cấu hình để kết nối với biến CSS của chúng ta. Lưu ý: Tất cả class Tailwind đều có tiền tố `tailwind-`.

### Ví dụ về màu sắc
```jsx
// Dùng màu primary làm nền
<div className="tailwind-bg-primary">Nền màu chính</div>

// Dùng primary-600 làm màu chữ
<div className="tailwind-text-primary-600">Chữ màu primary đậm</div>

// Dùng màu trạng thái
<div className="tailwind-text-success">Thông báo thành công</div>
<div className="tailwind-bg-error">Nền thông báo lỗi</div>

// Dùng màu quinary (chỉ có trong theme learning)
<div className="tailwind-bg-quinary-300">Nền màu cam nhạt</div>

// Dùng màu senary (chỉ có trong theme dark)
<div className="tailwind-bg-senary-700">Nền màu xám than đậm</div>
```

### Các biến theo ngữ cảnh theme
```jsx
// Biến nền
<div className="tailwind-bg-default">Nền mặc định</div>
<div className="tailwind-bg-secondary">Nền phụ</div>
<div className="tailwind-bg-tertiary">Nền mức 3</div>
<div className="tailwind-bg-inverse">Nền đảo ngược màu</div>
<div className="tailwind-bg-card">Nền thẻ</div>
<div className="tailwind-bg-spotlight">Nền vùng nhấn mạnh</div>
<div className="tailwind-bg-disabled">Nền đã vô hiệu hóa</div>
<div className="tailwind-bg-selected">Nền phần tử được chọn</div>
<div className="tailwind-bg-hover">Nền khi hover</div>

// Biến chữ
<div className="tailwind-text-default">Chữ mặc định</div>
<div className="tailwind-text-primary">Chữ nhấn mạnh</div>
<div className="tailwind-text-secondary">Chữ ít quan trọng hơn</div>
<div className="tailwind-text-tertiary">Chữ ít quan trọng nhất</div>
<div className="tailwind-text-inverse">Chữ trên nền tối</div>
<div className="tailwind-text-disabled">Chữ bị vô hiệu hóa</div>
<div className="tailwind-text-link">Chữ đường dẫn</div>
<div className="tailwind-text-placeholder">Chữ placeholder</div>

// Biến viền
<div className="tailwind-border tailwind-border-default">Viền mặc định</div>
<div className="tailwind-border tailwind-border-strong">Viền đậm hơn</div>
<div className="tailwind-border tailwind-border-light">Viền nhẹ</div>
<div className="tailwind-border tailwind-border-primary">Viền nhấn mạnh</div>
<div className="tailwind-border tailwind-border-focus">Viền khi focus</div>
<div className="tailwind-border tailwind-border-disabled">Viền bị vô hiệu hóa</div>

// Biến đổ bóng
<div className="tailwind-shadow">Đổ bóng mặc định</div>
<div className="tailwind-shadow-sm">Đổ bóng nhỏ</div>
<div className="tailwind-shadow-md">Đổ bóng vừa</div>
<div className="tailwind-shadow-lg">Đổ bóng lớn</div>
<div className="tailwind-shadow-card">Đổ bóng thẻ</div>
<div className="tailwind-shadow-popup">Đổ bóng popup</div>
<div className="tailwind-shadow-dropdown">Đổ bóng dropdown</div>
<div className="tailwind-shadow-focus">Đổ bóng khi focus</div>
```

### Ví dụ thực tế với Tailwind
```jsx
// Nút sử dụng Tailwind
<button className="tailwind-bg-primary tailwind-text-white tailwind-rounded tailwind-py-2 tailwind-px-4 tailwind-shadow hover:tailwind-bg-primary-600">
  Nút bấm
</button>
```

## Thêm biến cho các component cụ thể

Ngoài các biến màu ngữ cảnh, bạn có thể thêm các biến component cụ thể để làm cho việc tùy chỉnh các component dễ dàng hơn:

```css
:root {
  /* Button Component */
  --edtt-button-primary-bg: var(--edtt-color-primary);
  --edtt-button-primary-text: var(--edtt-color-text-inverse);
  --edtt-button-primary-border: var(--edtt-color-primary);
  --edtt-button-primary-hover-bg: var(--edtt-color-primary-600);
  --edtt-button-primary-hover-text: var(--edtt-color-text-inverse);
  --edtt-button-primary-hover-border: var(--edtt-color-primary-600);
  --edtt-button-primary-active-bg: var(--edtt-color-primary-700);
  --edtt-button-primary-active-text: var(--edtt-color-text-inverse);
  --edtt-button-primary-active-border: var(--edtt-color-primary-700);

  /* Card Component */
  --edtt-card-bg: var(--edtt-color-bg-card);
  --edtt-card-border: var(--edtt-color-border-default);
  --edtt-card-shadow: var(--edtt-shadow-card);
  --edtt-card-title-color: var(--edtt-color-text-primary);
  --edtt-card-text-color: var(--edtt-color-text-default);

  /* Input Component */
  --edtt-input-bg: var(--edtt-color-bg-default);
  --edtt-input-border: var(--edtt-color-border-default);
  --edtt-input-text: var(--edtt-color-text-default);
  --edtt-input-placeholder: var(--edtt-color-placeholder);
  --edtt-input-focus-border: var(--edtt-color-border-focus);
  --edtt-input-focus-shadow: var(--edtt-shadow-focus);
  --edtt-input-disabled-bg: var(--edtt-color-disabled-bg);
  --edtt-input-disabled-text: var(--edtt-color-text-disabled);
}
```

Trong Dark mode:

```css
[data-theme="dark"] {
  /* Button Component */
  --edtt-button-primary-bg: var(--edtt-color-primary);
  --edtt-button-primary-text: var(--edtt-color-text-inverse);
  --edtt-button-primary-border: var(--edtt-color-primary);
  --edtt-button-primary-hover-bg: var(--edtt-color-primary-400);
  --edtt-button-primary-hover-text: var(--edtt-color-text-inverse);
  --edtt-button-primary-hover-border: var(--edtt-color-primary-400);
  --edtt-button-primary-active-bg: var(--edtt-color-primary-500);
  --edtt-button-primary-active-text: var(--edtt-color-text-inverse);
  --edtt-button-primary-active-border: var(--edtt-color-primary-500);

  /* Card Component và Input Component tương tự */
}
```

### Cách sử dụng biến component trong CSS

```css
/* Sử dụng biến component cho button */
.button-primary {
  background-color: var(--edtt-button-primary-bg);
  color: var(--edtt-button-primary-text);
  border: 1px solid var(--edtt-button-primary-border);
}

.button-primary:hover {
  background-color: var(--edtt-button-primary-hover-bg);
  color: var(--edtt-button-primary-hover-text);
  border-color: var(--edtt-button-primary-hover-border);
}

.button-primary:active {
  background-color: var(--edtt-button-primary-active-bg);
  color: var(--edtt-button-primary-active-text);
  border-color: var(--edtt-button-primary-active-border);
}
```

## Nguyên tắc thực hành tốt

1. **Ưu tiên dùng biến theo ngữ cảnh**:
   - Dùng `--edtt-color-bg-default` thay vì `--edtt-color-white`
   - Dùng `--edtt-color-text-primary` thay vì `--edtt-color-primary-base`

2. **Hiệu ứng chuyển đổi theme**:
   - Thêm class `theme-transitions-enabled` để có hiệu ứng mượt khi chuyển theme:
   ```html
   <div class="component-cua-toi theme-transitions-enabled">...</div>
   ```

3. **Ưu tiên dùng Tailwind**:
   - Khi có thể, dùng class Tailwind (vd: `tailwind-bg-primary`) thay vì inline styles
   - Dễ đọc và tương thích tốt hơn với hệ thống theme

4. **Bảo trì biến màu**:
   - Khi thêm biến mới, luôn thêm vào cả ba theme (mặc định, learning, dark)
   - Tuân thủ quy tắc đặt tên `--edtt-[loại]-[mục đích]-[biến thể]`
   - Luôn sử dụng các biến có sẵn thay vì thêm màu mới

5. **Cập nhật file tailwind.config.js**:
   - Khi thêm biến mới, đảm bảo cập nhật cấu hình Tailwind để sử dụng với các class

## Hệ thống Theme

Dự án hỗ trợ 3 theme:
1. **Theme mặc định** (trong `:root`) - Sử dụng màu chủ đạo là hồng (#EA4C89)
2. **Theme học tập** (trong `[data-theme="learning"]`) - Sử dụng màu chủ đạo là xanh dương (#00AEEF)
3. **Dark Mode** (trong `[data-theme="dark"]`) - Phiên bản tối với màu chủ đạo là xanh dương sáng (#4DCFFF)

Cách chuyển đổi theme:
```javascript
// Chuyển sang theme học tập
document.documentElement.setAttribute('data-theme', 'learning');

// Chuyển sang dark mode
document.documentElement.setAttribute('data-theme', 'dark');

// Quay lại theme mặc định
document.documentElement.removeAttribute('data-theme');
```

Tuân thủ các hướng dẫn này sẽ giúp đảm bảo giao diện nhất quán trong ứng dụng và dễ dàng chuyển đổi giữa các theme.

## Biến Theme Tối Ưu (Default Theme Variables - Optimized)

Đây là nhóm biến rất quan trọng, giúp đơn giản hóa việc sử dụng màu sắc theo ngữ cảnh thiết kế. Thay vì sử dụng trực tiếp các mã màu, chúng ta sử dụng các biến ngữ nghĩa:

```css
/* Theme mặc định */
/* Màu sắc cơ bản theo ngữ cảnh */
--edtt-color-primary: var(--edtt-color-primary-base);      /* Màu chủ đạo của ứng dụng */
--edtt-color-secondary: var(--edtt-color-secondary-base);  /* Màu phụ */
--edtt-color-tertiary: var(--edtt-color-tertiary-base);    /* Màu thứ ba */

/* Màu nền */
--edtt-color-bg-default: var(--edtt-color-white);          /* Nền chính */
--edtt-color-bg-secondary: var(--edtt-color-gray-100);     /* Nền phụ */
--edtt-color-bg-tertiary: var(--edtt-color-gray-200);      /* Nền mức 3 */
--edtt-color-bg-inverse: var(--edtt-color-black);          /* Nền đảo ngược màu */
--edtt-color-bg-card: var(--edtt-color-white);             /* Nền thẻ */
--edtt-color-bg-overlay: rgba(0, 0, 0, 0.45);              /* Nền lớp phủ */
--edtt-color-bg-mask: rgba(0, 0, 0, 0.15);                 /* Lớp mờ */
--edtt-color-bg-spotlight: var(--edtt-color-primary-100);  /* Nền vùng nhấn mạnh */
--edtt-color-disabled-bg: rgba(0, 0, 0, 0.04);             /* Nền phần tử bị vô hiệu */
--edtt-color-selected: var(--edtt-color-primary-100);      /* Nền phần tử được chọn */
--edtt-color-hover: var(--edtt-color-primary-50);          /* Nền khi hover */

/* Màu chữ */
--edtt-color-text-default: var(--edtt-color-black);        /* Chữ chính */
--edtt-color-text-primary: var(--edtt-color-primary-base); /* Chữ nhấn mạnh */
--edtt-color-text-secondary: var(--edtt-color-grey-500);   /* Chữ ít quan trọng hơn */
--edtt-color-text-tertiary: var(--edtt-color-grey-400);    /* Chữ ít quan trọng nhất */
--edtt-color-text-inverse: var(--edtt-color-white);        /* Chữ trên nền tối */
--edtt-color-text-disabled: rgba(0, 0, 0, 0.25);           /* Chữ bị vô hiệu */
--edtt-color-link: var(--edtt-color-primary-base);         /* Màu đường dẫn */
--edtt-color-link-hover: var(--edtt-color-primary-500);    /* Màu đường dẫn khi hover */
--edtt-color-link-active: var(--edtt-color-primary-600);   /* Màu đường dẫn khi active */
--edtt-color-placeholder: var(--edtt-color-grey-400);      /* Màu placeholder trong input */

/* Màu viền */
--edtt-color-border-default: var(--edtt-color-gray-200);   /* Viền mặc định */
--edtt-color-border-strong: var(--edtt-color-gray-400);    /* Viền đậm hơn */
--edtt-color-border-light: var(--edtt-color-gray-100);     /* Viền nhẹ */
--edtt-color-border-primary: var(--edtt-color-primary-base); /* Viền nhấn mạnh */
--edtt-color-border-focus: var(--edtt-color-primary-400);  /* Viền khi focus */
--edtt-color-border-disabled: var(--edtt-color-gray-200);  /* Viền bị vô hiệu */

/* Độ sâu - Đổ bóng */
--edtt-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);            /* Đổ bóng mặc định */
--edtt-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);           /* Đổ bóng nhỏ */
--edtt-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);       /* Đổ bóng vừa */
--edtt-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);     /* Đổ bóng lớn */
--edtt-shadow-card: 0 1px 3px rgba(0, 0, 0, 0.12);         /* Đổ bóng thẻ */
--edtt-shadow-popup: 0 6px 16px 0 rgba(0, 0, 0, 0.08);     /* Đổ bóng popup */
--edtt-shadow-dropdown: 0 3px 6px -4px rgba(0, 0, 0, 0.12); /* Đổ bóng dropdown */
--edtt-shadow-focus: 0 0 0 2px rgba(234, 76, 137, 0.2);    /* Đổ bóng khi focus */

/* Hiệu ứng chuyển đổi */
--edtt-transition-slow: 0.3s ease-in-out;                  /* Chuyển đổi chậm */
--edtt-transition-normal: 0.2s ease-in-out;                /* Chuyển đổi thông thường */
--edtt-transition-fast: 0.1s ease-in-out;                  /* Chuyển đổi nhanh */
```

```css
/* Theme dark mode */
/* Màu sắc cơ bản theo ngữ cảnh */
--edtt-color-primary: var(--color-primary-base);           /* Màu chủ đạo sáng hơn */

/* Màu nền */
--edtt-color-bg-default: #121212;                          /* Nền tối */
--edtt-color-bg-secondary: #1E1E1E;                        /* Nền phụ tối */
--edtt-color-disabled-bg: rgba(255, 255, 255, 0.05);       /* Vùng vô hiệu hóa tối */

/* Màu chữ */
--edtt-color-text-default: #E0E0E0;                        /* Chữ màu sáng */
--edtt-color-text-primary: var(--color-primary-base);      /* Chữ nhấn mạnh màu sáng */
--edtt-color-disabled-text: rgba(255, 255, 255, 0.3);      /* Chữ vô hiệu hóa màu sáng */

/* Màu nền phần tử */
--edtt-color-selected: var(--color-primary-700);           /* Vùng chọn tối hơn */

/* Đổ bóng */
--edtt-shadow: 0 1px 2px 0 rgba(255, 255, 255, 0.06);      /* Đổ bóng tối */
```

### Ứng dụng thực tế

Việc dùng các biến này trong thực tế giúp ứng dụng nhìn chuyên nghiệp hơn và dễ bảo trì hơn trong tương lai:

```jsx
// Nút đăng nhập sử dụng các biến theme
<button
  className="
    tailwind-bg-primary
    tailwind-text-white
    tailwind-py-2
    tailwind-px-4
    tailwind-rounded-md
    hover:tailwind-bg-primary-600
    focus:tailwind-shadow-focus
  "
>
  Đăng nhập
</button>

// Thẻ thông tin sản phẩm
<div
  className="
    tailwind-bg-bg-card
    tailwind-border
    tailwind-border-border-default
    tailwind-rounded-lg
    tailwind-shadow-card
    tailwind-p-4
  "
>
  <h3 className="tailwind-text-text-primary tailwind-text-lg">Tên sản phẩm</h3>
  <p className="tailwind-text-text-secondary tailwind-mt-2">Mô tả sản phẩm...</p>
  <span className="tailwind-text-text-tertiary tailwind-text-sm">Thông tin phụ</span>
</div>
```

### Cách tự định nghĩa biến mới

Khi cần thêm biến mới, hãy tuân theo quy ước đặt tên và đảm bảo thêm biến cho cả 3 theme (mặc định, learning, dark):

```css
/* Trong :root */
:root {
  /* Biến mới */
  --edtt-color-notification: var(--edtt-color-quaternary-400); /* Màu thông báo */
  --edtt-component-avatar-size-sm: 32px;                      /* Kích thước avatar nhỏ */
  --edtt-component-avatar-size-md: 48px;                      /* Kích thước avatar vừa */
  --edtt-component-avatar-size-lg: 64px;                      /* Kích thước avatar lớn */
}

/* Trong [data-theme="learning"] */
[data-theme="learning"] {
  /* Biến mới cho theme learning */
  --edtt-color-notification: var(--edtt-color-quaternary-500);
}

/* Trong [data-theme="dark"] */
[data-theme="dark"] {
  /* Biến mới cho theme dark */
  --edtt-color-notification: var(--color-quaternary-300);
}
```

Sau đó, cập nhật trong tailwind.config.js để sử dụng với Tailwind:

```js
// tailwind.config.js
module.exports = {
  // ...
  theme: {
    extend: {
      // ...
      // Thêm biến mới
      colors: {
        // ...
        notification: 'var(--edtt-color-notification)',
      },
      // Thêm size mới
      width: {
        'avatar-sm': 'var(--edtt-component-avatar-size-sm)',
        'avatar-md': 'var(--edtt-component-avatar-size-md)',
        'avatar-lg': 'var(--edtt-component-avatar-size-lg)',
      },
      height: {
        'avatar-sm': 'var(--edtt-component-avatar-size-sm)',
        'avatar-md': 'var(--edtt-component-avatar-size-md)',
        'avatar-lg': 'var(--edtt-component-avatar-size-lg)',
      },
    }
  }
};
```

### Các ứng dụng thực tế

1. **Thiết kế giao diện nút bấm**:
```css
.nut-bam {
  background-color: var(--edtt-color-primary);        /* Màu nền chủ đạo */
  color: var(--edtt-color-white);                     /* Chữ màu trắng */
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  box-shadow: var(--edtt-shadow);                     /* Đổ bóng nhất quán */
}

.nut-bam:hover {
  background-color: var(--edtt-color-primary-600);   /* Tối hơn khi hover */
}

.nut-bam.disabled {
  background-color: var(--edtt-color-disabled-bg);    /* Màu nền vô hiệu hóa */
  color: var(--edtt-color-disabled-text);             /* Màu chữ vô hiệu hóa */
  cursor: not-allowed;
}
```

2. **Thiết kế phần tử được chọn**:
```css
.menu-item {
  background-color: var(--edtt-color-bg-default);    /* Nền mặc định */
  color: var(--edtt-color-text-default);              /* Chữ mặc định */
  padding: 10px 15px;
}

.menu-item.active {
  background-color: var(--edtt-color-selected);       /* Nền khi chọn */
  color: var(--edtt-color-primary);                   /* Màu chữ chủ đạo */
  font-weight: bold;
}
```

### Các biến bổ sung đề xuất

Ngoài các biến đã có trong `theme-variables.css`, bạn có thể bổ sung thêm các biến theo ngữ cảnh thiết kế:

```css
:root {
  /* Kích thước & khoảng cách */
  --edtt-border-radius-sm: 2px;                 /* Bo góc nhỏ */
  --edtt-border-radius-md: 4px;                 /* Bo góc vừa */
  --edtt-border-radius-lg: 8px;                 /* Bo góc lớn */
  --edtt-spacing-xs: 4px;                       /* Khoảng cách cực nhỏ */
  --edtt-spacing-sm: 8px;                       /* Khoảng cách nhỏ */
  --edtt-spacing-md: 16px;                      /* Khoảng cách vừa */
  --edtt-spacing-lg: 24px;                      /* Khoảng cách lớn */
  --edtt-spacing-xl: 32px;                      /* Khoảng cách cực lớn */

  /* Biến component */
  --edtt-header-height: 64px;                   /* Chiều cao header */
  --edtt-sidebar-width: 250px;                  /* Chiều rộng sidebar */
  --edtt-footer-height: 48px;                   /* Chiều cao footer */
  --edtt-container-max-width: 1200px;           /* Chiều rộng tối đa container */
  --edtt-container-padding: 16px;               /* Padding container */
}
```

## Nguyên tắc thực hành tốt

1. **Ưu tiên dùng biến theo ngữ cảnh**:
   - Dùng `--edtt-color-bg-default` thay vì `--edtt-color-white`
   - Dùng `--edtt-color-text-primary` thay vì `--edtt-color-primary-base`

2. **Hiệu ứng chuyển đổi theme**:
   - Thêm class `theme-transitions-enabled` để có hiệu ứng mượt khi chuyển theme:
   ```html
   <div class="component-cua-toi theme-transitions-enabled">...</div>
   ```

3. **Ưu tiên dùng Tailwind**:
   - Khi có thể, dùng class Tailwind (vd: `tailwind-bg-primary`) thay vì inline styles
   - Dễ đọc và tương thích tốt hơn với hệ thống theme
   - Ví dụ thực tế:
   ```jsx
   // Nút sử dụng Tailwind
   <button className="tailwind-bg-primary tailwind-text-white tailwind-rounded tailwind-py-2 tailwind-px-4 tailwind-shadow hover:tailwind-bg-primary-600">
     Nút bấm
   </button>
   ```

Tuân thủ các hướng dẫn này sẽ giúp đảm bảo giao diện nhất quán trong ứng dụng và dễ dàng chuyển đổi giữa các theme.

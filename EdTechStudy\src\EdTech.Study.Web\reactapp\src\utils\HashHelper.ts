class HashHelper {
  static computeHash(strings: string[]): string {
    if (!strings || strings.length === 0) {
      throw new Error('Input array cannot be null or empty');
    }

    // Convert string array to a single string
    const concatenatedString = strings.join('');

    // Simple hash function without external library
    let hash = 0;
    for (let i = 0; i < concatenatedString.length; i++) {
      const char = concatenatedString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    return hash.toString(16);
  }
}

export default HashHelper;

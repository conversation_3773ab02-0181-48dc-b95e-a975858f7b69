.question-list-view {
  position: relative;
}

.question-list-content {
  padding: 10px 0;
}

.question-list-item {
  margin-bottom: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.question-list-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-list-item.active {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
}

.question-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.empty-question-list {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  margin: 20px 0;
  text-align: center;
}

/* Global body class for config mode */
body.config-mode-active {
  padding-top: 0;
  padding-bottom: 0;
  min-height: 100vh;
}

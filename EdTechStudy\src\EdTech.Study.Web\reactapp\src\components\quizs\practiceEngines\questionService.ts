import { Question } from '../../../interfaces/quizs/questionBase';

export const questionService = {
  insertQuestionAtPosition(
    questions: Question[],
    newQuestion: Question,
    position: number
  ): Question[] {
    const newQuestions = [...questions];
    newQuestions.splice(position, 0, newQuestion);
    return newQuestions;
  },

  reorderQuestions(questions: Question[]): Question[] {
    return questions.map((question, index) => ({
      ...question,
      order: index + 1,
    }));
  },

  moveQuestion(
    questions: Question[],
    fromPosition: number,
    toPosition: number
  ): Question[] {
    const newQuestions = [...questions];
    const [movedQuestion] = newQuestions.splice(fromPosition, 1);
    newQuestions.splice(toPosition, 0, movedQuestion);
    return this.reorderQuestions(newQuestions);
  },

  // Hàm mới để chuẩn bị câu hỏi trước khi lưu
  prepareQuestionsForSave(questions: Question[]): Question[] {
    return questions.map((question) => ({
      ...question,
      // Giữ lại cấu hình câu hỏi nhưng xóa trạng thái người dùng
      isCompleted: false,
      userSelect: undefined,
      status: undefined,
    }));
  },

  // Kiểm tra xem có sự thay đổi về cấu hình giữa hai câu hỏi không
  hasConfigurationChanged(
    oldQuestion: Question,
    newQuestion: Question
  ): boolean {
    // So sánh các trường cấu hình, bỏ qua trạng thái người dùng
    const configFields = [
      'title',
      'question',
      'answers',
      'blanks',
      'correctAnswer',
      'leftItems',
      'rightItems',
      'explanation',
      'images',
    ];

    for (const field of configFields) {
      if (
        JSON.stringify(oldQuestion[field]) !==
        JSON.stringify(newQuestion[field])
      ) {
        return true;
      }
    }

    return false;
  },

  // Xác định xem có câu hỏi nào cần được lưu không
  hasQuestionsToSave(
    oldQuestions: Question[],
    newQuestions: Question[]
  ): boolean {
    if (oldQuestions.length !== newQuestions.length) {
      return true;
    }

    for (let i = 0; i < oldQuestions.length; i++) {
      if (this.hasConfigurationChanged(oldQuestions[i], newQuestions[i])) {
        return true;
      }
    }

    return false;
  },

  submitQuestions(questions: Question[]): boolean[] {
    return [];
  },
};

export function shallowEqual(
  obj1: any,
  obj2: any,
  disableKeys?: string[],
  depth?: number
): boolean {
  depth ??= 3;
  if (depth < 0) return true; // Stop recursion if depth limit reached

  if (
    typeof obj1 !== 'object' ||
    obj1 === null ||
    typeof obj2 !== 'object' ||
    obj2 === null
  ) {
    return obj1 === obj2; // primitive comparison
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (disableKeys?.includes(key)) continue;
    if (!keys2.includes(key)) return false;
    if (!shallowEqual(obj1[key], obj2[key], disableKeys, depth - 1)) {
      return false;
    }
  }
  return true;
}

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { removeItem } from '../../../store/slices/AppSlices/EdTechRenderDataSlice';
import { IEdTechRenderTreeData } from '../../../interfaces/AppComponents';
import { DeleteIcon } from '../../icons/IconIndex';

interface ComponentNotFoundAlertProps {
  node: IEdTechRenderTreeData;
}

const ComponentNotFoundAlert: React.FC<ComponentNotFoundAlertProps> = ({
  node,
}) => {
  const dispatch = useDispatch();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  const handleDelete = () => {
    setIsDeleteModalVisible(true);
  };

  const handleConfirmDelete = () => {
    dispatch(removeItem({ targetNode: node }));
    setIsDeleteModalVisible(false);
  };

  const handleCancelDelete = () => {
    setIsDeleteModalVisible(false);
  };

  return (
    <div className="tailwind-border tailwind-border-default tailwind-rounded-lg tailwind-p-4 tailwind-mb-4 tailwind-shadow-sm">
      <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
        <div className="tailwind-flex-1">
          <Alert
            message="Component không tìm thấy"
            description={`Không thể tìm thấy component "${node.name}". Vui lòng kiểm tra lại cấu hình.`}
            type="warning"
            showIcon
            className="tailwind-bg-transparent tailwind-border-none"
          />
        </div>
        <Button
          type="text"
          danger
          icon={<DeleteIcon />}
          onClick={handleDelete}
          className="tailwind-ml-4 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-w-8 tailwind-h-8 tailwind-rounded-full hover:tailwind-bg-red-50"
        />
      </div>

      <Modal
        title={
          <div className="tailwind-flex tailwind-items-center">
            <ExclamationCircleOutlined className="tailwind-text-red-500 tailwind-mr-2" />
            <span>Xác nhận xóa</span>
          </div>
        }
        open={isDeleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={handleCancelDelete}
        okText="Xóa"
        cancelText="Hủy"
        okButtonProps={{ danger: true }}
        className="tailwind-rounded-lg"
      >
        <div className="tailwind-flex tailwind-items-center tailwind-text-gray-700">
          <p>Bạn có chắc chắn muốn xóa component "{node.name}" không?</p>
        </div>
      </Modal>
    </div>
  );
};

export default ComponentNotFoundAlert;

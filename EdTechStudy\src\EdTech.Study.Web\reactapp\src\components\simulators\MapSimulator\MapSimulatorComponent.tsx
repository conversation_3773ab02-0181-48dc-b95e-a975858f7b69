import React, { memo, useEffect, useMemo, useState, useRef } from 'react';
import { MapSimulatorConfig, MapMarker } from './MapSimulatorConfig';
import MapComponent from '../../common/MapCommonComponent/components/MapComponent';
import './MapSimulator.css';
import {
  Input,
  Button,
  Select,
  message,
  Card,
  Typography,
  Checkbox,
  Divider,
  List,
  Form,
} from 'antd';
import {
  EnvironmentOutlined,
  EditOutlined,
  PlusOutlined,
  AimOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import {
  MapControlsConfig,
  MapPosition,
  changeBaseLayer,
  zoomToPosition,
} from '../../common/MapCommonComponent/MapUtils';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { DeleteIcon } from '../../icons/IconIndex';

const { Title, Text, Paragraph } = Typography;

interface MapSimulatorComponentProps {
  config: MapSimulatorConfig;
  onDataChange?: (data: any) => void;
}

// Không cần interface MarkerFormData nữa vì chúng ta không sử dụng form

const MapSimulatorComponent: React.FC<MapSimulatorComponentProps> = ({
  config,
  onDataChange,
}) => {
  const [leafletMap, setLeafletMap] = useState<any>(null);
  const [allMarkers, setAllMarkers] = useState<MapMarker[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  // State cho modal thêm marker (không sử dụng nhưng giữ lại để tương thích)
  const [isMarkerModalVisible, setIsMarkerModalVisible] = useState(false);
  const [editingMarkerId, setEditingMarkerId] = useState<string | null>(null);
  const [isAddingMarkerMode, setIsAddingMarkerMode] = useState<boolean>(false);
  const [isSidebarVisible, setIsSidebarVisible] = useState<boolean>(true);
  const [activeMarkerId, setActiveMarkerId] = useState<string | null>(null);
  // Không cần state sidebarWidth nữa vì đã bỏ Splitter

  // Sử dụng useRef để theo dõi lần render đầu tiên
  const isInitialRender = useRef(true);
  // Luôn sử dụng chế độ xem Tiêu chuẩn làm mặc định
  const defaultViewMode = useMemo(() => {
    return 'standard'; // Chế độ Tiêu chuẩn
  }, []);

  const [viewMode, setViewMode] = useState(defaultViewMode);

  const handleMapReady = (map: any) => {
    setLeafletMap(map);
  };

  // Không cần khởi tạo form nữa

  // Cập nhật allMarkers khi config thay đổi
  useEffect(() => {
    // Khởi tạo allMarkers từ config.markers
    setAllMarkers(config.markers);
    // Thông báo cập nhật thành công nếu cấu hình thay đổi
    if (
      !isInitialRender.current &&
      allMarkers.length > 0 &&
      JSON.stringify(config.markers) !== JSON.stringify(allMarkers)
    ) {
      message.success('Cấu hình đã được cập nhật');
    }
    isInitialRender.current = false;

    // Thông báo cho component cha về trạng thái hiện tại
    if (onDataChange) {
      onDataChange({
        action: 'config_updated',
        visibleMarkers: config.markers.filter((marker) => marker.visible)
          .length,
        center: config.initialCenter,
        zoom: config.initialZoom,
      });
    }
  }, [config, onDataChange]);

  // Đảm bảo map được hiển thị chính xác khi component được mount
  useEffect(() => {
    if (leafletMap) {
      // Đảm bảo map container đã được mount và có kích thước
      const mapContainer = leafletMap.getContainer();
      if (mapContainer && mapContainer.offsetParent !== null) {
        // Sử dụng requestAnimationFrame để đảm bảo DOM đã cập nhật
        requestAnimationFrame(() => {
          leafletMap.invalidateSize({ animate: true });

          // Đảm bảo chế độ xem mặc định là Tiêu chuẩn
          changeBaseLayer(leafletMap, 'standard');
        });
      }
    }
  }, [leafletMap]);

  // Cập nhật kích thước bản đồ khi ẩn/hiện sidebar
  useEffect(() => {
    if (leafletMap) {
      // Sử dụng setTimeout để đảm bảo DOM đã được cập nhật sau khi thay đổi trạng thái
      setTimeout(() => {
        // Force resize map
        leafletMap.invalidateSize({ animate: true, pan: false });
        // Trigger another resize after animation completes
        setTimeout(() => {
          leafletMap.invalidateSize({ animate: false });
        }, 400);
      }, 100);
    }
  }, [leafletMap, isSidebarVisible]);

  // Cập nhật bản đồ khi cấu hình thay đổi
  useEffect(() => {
    if (leafletMap && !isInitialRender.current) {
      // Cập nhật bản đồ khi cấu hình thay đổi
      setTimeout(() => {
        leafletMap.invalidateSize({ animate: true, pan: false });

        // Nếu có sự thay đổi về vị trí trung tâm hoặc mức độ zoom
        if (config.initialCenter && config.initialZoom) {
          leafletMap.setView(
            [config.initialCenter.lat, config.initialCenter.lng],
            config.initialZoom,
            { animate: true }
          );
        }
      }, 200);
    }
  }, [leafletMap, config]);

  const MapControl = useMemo(() => {
    return {
      scale: {
        enabled: config.controls.showScaleControl,
        position: 'bottomright',
      },
      search: {
        enabled: config.controls.showSearchControl,
        position: 'topleft',
        placeholder: 'Tìm vị trí...',
      },
      compass: {
        enabled: config.controls.showCompassControl,
        position: config.controls.compassControlPosition,
      },
      measure: {
        enabled: config.controls.showMeasurementTools,
        position: config.controls.measureControlPosition,
      },
      routing: {
        enabled: config.controls.showRoutingControl,
        position: config.controls.routingControlPosition,
      },
      zoom: {
        enabled: config.controls.showZoomControl,
        position: 'bottomright',
      },
      layers: {
        enabled: true,
        position: config.controls.layerControlPosition,
        baseLayers: {
          'Tiêu chuẩn': 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
          'Vệ tinh':
            'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
          'Địa hình': 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
          'Kết hợp':
            'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
        },
      },
    } as MapControlsConfig;
  }, [config.controls]);

  const handleToggleMarker = (markerId: string) => {
    const updatedMarkers = allMarkers.map((marker) => {
      if (marker.id === markerId) {
        return { ...marker, visible: !marker.visible };
      }
      return marker;
    });

    setAllMarkers(updatedMarkers);

    if (onDataChange) {
      const visibleState = updatedMarkers.find(
        (m) => m.id === markerId
      )?.visible;
      onDataChange({
        action: 'marker_toggled',
        markerId: markerId,
        visible: visibleState,
        allMarkers: updatedMarkers,
      });
    }
  };

  const handleSelectLocation = (location: any) => {
    setSearchResults([]);
    message.success(`Đã chọn: ${location.name}`);
    if (onDataChange) {
      onDataChange({
        action: 'location_selected',
        location: {
          name: location.name,
          coordinates: { lat: location.lat, lng: location.lng },
        },
      });
    }
  };

  const handleViewModeChange = (mode: string) => {
    setViewMode(mode);
    if (leafletMap) {
      const success = changeBaseLayer(leafletMap, mode);
      if (success) {
        message.info(`Đã chuyển sang chế độ xem: ${mode}`);
      } else {
        message.error('Không thể chuyển chế độ xem');
      }
    }
    if (onDataChange) {
      onDataChange({
        action: 'view_mode_changed',
        mode: mode,
      });
    }
  };

  const handleMapClick = (position: MapPosition) => {
    const clickedLocation = {
      id: `click-${Date.now()}`,
      name: `Vị trí (${position.lat.toFixed(4)}, ${position.lng.toFixed(4)})`,
      lat: position.lat,
      lng: position.lng,
    };

    // Nếu đang ở chế độ thêm điểm đánh dấu, tự động thêm marker mới
    if (isAddingMarkerMode) {
      const markerId = `marker-${Date.now()}`;
      const newMarker: MapMarker = {
        id: markerId,
        name: clickedLocation.name,
        description: '',
        latitude: position.lat,
        longitude: position.lng,
        visible: true,
      };

      // Thêm marker mới vào danh sách
      const updatedMarkers = [...allMarkers, newMarker];
      setAllMarkers(updatedMarkers);

      // Cập nhật cấu hình để lưu marker mới
      if (onDataChange) {
        onDataChange({
          action: 'marker_added',
          newMarker: newMarker,
          allMarkers: updatedMarkers,
        });
      }

      // Tự động tắt chế độ thêm điểm đánh dấu sau khi đã thêm
      setIsAddingMarkerMode(false);

      // Tự động chuyển sang chế độ chỉnh sửa cho marker mới
      setTimeout(() => {
        setEditingMarkerId(markerId);
        // Zoom đến vị trí marker mới
        if (leafletMap) {
          zoomToPosition(leafletMap, { lat: position.lat, lng: position.lng });
        }
      }, 100);

      message.success(
        'Đã thêm điểm đánh dấu mới. Bạn có thể chỉnh sửa tên ngay bây giờ.'
      );
    }

    if (onDataChange) {
      onDataChange({
        action: 'map_clicked',
        coordinates: { lat: position.lat, lng: position.lng },
      });
    }
  };

  // Luôn hiển thị tất cả các chế độ xem
  const availableViewModes = useMemo(() => {
    return [
      { value: 'standard', label: 'Tiêu chuẩn' },
      { value: 'satellite', label: 'Vệ tinh' },
      { value: 'terrain', label: 'Địa hình' },
      { value: 'hybrid', label: 'Kết hợp' },
    ];
  }, []);

  // Xác định chiều cao cố định cho bản đồ
  const mapHeight = 500;

  // Hàm để zoom đến một marker
  const handleZoomToMarker = (marker: MapMarker) => {
    if (leafletMap) {
      zoomToPosition(leafletMap, {
        lat: marker.latitude,
        lng: marker.longitude,
      });
      setActiveMarkerId(marker.id);
      message.info(`Đã di chuyển đến: ${marker.name}`);
    }
  };

  return (
    <div className="map-simulator">
      <Card
        className="tailwind-mb-4"
        size="small"
        title={
          <span className="tailwind-font-bold">{config.titleProps.text}</span>
        }
      >
        <Paragraph className="tailwind-text-gray-600 tailwind-text-sm">
          {config.description}
        </Paragraph>
      </Card>

      {searchResults.length > 0 && (
        <Card className="tailwind-mb-4" size="small" title="Kết quả tìm kiếm">
          <List
            size="small"
            dataSource={searchResults}
            renderItem={(result) => (
              <List.Item
                className="hover:tailwind-bg-gray-100 tailwind-cursor-pointer"
                onClick={() => handleSelectLocation(result)}
              >
                <div className="tailwind-flex tailwind-items-center tailwind-w-full">
                  <EnvironmentOutlined className="tailwind-text-red-500 tailwind-mr-2" />
                  <span>{result.name}</span>
                  <span className="tailwind-ml-auto tailwind-text-xs tailwind-text-gray-500">
                    {result.lat.toFixed(4)}, {result.lng.toFixed(4)}
                  </span>
                </div>
              </List.Item>
            )}
          />
        </Card>
      )}

      <div className="tailwind-flex tailwind-flex-col md:tailwind-flex-row tailwind-gap-4">
        <div
          className="tailwind-flex-grow tailwind-bg-gray-200 tailwind-rounded-lg tailwind-overflow-hidden tailwind-w-full"
          style={{
            height: mapHeight,
            flex: 1,
            display: 'flex',
          }}
        >
          <MapComponent
            initialCenter={config.initialCenter}
            initialZoom={config.initialZoom}
            markers={allMarkers
              .filter((m) => m.visible)
              .map((m) => ({
                position: { lat: m.latitude, lng: m.longitude },
                title: m.name,
                description: m.description,
                draggable: true,
              }))}
            polygons={config.polygons.map((p) => ({
              positions: p.coordinates.map((c) => ({ lat: c[0], lng: c[1] })),
              color: p.strokeColor,
              fillColor: p.fillColor,
              fillOpacity: p.fillOpacity,
            }))}
            onMapClick={handleMapClick}
            onMapReady={handleMapReady}
            onMarkerDragEnd={(marker, newPos) => {
              const updatedMarkers = allMarkers.map((m) =>
                Math.abs(m.latitude - marker.position.lat) < 0.0001 &&
                Math.abs(m.longitude - marker.position.lng) < 0.0001
                  ? { ...m, latitude: newPos.lat, longitude: newPos.lng }
                  : m
              );
              setAllMarkers(updatedMarkers);
              if (onDataChange) {
                onDataChange({
                  action: 'marker_moved',
                  markerId: updatedMarkers.find(
                    (m) =>
                      m.latitude === newPos.lat && m.longitude === newPos.lng
                  )?.id,
                  newPosition: newPos,
                  allMarkers: updatedMarkers,
                });
              }
            }}
            controls={MapControl}
          />
        </div>

        {isSidebarVisible && (
          <div
            className="tailwind-relative"
            style={{ height: mapHeight, width: '250px' }}
          >
            <div className="tailwind-absolute tailwind-top-0 tailwind-left-0 tailwind-w-full tailwind-flex tailwind-justify-end tailwind-z-10">
              <Button
                type="default"
                icon={<MenuFoldOutlined />}
                className="tailwind-rounded-full tailwind-bg-white tailwind-shadow-md hover:tailwind-bg-gray-100 tailwind-border-0 tailwind-mr-1 tailwind-mt-1"
                onClick={() => {
                  setIsSidebarVisible(false);
                  // Invalidate map size after hiding sidebar
                  if (leafletMap) {
                    setTimeout(() => {
                      leafletMap.invalidateSize({ animate: true, pan: false });
                      // Trigger another resize after animation completes
                      setTimeout(() => {
                        leafletMap.invalidateSize({ animate: false });
                      }, 300);
                    }, 50);
                  }
                }}
                size="small"
              />
            </div>
            <Card
              className="tailwind-w-full tailwind-h-full tailwind-flex tailwind-flex-col"
              styles={{
                body: {
                  padding: '12px',
                  paddingTop: '28px', // Thêm padding-top để tránh đè lên nội dung
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  overflow: 'hidden',
                  maxHeight: '100%',
                },
              }}
            >
              <div className="">
                <Title level={5} className="tailwind-mb-2 tailwind-mt-0">
                  Chế độ xem
                </Title>
                <Select
                  value={viewMode}
                  onChange={handleViewModeChange}
                  style={{ width: '100%' }}
                  options={availableViewModes}
                />
              </div>

              <Divider className="tailwind-my-3" />

              <div className="tailwind-flex tailwind-flex-col tailwind-flex-grow tailwind-overflow-hidden">
                <div className="tailwind-mb-3">
                  <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
                    <Title level={5} className="tailwind-m-0">
                      Các điểm đánh dấu
                    </Title>
                  </div>
                  {isAddingMarkerMode && (
                    <div className="tailwind-mt-2 tailwind-p-2 tailwind-bg-green-50 tailwind-border tailwind-border-green-200 tailwind-rounded-md tailwind-text-xs tailwind-text-green-700 tailwind-flex tailwind-items-center tailwind-justify-between">
                      <div className="tailwind-flex tailwind-items-center">
                        <AimOutlined className="tailwind-mr-1" />
                        Đang ở chế độ thêm điểm đánh dấu
                      </div>
                      <Button
                        type="text"
                        size="small"
                        className="tailwind-text-green-700 tailwind-p-0 tailwind-h-auto"
                        onClick={() => {
                          setIsAddingMarkerMode(false);
                          message.info('Đã tắt chế độ thêm điểm đánh dấu');
                        }}
                      >
                        Hủy
                      </Button>
                    </div>
                  )}
                </div>
                <div
                  className="tailwind-overflow-y-auto tailwind-flex-grow tailwind-pr-1 tailwind-custom-scrollbar"
                  style={{
                    height: 'calc(100% - 150px)',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    scrollbarWidth: 'thin',
                  }}
                >
                  {allMarkers.map((marker, idx) => (
                    <div
                      key={marker.id || idx}
                      className={`tailwind-mb-2 tailwind-border-b tailwind-border-gray-100 tailwind-pb-2 tailwind-rounded-md marker-item ${
                        activeMarkerId === marker.id ? 'active' : ''
                      }`}
                    >
                      {editingMarkerId === marker.id ? (
                        // Editing mode
                        <div className="tailwind-space-y-2 tailwind-w-full tailwind-p-2">
                          <Input
                            size="small"
                            value={marker.name}
                            style={{ width: '100%' }}
                            prefix={
                              <EnvironmentOutlined className="tailwind-text-pink-500" />
                            }
                            onChange={(e) => {
                              const updatedMarkers = allMarkers.map((m) => {
                                if (m.id === marker.id) {
                                  return { ...m, name: e.target.value };
                                }
                                return m;
                              });
                              setAllMarkers(updatedMarkers);
                            }}
                            onPressEnter={() => {
                              setEditingMarkerId(null);
                              // Lấy danh sách markers đã cập nhật
                              const updatedMarkers = allMarkers.map((m) => {
                                if (m.id === marker.id) {
                                  return { ...m, name: m.name };
                                }
                                return m;
                              });

                              if (onDataChange) {
                                onDataChange({
                                  action: 'marker_edited',
                                  markerId: marker.id,
                                  updatedMarker: {
                                    name: marker.name,
                                    description: marker.description,
                                  },
                                  allMarkers: updatedMarkers,
                                });
                              }
                              message.success(
                                `Đã cập nhật điểm đánh dấu: ${marker.name}`
                              );
                            }}
                            onBlur={() => {
                              setEditingMarkerId(null);
                              // Lấy danh sách markers đã cập nhật
                              const updatedMarkers = allMarkers.map((m) => {
                                if (m.id === marker.id) {
                                  return { ...m, name: m.name };
                                }
                                return m;
                              });

                              if (onDataChange) {
                                onDataChange({
                                  action: 'marker_edited',
                                  markerId: marker.id,
                                  updatedMarker: {
                                    name: marker.name,
                                    description: marker.description,
                                  },
                                  allMarkers: updatedMarkers,
                                });
                              }
                              message.success(
                                `Đã cập nhật điểm đánh dấu: ${marker.name}`
                              );
                            }}
                            placeholder="Tên điểm đánh dấu"
                            className="tailwind-mb-1"
                            autoFocus
                          />
                        </div>
                      ) : (
                        // View mode
                        <div className="tailwind-p-2">
                          <div className="tailwind-flex tailwind-flex-col tailwind-w-full">
                            <div className="tailwind-flex tailwind-items-center tailwind-w-full tailwind-gap-2">
                              <Checkbox
                                id={`marker-${marker.id}`}
                                checked={allMarkers.some(
                                  (m) => m.id === marker.id && m.visible
                                )}
                                onChange={() => handleToggleMarker(marker.id)}
                                className="tailwind-flex-shrink-0"
                              />
                              <div
                                className="tailwind-flex-grow tailwind-truncate tailwind-cursor-pointer hover:tailwind-text-blue-500 tailwind-transition-colors tailwind-duration-200"
                                onClick={() => handleZoomToMarker(marker)}
                              >
                                <div className="tailwind-flex tailwind-items-center tailwind-gap-1">
                                  <EnvironmentOutlined className="tailwind-text-pink-500 tailwind-flex-shrink-0" />
                                  <Text className="tailwind-text-sm tailwind-truncate tailwind-font-medium">
                                    {marker.name}
                                  </Text>
                                </div>
                              </div>
                              <div className="tailwind-flex tailwind-space-x-1 tailwind-flex-shrink-0">
                                <Button
                                  size="small"
                                  type="text"
                                  icon={<EditOutlined />}
                                  className="tailwind-text-gray-500 hover:tailwind-text-blue-500"
                                  onClick={() => {
                                    setEditingMarkerId(marker.id);
                                  }}
                                />
                                <Button
                                  size="small"
                                  type="text"
                                  danger
                                  icon={<DeleteIcon />}
                                  onClick={() => {
                                    const updatedMarkers = allMarkers.filter(
                                      (m) => m.id !== marker.id
                                    );
                                    setAllMarkers(updatedMarkers);
                                    if (onDataChange) {
                                      onDataChange({
                                        action: 'marker_removed',
                                        markerId: marker.id,
                                        allMarkers: updatedMarkers,
                                      });
                                    }
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          {marker.description && (
                            <div className="tailwind-text-xs tailwind-text-gray-500 tailwind-ml-7 tailwind-mt-1 tailwind-truncate tailwind-pr-2">
                              {marker.description}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Nút thêm điểm đánh dấu ở dưới danh sách */}
                <div className="tailwind-mt-3 tailwind-flex tailwind-justify-center">
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setIsAddingMarkerMode(!isAddingMarkerMode);
                      message.info(
                        isAddingMarkerMode
                          ? 'Đã tắt chế độ thêm điểm đánh dấu'
                          : 'Nhấp vào bản đồ để thêm điểm đánh dấu'
                      );
                    }}
                    className={
                      isAddingMarkerMode
                        ? 'tailwind-bg-green-500 hover:tailwind-bg-green-600 tailwind-border-green-500 tailwind-shadow-md'
                        : 'tailwind-bg-pink-500 hover:tailwind-bg-pink-600 tailwind-border-pink-500 tailwind-shadow-md'
                    }
                  >
                    {isAddingMarkerMode
                      ? 'Đang thêm điểm'
                      : 'Thêm điểm đánh dấu'}
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {!isSidebarVisible && (
          <div className="tailwind-relative tailwind-h-full tailwind-w-8 tailwind-flex tailwind-items-start tailwind-justify-center tailwind-pt-2">
            <Button
              type="default"
              icon={<MenuUnfoldOutlined />}
              className="tailwind-rounded-full tailwind-bg-white tailwind-shadow-md hover:tailwind-bg-gray-100 tailwind-border-0"
              onClick={() => {
                setIsSidebarVisible(true);
                // Invalidate map size after showing sidebar
                if (leafletMap) {
                  setTimeout(() => {
                    leafletMap.invalidateSize({ animate: true, pan: false });
                    // Trigger another resize after animation completes
                    setTimeout(() => {
                      leafletMap.invalidateSize({ animate: false });
                    }, 300);
                  }, 50);
                }
              }}
              size="small"
            />
          </div>
        )}
      </div>

      {/* Đã bỏ phần thêm điểm đánh dấu mới ở ngoài, chỉ giữ lại trong modal cấu hình */}

      {/* Modal chỉ dùng cho thêm marker (giữ lại nhưng không sử dụng) */}
      <ModalAntdCustom
        title="Thêm điểm đánh dấu mới"
        open={isMarkerModalVisible}
        onCancel={() => setIsMarkerModalVisible(false)}
        okText="Thêm"
        cancelText="Hủy"
      >
        <Form layout="vertical">
          <Form.Item
            name="name"
            label="Tên điểm đánh dấu"
            rules={[
              { required: true, message: 'Vui lòng nhập tên điểm đánh dấu' },
            ]}
          >
            <Input placeholder="Nhập tên điểm đánh dấu" />
          </Form.Item>
          <Form.Item name="description" label="Mô tả">
            <Input.TextArea
              rows={4}
              placeholder="Nhập mô tả về điểm đánh dấu này"
            />
          </Form.Item>
        </Form>
      </ModalAntdCustom>
    </div>
  );
};

export default memo(MapSimulatorComponent);

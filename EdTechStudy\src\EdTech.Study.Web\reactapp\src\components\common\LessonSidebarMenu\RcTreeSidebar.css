/* RC Tree exact match styles to replicate your original menu items */

/* ===== Main Container Styles ===== */
.rc-tree-sidebar {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding-left: 8px;
  position: relative;
  min-width: 200px;
}

/* ===== Header Styles ===== */
.sidebar-header {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  flex-shrink: 0;
  min-height: 48px;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
  height: 100%;
}

.sidebar-title {
  margin: 0 !important;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.title-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.title-actions .fullscreen-button {
  background-color: #fff !important;
  border-color: #ea4c89 !important;
  color: #ea4c89 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  padding: 0 12px !important;
}

.title-actions .fullscreen-button:hover {
  background-color: #ea4c89 !important;
  border-color: #fff !important;
  color: #fff !important;
}

/* ===== Content Area Styles ===== */
.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  min-height: 0;
}

.tree-container {
  flex: 1;
  overflow: auto;
  padding: 8px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  min-height: 0;
}

/* For WebKit browsers (Chrome, Safari, newer Edge) */
.tree-container::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.tree-container::-webkit-scrollbar-track {
  background: transparent;
}

.tree-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Only show scrollbar on hover and when content overflows */
.tree-container:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Firefox style for hover */
.tree-container:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* RC Tree basic structure overrides */
.custom-rc-tree {
  margin: 0;
  padding: 0;
  width: 100%;
}

.custom-rc-tree .rc-tree-treenode {
  padding: 0;
  margin: 0 !important;
  width: 100% !important;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
}

.custom-rc-tree .rc-tree-node-content-wrapper {
  padding: 0;
  width: 100% !important;
  display: inline-flex !important;
  align-items: center !important;
  height: auto !important;
  border: none !important;
  outline: none !important;
}

/* Node content styling */
.rc-tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px 8px;
  cursor: pointer;
  justify-content: space-between;
  border-radius: 8px;
  line-height: 2;
  min-height: 40px;
  flex: 1;
}

.rc-tree-node-selected {
  box-shadow: none !important;
  background-color: #fcedf1 !important;
  color: #ea4c89 !important;
}

.rc-tree-title {
  width: 100%;
}

/* Hover styling */
.rc-tree-node-content:hover {
  background-color: #fcedf1 !important;
}

.custom-rc-tree .rc-tree-node-content-wrapper:hover {
  background-color: transparent !important;
}

/* Selected state styling */
.custom-rc-tree .rc-tree-node-selected>.rc-tree-node-content-wrapper .rc-tree-node-content {
  background-color: #fcedf1 !important;
  color: #ea4c89 !important;
  font-weight: 500;
}

.lesson-sidebar-menu-indent {
  width: 20px;
  display: inline-block;
}

/* Custom expand icon styling */
.rc-tree-menu-expand-icon {
  margin-right: 8px;
  font-size: 10px;
  color: rgba(0, 0, 0, 0.45);
  min-width: 12px;
  text-align: center;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
}

.custom-rc-tree .rc-tree-node-selected .rc-tree-node-content .node-title,
.custom-rc-tree .rc-tree-node-selected .rc-tree-node-content .node-title .expand-icon {
  color: #ea4c89;
  font-weight: 400;
}

.custom-rc-tree .rc-tree-switcher {
  background: none !important;
  display: none;
  /* Hide default switcher */
}

/* Active state styling */
.custom-rc-tree .rc-tree-node-selected>.rc-tree-node-content-wrapper {
  background-color: transparent !important;
}

/* Node title styling */
.node-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  /* Allow text truncation */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 12px;
  color: #666;
  transition: all 0.2s;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 4px;
  flex-shrink: 0;
}

.expand-icon svg {
  width: 12px;
  height: 12px;
  transition: transform 0.2s;
}

.expand-icon:hover svg {
  transform: scale(1.1);
}

.custom-rc-tree .rc-tree-node-selected .node-title {
  color: #ea4c89;
}

/* Objective item special styling */
.objective-marker {
  color: #ea4c89;
  margin-right: 4px;
}

/* Drag handle styling */
.drag-handle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #999;
  margin-right: 8px;
  cursor: move;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.rc-tree-node-content:hover .drag-handle {
  opacity: 1;
}

/* Content drag drop styling */
.content-drag-drop {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: move;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.rc-tree-node-content:hover .content-drag-drop {
  opacity: 1;
}

/* Node actions styling */
.node-actions {
  display: flex;
  gap: 4px;
  margin-left: 4px;
  min-width: 70px;
  flex-shrink: 0;
  justify-content: flex-end;
}

/* Button styling */
.action-button {
  padding: 0 !important;
  margin: 0 !important;
  height: auto !important;
  width: auto !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.add-button {
  color: #908588 !important;
}

.add-button:hover {
  color: #706568 !important;
  background-color: rgba(144, 133, 136, 0.1) !important;
}

.edit-button {
  color: #908588 !important;
}

.edit-button:hover {
  color: #706568 !important;
  background-color: rgba(144, 133, 136, 0.1) !important;
}

/* Add button styling */
.add-content-button {
  background-color: #ea4c89;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  cursor: pointer;
  width: auto;
  max-width: 180px;
  margin: 12px auto;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
}

.add-content-button:hover {
  background-color: white;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

/* Edit input styling */
.edit-item-input {
  flex: 1;
  margin-right: 8px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 4px 8px;
  width: 100%;
  max-width: 200px;
  cursor: text !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.edit-item-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

/* Component selector container */
.component-selector-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Drag and drop visual indicators */
.drop-position-top {
  border-top: 3px solid #ea4c89 !important;
  padding-top: 2px;
  position: relative;
}

.drop-position-top::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 10px;
  width: 10px;
  height: 10px;
  background-color: #ea4c89;
  border-radius: 50%;
}

.drop-position-bottom {
  border-bottom: 3px solid #ea4c89 !important;
  padding-bottom: 2px;
  position: relative;
}

.drop-position-bottom::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 10px;
  width: 10px;
  height: 10px;
  background-color: #ea4c89;
  border-radius: 50%;
}

.drop-position-child {
  background-color: rgba(234, 76, 137, 0.1) !important;
  box-shadow: inset 0 0 0 2px #ea4c89 !important;
}

/* RC-Tree drag-drop indicators */
.custom-rc-tree .rc-tree-drop-indicator {
  background-color: #ea4c89;
  height: 2px;
}

.custom-rc-tree .rc-tree-node-content-wrapper.drop-target {
  background-color: rgba(234, 76, 137, 0.1);
}

.custom-rc-tree .rc-tree-treenode-drop-container {
  border: 1px dashed #ea4c89;
  background-color: rgba(234, 76, 137, 0.1);
}

/* Footer styling */
.lesson-sidebar-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 8px;
  align-items: center;
  width: 100%;
  min-height: 64px;
}

/* Save button styling */
.save-button {
  background-color: #4caf50 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
  height: 36px !important;
  min-width: 80px !important;
  flex: 1 1 auto !important;
  max-width: 120px !important;
  white-space: nowrap !important;
}

.save-button:hover {
  background-color: #388e3c !important;
}

/* Responsive styling */
@media (max-width: 768px) {
  .rc-tree-sidebar {
    padding-left: 4px;
    min-width: 180px;
  }

  .sidebar-header {
    padding: 6px 8px;
    min-height: 40px;
  }

  .add-content-button {
    padding: 5px 10px;
    font-size: 12px;
    max-width: 150px;
    margin: 8px auto;
  }

  .save-button {
    height: 32px !important;
    min-width: 40px !important;
    max-width: 80px !important;
    font-size: 12px !important;
    padding: 4px !important;
  }

  .node-title {
    font-size: 13px;
    max-width: calc(100% - 70px);
  }

  .node-actions {
    gap: 2px;
    min-width: 60px;
  }

  .lesson-sidebar-footer {
    min-height: 56px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .rc-tree-sidebar {
    min-width: 160px;
  }

  .node-title {
    font-size: 12px;
    max-width: calc(100% - 60px);
  }

  .node-actions {
    min-width: 50px;
  }

  .save-button {
    min-width: 36px !important;
    height: 36px !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
  }

  .lesson-sidebar-footer {
    min-height: 48px;
    padding: 8px;
  }
}
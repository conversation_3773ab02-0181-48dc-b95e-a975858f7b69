import React, { useRef, useEffect } from 'react';
import H5AudioPlayer, { RHAP_UI } from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';
import { Tooltip } from 'antd';
import { useAudio } from '../context/AudioContext';
import { DragDropMediaWrapper } from '../../../shared';

// Import sub-components
import { AudioHeader } from './AudioHeader';
import { AudioUploader } from './AudioUploader';
import { AudioTrackList } from './AudioTrackList';
import { URLInputSection } from './URLInputSection';
import { PlaybackRateSelector } from './PlaybackRateSelector';

// Import existing icon adapters
import {
  PlayCircle,
  PauseCircle,
  SkipBack,
  SkipForward,
  Volume2,
  Repeat,
  Forward,
  Rewind,
  VolumeMute,
} from './IconAdapter';

// Import custom CSS for the player styling
import '../styles/AudioPlayer.css';
import { isEmpty } from '@tsp/utils';

const AudioPlayer: React.FC<{
  addOrUpdateParamComponent: Function;
}> = ({ addOrUpdateParamComponent }) => {
  const {
    currentAudio,
    isEditing,
    config,
    handlePrevAudio,
    handleNextAudio,
    handleUploadSuccess,
    audioPlayerRef,
  } = useAudio();

  // Container ref for drag and drop
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize playback rate from config when audio changes
  useEffect(() => {
    if (
      audioPlayerRef.current &&
      audioPlayerRef.current.audio &&
      audioPlayerRef.current.audio.current &&
      config.playbackRate
    ) {
      audioPlayerRef.current.audio.current.playbackRate = config.playbackRate;
    }
  }, [config.playbackRate, currentAudio, audioPlayerRef]);

  // Initialize volume from config when audio changes
  useEffect(() => {
    if (
      audioPlayerRef.current &&
      audioPlayerRef.current.audio &&
      audioPlayerRef.current.audio.current
    ) {
      const volume = config.volume !== undefined ? config.volume : 1;
      audioPlayerRef.current.audio.current.volume = volume;
    }
  }, [config.volume, currentAudio, audioPlayerRef]);

  // Handle volume change from H5AudioPlayer
  const handleVolumeChangeFromPlayer = (e: Event) => {
    const audioElement = e.target as HTMLAudioElement;
    const newVolume = audioElement.volume;

    // Update config with new volume
    if (config.volume !== newVolume) {
      addOrUpdateParamComponent({
        ...config,
        volume: newVolume,
      });
    }
  };

  // Handle when audio ends
  const handleAudioEnded = () => {
    if (config.medias && config.medias.length > 1) {
      // Play next track if available
      handleNextAudio();
    }
  };

  // If no audio, show the upload component
  if (!currentAudio || !currentAudio.mediaUrl) {
    return (
      <div
        ref={containerRef}
        className="tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-min-h-[200px] media-file-uploader-container media-container editing"
      >
        <AudioUploader fullScreen={true} />
      </div>
    );
  }

  return (
    <div className="edtech-audio-player-container">
      {/* URL Input Section with Delete All Button - Only in edit mode */}
      {isEditing && <URLInputSection />}
      {isEditing && !isEmpty(config.medias) && (
        <AudioUploader fullScreen={false} />
      )}
      <div className="edtech-audio-player-card" ref={containerRef}>
        <div className="edtech-audio-player-wrapper">
          {/* Wrap with DragDropMediaWrapper for drag & drop functionality */}
          <DragDropMediaWrapper
            mediaType="audio"
            isEditing={isEditing}
            onUploadSuccess={handleUploadSuccess}
            className="tailwind-w-full"
          >
            <div className="tailwind-border-primary tailwind-rounded-xl tailwind-border tailwind-p-3">
              {/* Audio Header */}
              <AudioHeader />

              {/* H5 Audio Player */}
              <div className="edtech-audio-player">
                <H5AudioPlayer
                  ref={audioPlayerRef}
                  src={currentAudio.mediaUrl}
                  autoPlay={false} // Don't autoplay on first load
                  showJumpControls={true}
                  showSkipControls={config.medias && config.medias.length > 1}
                  loop={false} // Default loop off
                  volume={config.volume || 1} // Use volume from config, default to 1
                  progressJumpSteps={{ forward: 10000, backward: 10000 }} // 10 seconds jump
                  onEnded={handleAudioEnded}
                  onClickNext={handleNextAudio}
                  onClickPrevious={handlePrevAudio}
                  onVolumeChange={handleVolumeChangeFromPlayer} // Handle volume changes
                  customIcons={{
                    play: (
                      <Tooltip title="Phát">
                        <PlayCircle />
                      </Tooltip>
                    ),
                    pause: (
                      <Tooltip title="Tạm dừng">
                        <PauseCircle />
                      </Tooltip>
                    ),
                    next: (
                      <Tooltip title="Bài tiếp theo">
                        <SkipForward />
                      </Tooltip>
                    ),
                    previous: (
                      <Tooltip title="Bài trước đó">
                        <SkipBack />
                      </Tooltip>
                    ),
                    forward: (
                      <Tooltip title="Tua tới 10s">
                        <Forward />
                      </Tooltip>
                    ),
                    rewind: (
                      <Tooltip title="Tua lùi 10s">
                        <Rewind />
                      </Tooltip>
                    ),
                    loop: (
                      <Tooltip title="Tắt lặp lại">
                        <Repeat className="tailwind-text-primary" />
                      </Tooltip>
                    ),
                    loopOff: (
                      <Tooltip title="Lặp lại">
                        <Repeat className="tailwind-text-disabled" />
                      </Tooltip>
                    ),
                    volume: (
                      <Tooltip title="Âm lượng">
                        <Volume2 />
                      </Tooltip>
                    ),
                    volumeMute: (
                      <Tooltip title="Tắt tiếng">
                        <VolumeMute className="tailwind-text-red-600" />
                      </Tooltip>
                    ),
                  }}
                  customProgressBarSection={[
                    RHAP_UI.CURRENT_TIME,
                    RHAP_UI.PROGRESS_BAR,
                    RHAP_UI.DURATION,
                  ]}
                  customControlsSection={[
                    RHAP_UI.MAIN_CONTROLS,
                    RHAP_UI.VOLUME_CONTROLS,
                  ]}
                  customVolumeControls={[
                    <PlaybackRateSelector key="playback-rate" />,
                    RHAP_UI.LOOP,
                    RHAP_UI.VOLUME,
                  ]}
                  className="pink-audio-player"
                />
              </div>
            </div>
          </DragDropMediaWrapper>
          {/* Audio Track List - Inside the card */}
          {!isEmpty(config.medias) && <AudioTrackList />}

          {/* Upload button at the bottom when in edit mode - Inside the card */}
          {isEditing && isEmpty(config.medias) && (
            <div className="tailwind-px-4 tailwind-pb-4">
              <AudioUploader />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;

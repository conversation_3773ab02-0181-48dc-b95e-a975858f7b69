import React, { useCallback, useEffect, useState } from 'react';
import {
  Modal,
  Button,
  Space,
  Row,
  Col,
  Divider,
  Select,
  message,
  Tag,
  Input,
} from 'antd';
import {
  BaseQuestion,
  PracticeEngineContext,
  QuestionType,
  Subject,
  QuestionStatus,
} from '../../../interfaces/quizs/questionBase';
import {
  FullscreenOutlined,
  SettingOutlined,
  SaveOutlined,
  CloseOutlined,
  SwapOutlined,
  BookOutlined,
  CheckCircleOutlined,
  EditOutlined,
  SendOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  TagsOutlined,
  ProfileOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import './QuestionEditor.css';
import './QuestionEditorMetadata.css';
import SingleQuestionEngine from '../singleQuestionEngine/SingleQuestionEngine';
import { QuestionTemplateFactory } from '../practiceEngines/questionTemplates';
import practiceLocalization from '../localization';
import ResizableDivider from '../../common/ResizableDivider/ResizableDivider';

export interface QuestionEditorProps {
  visible: boolean;
  question: BaseQuestion;
  onCancel: () => void;
  onSave: (updatedQuestion: BaseQuestion) => void;
  title?: string;
  subjects: Subject[];
}

const QuestionEditor: React.FC<QuestionEditorProps> = ({
  visible,
  question,
  onCancel,
  onSave,
  title = 'Chỉnh sửa câu hỏi',
  subjects,
}) => {
  const [editedQuestion, setEditedQuestion] = useState<BaseQuestion | null>(
    null
  );
  const [configMode, setConfigMode] = useState<boolean>(true);
  const [selectedQuestionType, setSelectedQuestionType] = useState<string>('');
  const [selectedSubject, setSelectedSubject] = useState<string>('');

  // State for tags and topics
  const [tags, setTags] = useState<string[]>([]);
  const [topics, setTopics] = useState<string[]>([]);
  const [newTag, setNewTag] = useState<string>('');
  const [newTopic, setNewTopic] = useState<string>('');

  // State for panel resize
  const [leftPanelWidth, setLeftPanelWidth] = useState<number>(50);

  // State for metadata panel
  const [showMetadata, setShowMetadata] = useState<boolean>(false);

  // Initialize the edited question when the modal becomes visible or question changes
  useEffect(() => {
    if (visible && question) {
      setEditedQuestion({ ...question });
      setSelectedQuestionType(question.type);
      setSelectedSubject(question.subjectId || '');

      // Initialize tags and topics
      setTags(
        Array.isArray(question.tags)
          ? [...question.tags]
          : question.tags
          ? question.tags.split(',').map((tag) => tag.trim())
          : []
      );

      setTopics(
        Array.isArray(question.metadata?.topics)
          ? [...question.metadata.topics]
          : question.topics
          ? (question.topics as string).split(',').map((topic) => topic.trim())
          : []
      );
    }
  }, [visible, question]);

  // Handle question change
  const handleQuestionChange = useCallback((updatedQuestion: BaseQuestion) => {
    setEditedQuestion(updatedQuestion);
  }, []);

  // Handle subject change
  const handleSubjectChange = useCallback(
    (subjectId: string) => {
      if (!editedQuestion) return;

      const newQuestion = {
        ...editedQuestion,
        subjectId: subjectId,
        subject: subjects.find((s) => s.id === subjectId),
      };

      setEditedQuestion(newQuestion);
      setSelectedSubject(subjectId);
    },
    [editedQuestion, subjects]
  );

  // Handle tags management
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);

      if (editedQuestion) {
        setEditedQuestion({
          ...editedQuestion,
          tags: updatedTags,
        });
      }

      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(updatedTags);

    if (editedQuestion) {
      setEditedQuestion({
        ...editedQuestion,
        tags: updatedTags,
      });
    }
  };

  // Handle topics management
  const handleAddTopic = () => {
    if (newTopic.trim() && !topics.includes(newTopic.trim())) {
      const updatedTopics = [...topics, newTopic.trim()];
      setTopics(updatedTopics);

      if (editedQuestion) {
        const updatedMetadata = {
          ...editedQuestion.metadata,
          topics: updatedTopics,
        };

        setEditedQuestion({
          ...editedQuestion,
          topics: updatedTopics.join(','),
          metadata: updatedMetadata,
        });
      }

      setNewTopic('');
    }
  };

  const handleRemoveTopic = (topicToRemove: string) => {
    const updatedTopics = topics.filter((topic) => topic !== topicToRemove);
    setTopics(updatedTopics);

    if (editedQuestion) {
      const updatedMetadata = {
        ...editedQuestion.metadata,
        topics: updatedTopics,
      };

      setEditedQuestion({
        ...editedQuestion,
        topics: updatedTopics.join(','),
        metadata: updatedMetadata,
      });
    }
  };

  // Handle saving the question
  const handleSave = useCallback(() => {
    if (editedQuestion) {
      // Ensure tags and topics are properly formatted in the saved question
      const finalQuestion = {
        ...editedQuestion,
        tags: tags,
        topics: topics.join(','),
        metadata: {
          ...editedQuestion.metadata,
          topics: topics,
        },
      };

      onSave(finalQuestion);
    }
  }, [editedQuestion, onSave, tags, topics]);

  // Toggle between config and preview modes
  const toggleConfigMode = useCallback(() => {
    setConfigMode((prev) => !prev);
  }, []);

  // Toggle metadata panel
  const toggleMetadataPanel = useCallback(() => {
    setShowMetadata((prev) => !prev);
  }, []);

  // Handle panel resize
  const handlePanelResize = useCallback((newWidth: number) => {
    setLeftPanelWidth(newWidth);
  }, []);

  // Handle question type change
  const handleQuestionTypeChange = useCallback(
    (newType: string) => {
      if (newType === selectedQuestionType) {
        return; // No change needed
      }

      // Confirm before changing type
      Modal.confirm({
        title: 'Thay đổi loại câu hỏi',
        content:
          'Thay đổi loại câu hỏi có thể làm mất một số dữ liệu. Bạn có chắc chắn muốn tiếp tục?',
        okText: 'Đồng ý',
        cancelText: 'Hủy',
        onOk: () => {
          const factory = QuestionTemplateFactory.getInstance();
          // Create new question template of the selected type
          const newQuestionTemplate = factory.create(newType);

          if (!newQuestionTemplate) {
            message.error('Không thể tạo mẫu câu hỏi mới');
            return;
          }

          // Preserve some properties from the original question
          const updatedQuestion = {
            ...newQuestionTemplate,
            id: editedQuestion?.id || newQuestionTemplate.id,
            title: editedQuestion?.title || newQuestionTemplate.title,
            description:
              editedQuestion?.description || newQuestionTemplate.description,
            subjectId:
              editedQuestion?.subjectId || newQuestionTemplate.subjectId,
            points: editedQuestion?.points || newQuestionTemplate.points,
            statusEntity: editedQuestion?.statusEntity || QuestionStatus.Draft,
            tags: tags,
            topics: topics.join(','),
            metadata: {
              ...editedQuestion?.metadata,
              topics: topics,
            },
          };

          // Update the state
          setEditedQuestion(updatedQuestion);
          setSelectedQuestionType(newType);
          message.success(`Đã chuyển đổi sang câu hỏi dạng ${newType}`);
        },
      });
    },
    [selectedQuestionType, editedQuestion, tags, topics]
  );

  // Get status tag color and text
  const getStatusTag = () => {
    if (!editedQuestion) return null;

    const status = editedQuestion.statusEntity;
    let color = 'default';
    let text = 'Bản nháp';
    let icon = <EditOutlined />;

    switch (status) {
      case QuestionStatus.Published:
        color = 'success';
        text = 'Hoàn thành';
        icon = <CheckCircleOutlined />;
        break;
      case QuestionStatus.Approved:
        color = 'processing';
        text = 'Đã duyệt';
        icon = <CheckCircleOutlined />;
        break;
      case QuestionStatus.Submitted:
        color = 'warning';
        text = 'Đã gửi';
        icon = <SendOutlined />;
        break;
      case QuestionStatus.Draft:
        color = 'default';
        text = 'Bản nháp';
        icon = <EditOutlined />;
        break;
      case QuestionStatus.Rejected:
        color = 'error';
        text = 'Đã từ chối';
        icon = <CloseCircleOutlined />;
        break;
      default:
        color = 'default';
        text = 'Bản nháp';
        icon = <ClockCircleOutlined />;
    }

    return (
      <Tag color={color} icon={icon} className="status-tag">
        {text}
      </Tag>
    );
  };

  // Available question types
  const questionTypes = [
    { value: practiceLocalization.quiz, label: 'Trắc nghiệm 1 đáp án' },
    {
      value: practiceLocalization.multiselect,
      label: 'Trắc nghiệm nhiều đáp án',
    },
    { value: practiceLocalization.matching, label: 'Câu hỏi nối' },
    { value: practiceLocalization.fillblanks, label: 'Câu hỏi điền từ' },
    { value: practiceLocalization.shortanswer, label: 'Câu hỏi trả lời ngắn' },
  ];

  // Render metadata panel
  const renderMetadataPanel = () => {
    if (!showMetadata) return null;

    return (
      <div className="metadata-panel">
        <h3>Thông tin bổ sung</h3>
        <Divider />

        <div className="metadata-section">
          <h4>
            <TagsOutlined /> Thẻ
          </h4>
          <div className="tags-container">
            {tags.map((tag) => (
              <Tag
                key={tag}
                closable
                onClose={(e) => {
                  e.preventDefault();
                  handleRemoveTag(tag);
                }}
              >
                {tag}
              </Tag>
            ))}
          </div>
          <Input
            placeholder="Thêm thẻ mới"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onPressEnter={handleAddTag}
            suffix={
              <PlusOutlined
                onClick={handleAddTag}
                style={{ cursor: 'pointer' }}
              />
            }
            style={{ marginTop: '8px' }}
          />
        </div>

        <div className="metadata-section">
          <h4>
            <ProfileOutlined /> Chủ đề
          </h4>
          <div className="topics-container">
            {topics.map((topic) => (
              <Tag
                key={topic}
                closable
                color="blue"
                onClose={(e) => {
                  e.preventDefault();
                  handleRemoveTopic(topic);
                }}
              >
                {topic}
              </Tag>
            ))}
          </div>
          <Input
            placeholder="Thêm chủ đề mới"
            value={newTopic}
            onChange={(e) => setNewTopic(e.target.value)}
            onPressEnter={handleAddTopic}
            suffix={
              <PlusOutlined
                onClick={handleAddTopic}
                style={{ cursor: 'pointer' }}
              />
            }
            style={{ marginTop: '8px' }}
          />
        </div>
      </div>
    );
  };

  // If no question is available yet, show nothing
  if (!editedQuestion) {
    return null;
  }

  return (
    <>
      <Modal
        title={
          <div className="question-editor-header">
            <div className="title-with-tag">
              <span className="limited-title" title={title}>
                {title.length > 50 ? title.substring(0, 50) + '...' : title}
              </span>
              {getStatusTag()}
            </div>
            <Space wrap>
              <Select
                value={selectedQuestionType}
                onChange={handleQuestionTypeChange}
                style={{ width: 200 }}
                options={questionTypes}
                placeholder="Chọn loại câu hỏi"
                disabled={!configMode}
              />
              <Select
                value={selectedSubject}
                onChange={handleSubjectChange}
                style={{ width: 200 }}
                placeholder="Chọn môn học"
                disabled={!configMode}
              >
                {subjects.map((subject) => (
                  <Select.Option key={subject.id} value={subject.id}>
                    {subject.name}
                  </Select.Option>
                ))}
              </Select>
              <Button
                icon={<TagsOutlined />}
                onClick={toggleMetadataPanel}
                type={showMetadata ? 'primary' : 'default'}
              >
                Thẻ & Chủ đề
              </Button>
              <Button
                icon={configMode ? <FullscreenOutlined /> : <SettingOutlined />}
                onClick={toggleConfigMode}
              >
                {configMode ? 'Xem trước' : 'Chỉnh sửa'}
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
              >
                Lưu
              </Button>
            </Space>
          </div>
        }
        open={visible}
        onCancel={onCancel}
        footer={null}
        width="90%"
        centered
        className="question-editor-modal"
        closeIcon={<CloseOutlined />}
      >
        <div className="question-editor-content">
          {showMetadata && renderMetadataPanel()}

          {!configMode ? (
            // Full preview mode
            <Col span={24}>
              <div className="question-preview-container">
                <PracticeEngineContext.Provider
                  value={{
                    handleChangeQuestion: handleQuestionChange,
                    handleDeleteQuestion: () => {},
                    handleChangePosition: () => {},
                    handleToggleFullscreen: () => {},
                    isFullscreen: false,
                  }}
                >
                  <SingleQuestionEngine
                    question={editedQuestion}
                    configMode={false}
                    onQuestionChange={handleQuestionChange}
                  />
                </PracticeEngineContext.Provider>
              </div>
            </Col>
          ) : (
            // Split view with resizable panels
            <div
              className="question-split-view"
              style={{ position: 'relative', display: 'flex' }}
            >
              {/* Left Panel (Preview) */}
              <div
                className="question-preview-container"
                style={{
                  width: `${leftPanelWidth}%`,
                  overflow: 'auto',
                  paddingRight: '12px',
                }}
              >
                <h3>Xem trước</h3>
                <Divider />
                <PracticeEngineContext.Provider
                  value={{
                    handleChangeQuestion: handleQuestionChange,
                    handleDeleteQuestion: () => {},
                    handleChangePosition: () => {},
                    handleToggleFullscreen: () => {},
                    isFullscreen: false,
                  }}
                >
                  <SingleQuestionEngine
                    question={editedQuestion}
                    configMode={false}
                    onQuestionChange={handleQuestionChange}
                  />
                </PracticeEngineContext.Provider>
              </div>

              {/* Resizable Divider */}
              <ResizableDivider
                onChange={handlePanelResize}
                initialPosition={50}
              />

              {/* Right Panel (Config) */}
              <div
                className="question-config-container"
                style={{
                  width: `${100 - leftPanelWidth}%`,
                  overflow: 'auto',
                  paddingLeft: '12px',
                }}
              >
                <Space style={{ marginBottom: '16px' }}>
                  <h3>Chỉnh sửa</h3>
                  {selectedSubject && subjects.length > 0 && (
                    <span className="subject-badge">
                      <BookOutlined />{' '}
                      {subjects.find((s) => s.id === selectedSubject)?.name ||
                        'Chưa chọn môn học'}
                    </span>
                  )}
                </Space>
                <Divider />
                <PracticeEngineContext.Provider
                  value={{
                    handleChangeQuestion: handleQuestionChange,
                    handleDeleteQuestion: () => {},
                    handleChangePosition: () => {},
                    handleToggleFullscreen: () => {},
                    isFullscreen: false,
                  }}
                >
                  <SingleQuestionEngine
                    question={editedQuestion}
                    configMode={true}
                    onQuestionChange={handleQuestionChange}
                  />
                </PracticeEngineContext.Provider>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default QuestionEditor;

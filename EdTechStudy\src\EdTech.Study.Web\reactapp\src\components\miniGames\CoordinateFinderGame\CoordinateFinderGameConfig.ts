// Import the ITextProps type
import { ITextProps } from '../../core/title/CoreTitle';

// Định nghĩa kiểu dữ liệu cho tọa độ trong game
export interface Coordinate {
  latitude: number;
  longitude: number;
  name: string;
  hint?: string;
}

// Định nghĩa kiểu dữ liệu cho cấu hình game tọa độ
export interface CoordinateFinderGameConfig {
  titleProps: ITextProps; // Thuộc tính định dạng cho tiêu đề
  description?: string; // Mô tả trò chơi
  coordinates: Coordinate[]; // Danh sách các tọa độ bí ẩn cần tìm
  initialZoom: number; // Zoom ban đầu của bản đồ
  initialCenter: { lat: number; lng: number }; // Vị trí trung tâm ban đầu
  difficultyLevel: 'easy' | 'medium' | 'hard'; // <PERSON><PERSON> khó của game
  timeLimit?: number; // Giới hạn thời gian (giây), không bắt buộc
  hintPenalty?: number; // Điểm bị trừ khi sử dụng gợi ý
  correctAnswerScore: number; // Điểm thưởng cho câu trả lời đúng
  proximityThreshold: number; // Ngưỡng khoảng cách (km) được coi là đúng
  mapOptions?: any; // Tùy chọn bản đồ khác
  allowConfiguration: boolean; // Cho phép hiển thị nút cấu hình, mặc định là true
}

// Kiểu dữ liệu cho kết quả của mỗi lượt chơi
export interface GameResult {
  coordinate: Coordinate;
  userGuess: { lat: number; lng: number } | null;
  distance: number | null;
  correct: boolean;
  hintsUsed: number;
  score: number;
  timeTaken?: number;
}

// Kiểu dữ liệu tổng kết cho toàn bộ game
export interface GameSummary {
  totalScore: number;
  correctAnswers: number;
  totalQuestions: number;
  accuracy: number; // Tỷ lệ phần trăm đúng
  averageDistance: number; // Khoảng cách trung bình đến vị trí đúng
  totalHintsUsed: number;
  totalTimeTaken?: number;
  results: GameResult[];
}

// Cấu hình mặc định cho game tọa độ
export const defaultCoordinateFinderGameConfig: CoordinateFinderGameConfig = {
  titleProps: {
    text: 'Tìm Tọa Độ Bí Ẩn',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  description:
    'Tìm vị trí trên bản đồ dựa vào tọa độ bí ẩn. Thử thách kiến thức địa lý và khả năng định vị của bạn.',
  coordinates: [
    {
      name: 'Hồ Hoàn Kiếm, Hà Nội',
      latitude: 21.0287,
      longitude: 105.8524,
      hint: 'Một hồ nước nổi tiếng ở trung tâm thủ đô',
    },
    {
      name: 'Vịnh Hạ Long',
      latitude: 20.9101,
      longitude: 107.0448,
      hint: 'Di sản thiên nhiên thế giới nổi tiếng của Việt Nam',
    },
    {
      name: 'Đại Nội Huế',
      latitude: 16.4698,
      longitude: 107.5798,
      hint: 'Kinh thành của triều đại nhà Nguyễn',
    },
  ],
  initialZoom: 6,
  initialCenter: { lat: 16.0, lng: 106.0 }, // Trung tâm Việt Nam
  difficultyLevel: 'medium',
  timeLimit: 120,
  hintPenalty: 2,
  correctAnswerScore: 10,
  proximityThreshold: 10,
  mapOptions: {
    scrollWheelZoom: true,
    doubleClickZoom: false,
  },
  allowConfiguration: true,
};

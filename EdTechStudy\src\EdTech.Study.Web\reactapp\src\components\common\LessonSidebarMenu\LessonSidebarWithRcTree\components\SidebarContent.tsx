import React from 'react';
import Tree, { TreeProps } from 'rc-tree';
import { TreeMenuItem } from '../types';
import LessonSelectorComponent from '../../../LessonSelectorComponent/LessonSelectorComponent';
import AddContentButton from './AddContentButton';
import Tree<PERSON>odeRenderer from './TreeNodeRenderer';

interface SidebarContentProps {
  convertedItems: TreeMenuItem[];
  selectedKey?: string;
  expandedKeys: string[];
  autoExpandParent: boolean;
  isEditing: boolean;
  editingItemKey: string | null;
  editingItemValue: string;
  isShowSelectComponent: boolean;
  selectNode: any;
  handleExpand: (keys: string[]) => void;
  handleTreeSelect: TreeProps['onSelect'];
  handleStartEdit: (key: string, title: string) => void;
  handleAddNode: (key: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent, key: string) => void;
  handleSaveEdit: (key: string) => void;
  handleAddRootNode: () => void;
  handleComponentSelect: () => void;
  setIsShowSelectComponent: (value: boolean) => void;
  handleDrop: (info: any) => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  convertedItems,
  selectedKey,
  expandedKeys,
  autoExpandParent,
  isEditing,
  editingItemKey,
  editingItemValue,
  isShowSelectComponent,
  selectNode,
  handleExpand,
  handleTreeSelect,
  handleStartEdit,
  handleAddNode,
  handleInputChange,
  handleKeyDown,
  handleSaveEdit,
  handleAddRootNode,
  handleComponentSelect,
  setIsShowSelectComponent,
  handleDrop,
}) => {
  const titleRender = (nodeData: TreeMenuItem) => {
    // Calculate level from path
    const level = nodeData.path ? nodeData.path.split('/').length - 1 : 0;

    return (
      <TreeNodeRenderer
        nodeData={nodeData}
        isEditing={isEditing}
        editingItemKey={editingItemKey}
        editingItemValue={editingItemValue}
        expandedKeys={expandedKeys}
        handleExpand={handleExpand}
        handleStartEdit={handleStartEdit}
        handleAddNode={handleAddNode}
        handleInputChange={handleInputChange}
        handleKeyDown={handleKeyDown}
        handleSaveEdit={handleSaveEdit}
        level={level}
      />
    );
  };

  return (
    <div className="sidebar-content">
      {isShowSelectComponent && isEditing && (
        <div className="component-selector-container">
          <LessonSelectorComponent
            isOpen={isShowSelectComponent}
            parentNode={selectNode}
            onClose={() => setIsShowSelectComponent(false)}
            onSelectComponent={handleComponentSelect}
            hideButton
          />
        </div>
      )}

      <div className="tree-container">
        <Tree
          className={`custom-rc-tree ${
            editingItemKey !== null ? 'editing-mode' : ''
          }`}
          treeData={convertedItems}
          selectedKeys={selectedKey ? [selectedKey] : []}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onExpand={(keys) => handleExpand(keys as string[])}
          onSelect={handleTreeSelect}
          titleRender={titleRender}
          draggable={isEditing && editingItemKey === null}
          onDrop={handleDrop}
          showIcon={false}
          showLine={false}
        />
      </div>

      {isEditing && <AddContentButton onClick={handleAddRootNode} />}
    </div>
  );
};

export default SidebarContent;

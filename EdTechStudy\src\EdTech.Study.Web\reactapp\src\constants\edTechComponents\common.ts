import LessonObjectiveComponent, {
  lessonObjectiveSchema,
} from '../../components/common/elements/LessionObjectiveComponnet/LessonObjectiveComponent';
// AudioComponent
import AudioComponent from '../../components/common/elements/Media/AudioComponent/v0/AudioComponent';
import { audioComponentSchema } from '../../components/common/elements/Media/AudioComponent/shared/schema';
// ImageComponent
import ImageComponentV0 from '../../components/common/elements/Media/ImageComponent/v0/ImageComponent';
import { imageComponentSchema } from '../../components/common/elements/Media/ImageComponent/shared/schema';
// VideoComponent
import VideoComponent from '../../components/common/elements/Media/VideoComponent/v0/VideoComponent';
import { videoComponentSchema } from '../../components/common/elements/Media/VideoComponent/shared/schema';
//
import TableComponent, {
  tableComponentSchema,
} from '../../components/common/elements/Table/TableComponent/TableComponent';
import BoxTextComponent from '../../components/common/elements/Text/BoxTextComponent/BoxTextComponent';
import { boxTextSchema } from '../../components/common/elements/Text/BoxTextComponent/BoxTextComponentConfig';
import LessonTitleComponent, {
  lessonTitleSchema,
} from '../../components/common/elements/Text/LessonTitleComponent';
// RichTextEditor
import RichTextEditor from '../../components/common/elements/Text/RichTextEditorComponent/v0/RichTextEditor';

import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';
import { richTextEditorSchema } from '../../components/common/elements/Text/RichTextEditorComponent/shared/schema';
import Model3DComponent from '../../components/common/elements/Media/3DObjectComponent/v0/Model3DComponent';
import { model3DComponentSchema } from '../../components/common/elements/Media/3DObjectComponent/shared/schema';

export const COMMON_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'LessonTitleComponent',
    title: 'Tiêu Đề',
    components: [
      {
        version: '1.0.0',
        component: LessonTitleComponent,
        schema: lessonTitleSchema,
      },
    ],

    type: ETypeEdTechComponent.COMMON,
    tags: ['Tiêu đề', 'Văn bản', 'Định dạng'],
    description:
      'Component hiển thị tiêu đề với khả năng tùy chỉnh cỡ chữ, màu sắc, căn lề và các thuộc tính CSS khác.',
    schema: lessonTitleSchema,
  },
  {
    name: 'BoxTextComponent',
    title: 'Hộp văn bản tùy chỉnh',
    components: [
      {
        version: '1.0.0',
        component: BoxTextComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Văn bản', 'Định dạng', 'Hộp văn bản', 'Tùy chỉnh', 'Thiết kế'],
    description:
      'Component tạo hộp văn bản với khả năng tùy chỉnh cao. Người dùng có thể điều chỉnh kích thước, màu sắc, viền, và định dạng văn bản trong hộp. Hỗ trợ các tùy chọn như căn chỉnh, kiểu chữ, cỡ chữ, màu chữ và bố cục. Lý tưởng cho việc làm nổi bật thông tin quan trọng hoặc tạo các phần nội dung có tính thẩm mỹ cao trong bài giảng.',
    schema: boxTextSchema,
  },
  {
    name: 'RichTextEditor',
    title: 'Trình soạn thảo văn bản',
    components: [
      {
        version: '1.0.0',
        component: RichTextEditor,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Văn bản', 'Định dạng', 'Soạn thảo', 'Rich text'],
    description:
      'Component soạn thảo văn bản với khả năng định dạng phong phú như đậm, nghiêng, gạch chân, tiêu đề, danh sách và liên kết. Cho phép giáo viên và học sinh tạo nội dung học tập có định dạng đẹp mắt.',
    schema: richTextEditorSchema,
  },
  {
    name: 'ImageComponent',
    title: 'Trình hiển thị hình ảnh',
    components: [
      {
        version: '1.0.0',
        component: ImageComponentV0,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Hình ảnh', 'Đa phương tiện', 'Tương tác', 'Tải lên'],
    description:
      'Component cho phép tải lên và hiển thị hình ảnh với các tính năng tương tác như phóng to, xoay, xem toàn màn hình. Hỗ trợ kéo thả hình ảnh, thêm chú thích, và tùy chỉnh hiển thị. Giúp giáo viên và học sinh dễ dàng tích hợp nội dung trực quan vào bài học.',
    schema: imageComponentSchema,
  },
  {
    name: 'VideoComponent',
    title: 'Trình phát video',
    components: [
      {
        version: '1.0.0',
        component: VideoComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Video', 'Đa phương tiện', 'YouTube', 'Tương tác', 'Học trực quan'],
    description:
      'Component phát video với nhiều tùy chọn nguồn (tải lên, nhúng url) và các tính năng tương tác như điều khiển phát, tạm dừng, âm lượng, thời gian bắt đầu/kết thúc. Cho phép giáo viên tích hợp nội dung video phong phú vào bài học.',
    schema: videoComponentSchema,
  },
  {
    name: 'AudioComponent',
    title: 'Trình phát âm thanh',
    components: [
      {
        version: '1.0.0',
        component: AudioComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: [
      'Âm thanh',
      'Đa phương tiện',
      'Tương tác',
      'Podcast',
      'Học ngôn ngữ',
    ],
    description:
      'Component phát âm thanh với tính năng tải lên file, trực quan hoá dạng sóng, và điều khiển phát phong phú (tạm dừng, tốc độ phát, âm lượng). Hỗ trợ nhiều định dạng âm thanh và tùy chỉnh giao diện. Lý tưởng cho bài giảng ngôn ngữ, podcast giáo dục và nội dung âm thanh tương tác.',
    schema: audioComponentSchema,
  },
  {
    name: '3DObjectComponent',
    title: 'Trình hiển thị đối tượng 3D',
    components: [
      {
        version: '1.0.0',
        component: Model3DComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Hình ảnh 3D', 'Đa phương tiện', 'Tương tác', 'Tải lên', 'GLTF', 'GLB', 'OBJ', 'FBX', '3D'],
    description:
      'Component cho phép tải lên và hiển thị mô hình 3D với các tính năng tương tác như phóng to, xoay, xem toàn màn hình. Hỗ trợ nhiều định dạng 3D phổ biến (GLTF, GLB, OBJ, FBX) và tùy chỉnh hiển thị. Giúp giáo viên và học sinh dễ dàng tích hợp nội dung 3D trực quan vào bài học.',
    schema: model3DComponentSchema,
  },
  {
    name: 'TableComponent',
    title: 'Bảng dữ liệu',
    components: [
      {
        version: '1.0.0',
        component: TableComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Bảng', 'Dữ liệu', 'Định dạng', 'Tùy chỉnh', 'Trình bày'],
    description:
      'Component tạo và chỉnh sửa bảng dữ liệu với đầy đủ tính năng tương tự các trình soạn thảo văn bản chuyên nghiệp. Cho phép thêm/xóa hàng cột, gộp/tách ô, định dạng văn bản, tùy chỉnh viền và màu nền. Hỗ trợ nhiều kiểu bảng có sẵn và công cụ sao chép định dạng. Phù hợp để trình bày dữ liệu có cấu trúc, tạo thời khóa biểu, bảng so sánh và các hoạt động học tập tương tác.',
    schema: tableComponentSchema,
  },
  {
    name: 'LessonObjectiveComponent',
    title: 'Mục Tiêu Bài Học',
    components: [
      {
        version: '1.0.0',
        component: LessonObjectiveComponent,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.COMMON,
    tags: ['Mục tiêu', 'Bài học', 'Danh sách'],
    description:
      'Component hiển thị mục tiêu bài học cung cấp cấu trúc và khả năng tùy chỉnh cho mục tiêu bài học.',
    schema: lessonObjectiveSchema,
  },
];

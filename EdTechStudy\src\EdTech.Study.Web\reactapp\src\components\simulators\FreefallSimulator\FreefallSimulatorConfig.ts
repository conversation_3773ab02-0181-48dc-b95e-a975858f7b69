import { ITextProps } from '../../core/title/CoreTitle';

// FreefallSimulatorConfig.ts
export interface FreefallObject {
  id: string;
  name: string;
  mass: number; // kg
  radius: number; // meters (for visualization)
  color: string;
}

export interface FreefallEnvironment {
  id: string;
  name: string;
  gravity: number; // m/s^2
  airResistance: boolean;
  airDensity: number; // kg/m^3
  dragCoefficient: number; // dimensionless
}

export interface FreefallDataPoint {
  time: number;
  position: number;
  velocity: number;
  acceleration: number;
}

export interface FreefallSimulationResult {
  timeElapsed: number;
  maxHeight: number;
  maxVelocity: number;
  impactVelocity: number;
  impactTime: number;
  dataPoints: FreefallDataPoint[];
}

export interface FreefallSimulatorConfig {
  titleProps: ITextProps;
  description: string;
  initialHeight: number; // meters
  initialVelocity: number; // m/s (positive is upward)
  simulationDuration: number; // seconds
  timeStep: number; // seconds per step

  // Environment settings
  environments: FreefallEnvironment[];
  activeEnvironmentId: string;

  // Single object for simulation
  object: FreefallObject;

  // Display settings
  displayOptions: {
    showGrid: boolean;
    showVelocityVector: boolean;
    showAccelerationVector: boolean;
    showForceVectors: boolean;
    showRuler: boolean;
    showTimer: boolean;
    showDataTable: boolean;
    realTimeGraph: boolean;
  };

  // Controls
  controls: {
    allowPause: boolean;
    allowSlowMotion: boolean;
    allowFastForward: boolean;
    showResetButton: boolean;
    showPlayPauseButton: boolean;
    showStepButton: boolean;
    showTimeControl: boolean;
  };

  // Special features
  specialFeatures: {
    enableExport: boolean; // Allow data export
    showFormulas: boolean; // Display relevant physics formulas
  };
}

export const defaultFreefallSimulatorConfig: FreefallSimulatorConfig = {
  titleProps: {
    text: 'Mô phỏng Rơi Tự Do',
    fontSize: 24,
    align: 'left',
    bold: true, // Must be boolean as per ITextProps interface
  },
  description: 'Mô phỏng chuyển động rơi tự do trong vật lý lớp 10',
  initialHeight: 100, // meters
  initialVelocity: 0, // m/s
  simulationDuration: 10, // seconds
  timeStep: 0.02, // seconds

  environments: [
    {
      id: 'earth',
      name: 'Trái Đất',
      gravity: 9.8, // m/s^2
      airResistance: false,
      airDensity: 1.225, // kg/m^3
      dragCoefficient: 0.5,
    },
    {
      id: 'moon',
      name: 'Mặt Trăng',
      gravity: 1.625, // m/s^2
      airResistance: false,
      airDensity: 0, // No atmosphere
      dragCoefficient: 0,
    },
    {
      id: 'mars',
      name: 'Sao Hỏa',
      gravity: 3.72, // m/s^2
      airResistance: true,
      airDensity: 0.02, // kg/m^3
      dragCoefficient: 0.5,
    },
    {
      id: 'custom',
      name: 'Tùy chỉnh',
      gravity: 9.8, // m/s^2
      airResistance: true,
      airDensity: 1.225, // kg/m^3
      dragCoefficient: 0.5,
    },
  ],
  activeEnvironmentId: 'earth',

  object: {
    id: 'ball',
    name: 'Bóng',
    mass: 0.5, // kg
    radius: 0.1, // meters
    color: '#FF5733',
  },

  displayOptions: {
    showGrid: true,
    showVelocityVector: true,
    showAccelerationVector: true,
    showForceVectors: false,
    showRuler: true,
    showTimer: true,
    showDataTable: false,
    realTimeGraph: true,
  },

  controls: {
    allowPause: true,
    allowSlowMotion: true,
    allowFastForward: true,
    showResetButton: true,
    showPlayPauseButton: true,
    showStepButton: true,
    showTimeControl: true,
  },

  specialFeatures: {
    enableExport: true,
    showFormulas: true,
  },
};

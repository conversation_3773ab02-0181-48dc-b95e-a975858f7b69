import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { getThemeVariable } from '../../../utils/themeUtils';

interface LoadingScreenProps {
  /** Text to display while loading */
  message?: string;
  /** Whether to display loading in fullscreen */
  fullscreen?: boolean;
  /** Custom icon size (default: 40) */
  iconSize?: number;
  /** Custom icon color (default: #1890ff) */
  iconColor?: string;
  /** Additional CSS class name */
  className?: string;
  /** Additional inline styles */
  style?: React.CSSProperties;
}

/**
 * A loading screen component to display while content is being loaded
 */
const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  fullscreen = false,
  iconSize = 40,
  iconColor,
  className = '',
  style = {},
}) => {
  // Sử dụng biến theme hoặc giá trị mặc định
  const themeIconColor = iconColor || getThemeVariable('primary-color');

  const loadingIcon = (
    <LoadingOutlined
      style={{ fontSize: iconSize, color: themeIconColor }}
      spin
    />
  );

  const containerClass = fullscreen
    ? 'loading-app themed-background'
    : 'tailwind-flex tailwind-justify-center tailwind-items-center tailwind-p-8 tailwind-w-full themed-background';

  return (
    <div className={`${containerClass} ${className}`} style={style}>
      <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-gap-4">
        <Spin indicator={loadingIcon} />
        {message && (
          <div className="tailwind-mt-4 tailwind-text-center themed-text tailwind-text-main">
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingScreen;

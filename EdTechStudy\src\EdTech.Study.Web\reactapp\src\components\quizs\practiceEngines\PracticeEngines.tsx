import React, {
  useState,
  useEffect,
  useLayoutEffect,
  forwardRef,
  Ref,
  useImperativeHandle,
  useContext,
  useMemo,
} from 'react';
import {
  Progress,
  Button,
  Space,
  message,
  Card,
  Typography,
  Empty,
  Tooltip,
} from 'antd';
import {
  SwapOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  SettingOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  MenuOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  mapQuestionComponents,
  PracticeEngineContext,
  Question,
  BaseAnswer,
} from '../../../interfaces/quizs/questionBase';
import './PracticeEngines.css';
import {
  PracticeEnginesRef,
  PracticeEnginesProps,
} from '../../../interfaces/quizs/practiceEngines.interface';
import { QuestionTemplateFactory } from './questionTemplates';
import HashHelper from '../../../utils/HashHelper';
import QuestionListView from './QuestionListView';
import './QuestionListView.css';
import { questionService, shallowEqual } from './questionService';
import practiceLocalization from '../localization';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';

import ModalSelectQuestionType from './ModalSelectQuestionType';
import FireworksComponent from '../quiz/FireworksComponent';

const { Title, Text } = Typography;
const PracticeEngines = forwardRef<PracticeEnginesRef, PracticeEnginesProps>(
  (
    {
      id,
      questions,
      showConfig: externalShowConfig = false,
      showProgressBar = true,
      disableCreateQuestion = false,
      onCreateQuestion,
      onComplete,
      onRefresh,
      onShowMenu,
    },
    ref: Ref<PracticeEnginesRef>
  ) => {
    const { handleChangePosition, handleSaveAllQuestions } = useContext(
      PracticeEngineContext
    );
    const [showFireworks, setShowFireworks] = useState(false);
    const [activeQuestionIndex, setActiveQuestionIndex] = useState(0);
    const [completedQuestions, setCompletedQuestions] = useState<{[key: string]: boolean;}>({}); // prettier-ignore
    const [countAnswers, setCountAnswers] = useState<boolean[]>(Array(questions.length).fill(false)); // prettier-ignore
    const questionPositions = useMemo(() => {
      return questions.map((q) => q.id);
    }, [questions]);

    // Add new state for modal
    const [modalVisible, setModalVisible] = useState(false);

    // Save user answers
    const [userAnswers, setUserAnswers] = useState<{
      [key: string]: {
        complete: boolean;
        userSelect?: BaseAnswer | BaseAnswer[];
        isCorrect?: boolean;
      };
    }>(() => {
      const res = {} as {
        [key: string]: {
          complete: boolean;
        };
      };
      questions.forEach((q) => {
        res[q.id] = {
          complete: false,
        };
      });
      return res;
    });

    // Add state for submission
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [showCompletionScreen, setShowCompletionScreen] = useState(false);
    const [showConfigMode, setShowConfigMode] = useState<boolean>(false);

    // Add state for insert position
    const [insertPosition, setInsertPosition] = useState<number | undefined>(
      undefined
    );

    // Modify handleQuestionComplete to not auto-submit
    const handleQuestionComplete = (
      questionId: string,
      selectedOption?: BaseAnswer | BaseAnswer[]
    ) => {
      let questionIndex = questions.findIndex((q) => q.id === questionId);
      if (questionIndex >= 0) {
        setCountAnswers((prev) => {
          const newAnswers = [...prev];
          newAnswers[questionIndex] = true;
          return newAnswers;
        });
      }

      if (selectedOption) {
        setUserAnswers((prev) => ({
          ...prev,
          [questionId]: {
            complete: true,
            userSelect: selectedOption,
          },
        }));
      } else {
        setUserAnswers((prev) => ({
          ...prev,
          [questionId]: {
            complete: true,
          },
        }));
      }
    };

    // Kiểm tra xem đã hoàn thành tất cả câu hỏi chưa
    const isAllCompleted = () => {
      return countAnswers.every((q) => q);
    };

    // Tính tỷ lệ hoàn thành
    const calculateProgress = () => {
      return Math.round(
        (countAnswers.filter((p) => p).length / questions.length) * 100
      );
    };

    // Tính điểm số
    const calculateScore = () => {
      const correctCount =
        Object.values(completedQuestions).filter(Boolean).length;
      return {
        totalQuestions: questions.length,
        correctQuestions: correctCount,
        score: Math.round((correctCount / questions.length) * 100),
        completedQuestions: Object.fromEntries(
          Object.entries(completedQuestions).map(([id, isCorrect]) => [
            id,
            { complete: true, isCorrect },
          ])
        ),
      };
    };

    // Add submit all questions function
    const handleSubmitAll = () => {
      const results = questions.map((question) => {
        const userAnswer = userAnswers[question.id];
        let isCorrect = false;
        let defaultProps = {
          id: '',
          isCorrect: false,
        };
        if (question.answers) {
          let correctAnswers = question.answers
            .filter((a: { isCorrect: boolean }) => a.isCorrect)
            .map((p) => ({
              ...p,
              ...defaultProps,
            }));
          let selectedAnswers = userAnswer?.userSelect;
          if (Array.isArray(selectedAnswers)) {
            isCorrect = selectedAnswers
              .map((a) => ({
                ...a,
                ...defaultProps,
              }))
              .every(
                (a: BaseAnswer) =>
                  correctAnswers.findIndex((correct) =>
                    shallowEqual(correct, a, ['id', 'isCorrect'])
                  ) >= 0
              );
          } else if (correctAnswers.length == 1 && userAnswer) {
            userAnswer.userSelect = {
              ...userAnswer.userSelect,
              ...defaultProps,
            };
            isCorrect = shallowEqual(userAnswer.userSelect, correctAnswers[0], [
              'id',
              'isCorrect',
            ]);
          }
        }
        return {
          questionId: question.id,
          isCorrect,
        };
      });

      const newCompletedQuestions = results.reduce((acc, result) => {
        acc[result.questionId] = result.isCorrect;
        return acc;
      }, {} as { [key: string]: boolean });

      setCompletedQuestions(newCompletedQuestions);
      setIsSubmitted(true);

      // Show fireworks if all answers are correct
      const allCorrect = results.every((r) => r.isCorrect);
      if (allCorrect) {
        setTimeout(() => {
          setShowFireworks(true);
        }, 200);
      }

      // Update user answers with correctness
      setUserAnswers((prev) => {
        const newAnswers = { ...prev };
        results.forEach((result) => {
          if (newAnswers[result.questionId]) {
            newAnswers[result.questionId] = {
              ...newAnswers[result.questionId],
              isCorrect: result.isCorrect,
            };
          }
        });
        return newAnswers;
      });

      setShowCompletionScreen(true);
      if (onComplete) {
        onComplete(calculateScore());
      }
    };

    // Hàm lưu tất cả câu hỏi
    const handleSaveQuestions = () => {
      try {
        // Chuẩn bị dữ liệu câu hỏi trước khi lưu
        const questionsToSave =
          questionService.prepareQuestionsForSave(questions);
        // Thực hiện lưu các câu hỏi thông qua handleChangeQuestion
        if (handleSaveAllQuestions) {
          // Lưu toàn bộ danh sách câu hỏi
          handleSaveAllQuestions(
            id, // ID của bài tập
            questionsToSave // Danh sách câu hỏi
          );
        } else {
          message.error('Không thể lưu câu hỏi, vui lòng thử lại sau!');
        }
      } catch (error) {
        console.error('Lỗi khi lưu câu hỏi:', error);
        message.error('Đã xảy ra lỗi khi lưu câu hỏi!');
      } finally {
        setShowConfigMode(false);
      }
    };

    // Chuyển đến câu hỏi tiếp theo
    const goToNextQuestion = () => {
      if (activeQuestionIndex < questions.length - 1) {
        setActiveQuestionIndex((prev) => {
          return prev + 1;
        });
      } else if (isAllCompleted()) {
        // Hiển thị màn hình hoàn thành nếu đã làm hết
        setShowCompletionScreen(true);
        if (onComplete) {
          onComplete(calculateScore());
        }
      } else {
        message.info('Vui lòng hoàn thành tất cả các câu hỏi!');
      }
    };

    // Quay lại câu hỏi trước
    const goToPreviousQuestion = () => {
      if (activeQuestionIndex > 0) {
        setActiveQuestionIndex((prev) => {
          return prev - 1;
        });
      }
    };

    // Toggle config mode
    function handleToggleEdit(
      _: React.MouseEvent<HTMLElement, MouseEvent>
    ): void {
      setShowConfigMode((prev) => !prev);
    }

    // Modify restartPractice to reset submission state
    const restartPractice = () => {
      const resetState: { [key: string]: boolean } = {};
      questions.forEach((question) => {
        resetState[question.id] = false;
      });
      setCountAnswers(Array(questions.length).fill(false));
      setCompletedQuestions(resetState);
      setActiveQuestionIndex(0);
      setUserAnswers({});
      setShowCompletionScreen(false);
      setIsSubmitted(false);
    };

    // Render câu hỏi hiện tại
    const renderCurrentQuestion = () => {
      const currentQuestion = questions[activeQuestionIndex];
      if (!currentQuestion) return null;
      if (mapQuestionComponents) {
        const currentQuestionInject = mapQuestionComponents.get(
          currentQuestion.type
        );
        const userSelect = userAnswers[currentQuestion.id]?.userSelect;
        const questionData = {
          ...currentQuestion,
          userSelect: userSelect,
        } as Question;

        const componentId = HashHelper.computeHash([questionData.id]);
        if (currentQuestionInject)
          return (
            <div id={currentQuestion.id}>
              <currentQuestionInject.component
                id={componentId}
                question={questionData}
                onComplete={handleQuestionComplete}
                showFeedback={true}
                configMode={showConfigMode}
              />
            </div>
          );
      }
    };

    // Render màn hình hoàn thành
    const renderCompletionScreen = () => {
      const scoreResult = calculateScore();
      const scorePercent = scoreResult.score;

      let emoji = '😊';
      let message = 'Làm tốt lắm!';

      if (scorePercent === 100) {
        emoji = '🏆';
        message = 'Xuất sắc! Bạn đã hoàn thành tất cả câu hỏi!';
      } else if (scorePercent >= 80) {
        emoji = '🌟';
        message = 'Làm tốt lắm! Chỉ còn một vài câu hỏi!';
      } else if (scorePercent >= 60) {
        emoji = '👍';
        message = 'Khá tốt! Hãy tiếp tục cố gắng!';
      } else {
        emoji = '🙂';
        message = 'Hãy tiếp tục luyện tập nhé!';
      }

      return (
        <Card className="practice-completion-card">
          <div
            className="practice-completion-container"
            style={{ display: 'flex' }}
          >
            <div className="mx-auto">
              <div className="practice-completion-emoji text-center">
                {emoji}
              </div>
              <Title level={2}>Hoàn thành!</Title>
              <Text style={{ margin: '1rem 0', textAlign: 'center' }}>
                {message}
              </Text>

              <div
                className="practice-score-container"
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  alignItems: 'center',
                  margin: '1rem 0 0 0',
                }}
              >
                <Progress
                  type="circle"
                  percent={scorePercent}
                  format={(percent) => `${percent}%`}
                  status={scorePercent >= 60 ? 'success' : 'normal'}
                />
                <div className="practice-score-details">
                  <Text strong>
                    Câu hỏi đúng: {scoreResult.correctQuestions}/
                    {scoreResult.totalQuestions}
                  </Text>
                </div>
              </div>

              <div style={{ display: 'flex' }}>
                <Button
                  type="primary"
                  style={{
                    margin: '1rem 0 0 0',
                    width: '100%',
                  }}
                  className="flex-1 bg-blue-500 hover:bg-blue-600"
                  onClick={restartPractice}
                  icon={<SwapOutlined />}
                >
                  Làm lại bài tập
                </Button>
              </div>
            </div>
          </div>
        </Card>
      );
    };

    // Modify renderNavigationControls to include submit button
    const renderNavigationControls = () => {
      if (showConfigMode) {
        return null;
      }
      const currentProgress = calculateProgress();
      return (
        <div className="practice-navigation">
          {showProgressBar && (
            <div className="practice-progress">
              <Progress
                percent={currentProgress}
                size="small"
                status="active"
                format={(percent) =>
                  `${percent}% (${
                    Object.values(countAnswers).filter(Boolean).length
                  }/${questions.length})`
                }
              />
            </div>
          )}

          {!showConfigMode && (
            <div className="practice-controls">
              <Space>
                <Button
                  onClick={goToPreviousQuestion}
                  disabled={activeQuestionIndex === 0}
                  icon={<CaretLeftOutlined />}
                >
                  Câu trước
                </Button>

                <Button
                  onClick={goToNextQuestion}
                  disabled={activeQuestionIndex === questions.length - 1}
                  icon={<CaretRightOutlined />}
                >
                  Câu tiếp theo
                </Button>

                {!isSubmitted && (
                  <Button
                    type="primary"
                    onClick={handleSubmitAll}
                    className="flex-1 bg-blue-500 hover:bg-blue-600"
                  >
                    Nộp bài
                  </Button>
                )}

                <div className="practice-indicator">
                  Câu hỏi {questions.length > 0 ? activeQuestionIndex + 1 : 0}/
                  {questions.length}
                </div>
              </Space>
            </div>
          )}
        </div>
      );
    };

    const handleAddQuestion = (type: string, position?: number) => {
      const factory = QuestionTemplateFactory.getInstance();
      const newQuestion = factory.create(type);
      if (!newQuestion) {
        return;
      }

      if (onCreateQuestion) {
        onCreateQuestion(newQuestion, position);
      }

      // Reset counts and completion states to include new question
      setCountAnswers((prev) => [...prev, false]);
      setCompletedQuestions((prev) => ({
        ...prev,
        [newQuestion!.id]: false,
      }));
      // Move to the new question position
      setActiveQuestionIndex(
        position !== undefined ? position : questions.length
      );

      // Close modal
      setModalVisible(false);
    };

    useImperativeHandle(
      ref,
      () => ({
        changeMode(value) {
          if (value === 'config') {
            setShowConfigMode(true);
          } else setShowConfigMode(false);
        },
        goToQuestion(id) {
          const gotoQuestionIndex = questionPositions.findIndex(
            (q) => q === id
          );
          setActiveQuestionIndex((prev) =>
            gotoQuestionIndex >= 0 ? gotoQuestionIndex : prev
          );
          const aElem = document.createElement('a');
          aElem.href = `#${id}`;
          document.body.appendChild(aElem);
          aElem.click();
          setTimeout(() => {
            document.body.removeChild(aElem);
          }, 500);
        },
        forceReset() {
          restartPractice();
        },
      }),
      [questionPositions]
    );
    // Hide fireworks after 2 seconds
    useEffect(() => {
      if (showFireworks) {
        const timer = setTimeout(() => {
          setShowFireworks(false);
        }, 2000);
        return () => clearTimeout(timer);
      }
    }, [showFireworks]);

    useLayoutEffect(() => {
      setShowConfigMode(externalShowConfig);
    }, [externalShowConfig]);

    useEffect(() => {
      if (activeQuestionIndex >= 0 && activeQuestionIndex < questions.length) {
        if (questions[activeQuestionIndex]?.id)
          handleChangePosition(questions[activeQuestionIndex]?.id);
      } else {
        setActiveQuestionIndex(0);
      }
    }, [activeQuestionIndex, showConfigMode]);

    return (
      <>
        <div id={id} className="practice-engines-container">
          <div
            style={{
              display: showConfigMode ? 'none' : 'flex',
              justifyContent: 'end',
              marginBottom: '1rem',
              gap: '0.5rem',
            }}
          >
            <Tooltip title="Làm lại">
              <PopconfirmAntdCustom
                title="Bạn có chắc muốn làm lại"
                description="Làm lại bài luyện tập"
                onConfirm={onRefresh || restartPractice}
                okText="Xác nhận"
                cancelText="Hủy"
              >
                <Button type="default" variant="filled" color="default">
                  <ReloadOutlined />
                </Button>
              </PopconfirmAntdCustom>
            </Tooltip>

            <Tooltip title="Menu">
              <Button
                type="default"
                variant="filled"
                color="default"
                onClick={onShowMenu}
              >
                <MenuOutlined />
              </Button>
            </Tooltip>

            {externalShowConfig && questions.length > 0 && (
              <Tooltip title="Chỉnh sửa">
                <Button
                  type="primary"
                  onClick={handleToggleEdit}
                  className="flex-1 bg-blue-500 hover:bg-blue-600"
                  icon={<SettingOutlined />}
                >
                  {practiceLocalization['Settings']}
                </Button>
              </Tooltip>
            )}
          </div>
          {showCompletionScreen && !showConfigMode ? (
            renderCompletionScreen()
          ) : (
            <>
              <div
                className="practice-content"
                style={{
                  overflowY: 'auto',
                }}
              >
                {questions.length > 0 && showConfigMode && (
                  <div className="practice-engines-container">
                    <QuestionListView
                      questions={questions}
                      currentQuestionId={
                        activeQuestionIndex >= 0 &&
                        activeQuestionIndex < questions.length
                          ? questions[activeQuestionIndex]?.id
                          : questions[0]?.id || undefined
                      }
                      showConfigMode={showConfigMode}
                      onSaveQuestions={handleSaveQuestions}
                      onSelectQuestion={(id) => {
                        const index = questions.findIndex((q) => q.id === id);
                        if (index >= 0) {
                          setActiveQuestionIndex(index);
                        }
                      }}
                      onAddQuestion={(position) => {
                        setInsertPosition(position);
                        setModalVisible(true);
                      }}
                      disableCreateQuestion={disableCreateQuestion}
                    />
                  </div>
                )}
                {!showConfigMode &&
                  questions.length > 0 &&
                  renderCurrentQuestion()}
                {questions.length === 0 && (
                  <div className="tailwind-h-full tailwind-flex">
                    <div className="tailwind-text-center tailwind-m-auto">
                      <Empty
                        image={
                          <QuestionCircleOutlined className="tailwind-text-blue-500 tailwind-text-7xl" />
                        }
                        description={null}
                      />

                      <Title
                        level={4}
                        className="tailwind-text-center tailwind-mb-1"
                      >
                        Chưa có câu hỏi
                      </Title>
                      <Text className="tailwind-text-gray-500 tailwind-text-center mb-6">
                        Bắt đầu bằng việc thêm 1 câu hỏi mới
                      </Text>
                      <br />

                      <Space
                        direction="vertical"
                        size="middle"
                        className="tailwind-w-full tailwind-max-w-md tailwind-mt-5"
                      >
                        <Button
                          type="primary"
                          size="large"
                          onClick={() => setModalVisible(true)}
                          icon={<PlusOutlined />}
                          className="tailwind-w-full tailwind-bg-blue-500 tailwind-hover:tailwind-bg-blue-600 tailwind-h-6 tailwind-flex tailwind-items-center tailwind-justify-center"
                        >
                          Thêm câu hỏi
                        </Button>
                      </Space>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        {renderNavigationControls()}
        {/* Fireworks component when answer is correct */}
        {showFireworks && <FireworksComponent show={showFireworks} />}
        {/* Add this at the end of your component */}
        <ModalSelectQuestionType
          visible={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            setInsertPosition(undefined);
          }}
          onSelectType={(type) => handleAddQuestion(type, insertPosition)}
        />
      </>
    );
  }
);

export default PracticeEngines;

import { Model3DComponentProps } from "./type";

/**
 * Default configuration for the Model3DComponent
 * Shared between all versions of the Model3DComponent
 */
export const defaultProps: Model3DComponentProps = {
  // Default title properties
  titleProps: {
    text: 'Thêm mô hình 3D',
    fontSize: 18,
    color: '#000000',
    align: 'left',
    bold: true
  },

  // Model properties
  medias: [],
  mediaUrl: '',
  mediaType: 'upload',
  format: 'glb',

  // Display options
  autoRotate: false,
  rotationSpeed: 1,

  // Container dimensions
  width: '100%',
  height: '600px',

  // Description
  description: '',

  // Display mode
  displayMode: 'horizontal',

  // Instructions content
  instructionsContent: {
    objective: 'Khám phá và tương tác với mô hình 3D để hiểu rõ hơn về cấu trúc và chi tiết của đối tượng.',
    steps: [
      'Sử dụng chuột để xoay mô hình: Nhấn giữ chuột trái và di chuyển để xoay mô hình theo hướng mong muốn.',
      'Phóng to/thu nhỏ: Sử dụng con lăn chuột hoặc thao tác pinch trên màn hình cảm ứng để phóng to hoặc thu nhỏ mô hình.',
      'Di chuyển mô hình: Nhấn giữ chuột phải (hoặc nhấn giữ Shift + chuột trái) và di chuyển để thay đổi vị trí mô hình trong khung nhìn.',
      'Bật/tắt xoay tự động: Sử dụng nút "Xoay tự động" để cho phép mô hình tự động xoay, giúp quan sát toàn diện.',
      'Điều chỉnh tốc độ xoay: Khi bật xoay tự động, bạn có thể điều chỉnh tốc độ xoay để phù hợp với nhu cầu quan sát.',
      'Chuyển đổi giữa các mô hình: Nếu có nhiều mô hình, sử dụng các nút điều hướng hoặc nhấp vào hình thu nhỏ để chuyển đổi giữa các mô hình.'
    ],
    notes: 'Một số mô hình 3D phức tạp có thể mất thời gian để tải. Hiệu suất hiển thị có thể phụ thuộc vào thiết bị và trình duyệt của bạn.'
  }
};
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Dropdown, Button, Radio, Modal } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';

import { IEdTechRenderProps } from '../../../../interfaces/AppComponents';
import { AppFunctions } from '../../../../utils/AppFunctions';
import { updateItems } from '../../../../store/slices/AppSlices/EdTechRenderDataSlice';
import {
  removeParam,
  updateParam,
} from '../../../../store/slices/AppSlices/EdComponentParamsSlice';
import { ED_TECH_COMPONENT } from '../../../../constants/edTechComponents';
import { validateParams } from '../../../../utils/validation/validateSchema';

interface IVersionSelectorButtonProps {
  nodeTree: IEdTechRenderProps;
}

interface ConversionResult {
  success: boolean;
  convertedData?: any;
}

/**
 * Component that allows users to select different versions of an EdTech component
 */
const VersionSelectorButton: React.FC<IVersionSelectorButtonProps> = ({
  nodeTree,
}) => {
  const dispatch = useDispatch();

  // State management
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(nodeTree.version);
  const [pendingVersion, setPendingVersion] = useState<string | null>(null);
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [isDeleteSettingsModalVisible, setIsDeleteSettingsModalVisible] =
    useState(false);

  // Get component versions
  const versions = AppFunctions.getComponentVersions({ name: nodeTree.name });

  // If there are no multiple versions available, don't render the component
  if (!versions || versions.length <= 1) return null;

  /**
   * Checks if changing to a new version is possible without data loss
   */
  const isVersionChangeValid = (newVersion: string): boolean => {
    const version = AppFunctions.getComponentVersions({
      name: nodeTree.name,
      version: newVersion,
    });

    const newSchema = version?.[0]?.schema;
    if (!newSchema) return true;

    try {
      newSchema.parse(nodeTree);
      return true;
    } catch (error) {
      console.log('Version change validation error:', error);
      return false;
    }
  };

  /**
   * Attempts to convert data between versions
   */
  const convertToNewVersion = (newVersion: string): ConversionResult => {
    const component = ED_TECH_COMPONENT.find((x) => x.name === nodeTree.name);
    const version = component?.components?.find(
      (x) => x.version === newVersion
    );

    const newSchema = version?.schema;
    if (!newSchema) return { success: true };

    try {
      let convertedData = nodeTree.params;

      // Apply conversion function if available
      if (component?.convertVersion) {
        convertedData = component.convertVersion(
          nodeTree.version,
          newVersion,
          convertedData
        );
      }

      // Validate converted params against new schema
      convertedData = validateParams(newSchema, convertedData).params as any;

      return {
        success: true,
        convertedData,
      };
    } catch (error) {
      console.log('Conversion failed:', error);
      return { success: false };
    }
  };

  /**
   * Applies the version change by dispatching Redux actions
   */
  const applyVersionChange = (version: string, params?: any) => {
    setSelectedVersion(version);

    // Update version
    dispatch(
      updateItems({
        items: [{ path: nodeTree.path, data: { version } }],
      })
    );

    // Update params if provided
    if (params) {
      dispatch(
        updateParam({
          id: nodeTree.id,
          params,
        })
      );
    }

    // Close the dropdown
    setDropdownOpen(false);
  };

  /**
   * Handles the version selection change
   */
  const handleVersionChange = (e: RadioChangeEvent) => {
    const newVersion = e.target.value;

    // Check if version change requires confirmation
    if (!isVersionChangeValid(newVersion)) {
      setPendingVersion(newVersion);
      setIsConfirmModalVisible(true);
      // Keep dropdown open
      setDropdownOpen(true);
    } else {
      // Direct change is possible
      applyVersionChange(newVersion);
    }
  };

  /**
   * Confirms version change with potential data conversion
   */
  const handleConfirmChange = () => {
    if (!pendingVersion) return;

    const conversionResult = convertToNewVersion(pendingVersion);

    if (conversionResult.success) {
      // Apply version change with converted data
      applyVersionChange(pendingVersion, conversionResult.convertedData);
      resetModals();
    } else {
      // Show delete settings modal if conversion failed
      setIsConfirmModalVisible(false);
      setIsDeleteSettingsModalVisible(true);
    }
  };

  /**
   * Resets all modals and pending states
   */
  const resetModals = () => {
    setPendingVersion(null);
    setIsConfirmModalVisible(false);
    setIsDeleteSettingsModalVisible(false);
    setDropdownOpen(false);
  };

  /**
   * Handles deleting settings and applying version change
   */
  const handleDeleteSettings = () => {
    if (!pendingVersion) return;

    // Apply version change with default settings
    setSelectedVersion(pendingVersion);
    dispatch(removeParam({ id: nodeTree.id }));
    dispatch(
      updateItems({
        items: [{ path: nodeTree.path, data: { version: pendingVersion } }],
      })
    );
    resetModals();
  };

  return (
    <>
      <Dropdown
        open={dropdownOpen}
        onOpenChange={setDropdownOpen}
        placement="bottomRight"
        dropdownRender={() => (
          <div className="tailwind-rounded-lg tailwind-shadow-md tailwind-px-4 tailwind-w-60 tailwind-max-h-72 tailwind-overflow-y-auto">
            <Radio.Group
              onChange={handleVersionChange}
              value={selectedVersion}
              className="tailwind-w-full"
            >
              {versions.map((version) => (
                <div key={version.version} className="tailwind-my-3">
                  <Radio
                    value={version.version}
                    className="tailwind-w-full tailwind-flex tailwind-items-start"
                  >
                    <div className="tailwind-pl-2">
                      <div className="tailwind-text-sm tailwind-font-medium">
                        {version.version}
                      </div>
                      <div className="tailwind-text-xs tailwind-mt-1">
                        {version.description}
                      </div>
                    </div>
                  </Radio>
                </div>
              ))}
            </Radio.Group>
          </div>
        )}
      >
        <Button
          type="primary"
          className="tailwind-flex tailwind-items-center tailwind-px-3 tailwind-py-1 tailwind-h-[25px]"
        >
          <span>Phiên bản: {nodeTree.version}</span>
          <span className="tailwind-ml-2 tailwind-text-xs">▼</span>
        </Button>
      </Dropdown>

      {/* Confirmation Modal for Version Change */}
      <ConfirmationModal
        title="Thay đổi phiên bản"
        open={isConfirmModalVisible}
        onCancel={() => setIsConfirmModalVisible(false)}
        onConfirm={handleConfirmChange}
        iconColor="tailwind-text-yellow-400"
        content={
          <p className="tailwind-mb-4">
            Đổi từ phiên bản {nodeTree.version} sang {pendingVersion} sẽ làm
            thay đổi một số cài đặt có sẵn của bạn. Bạn có chắc chắn muốn đổi
            không?
          </p>
        }
        confirmText="Thay đổi"
        confirmButtonClass="tailwind-bg-blue-500"
      />

      {/* Delete Settings Confirmation Modal */}
      <ConfirmationModal
        title="Xóa cài đặt cũ"
        open={isDeleteSettingsModalVisible}
        onCancel={() => setIsDeleteSettingsModalVisible(false)}
        onConfirm={handleDeleteSettings}
        iconColor="tailwind-text-red-500"
        content={
          <p className="tailwind-mb-4">
            Không thể chuyển đổi cài đặt từ phiên bản {nodeTree.version} sang{' '}
            {pendingVersion}. Bạn có muốn xóa các cài đặt cũ và sử dụng cài đặt
            mặc định cho phiên bản mới không?
          </p>
        }
        confirmText="Xóa cài đặt"
        confirmDanger={true}
      />
    </>
  );
};

// Helper component for modals
interface ConfirmationModalProps {
  title: string;
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  iconColor: string;
  content: React.ReactNode;
  confirmText: string;
  confirmButtonClass?: string;
  confirmDanger?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  title,
  open,
  onCancel,
  onConfirm,
  iconColor,
  content,
  confirmText,
  confirmButtonClass,
  confirmDanger = false,
}) => (
  <Modal
    title={
      <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
        <ExclamationCircleFilled className={`${iconColor} tailwind-text-xl`} />
        <span className="tailwind-font-medium">{title}</span>
      </div>
    }
    open={open}
    onCancel={onCancel}
    centered
    footer={[
      <Button key="cancel" onClick={onCancel} className="tailwind-mr-2">
        Huỷ
      </Button>,
      <Button
        key="confirm"
        type="primary"
        danger={confirmDanger}
        onClick={onConfirm}
        className={confirmButtonClass}
      >
        {confirmText}
      </Button>,
    ]}
    zIndex={1051}
    maskClosable={false}
  >
    <div className="tailwind-py-2">{content}</div>
  </Modal>
);

export default VersionSelectorButton;

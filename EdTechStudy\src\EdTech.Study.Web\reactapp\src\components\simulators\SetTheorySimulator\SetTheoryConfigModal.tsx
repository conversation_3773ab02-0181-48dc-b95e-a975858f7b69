import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Tabs,
  Button,
  message,
  Divider,
  Row,
  Col,
  Space,
  ColorPicker,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { SetTheoryConfig, defaultSetTheoryConfig } from './SetTheoryConfig';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { DeleteIcon } from '../../icons/IconIndex';

interface SetTheoryConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: Partial<SetTheoryConfig>) => void;
  defaultConfig?: Partial<SetTheoryConfig>;
}

const SetTheoryConfigModal: React.FC<SetTheoryConfigModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig,
}) => {
  const [form] = Form.useForm();

  // Khởi tạo config state từ defaultConfig, đảm bảo tất cả các trường đều tồn tại
  const initialConfig = {
    ...defaultSetTheoryConfig,
    ...(defaultConfig || {}),
    elementSets: {
      ...defaultSetTheoryConfig.elementSets,
      ...(defaultConfig?.elementSets || {}),
    },
    options: {
      ...defaultSetTheoryConfig.options,
      ...(defaultConfig?.options || {}),
    },
    theme: {
      ...defaultSetTheoryConfig.theme,
      ...(defaultConfig?.theme || {}),
    },
  };

  const [config, setConfig] = useState<SetTheoryConfig>({ ...initialConfig });

  // State for custom set types
  const [newSetTypeName, setNewSetTypeName] = useState<string>('');
  const [newSetTypeElements, setNewSetTypeElements] = useState<string>('');

  // Khi modal mở, reset form với config đã được chuẩn hóa
  useEffect(() => {
    if (isOpen) {
      form.setFieldsValue(config);
      setNewSetTypeName('');
      setNewSetTypeElements('');
    }
  }, [isOpen, form, config]);

  // Xử lý thêm loại tập hợp mới
  const handleAddNewSetType = () => {
    if (!newSetTypeName.trim()) {
      message.error('Vui lòng nhập tên loại tập hợp!');
      return;
    }

    const setKey = newSetTypeName.trim().toLowerCase().replace(/\s+/g, '_');

    // Kiểm tra nếu tên đã tồn tại
    if (config.elementSets[setKey]) {
      message.error('Tên loại tập hợp đã tồn tại, vui lòng chọn tên khác!');
      return;
    }

    // Xử lý các phần tử
    const elementValues = newSetTypeElements
      .split(',')
      .map((v) => v.trim())
      .filter(Boolean);

    if (elementValues.length === 0) {
      message.error('Vui lòng nhập ít nhất một phần tử!');
      return;
    }

    const elements = elementValues.map((value, index) => ({
      id: `${setKey}-${index + 1}`,
      value,
      type: 'custom' as const,
    }));

    // Cập nhật config và reset các trường
    setConfig((prevConfig) => ({
      ...prevConfig,
      elementSets: {
        ...prevConfig.elementSets,
        [setKey]: elements,
      },
    }));

    setNewSetTypeName('');
    setNewSetTypeElements('');
    message.success(`Đã thêm loại tập hợp "${newSetTypeName}" thành công!`);
  };

  // Xử lý thêm phần tử vào loại tập hợp tùy chỉnh
  const handleAddElementToCustomSet = (setType: string) => {
    let tempElement = '';

    Modal.confirm({
      title: `Thêm phần tử vào ${
        setType.charAt(0).toUpperCase() + setType.slice(1).replace(/_/g, ' ')
      }`,
      content: (
        <Input
          placeholder="Nhập giá trị phần tử mới"
          defaultValue=""
          onChange={(e) => {
            tempElement = e.target.value;
          }}
        />
      ),
      onOk: () => {
        if (!tempElement.trim()) {
          message.error('Giá trị phần tử không được để trống!');
          return Promise.reject();
        }

        // Tạo phần tử mới
        const newElement = {
          id: `${setType}-${Date.now()}`,
          value: tempElement.trim(),
          type: 'custom' as const,
        };

        // Cập nhật danh sách phần tử
        const updatedElements = [
          ...(config.elementSets[setType] || []),
          newElement,
        ];

        setConfig((prevConfig) => ({
          ...prevConfig,
          elementSets: {
            ...prevConfig.elementSets,
            [setType]: updatedElements,
          },
        }));

        message.success(`Đã thêm phần tử "${tempElement}" thành công!`);
        return Promise.resolve();
      },
    });
  };

  // Xử lý xóa phần tử khỏi loại tập hợp tùy chỉnh
  const handleRemoveElementFromCustomSet = (
    setType: string,
    elementId: string
  ) => {
    // Lấy danh sách phần tử hiện tại
    const currentElements = config.elementSets[setType] || [];

    // Đảm bảo có ít nhất một phần tử
    if (currentElements.length <= 1) {
      message.warning('Phải có ít nhất một phần tử trong tập hợp!');
      return;
    }

    // Lọc phần tử cần xóa
    const updatedElements = currentElements.filter((el) => el.id !== elementId);

    // Cập nhật config
    setConfig((prevConfig) => ({
      ...prevConfig,
      elementSets: {
        ...prevConfig.elementSets,
        [setType]: updatedElements,
      },
    }));
  };

  // Xử lý xóa loại tập hợp tùy chỉnh
  const handleRemoveCustomSetType = (setType: string) => {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: `Bạn có chắc chắn muốn xóa loại tập hợp "${
        setType.charAt(0).toUpperCase() + setType.slice(1).replace(/_/g, ' ')
      }" không?`,
      onOk: () => {
        // Xóa loại tập hợp
        const { [setType]: _, ...restElementSets } = config.elementSets;

        setConfig((prevConfig) => ({
          ...prevConfig,
          elementSets: restElementSets,
        }));

        message.success('Đã xóa loại tập hợp thành công!');
      },
    });
  };

  // Xử lý khi submit form
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        // Xử lý các giá trị, đặc biệt là các đối tượng màu sắc
        const processedValues = { ...values };

        // Xử lý màu sắc
        if (processedValues.theme) {
          if (
            processedValues.theme.setAColor &&
            typeof processedValues.theme.setAColor === 'object'
          ) {
            processedValues.theme.setAColor = processedValues.theme.setAColor
              .toHexString
              ? processedValues.theme.setAColor.toHexString()
              : '#1890ff';
          }

          if (
            processedValues.theme.setBColor &&
            typeof processedValues.theme.setBColor === 'object'
          ) {
            processedValues.theme.setBColor = processedValues.theme.setBColor
              .toHexString
              ? processedValues.theme.setBColor.toHexString()
              : '#52c41a';
          }

          if (
            processedValues.theme.elementColor &&
            typeof processedValues.theme.elementColor === 'object'
          ) {
            processedValues.theme.elementColor = processedValues.theme
              .elementColor.toHexString
              ? processedValues.theme.elementColor.toHexString()
              : '#f5f5f5';
          }

          if (
            processedValues.theme.highlightColor &&
            typeof processedValues.theme.highlightColor === 'object'
          ) {
            processedValues.theme.highlightColor = processedValues.theme
              .highlightColor.toHexString
              ? processedValues.theme.highlightColor.toHexString()
              : '#faad14';
          }
        }

        // Đảm bảo elementSets tồn tại và thêm các tập hợp tùy chỉnh
        if (!processedValues.elementSets) {
          processedValues.elementSets = { ...config.elementSets };
        }

        // Gửi kết quả về
        onSubmit(processedValues);
        message.success('Cấu hình đã được lưu thành công');
        onClose();
      })
      .catch(() => {
        message.error('Vui lòng kiểm tra lại thông tin cấu hình');
      });
  };

  // Hàm hỗ trợ deep merge để cập nhật config khi form thay đổi
  const deepMerge = (prevObj: any, newValues: any) => {
    const result = { ...prevObj };

    Object.keys(newValues).forEach((key) => {
      if (Array.isArray(newValues[key])) {
        result[key] = newValues[key];
      } else if (
        typeof newValues[key] === 'object' &&
        newValues[key] !== null &&
        typeof result[key] === 'object' &&
        result[key] !== null
      ) {
        result[key] = deepMerge(result[key], newValues[key]);
      } else {
        result[key] = newValues[key];
      }
    });

    return result;
  };

  // Xóa phần tử
  const handleRemoveElement = (
    elementType: 'numbers' | 'letters' | 'custom',
    elementId: string
  ) => {
    // Lấy danh sách hiện tại và kiểm tra
    const currentElementSets = {
      ...(config.elementSets || defaultSetTheoryConfig.elementSets),
    };
    const currentTypeElements = [...(currentElementSets[elementType] || [])];

    // Đảm bảo có ít nhất một phần tử
    if (currentTypeElements.length <= 1) {
      message.warning('Phải có ít nhất một phần tử');
      return;
    }

    // Lọc phần tử cần xóa
    const updatedElements = currentTypeElements.filter(
      (el) => el.id !== elementId
    );

    // Cập nhật config và form
    const updatedElementSets = {
      ...currentElementSets,
      [elementType]: updatedElements,
    };

    const updatedConfig = {
      ...config,
      elementSets: updatedElementSets,
    };

    setConfig(updatedConfig);
    form.setFieldsValue({
      elementSets: updatedElementSets,
    });
  };

  // Cấu hình tabs cho form
  const tabItems = [
    // Bỏ qua các tab khác để rút gọn
    {
      key: '1',
      label: 'Thông tin cơ bản',
      children: (
        <>
          {/* Title configuration removed as it's now handled by EngineContainer */}

          <Form.Item name="description" label="Mô tả">
            <Input.TextArea rows={3} placeholder="Mô tả chi tiết về mô phỏng" />
          </Form.Item>

          <Form.Item
            name={['options', 'maxElementsPerSet']}
            label="Số lượng phần tử tối đa"
            rules={[{ required: true, message: 'Vui lòng nhập số lượng' }]}
          >
            <InputNumber min={1} max={20} style={{ width: '100%' }} />
          </Form.Item>
        </>
      ),
    },
    {
      key: '2',
      label: 'Phần tử',
      children: (
        <>
          <Tabs defaultActiveKey="numbers">
            <Tabs.TabPane tab="Số" key="numbers">
              <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-4">
                <h3 className="tailwind-text-base tailwind-font-medium">
                  Phần tử số
                </h3>
              </div>

              <div className="tailwind-grid tailwind-grid-cols-2 md:tailwind-grid-cols-4 tailwind-gap-2">
                {(config.elementSets?.numbers || []).map((element) => (
                  <div
                    key={element.id}
                    className="tailwind-flex tailwind-items-center tailwind-bg-gray-50 tailwind-p-2 tailwind-rounded tailwind-border"
                  >
                    <span className="tailwind-flex-grow">{element.value}</span>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteIcon />}
                      onClick={() => handleRemoveElement('numbers', element.id)}
                      size="small"
                    />
                  </div>
                ))}
              </div>
            </Tabs.TabPane>

            <Tabs.TabPane tab="Chữ cái" key="letters">
              <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-4">
                <h3 className="tailwind-text-base tailwind-font-medium">
                  Phần tử chữ cái
                </h3>
              </div>

              <div className="tailwind-grid tailwind-grid-cols-2 md:tailwind-grid-cols-4 tailwind-gap-2">
                {(config.elementSets?.letters || []).map((element) => (
                  <div
                    key={element.id}
                    className="tailwind-flex tailwind-items-center tailwind-bg-gray-50 tailwind-p-2 tailwind-rounded tailwind-border"
                  >
                    <span className="tailwind-flex-grow">{element.value}</span>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteIcon />}
                      onClick={() => handleRemoveElement('letters', element.id)}
                      size="small"
                    />
                  </div>
                ))}
              </div>
            </Tabs.TabPane>

            {/* Tùy chỉnh loại tập hợp mới */}
            {Object.keys(config.elementSets || {})
              .filter((key) => !['numbers', 'letters', 'custom'].includes(key))
              .map((customSetType) => (
                <Tabs.TabPane
                  tab={
                    customSetType.charAt(0).toUpperCase() +
                    customSetType.slice(1).replace(/_/g, ' ')
                  }
                  key={customSetType}
                >
                  <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-4">
                    <h3 className="tailwind-text-base tailwind-font-medium">
                      Phần tử{' '}
                      {customSetType.charAt(0).toUpperCase() +
                        customSetType.slice(1).replace(/_/g, ' ')}
                    </h3>
                    <Space>
                      <Button
                        icon={<PlusOutlined />}
                        onClick={() =>
                          handleAddElementToCustomSet(customSetType)
                        }
                      >
                        Thêm phần tử
                      </Button>
                      <Button
                        danger
                        icon={<DeleteIcon />}
                        onClick={() => handleRemoveCustomSetType(customSetType)}
                      >
                        Xóa loại tập hợp
                      </Button>
                    </Space>
                  </div>

                  <div className="tailwind-grid tailwind-grid-cols-2 md:tailwind-grid-cols-4 tailwind-gap-2">
                    {(config.elementSets?.[customSetType] || []).map(
                      (element) => (
                        <div
                          key={element.id}
                          className="tailwind-flex tailwind-items-center tailwind-bg-gray-50 tailwind-p-2 tailwind-rounded tailwind-border"
                        >
                          <span className="tailwind-flex-grow">
                            {element.value}
                          </span>
                          <Button
                            type="text"
                            danger
                            icon={<DeleteIcon />}
                            onClick={() =>
                              handleRemoveElementFromCustomSet(
                                customSetType,
                                element.id
                              )
                            }
                            size="small"
                          />
                        </div>
                      )
                    )}
                  </div>
                </Tabs.TabPane>
              ))}

            {/* Tab thêm loại tập hợp mới */}
            <Tabs.TabPane tab="+ Thêm loại mới" key="add_new_set_type">
              <div className="tailwind-p-4 tailwind-bg-gray-50 tailwind-rounded">
                <h3 className="tailwind-text-base tailwind-font-medium tailwind-mb-4">
                  Thêm loại tập hợp mới
                </h3>

                <Form.Item
                  label="Tên loại tập hợp"
                  extra="Ví dụ: Quốc gia, Màu sắc, Động vật"
                >
                  <Input
                    placeholder="Nhập tên loại tập hợp mới"
                    value={newSetTypeName}
                    onChange={(e) => setNewSetTypeName(e.target.value)}
                  />
                </Form.Item>

                <Form.Item
                  label="Các phần tử (cách nhau bởi dấu phẩy)"
                  extra="Ví dụ: Đỏ, Xanh, Vàng, Tím, Cam"
                >
                  <Input.TextArea
                    placeholder="Nhập các phần tử, cách nhau bởi dấu phẩy"
                    rows={4}
                    value={newSetTypeElements}
                    onChange={(e) => setNewSetTypeElements(e.target.value)}
                  />
                </Form.Item>

                <Button
                  type="primary"
                  onClick={handleAddNewSetType}
                  disabled={!newSetTypeName.trim()}
                >
                  Thêm loại tập hợp
                </Button>
              </div>
            </Tabs.TabPane>
          </Tabs>
        </>
      ),
    },
    {
      key: '3',
      label: 'Tùy chọn hiển thị',
      children: (
        <>
          <Divider orientation="left">Tùy chọn chung</Divider>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['options', 'showMembershipTesting']}
                label="Hiển thị kiểm tra thuộc tính"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['options', 'showExamples']}
                label="Hiển thị ví dụ"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['options', 'showSetNotation']}
                label="Hiển thị công thức tập hợp"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['options', 'allowDragDrop']}
                label="Cho phép kéo thả"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">Màu sắc</Divider>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name={['theme', 'setAColor']} label="Màu tập hợp A">
                <ColorPicker showText />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['theme', 'setBColor']} label="Màu tập hợp B">
                <ColorPicker showText />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['theme', 'elementColor']} label="Màu phần tử">
                <ColorPicker showText />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <Form.Item
                name={['theme', 'highlightColor']}
                label="Màu đánh dấu"
              >
                <ColorPicker showText />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <ModalAntdCustom
      title="Cấu hình mô phỏng tập hợp"
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Hủy
        </Button>,
        <Button key="submit" onClick={handleSubmit}>
          Áp dụng
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={config}
        onValuesChange={(changedValues) => {
          setConfig((prevConfig) => deepMerge(prevConfig, changedValues));
        }}
      >
        <Tabs defaultActiveKey="1" items={tabItems} />
      </Form>
    </ModalAntdCustom>
  );
};

export default SetTheoryConfigModal;

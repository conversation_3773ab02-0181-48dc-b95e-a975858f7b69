import React from 'react';
import { Alert, Typography } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
  Question,
  QuestionAnswer,
  OrderingQuestion,
  MatchingQuestion,
} from './QuestionBankConfig';

const { Text } = Typography;

interface QuestionResultComponentProps {
  question: Question;
  userAnswer: QuestionAnswer;
  isCorrect: boolean;
  showExplanation?: boolean;
}

const QuestionResultComponent: React.FC<QuestionResultComponentProps> = ({
  question,
  userAnswer,
  isCorrect,
  showExplanation = true,
}) => {
  // Render content based on question type
  let userAnswerDisplay = '';
  let correctAnswerDisplay = '';

  if (userAnswer !== null) {
    switch (question.type) {
      case 'truefalse':
        userAnswerDisplay = userAnswer === true ? 'Đúng' : 'Sai';
        correctAnswerDisplay = question.isCorrect ? 'Đúng' : 'Sai';
        break;
      case 'multiplechoice':
        if (typeof userAnswer === 'number') {
          userAnswerDisplay = question.options[userAnswer] || '';
          correctAnswerDisplay =
            question.options[question.correctOptionIndex] || '';
        }
        break;
      case 'fillin':
        if (typeof userAnswer === 'string') {
          userAnswerDisplay = userAnswer;
          correctAnswerDisplay = question.correctAnswer;
        }
        break;
      case 'ordering':
        if (Array.isArray(userAnswer)) {
          const orderingQuestion = question as OrderingQuestion;

          // Check if userAnswer contains indices (numbers) or the actual items (strings)
          if (userAnswer.length > 0 && typeof userAnswer[0] === 'number') {
            // Handle indices
            const userAnswerArray = userAnswer as number[];
            userAnswerDisplay = userAnswerArray
              .map((index) => orderingQuestion.items[index])
              .join(' → ');
          } else if (
            userAnswer.length > 0 &&
            typeof userAnswer[0] === 'string'
          ) {
            // Handle items directly
            userAnswerDisplay = (userAnswer as unknown as string[]).join(' → ');
          }

          // Always display the correct order
          correctAnswerDisplay = orderingQuestion.correctOrder
            .map((index) => orderingQuestion.items[index])
            .join(' → ');
        }
        break;
      case 'matching':
        if (
          Array.isArray(userAnswer) &&
          userAnswer.length > 0 &&
          Array.isArray(userAnswer[0] as unknown[])
        ) {
          const matchingQuestion = question as MatchingQuestion;
          const userAnswerArray = userAnswer as Array<[number, number]>;

          try {
            // Generate user answer display string
            userAnswerDisplay = userAnswerArray
              .map((pair) => {
                // Check if indices are valid
                if (
                  pair[0] < 0 ||
                  pair[0] >= matchingQuestion.leftItems.length ||
                  pair[1] < 0 ||
                  pair[1] >= matchingQuestion.rightItems.length
                ) {
                  return `[Không hợp lệ]`;
                }
                return `${matchingQuestion.leftItems[pair[0]]} → ${
                  matchingQuestion.rightItems[pair[1]]
                }`;
              })
              .join(', ');

            // Generate correct answer display string
            correctAnswerDisplay = matchingQuestion.correctLeftToRight
              .map((pair) => {
                // Check if indices are valid
                if (
                  pair[0] < 0 ||
                  pair[0] >= matchingQuestion.leftItems.length ||
                  pair[1] < 0 ||
                  pair[1] >= matchingQuestion.rightItems.length
                ) {
                  return `[Không hợp lệ]`;
                }
                return `${matchingQuestion.leftItems[pair[0]]} → ${
                  matchingQuestion.rightItems[pair[1]]
                }`;
              })
              .join(', ');
          } catch (error) {
            // Fallback if there's an error
            userAnswerDisplay = 'Không thể hiển thị kết quả';
            correctAnswerDisplay = 'Không thể hiển thị kết quả';
            console.error('Error displaying matching question result:', error);
          }
        } else {
          userAnswerDisplay = 'Không có đáp án';
          const matchingQuestion = question as MatchingQuestion;
          correctAnswerDisplay = matchingQuestion.correctLeftToRight
            .map(
              (pair: number[]) =>
                `${matchingQuestion.leftItems[pair[0]]} → ${
                  matchingQuestion.rightItems[pair[1]]
                }`
            )
            .join(', ');
        }
        break;
    }
  }

  return (
    <div>
      <Alert
        type={isCorrect ? 'success' : 'error'}
        showIcon
        message={isCorrect ? 'Chính xác!' : 'Chưa chính xác!'}
        description={
          <div className="tailwind-text-sm">
            <div className="tailwind-mb-1">
              <Text strong className="tailwind-text-gray-700">
                Câu trả lời của bạn:
              </Text>{' '}
              <Text type={isCorrect ? 'success' : 'danger'}>
                {userAnswerDisplay}
              </Text>
            </div>
            {!isCorrect && (
              <div className="tailwind-mb-1">
                <Text strong className="tailwind-text-gray-700">
                  Đáp án đúng:
                </Text>{' '}
                <Text type="success">{correctAnswerDisplay}</Text>
              </div>
            )}
          </div>
        }
        className="tailwind-mb-4"
      />

      {/* Show explanation if available */}
      {showExplanation &&
        question.explanation &&
        question.explanation.trim() && (
          <Alert
            type="info"
            message="Giải thích"
            description={question.explanation}
            icon={<InfoCircleOutlined />}
            className="tailwind-mt-4"
          />
        )}
    </div>
  );
};

export default QuestionResultComponent;

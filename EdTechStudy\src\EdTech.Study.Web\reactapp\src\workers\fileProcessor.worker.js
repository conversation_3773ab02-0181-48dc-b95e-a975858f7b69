// workers/fileProcessor.worker.js
// Base64 processing worker for handling large file conversions without blocking UI
// ✅ Pure JavaScript version to avoid module compilation issues

'use strict';

// Remove any exports or module-related code at the top of the file
// The error is likely coming from webpack adding module code

// Worker event listener
self.addEventListener('message', async function (event) {
  const { id, type, base64Data, mimeType, options = {} } = event.data;

  try {
    switch (type) {
      case 'BASE64_TO_UINT8ARRAY':
        if (base64Data && mimeType) {
          await processBase64ToUint8Array(id, base64Data, mimeType, options);
        } else {
          throw new Error('Missing base64Data or mimeType');
        }
        break;

      case 'VALIDATE_BASE64':
        if (base64Data) {
          validateBase64(id, base64Data);
        } else {
          throw new Error('Missing base64Data for validation');
        }
        break;

      case 'CLEANUP':
        // Perform any cleanup if needed
        postSuccessMessage(id, { cleanupCompleted: true });
        break;

      default:
        throw new Error('Unknown message type: ' + type);
    }
  } catch (error) {
    postErrorMessage(
      id,
      error instanceof Error ? error.message : 'Unknown error occurred'
    );
  }
});

/**
 * Process base64 string to Uint8Array in chunks to prevent blocking
 */
async function processBase64ToUint8Array(id, base64Data, mimeType, options) {
  try {
    // Clean up base64 string
    let base64String = cleanBase64String(base64Data);

    // ✅ Add debugging info for base64 processing
    console.log('🔍 Base64 Debug Info:', {
      originalLength: base64Data.length,
      cleanedLength: base64String.length,
      startsWithDataUrl: base64Data.startsWith('data:'),
      containsComma: base64Data.includes(','),
      firstChars: base64String.substring(0, 50),
      lastChars: base64String.substring(Math.max(0, base64String.length - 50)),
    });

    // Validate base64 string
    if (!isValidBase64(base64String)) {
      throw new Error(
        'Invalid base64 string format. Length: ' +
          base64String.length +
          ', Valid chars: ' +
          /^[A-Za-z0-9+/]*={0,2}$/.test(base64String) +
          ', Length divisible by 4: ' +
          (base64String.length % 4 === 0)
      );
    }

    // Check file size limits
    const estimatedSize = Math.floor((base64String.length * 3) / 4);
    const maxSize = options.maxFileSize || 100 * 1024 * 1024; // 100MB default

    if (estimatedSize > maxSize) {
      throw new Error(
        'File too large: ' +
          estimatedSize +
          ' bytes (max: ' +
          maxSize +
          ' bytes)'
      );
    }

    postProgressMessage(id, 0, 'base64_validation_complete');

    // ✅ For small files, process directly without chunking to avoid padding issues
    if (base64String.length < 1024 * 1024) {
      // Less than 1MB base64 (roughly 750KB actual data)
      const binaryString = atob(base64String);
      const finalBytes = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        finalBytes[i] = binaryString.charCodeAt(i);
      }

      postProgressMessage(id, 100, 'conversion_complete');

      const response = {
        id: id,
        type: 'SUCCESS',
        result: {
          uint8Array: finalBytes,
          mimeType: mimeType,
          originalSize: base64Data.length,
          processedSize: finalBytes.length,
        },
      };

      // ✅ Use transferable objects for better performance with fallback
      try {
        self.postMessage(response, [finalBytes.buffer]);
      } catch (transferError) {
        self.postMessage(response);
      }
      return;
    }

    // ✅ For large files, use improved chunking strategy
    if (base64String.length > 50 * 1024 * 1024) {
      // Only chunk if larger than 50MB base64
      // Use simpler chunking approach - decode entire sections at boundaries
      const maxChunkSize = 4 * 1024 * 1024; // 4MB base64 chunks
      const alignedChunkSize = Math.floor(maxChunkSize / 4) * 4; // Ensure multiple of 4

      const uint8Chunks = [];
      let totalProcessedBytes = 0;
      let position = 0;

      while (position < base64String.length) {
        const remainingLength = base64String.length - position;
        const chunkSize = Math.min(alignedChunkSize, remainingLength);

        // Get chunk - always aligned to 4-byte boundaries except for last chunk
        let base64Chunk = base64String.slice(position, position + chunkSize);

        // For last chunk, pad if necessary
        if (position + chunkSize >= base64String.length) {
          base64Chunk = padBase64String(base64Chunk);
        }

        // Skip empty chunks
        if (base64Chunk.length === 0) {
          break;
        }

        try {
          // ✅ Validate chunk before decoding
          if (!validateBase64Chunk(base64Chunk)) {
            console.warn(
              '⚠️ Invalid base64 chunk at position ' +
                position +
                ', length: ' +
                base64Chunk.length
            );
            throw new Error(
              'Invalid base64 chunk format at position ' + position
            );
          }

          // Decode chunk to binary
          const binaryString = atob(base64Chunk);
          const bytes = new Uint8Array(binaryString.length);

          // Convert binary string to bytes
          for (let j = 0; j < binaryString.length; j++) {
            bytes[j] = binaryString.charCodeAt(j);
          }

          uint8Chunks.push(bytes);
          totalProcessedBytes += bytes.length;
          position += chunkSize;

          // Report progress
          const progress = (position / base64String.length) * 95; // Reserve 5% for combining
          postProgressMessage(id, progress, 'base64_decoding', {
            processedBytes: totalProcessedBytes,
            totalBytes: estimatedSize,
            position: position,
            totalLength: base64String.length,
          });

          // Yield control to prevent blocking
          if (position % (10 * alignedChunkSize) === 0) {
            await yieldControl();
          }
        } catch (chunkError) {
          // ✅ Enhanced error reporting with chunk details
          const errorDetails = {
            position: position,
            chunkSize: chunkSize,
            totalLength: base64String.length,
            chunkLength: base64Chunk.length,
            chunkSample:
              base64Chunk.substring(0, 100) +
              (base64Chunk.length > 100 ? '...' : ''),
            isValidLength: base64Chunk.length % 4 === 0,
            hasValidChars: /^[A-Za-z0-9+/]*={0,2}$/.test(base64Chunk),
          };

          console.error('❌ Base64 chunk decode error:', errorDetails);

          throw new Error(
            'Failed to decode base64 chunk at position ' +
              position +
              '. ' +
              'Chunk length: ' +
              base64Chunk.length +
              ', ' +
              'Valid format: ' +
              errorDetails.hasValidChars +
              ', ' +
              'Error: ' +
              (chunkError instanceof Error
                ? chunkError.message
                : 'Unknown error')
          );
        }
      }

      // Combine all chunks into final Uint8Array
      postProgressMessage(id, 95, 'combining_chunks');

      const totalLength = uint8Chunks.reduce(function (sum, chunk) {
        return sum + chunk.length;
      }, 0);

      const finalBytes = new Uint8Array(totalLength);
      let offset = 0;

      for (let i = 0; i < uint8Chunks.length; i++) {
        const chunk = uint8Chunks[i];
        finalBytes.set(chunk, offset);
        offset += chunk.length;
      }

      // Send success response
      postProgressMessage(id, 100, 'conversion_complete');

      const response = {
        id: id,
        type: 'SUCCESS',
        result: {
          uint8Array: finalBytes,
          mimeType: mimeType,
          originalSize: base64Data.length,
          processedSize: finalBytes.length,
        },
      };

      // ✅ Use transferable objects for better performance with fallback
      try {
        self.postMessage(response, [finalBytes.buffer]);
      } catch (transferError) {
        self.postMessage(response);
      }
    } else {
      // ✅ For medium-sized files, decode all at once for reliability
      const binaryString = atob(base64String);
      const finalBytes = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        finalBytes[i] = binaryString.charCodeAt(i);
      }

      postProgressMessage(id, 100, 'conversion_complete');

      const response = {
        id: id,
        type: 'SUCCESS',
        result: {
          uint8Array: finalBytes,
          mimeType: mimeType,
          originalSize: base64Data.length,
          processedSize: finalBytes.length,
        },
      };

      // ✅ Use transferable objects for better performance with fallback
      try {
        self.postMessage(response, [finalBytes.buffer]);
      } catch (transferError) {
        self.postMessage(response);
      }
    }
  } catch (error) {
    postErrorMessage(
      id,
      'Base64 processing failed: ' +
        (error instanceof Error ? error.message : 'Unknown error')
    );
  }
}

/**
 * Validate base64 string format
 */
function validateBase64(id, base64Data) {
  try {
    const cleanBase64 = cleanBase64String(base64Data);
    const isValid = isValidBase64(cleanBase64);

    // ✅ Add debugging info for validation
    const debugInfo = {
      originalLength: base64Data.length,
      cleanedLength: cleanBase64.length,
      startsWithDataUrl: base64Data.startsWith('data:'),
      containsComma: base64Data.includes(','),
      validFormat: /^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64),
      correctLength: cleanBase64.length % 4 === 0,
      // Sample of first and last characters for debugging
      firstChars: cleanBase64.substring(0, 20),
      lastChars: cleanBase64.substring(Math.max(0, cleanBase64.length - 20)),
    };

    const response = {
      id: id,
      type: 'VALIDATION_RESULT',
      isValid: isValid,
      cleanedLength: cleanBase64.length,
      originalLength: debugInfo.originalLength,
      startsWithDataUrl: debugInfo.startsWithDataUrl,
      containsComma: debugInfo.containsComma,
      validFormat: debugInfo.validFormat,
      correctLength: debugInfo.correctLength,
      firstChars: debugInfo.firstChars,
      lastChars: debugInfo.lastChars,
    };

    self.postMessage(response);
  } catch (error) {
    postErrorMessage(
      id,
      'Validation failed: ' +
        (error instanceof Error ? error.message : 'Unknown error')
    );
  }
}

/**
 * Utility functions
 */
function cleanBase64String(base64Data) {
  // Remove data URL prefix if present
  let cleaned = base64Data.replace(/^data:[^;]+;base64,/, '');

  // Remove whitespace and newlines
  cleaned = cleaned.replace(/\s/g, '');

  // If there's still a comma, take the part after it
  if (cleaned.includes(',')) {
    const parts = cleaned.split(',');
    cleaned = parts[parts.length - 1]; // Take the last part after comma
  }

  return cleaned;
}

function isValidBase64(base64String) {
  // Empty string is not valid
  if (!base64String || base64String.length === 0) {
    return false;
  }

  // Check if string contains only valid base64 characters
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;

  // Check format and length
  return base64Regex.test(base64String) && base64String.length % 4 === 0;
}

function padBase64String(base64String) {
  // Ensure proper padding
  const missingPadding = base64String.length % 4;
  if (missingPadding > 0) {
    return base64String + '='.repeat(4 - missingPadding);
  }
  return base64String;
}

// ✅ New function to validate base64 chunk before decoding
function validateBase64Chunk(chunk) {
  if (!chunk || chunk.length === 0) {
    return false;
  }

  // Must be multiple of 4 for valid base64
  if (chunk.length % 4 !== 0) {
    return false;
  }

  // Check valid characters
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  return base64Regex.test(chunk);
}

function yieldControl() {
  return new Promise(function (resolve) {
    setTimeout(resolve, 0);
  });
}

function postProgressMessage(id, progress, stage, extra) {
  const response = {
    id: id,
    type: 'PROGRESS',
    progress: progress,
    stage: stage,
  };

  // Add extra properties if provided
  if (extra) {
    for (const key in extra) {
      if (extra.hasOwnProperty(key)) {
        response[key] = extra[key];
      }
    }
  }

  self.postMessage(response);
}

function postSuccessMessage(id, result) {
  const response = {
    id: id,
    type: 'SUCCESS',
    result: result,
  };

  self.postMessage(response);
}

function postErrorMessage(id, error) {
  const response = {
    id: id,
    type: 'ERROR',
    error: error,
  };

  self.postMessage(response);
}

import { connect } from 'react-redux';
import {
  IEdTechRenderParam,
  IEdTechRenderTreeData,
} from '../../interfaces/AppComponents';
import { EdComponentParamsSlice } from '../../store/slices/AppSlices/EdComponentParamsSlice';
import { EdTechAppDispatch, EdTechRootState } from '../../store/store';
import { ETypeMode } from '../../enums/AppEnums';
import {
  IEngineInteractionState,
  updateEngineState,
} from '../../store/slices/AppConfig/AppConfigSlice';
import WrapperComponent from './components/WrapperComponent';

export interface IEdComponentParamsMapSateToProps<T = any> {
  params: T | undefined;
  isEditing: boolean;
  engineState: IEngineInteractionState | undefined;
}
export interface IEdComponentParamsMapDispatchToProps<T = any> {
  setParams: (data: IEdTechRenderParam[]) => void;
  addParamComponent: (data: T) => void;
  addOrUpdateParamComponent: (data: T) => void;
  updateParamComponent: (data: T) => void;
  removeParamComponent: () => void;
  clearParams: () => void;
  updateEngineState: (state: Partial<IEngineInteractionState>) => void;
}

const mapStateToProps = (
  { edComponentParams, appConfig }: EdTechRootState,
  ownProps: IEdTechRenderTreeData
): IEdComponentParamsMapSateToProps => {
  let data = edComponentParams.data;
  return {
    params: data?.find((x) => x.id === ownProps?.id)?.params,
    isEditing: appConfig.mode === ETypeMode.CONFIGURATION,
    engineState: appConfig.engineState, // Lấy từ engineState
  };
};
const mapDispatchToProps = (
  dispatch: EdTechAppDispatch,
  ownProps: IEdTechRenderTreeData
): IEdComponentParamsMapDispatchToProps => {
  const {
    addOrUpdateParam,
    setParams,
    addParam,
    updateParam,
    removeParam,
    clearParams,
  } = EdComponentParamsSlice.actions;
  return {
    setParams: (data) => dispatch(setParams(data)),
    addParamComponent: (params) =>
      dispatch(addParam({ id: ownProps.id, params })),
    addOrUpdateParamComponent: (params) =>
      dispatch(addOrUpdateParam({ id: ownProps.id, params })),
    updateParamComponent: (params) =>
      dispatch(updateParam({ id: ownProps.id, params })),
    removeParamComponent: () => dispatch(removeParam({ ...ownProps })),
    clearParams: () => dispatch(clearParams()),
    updateEngineState: (state: Partial<IEngineInteractionState>) => {
      dispatch(updateEngineState(state));
    },
  };
};

export const withEdComponentParams = (component: any) => {
  return connect(
    mapStateToProps,
    mapDispatchToProps
  )(WrapperComponent(component));
};

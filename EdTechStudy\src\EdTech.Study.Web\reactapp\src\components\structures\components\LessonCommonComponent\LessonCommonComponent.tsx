import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  useCallback,
} from 'react';
import {
  IEdTechRenderProps,
  IEdTechRenderTreeData,
} from '../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../hocs/withEdComponentParams/withEdComponentParams';
import { Splitter } from 'antd';
import LessonRenderComponent from '../../../common/LessonRenderComponent/LessonRenderComponent';
import { useDispatch, useSelector } from 'react-redux';
import { toggleEditing } from '../../../../store/slices/utils/createTreeDataSlice';
import { ETypeEdTechComponent, ETypeMode } from '../../../../enums/AppEnums';
import { EdTechRootState } from '../../../../store/store';
import { AppFunctions } from '../../../../utils/AppFunctions';
import { z } from 'zod';
import { createComponentSchema } from '../../../../utils/schema/createComponentSchema';
import LessonSidebarWithRcTree from '../../../common/LessonSidebarMenu/LessonSidebarWithRcTree/LessonSidebarWithRcTree';
import './styles/LessonCommonComponent.css';
import { TreeMenuItem } from '../../../common/LessonSidebarMenu/LessonSidebarWithRcTree/types';
import {
  findItemById,
  findParentContentItem,
  findSubItemById,
} from './utils/LessonCommonUtils';

export const lessonCommonSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    content: z.string().optional(),
    subItems: z.array(z.any()).optional(),
  },
});

const LessonCommonComponent: React.FC<IEdTechRenderProps<any>> = (props) => {
  const { params, subItems = [], isEditing, updateEngineState } = props;
  const isHideSidebar = params?.isHideSidebar ?? false;
  const [activeItem, setActiveItem] = useState(subItems[0]?.id || 'item-0');
  const [activeContent, setActiveContent] = useState(subItems[0]);
  const dispatch = useDispatch();
  const contentRef = useRef<HTMLDivElement>(null);

  // State for responsive sidebar size
  const [sidebarSize, setSidebarSize] = useState('20%');
  const [sidebarMinSize, setSidebarMinSize] = useState('15%');
  const [sidebarMaxSize, setSidebarMaxSize] = useState('40%');

  // Get data from Redux store
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );
  const paramData = useSelector(
    (state: EdTechRootState) => state.edComponentParams.data
  );

  // Update sidebar size based on screen width
  const updateSidebarSize = useCallback(() => {
    const width = window.innerWidth;
    if (width <= 480) {
      setSidebarSize('50%');
      setSidebarMinSize('50%');
      setSidebarMaxSize('100%');
    } else if (width <= 768) {
      setSidebarSize('30%');
      setSidebarMinSize('20%');
      setSidebarMaxSize('60%');
    } else if (width <= 1024) {
      setSidebarSize('25%');
      setSidebarMinSize('20%');
      setSidebarMaxSize('50%');
    } else {
      setSidebarSize('20%');
      setSidebarMinSize('15%');
      setSidebarMaxSize('40%');
    }
  }, []);

  const handleActiveEngine = (targetElement: any) => {
    // Check if scroll-margin-top is supported
    if ('scrollMarginTop' in document.documentElement.style) {
      // Modern browsers: use scroll-margin-top
      const originalScrollMargin = targetElement.style.scrollMarginTop;
      targetElement.style.scrollMarginTop = '30px';

      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });

      // Reset after animation completes
      setTimeout(() => {
        targetElement.style.scrollMarginTop = originalScrollMargin;
      }, 1000);
    } else {
      // Fallback for older browsers
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      // Add a small offset after scrolling
      setTimeout(() => {
        window.scrollBy(0, -20);
      }, 100);
    }
  };
  // Function to scroll to a specific element by ID
  const scrollToElement = (id: string) => {
    // First, find the parent CONTENT item or the root item itself
    const parentItem = findParentContentItem(id, subItems);
    if (!parentItem) return;

    // Set the parent as active content to ensure it's rendered
    setActiveContent(parentItem);
    setActiveItem(parentItem.id);

    // If the item is a root item and not a CONTENT type, we need to render it directly
    const item = findItemById(id, subItems);
    if (
      item &&
      item.id === parentItem.id &&
      parentItem.type !== ETypeEdTechComponent.CONTENT &&
      parentItem.type !== ETypeEdTechComponent.LAYOUT
    ) {
      // For root items that aren't CONTENT type, we need to render them directly
      // This is handled in the return JSX by adding a condition to render non-CONTENT active items
      return;
    }

    // After a short delay to ensure rendering, try to find the element by ID
    setTimeout(() => {
      // Now that the parent content is rendered, we can try to find the element by ID
      const targetElement = document.getElementById(id);
      updateEngineState({
        pathEngineActive: item?.path,
      });

      if (targetElement) {
        handleActiveEngine(targetElement);
      } else {
        // If we couldn't find the element by ID, try to find it by other attributes
        const elementByDataType = document.querySelector(
          `[data-type="${findSubItemById(id, subItems)?.type}"][data-name="${
            findSubItemById(id, subItems)?.name
          }"]`
        );

        if (elementByDataType) {
          handleActiveEngine(elementByDataType);
        } else {
          // If all else fails, at least scroll to the parent content
          const parentElement = document.getElementById(parentItem.id);
          if (parentElement) {
            handleActiveEngine(parentElement);
          }
        }
      }
    }, 200);
  };

  // Handle fullscreen - redirect to preview page
  const handleFullscreen = () => {
    // Lấy dữ liệu hiện tại từ Redux store
    const currentState = {
      edComponentParams: paramData,
      edTechRenderTreeData: renderTreeData,
    };

    // Lưu dữ liệu vào localStorage để trang preview có thể sử dụng
    localStorage.setItem('previewData', JSON.stringify(currentState));

    AppFunctions.redirectToModeApp({ mode: ETypeMode.PRESENT });
  };

  const handleMenuSelect = (key: string) => {
    if (key === 'config') {
      dispatch(toggleEditing());
    }
    // Find the item by key
    const item = findItemById(key, subItems);
    updateEngineState({
      pathEngineActive: item?.path,
    });
    if (!item) return;

    // Handle different behaviors based on item type
    if (item.type === ETypeEdTechComponent.CONTENT) {
      // For content display, update the active content
      setActiveItem(key);
      setActiveContent(item);
    } else if (
      item.type === ETypeEdTechComponent.COMMON ||
      item.type === ETypeEdTechComponent.SIMULATORS ||
      item.type === ETypeEdTechComponent.MINI_GAMES ||
      item.type === ETypeEdTechComponent.QUIZ ||
      item.type === ETypeEdTechComponent.LAYOUT
    ) {
      // For interactive types, use the scrollToElement function
      // which will find the parent CONTENT item and scroll to the specific element
      scrollToElement(key);
    }
  };

  // Initialize activeContent when component mounts
  useEffect(() => {
    const initialItem = findItemById(activeItem, subItems) || subItems[0];
    if (!initialItem) return;

    // Set active content for any item type
    setActiveContent(initialItem);
  }, [subItems, activeItem]);

  // Create a nested menu structure from subItems
  const menuItems: TreeMenuItem[] = useMemo(() => {
    // Define allowed types
    const allowedTypes = [
      ETypeEdTechComponent.STRUCTURE,
      ETypeEdTechComponent.COMMON,
      ETypeEdTechComponent.SIMULATORS,
      ETypeEdTechComponent.MINI_GAMES,
      ETypeEdTechComponent.QUIZ,
      ETypeEdTechComponent.CONTENT,
      ETypeEdTechComponent.LAYOUT,
    ];

    // Process subItems to create a nested structure
    const processItems = (items: IEdTechRenderTreeData[]): TreeMenuItem[] => {
      const result: TreeMenuItem[] = [];

      items.forEach((item) => {
        // Check if item type is in allowed types
        const isAllowedType = allowedTypes.includes(
          item.type as ETypeEdTechComponent
        );

        // Process children if any
        let children: TreeMenuItem[] = [];
        if (item.subItems && item.subItems.length > 0) {
          children = processItems(item.subItems);
        }

        if (isAllowedType) {
          // If item type is allowed, add it to the menu
          const menuItem: TreeMenuItem = {
            key: item.id || '',
            title: item.title || item.name,
            type: item.type, // Bổ sung thuộc tính type
            path: item.path,
            name: item.name,
          };

          // Add children if any
          if (children.length > 0) {
            menuItem.children = children;
          }

          result.push(menuItem);
        } else if (children.length > 0) {
          // If item type is not allowed but it has valid children,
          // add those children directly to the result
          result.push(...children);
        }
        // If item type is not allowed and has no valid children, skip it
      });

      return result;
    };

    const processedItems = processItems(subItems);

    // Add the config option at the end
    return processedItems;
  }, [subItems, props.isEditing]);

  // Add event listener for window resize to update sidebar size
  useEffect(() => {
    // Initial setup
    updateSidebarSize();

    // Add resize event listener
    window.addEventListener('resize', updateSidebarSize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateSidebarSize);
    };
  }, [updateSidebarSize]);

  return (
    <div className="lesson-common-wrapper">
      <div className="lesson-common-layout">
        {!isHideSidebar ? (
          <Splitter className="tailwind-w-full lesson-splitter" lazy>
            <Splitter.Panel className="tailwind-w-full tailwind-bg-white">
              <div className="lesson-content-container">
                <div className="lesson-content-scrollable" ref={contentRef}>
                  {/* Render active item */}
                  {activeContent && (
                    <div
                      id={activeContent.id}
                      className="content-item-container"
                    >
                      {/* Add a wrapper for the LessonRenderComponent */}
                      <div className="lesson-render-wrapper">
                        <LessonRenderComponent data={activeContent} />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Splitter.Panel>
            <Splitter.Panel
              resizable={true}
              className="lesson-sidebar-panel tailwind-bg-default"
              defaultSize={sidebarSize}
              min={sidebarMinSize}
              max={sidebarMaxSize}
            >
              <div className="lesson-sidebar-container tailwind-h-full">
                {/* Replace LessonSidebarMenu with LessonSidebarWithRcTree */}
                <LessonSidebarWithRcTree
                  items={menuItems.map((item) => ({
                    ...item,
                    title: item.title,
                  }))}
                  root={renderTreeData}
                  title="Nội dung"
                  onSelect={handleMenuSelect}
                  isEditing={isEditing}
                  onFullscreen={handleFullscreen}
                />
              </div>
            </Splitter.Panel>
          </Splitter>
        ) : (
          <div className="lesson-content-container">
            <div className="lesson-content-scrollable" ref={contentRef}>
              {/* Render active item */}
              {activeContent && (
                <div id={activeContent.id} className="content-item-container">
                  {/* Add a wrapper for the LessonRenderComponent */}
                  <div className="lesson-render-wrapper">
                    <LessonRenderComponent data={activeContent} />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default withEdComponentParams(LessonCommonComponent);

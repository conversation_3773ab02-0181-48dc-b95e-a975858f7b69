import React from 'react';
import { BoxTextComponentProps } from './BoxTextComponentConfig';

export const getBoxStyle = (
  finalWidth: number,
  finalHeight: number,
  config: BoxTextComponentProps,
  localStyles: {
    backgroundColor: string | undefined;
    borderColor: string | undefined;
    textColor?: string | undefined;
    fontSize?: number | undefined;
    fontWeight?: string | number | undefined;
    fontStyle?: string | undefined;
    textAlign?: string | undefined;
  }
): React.CSSProperties => {
  return {
    width: `${finalWidth}px`,
    height: `${finalHeight}px`,
    backgroundColor: localStyles.backgroundColor,
    borderColor: localStyles.borderColor,
    borderWidth: `${config.borderWidth}px`,
    borderStyle: 'solid',
    borderRadius: `${config.borderRadius}px`,
    boxShadow: config.boxShadow ? '0 4px 8px rgba(0, 0, 0, 0.1)' : 'none',
    padding: `${config.padding}px`,
    position: 'relative',
    overflow: 'hidden',
  };
};

export const getTextStyle = (
  config: BoxTextComponentProps,
  localStyles: {
    textColor: string | undefined;
    fontSize: number | undefined;
    fontWeight: string | number | undefined;
    fontStyle: string | undefined;
    textAlign: string | undefined;
  }
): React.CSSProperties => {
  return {
    width: '100%',
    height: '100%',
    padding: `${config.padding}px`,
    color: localStyles.textColor,
    fontSize: `${localStyles.fontSize}px`,
    fontWeight: localStyles.fontWeight,
    fontStyle: localStyles.fontStyle,
    lineHeight: config.lineHeight,
    textAlign: localStyles.textAlign as any,
  };
};

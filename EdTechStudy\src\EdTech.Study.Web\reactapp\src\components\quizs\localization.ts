const practiceLocalization = {
  quiz: 'quiz',
  matching: 'matching',
  fillblanks: 'fillblanks',
  quiz_description: 'Trắ<PERSON> nghiệm chọn 1 đáp án',
  matching_description: 'N<PERSON>i các đáp án ở 2 cột cho phù hợp',
  fillblanks_description: '<PERSON><PERSON><PERSON>n từ thích hợp vào ô trống',
  // Add new entries for multiselect
  multiselect: 'multiselect',
  multiselect_description:
    'Câu hỏi nhiều lựa chọn cho phép người dùng chọn nhiều đáp án đúng.',
  shortanswer: 'shortanswer',
  shortanswer_description:
    '<PERSON><PERSON><PERSON> hỏi trả lời ngắn yêu cầu người dùng nhập câu trả lời ngắn gọn.',
  correct: 'correct',
  incorrect: 'incorrect',
  exercise: 'exercise',
  config: 'config',
  'Question Title': 'Tiêu đề câu hỏi',
  'Question Text': 'Nội dung câu hỏi',
  'Question Image': '<PERSON><PERSON><PERSON> ảnh câu hỏi',
  Options: '<PERSON><PERSON><PERSON> đáp án',
  Explanation: 'Gi<PERSON>i thích',
  'Correct Answer': '<PERSON><PERSON><PERSON> án đúng',
  'Explanation:': 'Giải thích:',
  'Correct Answer:': 'Đáp án đúng:',
  'Question Type': 'Loại câu hỏi',
  'Multiple Choice': 'Trắc nghiệm',
  'Add Option': 'Thêm đáp án',
  'Save Changes': 'Lưu thay đổi',
  'Delete Question': 'Xóa câu hỏi',
  'Are you sure you want to delete this question?':
    'Bạn có chắc chắn muốn xóa câu hỏi này?',
  Yes: 'Đồng ý',
  No: 'Hủy',
  'Enter question title': 'Nhập tiêu đề câu hỏi',
  'Enter your question': 'Nhập nội dung câu hỏi',
  'Enter explanation for the correct answer': 'Nhập giải thích cho đáp án đúng',
  Option: 'Đáp án',
  Present: 'Trình chiếu',
  'View demo': 'Xem demo',
  'Advanced Options': 'Tùy chọn nâng cao',
  Settings: 'Cài đặt',
};

export const quizLocalization = {
  questionType: {
    label: 'Loại câu hỏi',
    multipleChoice: 'Trắc nghiệm',
    matching: 'Nối cặp',
  },
  form: {
    questionTitle: {
      label: practiceLocalization['Question Title'],
      placeholder: practiceLocalization['Enter question title'],
    },
    questionText: {
      label: practiceLocalization['Question Text'],
      placeholder: practiceLocalization['Enter your question'],
    },
    questionImage: {
      label: practiceLocalization['Question Image'],
    },
    questionImages: {
      label: 'Hình ảnh câu hỏi',
      placeholder: 'Thêm hình ảnh cho câu hỏi',
    },
    options: {
      label: practiceLocalization['Options'],
      addOption: practiceLocalization['Add Option'],
      optionPlaceholder: (index: string) =>
        `${practiceLocalization['Option']} ${index}`,
    },
    explanation: {
      label: practiceLocalization['Explanation'],
      placeholder:
        practiceLocalization['Enter explanation for the correct answer'],
    },
    correctAnswer: practiceLocalization['Correct Answer'],
  },
  buttons: {
    saveChanges: practiceLocalization['Save Changes'],
    deleteQuestion: {
      button: practiceLocalization['Delete Question'],
      confirmTitle:
        practiceLocalization['Are you sure you want to delete this question?'],
      yes: practiceLocalization['Yes'],
      no: practiceLocalization['No'],
    },
  },
  feedback: {
    explanation: practiceLocalization['Explanation:'],
    correctAnswer: practiceLocalization['Correct Answer:'],
  },
};

export default practiceLocalization;

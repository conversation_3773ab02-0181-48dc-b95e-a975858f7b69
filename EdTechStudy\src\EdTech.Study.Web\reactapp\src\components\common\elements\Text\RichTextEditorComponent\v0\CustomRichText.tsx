import {
  forwardRef,
  Ref,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
  useCallback,
} from 'react';
import {
  Audio,
  Count,
  EmojiPicker,
  FormatPainter,
  HtmlEditor,
  Image,
  ImageSettingsModel,
  Inject,
  Link,
  PasteCleanup,
  QuickToolbar,
  RichTextEditorComponent,
  Table,
  Toolbar,
  Video,
  VideoSettingsModel,
  AudioSettingsModel,
} from '@syncfusion/ej2-react-richtexteditor';
import { CommandName, SlashMenu } from '@syncfusion/ej2-richtexteditor';
import { L10n } from '@syncfusion/ej2-base';
import * as viVNLocale from './vi-VN.json';
import './RichTextEditorStyles.css';
import MathLiveFormulaDialog from './MathLiveFormulaDialog';

// Utility imports
import { typesetMathJax, configureMathJax } from './utils/mathJaxUtils';
import {
  extractYoutubeId,
  enhanceAudioElements,
  enhanceVideoElements,
  enhanceImageElements,
  enhanceTableElements,
} from './utils/mediaUtils';
import {
  isContentEmpty,
  saveSelection as saveEditorSelection,
  restoreSelection as restoreEditorSelection,
} from './utils/editorUtils';
import {
  addFormulaButton,
  setupFormulaClickHandler,
  insertFormula,
} from './utils/formulaUtils';

// MathJax
import 'mathjax/es5/tex-chtml';
import './MathLiveFormulaDialogStyles.css';

export interface CustomRichTextRef {
  getContent: () => string;
  setContent: (value: string) => any;
  focus: () => void;
  executeCommand: (commandName: string | CommandName, value?: string) => void;
  saveSelection: () => void;
  restoreSelection: () => void;
}

export interface CustomRichTextProps {
  disabled?: boolean;
  id?: string;
  displayName?: string;
  name?: string;
  cssClassInput?: string;
  cssClassDisPlayName?: string;
  isRequired?: boolean;
  value?: string;
  onChangeValue?: Function;
  enableXhtml?: boolean;
  htmlAttributes?: {
    [key: string]: string;
  };
  height?: number | string;
  toolbarSettingItems?: string[];
  handleBur?: () => any;
  handleActionBegin?: (args: any) => void;
  handleActionComplete?: (args: any) => void;
  insertVideoSettings?: VideoSettingsModel;
  insertAudioSettings?: AudioSettingsModel;
  insertImageSettings?: ImageSettingsModel;
  quickToolbarSettings?: object;
  showCharCount?: boolean;
  maxLength?: number;
  placeholder?: string;
  isEditing?: boolean;
  renderViewMode?: boolean;
}

// Define toolbar settings type
interface ToolbarSettings {
  items: string[];
}
const CustomRichText = forwardRef(function CustomRichText(
  props: CustomRichTextProps,
  ref: Ref<CustomRichTextRef>
): JSX.Element {
  const {
    name,
    onChangeValue,
    htmlAttributes,
    height,
    toolbarSettingItems,
    handleBur,
    handleActionComplete,
    quickToolbarSettings,
    showCharCount,
    maxLength,
    placeholder,
  } = props;

  const richTextEditorComponentRef = useRef<RichTextEditorComponent>(null);

  // State for formula dialog
  const [isFormulaDialogOpen, setIsFormulaDialogOpen] =
    useState<boolean>(false);
  const [currentFormula, setCurrentFormula] = useState<string>('');

  // Reference to store cursor position
  const savedRange = useRef<Range | null>(null);

  // Register Vietnamese locale
  L10n.load({
    'vi-VN': viVNLocale,
  });

  // Default settings for image insertion
  const defaultImageSettings: ImageSettingsModel = {
    saveFormat: 'Base64',
  };

  // Default settings for video insertion
  const defaultVideoSettings: VideoSettingsModel = {
    saveFormat: 'Blob',
    width: '560px',
    height: '315px',
    layoutOption: 'Break', // Always use Break layout for videos
  };

  // Default settings for audio insertion
  const defaultAudioSettings: AudioSettingsModel = {
    saveFormat: 'Blob',
    layoutOption: 'Break', // Always use Break layout for audio
  };

  const mergedImageSettings = useMemo(() => {
    if (props.insertImageSettings) {
      return props.insertImageSettings;
    }
    return defaultImageSettings;
  }, [props.insertImageSettings]);

  const mergedVideoSettings = useMemo(() => {
    if (props.insertVideoSettings) {
      return props.insertVideoSettings;
    }
    return defaultVideoSettings;
  }, [props.insertVideoSettings]);

  const mergedAudioSettings = useMemo(() => {
    if (props.insertAudioSettings) {
      return props.insertAudioSettings;
    }
    return defaultAudioSettings;
  }, [props.insertAudioSettings]);

  // Define default toolbar settings
  const [toolbarSettings, setToolbarSettings] = useState<ToolbarSettings>({
    items: [
      'Undo',
      'Redo',
      '|',
      'Formats',
      '|',
      'Bold',
      'Italic',
      'Underline',
      'StrikeThrough',
      '|',
      'FontName',
      'FontSize',
      'FontColor',
      'BackgroundColor',
      '|',
      'LowerCase',
      'UpperCase',
      '|',
      'Superscript',
      'Subscript',
      '|',
      'Alignments',
      '|',
      'OrderedList',
      'UnorderedList',
      '|',
      'Outdent',
      'Indent',
      '|',
      'CreateLink',
      'Image',
      'Video',
      'Audio',
      'CreateTable',
      '|',
      'ClearFormat',
      'Formula',
    ],
  });

  // Define default quick toolbar settings
  const defaultQuickToolbarSettings = {
    image: [
      'Replace',
      'Align',
      'Caption',
      'Remove',
      'InsertLink',
      'OpenImageLink',
      '-',
      'EditImageLink',
      'RemoveImageLink',
      'Display',
      'AltText',
      'Dimension',
    ],
    link: ['Open', 'Edit', 'UnLink'],
    video: [
      'VideoReplace',
      'VideoAlign',
      'VideoRemove',
      'VideoLayoutOption',
      'VideoDimension',
    ],
    audio: ['AudioReplace', 'AudioRemove', 'AudioLayoutOption'],
  };
  const mergedQuickToolbarSettings = useMemo(() => {
    if (quickToolbarSettings) {
      return quickToolbarSettings;
    }
    return defaultQuickToolbarSettings;
  }, [quickToolbarSettings]);

  // MathJax configuration
  useEffect(() => {
    // Configure MathJax
    configureMathJax();

    // Initial typesetting after a short delay
    const initialTypesetTimer = setTimeout(() => {
      typesetMathJax();

      // Run a second typesetting after a longer delay to catch any missed formulas
      setTimeout(typesetMathJax, 1000);
    }, 500);

    return () => {
      clearTimeout(initialTypesetTimer);
    };
  }, []);

  useEffect(() => {
    if (toolbarSettingItems) {
      setToolbarSettings({
        items: toolbarSettingItems,
      });
    }
  }, [toolbarSettingItems]);

  // Save current selection
  const saveSelection = useCallback(() => {
    savedRange.current = saveEditorSelection();
  }, []);

  // Restore saved selection
  const restoreSelection = useCallback(() => {
    restoreEditorSelection(savedRange.current);
  }, []);

  // Function to enhance all content elements in view mode
  const enhanceContentElements = useCallback(() => {
    enhanceAudioElements();
    enhanceVideoElements();
    enhanceImageElements();
    enhanceTableElements();
    typesetMathJax();
  }, []);

  // Handle video action begin (before insertion)
  const handleVideoActionBegin = useCallback(
    (args: any) => {
      // Check if this is a video insertion action
      if (
        args.requestType === 'VideoInsert' ||
        args.requestType === 'VideoReplace'
      ) {
        // Process video URL if it exists
        if (args.videoUrl) {
          // Check if it's a YouTube URL
          if (
            args.videoUrl.includes('youtube.com') ||
            args.videoUrl.includes('youtu.be')
          ) {
            // Extract the YouTube video ID
            const videoId = extractYoutubeId(args.videoUrl);
            if (videoId) {
              // Create a proper YouTube embed URL with parameters
              args.videoUrl = `https://www.youtube.com/embed/${videoId}?autoplay=0&controls=1&loop=0`;
            }
          }
        }
      }

      // Process video element after insertion
      if (
        args.requestType === 'VideoInserted' ||
        args.requestType === 'VideoReplaced'
      ) {
        // Find all iframe elements (YouTube embeds)
        const iframes = document.querySelectorAll('.e-richtexteditor iframe');
        iframes.forEach((iframe) => {
          // Ensure proper attributes for YouTube embeds
          iframe.setAttribute('allowfullscreen', 'true');
          iframe.setAttribute(
            'allow',
            'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
          );

          // Set proper dimensions if not already set
          const htmlIframe = iframe as HTMLIFrameElement;
          if (!htmlIframe.style.width) htmlIframe.style.width = '560px';
          if (!htmlIframe.style.height) htmlIframe.style.height = '315px';
        });
      }

      // Call the original handler if provided
      if (props.handleActionBegin) {
        props.handleActionBegin(args);
      }
    },
    [props.handleActionBegin]
  );

  // Handle formula insertion
  const handleFormulaInsert = useCallback(
    (formula: string) => {
      insertFormula(
        formula,
        {
          focus: () => {
            if (richTextEditorComponentRef.current) {
              richTextEditorComponentRef.current.focusIn();
            }
          },
          executeCommand: (
            commandName: string | CommandName,
            value?: string
          ) => {
            if (richTextEditorComponentRef.current) {
              richTextEditorComponentRef.current.executeCommand(
                commandName as CommandName,
                value
              );
            }
          },
        },
        savedRange.current,
        restoreSelection,
        setIsFormulaDialogOpen
      );
    },
    [restoreSelection]
  );

  // Handle formula dialog close
  const handleFormulaDialogClose = useCallback(() => {
    setIsFormulaDialogOpen(false);
    // Restore the cursor position
    setTimeout(() => {
      restoreSelection();
    }, 50);
  }, [restoreSelection]);

  const handleChange = (event: any) => {
    if (props.disabled) return;
    saveSelection();
    onChangeValue && onChangeValue(name, event.value);
  };

  // Add custom formula button and setup formula click handler after component is mounted
  useEffect(() => {
    if (!props.disabled && toolbarSettings.items.includes('Formula')) {
      // Start the process after a delay to ensure the editor is rendered
      const formulaButtonTimer = setTimeout(() => {
        addFormulaButton(
          props.id || 'default',
          saveSelection,
          setIsFormulaDialogOpen
        );
      }, 800);

      const formulaClickHandlerTimer = setTimeout(() => {
        setupFormulaClickHandler(
          saveSelection,
          setCurrentFormula,
          setIsFormulaDialogOpen
        );
      }, 1000);

      return () => {
        // Clean up timers on unmount
        clearTimeout(formulaButtonTimer);
        clearTimeout(formulaClickHandlerTimer);
      };
    }
  }, [props.disabled, toolbarSettings.items, props.id]);

  useImperativeHandle(
    ref,
    () => ({
      getContent() {
        let content = richTextEditorComponentRef.current?.getContent();
        return content?.outerHTML.toString() ?? '';
      },
      setContent(value: string) {
        if (richTextEditorComponentRef.current)
          richTextEditorComponentRef.current.value = value;
      },
      focus() {
        if (richTextEditorComponentRef.current) {
          // Đơn giản chỉ focus vào editor
          richTextEditorComponentRef.current.focusIn();
        }
      },
      executeCommand(commandName: string | CommandName, value?: string) {
        if (richTextEditorComponentRef.current) {
          // Sử dụng phương thức executeCommand của Syncfusion RichTextEditor
          const editorObj = richTextEditorComponentRef.current;
          if (editorObj && typeof editorObj.executeCommand === 'function') {
            // Cast commandName to CommandName if it's a string
            editorObj.executeCommand(commandName as CommandName, value);
          } else {
            console.warn(
              'executeCommand method not available on RichTextEditor instance'
            );
          }
        }
      },
      saveSelection() {
        savedRange.current = saveEditorSelection();
      },
      restoreSelection() {
        restoreEditorSelection(savedRange.current);
      },
    }),
    [richTextEditorComponentRef.current]
  );

  // Ensure we have a valid toolbar configuration
  const safeToolbarSettings = useMemo<ToolbarSettings>(() => {
    const standardItems = [
      'Undo',
      'Redo',
      'Cut',
      'Copy',
      'Paste',
      'Bold',
      'Italic',
      'Underline',
      'StrikeThrough',
      'FontName',
      'FontSize',
      'FontColor',
      'BackgroundColor',
      'LowerCase',
      'UpperCase',
      'Formats',
      'Alignments',
      'OrderedList',
      'UnorderedList',
      'Outdent',
      'Indent',
      'CreateLink',
      'Image',
      'Video',
      'Audio',
      'CreateTable',
      'FormatPainter',
      'ClearFormat',
      'Print',
      'SourceCode',
      'FullScreen',
      'Superscript',
      'Subscript',
      '|',
    ];
    // Make sure we have a valid toolbar items array
    if (
      !toolbarSettings ||
      !toolbarSettings.items ||
      !Array.isArray(toolbarSettings.items) ||
      toolbarSettings.items.length === 0
    ) {
      // Return default toolbar settings if the current settings are invalid
      return {
        items: [
          'Bold',
          'Italic',
          'Underline',
          'FontName',
          'FontSize',
          'FontColor',
          'BackgroundColor',
          'Formats',
          'Alignments',
          'OrderedList',
          'UnorderedList',
          'CreateLink',
          'Image',
          'Video',
          'Audio',
          'CreateTable',
          'ClearFormat',
          'SourceCode',
        ],
      };
    }
    const validItems = toolbarSettings.items.filter(
      (item) =>
        typeof item === 'string' &&
        item !== undefined &&
        standardItems.includes(item)
    );

    return {
      items:
        validItems.length > 0 ? validItems : ['Bold', 'Italic', 'Underline'],
    };
  }, [toolbarSettings]);

  // Render view mode (read-only)
  const renderViewMode = () => {
    const contentEmpty = isContentEmpty(props.value || '');

    // Enhance content elements when content is not empty
    useEffect(() => {
      if (!contentEmpty) {
        // Wait for DOM to update after render
        const enhanceTimer = setTimeout(enhanceContentElements, 100);
        return () => clearTimeout(enhanceTimer);
      }
    }, [props.value, contentEmpty]);

    return (
      <div className="rich-text-content-container">
        {contentEmpty ? (
          // Show empty div without placeholder when content is empty
          <div className="rich-text-content empty-content"></div>
        ) : (
          // Show content when available
          <div
            className="rich-text-content"
            dangerouslySetInnerHTML={{ __html: props.value || '' }}
          />
        )}
      </div>
    );
  };

  // Render edit mode
  const renderEditMode = () => {
    return (
      <div className="syncfusion-rte-container">
        <RichTextEditorComponent
          ref={richTextEditorComponentRef}
          enableXhtml={props.enableXhtml}
          change={handleChange}
          blur={handleBur}
          name={name}
          key={name}
          id={props.id}
          readonly={props.disabled}
          htmlAttributes={htmlAttributes}
          value={props.value}
          insertImageSettings={mergedImageSettings}
          insertVideoSettings={mergedVideoSettings}
          insertAudioSettings={mergedAudioSettings}
          quickToolbarSettings={mergedQuickToolbarSettings}
          toolbarSettings={safeToolbarSettings}
          locale="vi-VN"
          height={height}
          placeholder={placeholder || 'Nhập nội dung của bạn tại đây...'}
          showCharCount={showCharCount}
          maxLength={maxLength}
          fontSize={{
            default: '13pt',
            width: '35px',
            items: [
              { text: '8', value: '8pt' },
              { text: '10', value: '10pt' },
              { text: '12', value: '12pt' },
              { text: '13', value: '13pt' },
              { text: '14', value: '14pt' },
              { text: '18', value: '18pt' },
              { text: '24', value: '24pt' },
              { text: '36', value: '36pt' },
            ],
          }}
          fontFamily={{
            default: 'Arial',
            width: '65px',
            items: [
              { text: 'Segoe UI', value: 'Segoe UI' },
              { text: 'Arial', value: 'Arial,Helvetica,sans-serif' },
              { text: 'Courier New', value: 'Courier New,Courier,monospace' },
              { text: 'Georgia', value: 'Georgia,serif' },
              { text: 'Impact', value: 'Impact,Charcoal,sans-serif' },
              {
                text: 'Lucida Console',
                value: 'Lucida Console,Monaco,monospace',
              },
              { text: 'Tahoma', value: 'Tahoma,Geneva,sans-serif' },
              { text: 'Times New Roman', value: 'Times New Roman,Times,serif' },
              {
                text: 'Trebuchet MS',
                value: 'Trebuchet MS,Helvetica,sans-serif',
              },
              { text: 'Verdana', value: 'Verdana,Geneva,sans-serif' },
            ],
          }}
          actionBegin={handleVideoActionBegin}
          actionComplete={handleActionComplete}
          saveInterval={100}
        >
          <Inject
            services={[
              Toolbar,
              Image,
              Link,
              HtmlEditor,
              Count,
              QuickToolbar,
              Table,
              EmojiPicker,
              Video,
              Audio,
              FormatPainter,
              PasteCleanup,
              SlashMenu,
            ]}
          />
        </RichTextEditorComponent>
      </div>
    );
  };

  return (
    <>
      {props.displayName && (
        <label className={props.cssClassDisPlayName}>
          {props.displayName}{' '}
          {props.isRequired && <span className="text-danger">*</span>}
        </label>
      )}
      <div className={props.cssClassInput}>
        {props.disabled ? renderViewMode() : renderEditMode()}
      </div>
      {toolbarSettings.items.includes('Formula') && (
        <MathLiveFormulaDialog
          isOpen={isFormulaDialogOpen}
          onClose={handleFormulaDialogClose}
          onInsert={handleFormulaInsert}
          initialFormula={currentFormula}
        />
      )}
    </>
  );
});
export default CustomRichText;

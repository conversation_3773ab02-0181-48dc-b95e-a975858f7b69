import React from 'react';
import { Input, Typography, Space, Divider, Select } from 'antd';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { EngineContainer } from '../../common/engines';

const { Text, Title } = Typography;
const { Option } = Select;

// Định nghĩa các loại phép tính
export type OperationType = 'add' | 'subtract' | 'multiply' | 'divide';

// Giao diện cho phép tính với phân số
interface FractionPropsOperation {
  numerator?: number;
  denominator?: number;
  operation?: OperationType;
  showDecimal?: boolean;
}

const FractionDemoOperation: React.FC<
  IEdTechRenderProps<FractionPropsOperation>
> = (props) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Gi<PERSON> trị mặc định
  const numerator = params?.numerator || 1;
  const denominator = params?.denominator || 2;
  const operation = params?.operation || 'add';
  const showDecimal =
    params?.showDecimal !== undefined ? params.showDecimal : true;

  // Cập nhật tham số
  const handleParamChange = (newParams: Partial<FractionPropsOperation>) => {
    addOrUpdateParamComponent({
      ...params,
      operation,
      ...newParams,
    });
  };

  // Định nghĩa kiểu kết quả
  type CalculationResult = {
    numerator: number;
    denominator: number;
    error?: string;
  };

  // Tìm ước chung lớn nhất (GCD)
  const findGCD = (a: number, b: number): number => {
    return b === 0 ? a : findGCD(b, a % b);
  };

  // Rút gọn phân số
  const simplifyFraction = (
    numerator: number,
    denominator: number
  ): CalculationResult => {
    if (denominator === 0)
      return { numerator, denominator, error: 'Mẫu số không thể bằng 0' };
    if (numerator === 0) return { numerator: 0, denominator: 1 };

    const gcd = Math.abs(findGCD(numerator, denominator));
    return {
      numerator: Math.round(numerator / gcd),
      denominator: Math.round(denominator / gcd),
    };
  };

  // Thực hiện phép tính
  const calculateResult = (): CalculationResult => {
    if (denominator === 0) {
      return { numerator: 0, denominator: 0, error: 'Mẫu số không thể bằng 0' };
    }

    switch (operation) {
      case 'add':
        // 1 + (numerator/denominator) = (denominator + numerator) / denominator
        return simplifyFraction(denominator + numerator, denominator);

      case 'subtract':
        // 1 - (numerator/denominator) = (denominator - numerator) / denominator
        return simplifyFraction(denominator - numerator, denominator);

      case 'multiply':
        // 2 * (numerator/denominator) = (2 * numerator) / denominator
        return simplifyFraction(2 * numerator, denominator);

      case 'divide':
        // 1 ÷ (numerator/denominator) = denominator / numerator
        if (numerator === 0) {
          return {
            numerator: 0,
            denominator: 0,
            error: 'Không thể chia cho 0',
          };
        }
        return simplifyFraction(denominator, numerator);

      default:
        return {
          numerator: 0,
          denominator: 0,
          error: 'Phép tính không hợp lệ',
        };
    }
  };

  // Hiển thị phân số
  const renderFraction = (numerator: number, denominator: number) => {
    return (
      <div className="tailwind-flex tailwind-flex-col tailwind-items-center">
        <div className="tailwind-text-2xl tailwind-font-bold">{numerator}</div>
        <div className="tailwind-w-12 tailwind-border-b-2 tailwind-border-black tailwind-my-1"></div>
        <div className="tailwind-text-2xl tailwind-font-bold">
          {denominator}
        </div>
      </div>
    );
  };

  // Lấy ký hiệu phép tính
  const getOperationSymbol = () => {
    switch (operation) {
      case 'add':
        return '+';
      case 'subtract':
        return '-';
      case 'multiply':
        return '×';
      case 'divide':
        return '÷';
      default:
        return '+';
    }
  };

  // Lấy tiêu đề phép tính
  const getOperationTitle = () => {
    switch (operation) {
      case 'add':
        return 'Phép cộng phân số';
      case 'subtract':
        return 'Phép trừ phân số';
      case 'multiply':
        return 'Phép nhân phân số';
      case 'divide':
        return 'Phép chia phân số';
      default:
        return 'Phép tính với phân số';
    }
  };

  // Lấy số đầu tiên trong phép tính
  const getFirstNumber = () => {
    return operation === 'multiply' ? 2 : 1;
  };

  // Kết quả tính toán
  const result = calculateResult();

  // Hiển thị nội dung
  const renderContent = () => {
    return (
      <div className="tailwind-p-6 tailwind-border tailwind-border-gray-200 tailwind-rounded">
        <Title level={4} className="tailwind-text-center tailwind-mb-6">
          {getOperationTitle()}
        </Title>

        <div className="tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-4 tailwind-mb-8">
          <div className="tailwind-text-2xl tailwind-font-bold">
            {getFirstNumber()}
          </div>
          <Text className="tailwind-text-2xl tailwind-font-bold">
            {getOperationSymbol()}
          </Text>
          {renderFraction(numerator, denominator)}
          <Text className="tailwind-text-2xl tailwind-font-bold">=</Text>
          {result.error ? (
            <Text className="tailwind-text-red-500 tailwind-text-lg">
              {result.error}
            </Text>
          ) : (
            renderFraction(result.numerator, result.denominator)
          )}
        </div>

        {showDecimal && !result.error && result.denominator !== 0 && (
          <div className="tailwind-text-center tailwind-mb-4">
            <Text className="tailwind-text-lg">
              Giá trị thập phân:{' '}
              <strong>
                {(result.numerator / result.denominator).toFixed(4)}
              </strong>
            </Text>
          </div>
        )}

        {isEditing && (
          <>
            <Divider />
            <div className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-4">
              <div>
                <Title level={5}>Phân số</Title>
                <div className="tailwind-flex tailwind-items-center tailwind-gap-4 tailwind-w-full">
                  <div className="tailwind-flex-1">
                    <Text strong>Tử số:</Text>
                    <Input
                      type="number"
                      value={numerator}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value)) {
                          handleParamChange({ numerator: value });
                        }
                      }}
                      className="tailwind-mt-1 tailwind-w-full"
                    />
                  </div>

                  <div className="tailwind-flex-1">
                    <Text strong>Mẫu số:</Text>
                    <Input
                      type="number"
                      value={denominator}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value)) {
                          handleParamChange({ denominator: value });
                        }
                      }}
                      className="tailwind-mt-1 tailwind-w-full"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="tailwind-mt-4">
              <Space direction="vertical" className="tailwind-w-full">
                <div>
                  <Text strong>Phép tính:</Text>
                  <Select
                    value={operation}
                    onChange={(value) =>
                      handleParamChange({ operation: value })
                    }
                    className="tailwind-w-full tailwind-mt-1"
                  >
                    <Option value="add">Cộng (+)</Option>
                    <Option value="subtract">Trừ (-)</Option>
                    <Option value="multiply">Nhân (×)</Option>
                    <Option value="divide">Chia (÷)</Option>
                  </Select>
                </div>

                <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                  <Text strong>Hiển thị giá trị thập phân:</Text>
                  <input
                    type="checkbox"
                    checked={showDecimal}
                    onChange={(e) =>
                      handleParamChange({ showDecimal: e.target.checked })
                    }
                    className="tailwind-ml-2"
                  />
                </div>
              </Space>
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <EngineContainer
      node={props}
      titleProps={{ text: 'Phép tính với phân số' }}
      showFullscreenButton={false}
      mainComponent={renderContent()}
    />
  );
};

export default withEdComponentParams(FractionDemoOperation);

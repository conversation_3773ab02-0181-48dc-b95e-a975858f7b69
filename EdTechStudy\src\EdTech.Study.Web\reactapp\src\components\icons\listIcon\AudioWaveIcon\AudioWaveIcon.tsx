// AudioWaveIcon.tsx
import React, { SVGProps } from 'react';
import './AudioWaveIcon.css';

export const AudioWaveIconBase: React.FC<SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      width="30"
      height="20"
      viewBox="0 0 18 12"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g className="audiowave-bar audiowave-bar-1">
        <path d="M0 5.99961C0 5.50236 0.40275 5.09961 0.9 5.09961C1.39725 5.09961 1.8 5.50236 1.8 5.99961V6.89961C1.8 7.39686 1.39725 7.79961 0.9 7.79961C0.40275 7.79961 0 7.39686 0 6.89961V5.99961Z" />
      </g>
      <g className="audiowave-bar audiowave-bar-2">
        <path d="M2.7 5.09961C2.7 4.60236 3.10275 4.19961 3.6 4.19961C4.09725 4.19961 4.5 4.60236 4.5 5.09961V7.79961C4.5 8.29686 4.09725 8.69961 3.6 8.69961C3.10275 8.69961 2.7 8.29686 2.7 7.79961V5.09961Z" />
      </g>
      <g className="audiowave-bar audiowave-bar-3">
        <path d="M5.4 1.49961C5.4 1.00236 5.80275 0.599609 6.3 0.599609C6.79725 0.599609 7.2 1.00236 7.2 1.49961V10.4996C7.2 10.9969 6.79725 11.3996 6.3 11.3996C5.80275 11.3996 5.4 10.9969 5.4 10.4996V1.49961Z" />
      </g>
      <g className="audiowave-bar audiowave-bar-4">
        <path d="M8.1 4.19961C8.1 3.70236 8.50275 3.29961 9 3.29961C9.49725 3.29961 9.9 3.70236 9.9 4.19961V7.79961C9.9 8.29686 9.49725 8.69961 9 8.69961C8.50275 8.69961 8.1 8.29686 8.1 7.79961V4.19961Z" />
      </g>
      <g className="audiowave-bar audiowave-bar-5">
        <path d="M10.8 3.29961C10.8 2.80236 11.2027 2.39961 11.7 2.39961C12.1972 2.39961 12.6 2.80236 12.6 3.29961V9.59961C12.6 10.0969 12.1972 10.4996 11.7 10.4996C11.2027 10.4996 10.8 10.0969 10.8 9.59961V3.29961Z" />
      </g>
      <g className="audiowave-bar audiowave-bar-6">
        <path d="M13.5 5.09961C13.5 4.60236 13.9028 4.19961 14.4 4.19961C14.8973 4.19961 15.3 4.60236 15.3 5.09961V7.79961C15.3 8.29686 14.8973 8.69961 14.4 8.69961C13.9028 8.69961 13.5 8.29686 13.5 7.79961V5.09961Z" />
      </g>
      <g className="audiowave-bar audiowave-bar-7">
        <path d="M16.2 5.99961C16.2 5.50236 16.6028 5.09961 17.1 5.09961C17.5973 5.09961 18 5.50236 18 5.99961V6.89961C18 7.39686 17.5973 7.79961 17.1 7.79961C16.6028 7.79961 16.2 7.39686 16.2 6.89961V5.99961Z" />
      </g>
    </svg>
  );
};

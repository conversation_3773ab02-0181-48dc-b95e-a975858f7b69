import React, { useState } from 'react';
import { Button } from 'antd';
import { OrderingQuestion } from '../QuestionBankConfig';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Component SortableItem để sử dụng trong danh sách có thể sắp xếp
interface SortableItemProps {
  id: string;
  index: number;
  item: string;
  disabled?: boolean;
}

const SortableItem: React.FC<SortableItemProps> = ({ id, index, item }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`tailwind-p-3 tailwind-rounded-lg tailwind-border-2 tailwind-cursor-pointer tailwind-transition-all ${
        isDragging
          ? 'tailwind-bg-indigo-100 tailwind-border-indigo-500 tailwind-shadow-md'
          : 'tailwind-border-gray-200 hover:tailwind-border-indigo-200'
      }`}
    >
      <div className="tailwind-flex tailwind-items-center">
        <div className="tailwind-w-8 tailwind-h-8 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-rounded-full tailwind-mr-4 tailwind-font-medium tailwind-flex-shrink-0">
          {index + 1}
        </div>
        <span>{item}</span>
      </div>
    </div>
  );
};

interface OrderingQuestionComponentProps {
  question: OrderingQuestion;
  onAnswer: (answer: any) => void;
  disabled?: boolean;
}

const OrderingQuestionComponent: React.FC<OrderingQuestionComponentProps> = ({
  question,
  onAnswer,
  disabled = false,
}) => {
  const [items, setItems] = useState<string[]>(question.items);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    if (disabled) return;
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item === active.id);
      const newIndex = items.findIndex((item) => item === over.id);
      const newItems = arrayMove(items, oldIndex, newIndex);
      setItems(newItems);
    }
  };

  return (
    <div className="tailwind-space-y-4 tailwind-mb-4">
      <p className="tailwind-text-sm tailwind-text-gray-600 tailwind-mb-2">
        Kéo thả các mục để sắp xếp theo thứ tự đúng:
      </p>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          <div className="tailwind-space-y-2">
            {items.map((item, index) => (
              <SortableItem
                key={item}
                id={item}
                index={index}
                item={item}
                disabled={disabled}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
      <div className="tailwind-mt-6 tailwind-flex tailwind-justify-center">
        <Button
          onClick={() => {
            // Convert ordered items to their indices in the original question.items array
            const indices = items.map((item) => question.items.indexOf(item));
            onAnswer(indices);
          }}
          className="tailwind-px-4 tailwind-py-2 tailwind-bg-indigo-600 tailwind-text-white tailwind-font-medium tailwind-rounded-md tailwind-hover:tailwind-bg-indigo-700 tailwind-transition-colors tailwind-focus:tailwind-outline-none tailwind-focus:tailwind-ring-2 tailwind-focus:tailwind-ring-indigo-500 tailwind-focus:tailwind-ring-offset-2"
        >
          Kiểm tra đáp án
        </Button>
      </div>
    </div>
  );
};

export default OrderingQuestionComponent;

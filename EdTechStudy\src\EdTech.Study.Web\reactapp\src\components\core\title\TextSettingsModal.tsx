import React, { memo, useEffect } from 'react';
import { Space, Button, Tooltip, ColorPicker, InputNumber, theme } from 'antd';
import {
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  FontSizeOutlined,
} from '@ant-design/icons';
import { ITextProps } from './CoreTitle';

const { useToken } = theme;

interface ITextSettingsProps {
  settings: ITextProps;
  onUpdate: (updates: Partial<ITextProps>) => void;
  onColorPickerChange?: (open: boolean) => void;
  isColorDisabled?: boolean; // Vô hiệu hóa chỉnh sửa màu sắc
  isFormattingDisabled?: boolean; // Vô hiệu hóa định dạng văn bản
}

const TextSettings: React.FC<ITextSettingsProps> = ({
  settings,
  onUpdate,
  onColorPickerChange,
  isColorDisabled = false,
  isFormattingDisabled = false,
}) => {
  const { token } = useToken();
  const {
    fontSize,
    color,
    align,
    bold = false,
    italic = false,
    underline = false,
  } = settings;
  const [showFontSize, setShowFontSize] = React.useState(false);
  const [colorPickerOpen, setColorPickerOpen] = React.useState(false);

  // Thông báo khi trạng thái ColorPicker thay đổi
  useEffect(() => {
    if (onColorPickerChange) {
      onColorPickerChange(colorPickerOpen);
    }
  }, [colorPickerOpen, onColorPickerChange]);

  // Styles
  const containerStyle: React.CSSProperties = {
    marginTop: token.marginXS,
    padding: token.paddingXXS,
    background: token.colorBgContainer,
    border: `1px solid ${token.colorBorder}`,
    display: 'inline-flex',
    alignItems: 'center',
  };

  const buttonStyle: React.CSSProperties = {
    width: 32,
    height: 32,
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  const activeButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    color: token.colorPrimary,
    background: token.colorFillQuaternary,
  };

  const disabledButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    color: token.colorTextDisabled,
    cursor: 'not-allowed',
  };

  const dividerStyle: React.CSSProperties = {
    width: 1,
    height: 20,
    background: token.colorSplit,
    margin: `0 ${token.marginXXS}px`,
    alignSelf: 'center',
  };

  // Giúp xác định style cho các button dựa trên trạng thái active và disabled
  const getButtonStyle = (isActive: boolean, isDisabled = false) => {
    if (isDisabled) return disabledButtonStyle;
    return isActive ? activeButtonStyle : buttonStyle;
  };
  return (
    <div style={containerStyle} className="text-settings-modal">
      <Space.Compact block>
        {/* Định dạng văn bản */}
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'In đậm'}>
          <Button
            type="text"
            icon={<BoldOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) onUpdate({ bold: !bold });
            }}
            style={getButtonStyle(bold, isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'In nghiêng'}>
          <Button
            type="text"
            icon={<ItalicOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) onUpdate({ italic: !italic });
            }}
            style={getButtonStyle(italic, isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'Gạch chân'}>
          <Button
            type="text"
            icon={<UnderlineOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) onUpdate({ underline: !underline });
            }}
            style={getButtonStyle(underline, isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>

        <div style={dividerStyle} />

        {/* Căn chỉnh văn bản */}
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'Căn trái'}>
          <Button
            type="text"
            icon={<AlignLeftOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) onUpdate({ align: 'left' });
            }}
            style={getButtonStyle(align === 'left', isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'Căn giữa'}>
          <Button
            type="text"
            icon={<AlignCenterOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) onUpdate({ align: 'center' });
            }}
            style={getButtonStyle(align === 'center', isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'Căn phải'}>
          <Button
            type="text"
            icon={<AlignRightOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) onUpdate({ align: 'right' });
            }}
            style={getButtonStyle(align === 'right', isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>

        <div style={dividerStyle} />

        {/* Cỡ chữ */}
        <Tooltip title={isFormattingDisabled ? 'Khóa định dạng' : 'Cỡ chữ'}>
          <Button
            type="text"
            icon={<FontSizeOutlined />}
            onClick={() => {
              if (!isFormattingDisabled) setShowFontSize(!showFontSize);
            }}
            style={getButtonStyle(showFontSize, isFormattingDisabled)}
            disabled={isFormattingDisabled}
          />
        </Tooltip>

        {showFontSize && !isFormattingDisabled && (
          <InputNumber
            min={12}
            max={72}
            value={
              typeof fontSize === 'number'
                ? fontSize
                : parseInt(fontSize || '24')
            }
            onChange={(value) => {
              onUpdate({ fontSize: value ?? 24 });
            }}
            style={{
              width: 60,
              height: 32,
              margin: '0 4px',
              background: 'transparent',
            }}
            controls={false}
          />
        )}

        {/* Màu sắc */}
        {!isColorDisabled && (
          <ColorPicker
            value={color}
            onChange={(value) => {
              onUpdate({ color: value.toHexString() });
            }}
            style={{ marginLeft: 4 }}
            className="text-settings-color-picker"
            // Thêm thuộc tính để đảm bảo ColorPicker hoạt động đúng
            open={colorPickerOpen}
            onOpenChange={(open) => {
              setColorPickerOpen(open);
            }}
            panelRender={(panel) => {
              // Thêm class cho panel của ColorPicker để dễ dàng xử lý sự kiện
              return (
                <div className="text-settings-color-picker-panel">{panel}</div>
              );
            }}
          />
        )}
      </Space.Compact>
    </div>
  );
};

export default memo(TextSettings);

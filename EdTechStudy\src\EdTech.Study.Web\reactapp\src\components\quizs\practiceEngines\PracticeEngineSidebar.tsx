import React from 'react';
import {
  Button,
  Typography,
  Space,
  Row,
  Col,
  Divider,
  List,
  Tooltip,
} from 'antd';
import {
  CloseOutlined,
  FlagOutlined,
  QuestionCircleOutlined,
  MenuOutlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  SaveOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import { FloatButton } from 'antd';
import './PracticeEngineSidebar.css';

const { Title, Text } = Typography;

interface PracticeEngineSidebarProps {
  visible: boolean;
  onClose: () => void;
  questions: BaseQuestion[];
  onSelectQuestion: (questionId: string) => void;
  currentQuestionId?: string;
  configMode?: boolean;
  onAddQuestion?: () => void;
  onSave?: () => void;
}

const PracticeEngineSidebar: React.FC<PracticeEngineSidebarProps> = ({
  visible,
  onClose,
  questions,
  onSelectQuestion,
  currentQuestionId,
  configMode = false,
  onAddQuestion,
  onSave,
}) => {
  const getQuestionTypeIcon = (type: string) => {
    switch (type) {
      case 'multiple-choice':
        return <QuestionCircleOutlined style={{ color: '#1890ff' }} />;
      case 'fill-blanks':
        return <EditOutlined style={{ color: '#52c41a' }} />;
      case 'matching':
        return <FlagOutlined style={{ color: '#fa8c16' }} />;
      default:
        return <QuestionCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getQuestionStatus = (question: BaseQuestion) => {
    if (!question.isCompleted) return null;
    return question.status === 'correct' ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    );
  };

  return (
    <>
      {!configMode && (
        <FloatButton
          icon={<MenuOutlined />}
          type="primary"
          style={{ right: visible ? 340 : 24 }}
          onClick={onClose}
        />
      )}
      <div
        className={`practice-engine-sidebar ${
          configMode ? 'config-mode' : visible ? 'visible' : ''
        }`}
      >
        <div className="practice-engine-sidebar-header">
          <Title level={4} style={{ margin: 0 }}>
            {configMode ? 'Cấu hình bài thực hành' : 'Danh sách câu hỏi'}
          </Title>
          {configMode ? (
            onSave && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={onSave}
                className="save-button"
              >
                Lưu
              </Button>
            )
          ) : (
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={onClose}
              aria-label="Đóng menu"
            />
          )}
        </div>

        <div className="practice-engine-sidebar-content">
          {configMode ? (
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="config-header">
                <Text strong>Danh sách câu hỏi</Text>
                {onAddQuestion && (
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={onAddQuestion}
                  >
                    Thêm câu hỏi
                  </Button>
                )}
              </div>

              <List
                className="question-list"
                dataSource={questions}
                renderItem={(question, index) => (
                  <List.Item
                    className={`question-list-item ${
                      currentQuestionId === question.id ? 'active' : ''
                    }`}
                    onClick={() => onSelectQuestion(question.id)}
                  >
                    <div className="question-item-content">
                      <div className="question-number">{index + 1}</div>
                      <div className="question-icon">
                        {getQuestionTypeIcon(question.type)}
                      </div>
                      <div className="question-title">
                        {question.title || `Câu hỏi ${index + 1}`}
                      </div>
                      <div className="question-status">
                        {getQuestionStatus(question)}
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </Space>
          ) : (
            <Space direction="vertical" style={{ width: '100%' }}>
              <Row gutter={[8, 8]}>
                {questions.map((question, index) => (
                  <Col span={8} key={question.id}>
                    <Button
                      type={
                        currentQuestionId === question.id
                          ? 'primary'
                          : 'default'
                      }
                      onClick={() => {
                        onSelectQuestion(question.id);
                        onClose();
                      }}
                      className={
                        currentQuestionId === question.id
                          ? 'flex-1 bg-blue-500 hover:bg-blue-600'
                          : ''
                      }
                      style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '8px 4px',
                      }}
                    >
                      {question.isCompleted ? (
                        <FlagOutlined
                          style={{ fontSize: '18px', marginBottom: '4px' }}
                        />
                      ) : (
                        <QuestionCircleOutlined
                          style={{ fontSize: '18px', marginBottom: '4px' }}
                        />
                      )}

                      <div style={{ fontSize: '12px', lineHeight: '1.2' }}>
                        {index + 1}
                      </div>
                    </Button>
                  </Col>
                ))}
              </Row>
            </Space>
          )}
        </div>
      </div>
    </>
  );
};

export default PracticeEngineSidebar;

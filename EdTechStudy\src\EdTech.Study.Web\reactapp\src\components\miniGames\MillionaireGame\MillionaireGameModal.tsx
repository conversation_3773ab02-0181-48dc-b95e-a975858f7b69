import React, { useState } from 'react';
import {
  MillionaireGameConfig,
  LifelineType,
  PrizeLevel,
} from './MillionaireGameConfig';
import {
  Question,
  MultipleChoiceQuestion,
} from '../../common/QuestionComponent/QuestionBankConfig';
import {
  QuestionListComponent,
  QuestionFormModal,
} from '../../common/QuestionComponent';
import {
  Tabs,
  Form,
  Input,
  Button,
  InputNumber,
  Checkbox,
  Typography,
  Space,
  Table,
  Card,
  message,
  Alert,
} from 'antd';
import {
  InfoCircleOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  TrophyOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { handleQuestionReordering } from '../../common/QuestionComponent/questionOrderUtils';
import { DeleteIcon } from '../../icons/IconIndex';

const { TextArea } = Input;
const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

// Fix for the handleConfigChange function
type ConfigChangeEvent = {
  target: {
    name: string;
    value: string | number | boolean;
    type?: string;
    checked?: boolean;
  };
};

// Xác định lại component Tabs để sử dụng TabType
type TabType = 'basic' | 'questions' | 'advanced' | 'prizes';

interface MillionaireGameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: MillionaireGameConfig) => void;
  defaultConfig: MillionaireGameConfig;
}

const MillionaireGameModal: React.FC<MillionaireGameModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig,
}) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<TabType>('basic');

  // State for game configuration
  const [config, setConfig] = useState<MillionaireGameConfig>(defaultConfig);

  // State for new prize level input
  const [newPrize, setNewPrize] = useState<PrizeLevel>({
    level: config.prizeTree.length + 1,
    amount: 0,
    isMilestone: false,
    awardType: config.awardType || '',
  });

  // Updated state management for question editing
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | undefined>(
    undefined
  );
  const [editingQuestionIndex, setEditingQuestionIndex] = useState<number>(-1);

  // Handle input changes for basic configuration
  const handleConfigChange = (
    e:
      | React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
      | ConfigChangeEvent
  ) => {
    const target = 'target' in e ? e.target : (e as any).target;
    const { name, value, type } = target;
    const checked = target.checked;

    if (type === 'checkbox') {
      setConfig((prev) => ({ ...prev, [name]: checked }));
    } else {
      setConfig((prev) => ({
        ...prev,
        [name]: type === 'number' ? Number(value) : value,
      }));
    }
  };

  // Handle lifeline toggle
  const handleLifelineToggle = (lifeline: LifelineType) => {
    setConfig((prev) => {
      // Check if the lifeline is already in the array
      const isSelected = prev.lifelines.includes(lifeline);

      if (isSelected) {
        // Remove the lifeline
        return {
          ...prev,
          lifelines: prev.lifelines.filter((l) => l !== lifeline),
        };
      } else {
        // Add the lifeline
        return {
          ...prev,
          lifelines: [...prev.lifelines, lifeline],
        };
      }
    });
  };

  // Handle prize milestone toggle correctly
  const toggleMilestone = (index: number) => {
    const newConfig = { ...config };
    newConfig.prizeTree[index].isMilestone =
      !newConfig.prizeTree[index].isMilestone;
    setConfig(newConfig);
  };

  // Handle input changes for prize levels
  const handlePrizeChange = (
    e: React.ChangeEvent<HTMLInputElement> | { target: any },
    index?: number
  ) => {
    const { name, value, checked } = e.target;

    if (index !== undefined) {
      // Update existing prize
      setConfig((prev) => {
        const newPrizeTree = [...prev.prizeTree];

        if (name === 'amount') {
          newPrizeTree[index].amount = Number(value);
        } else if (name === 'isMilestone') {
          toggleMilestone(index);
        } else if (name === 'awardType') {
          newPrizeTree[index].awardType = value;
        }

        return { ...prev, prizeTree: newPrizeTree };
      });
    } else {
      // Update new prize form
      setNewPrize((prev) => ({
        ...prev,
        [name]:
          name === 'amount'
            ? Number(value)
            : name === 'isMilestone'
            ? checked
            : name === 'level'
            ? Number(value)
            : value,
      }));
    }
  };

  // Handle adding a new prize level
  const handleAddPrize = () => {
    // Validate
    if (!newPrize.amount) {
      alert('Vui lòng nhập giá trị phần thưởng');
      return;
    }

    // Add to config
    setConfig((prev) => ({
      ...prev,
      prizeTree: [...prev.prizeTree, { ...newPrize }],
    }));

    // Reset form and update level
    setNewPrize({
      level: config.prizeTree.length + 2, // Next level
      amount: 0,
      isMilestone: false,
      awardType: config.awardType || '',
    });
  };

  // Handle removing a prize level
  const handleRemovePrize = (index: number) => {
    setConfig((prev) => {
      const newPrizeTree = prev.prizeTree.filter((_, i) => i !== index);

      // Update levels
      newPrizeTree.forEach((prize, i) => {
        prize.level = i + 1;
      });

      return { ...prev, prizeTree: newPrizeTree };
    });

    // Update new prize form level
    setNewPrize((prev) => ({
      ...prev,
      level: config.prizeTree.length,
    }));
  };

  // Handle showing the add question form
  const handleShowAddQuestionForm = () => {
    // Prevent form submission
    setEditingQuestion(undefined);
    setEditingQuestionIndex(-1);
    setShowQuestionForm(true);
  };

  // Handle editing a question
  const handleEditQuestion = (question: Question, index: number) => {
    // Prevent form submission
    setEditingQuestion(question);
    setEditingQuestionIndex(index);
    setShowQuestionForm(true);
  };

  // Handle deleting a question
  const handleDeleteQuestion = (questionIds: string[]) => {
    const updatedQuestions = config.questions.filter(
      (question) => !questionIds.includes(question.id)
    );
    setConfig({
      ...config,
      questions: updatedQuestions,
    });
  };

  // Handle closing the question form
  const handleCloseQuestionForm = () => {
    setShowQuestionForm(false);
    setEditingQuestion(undefined);
    setEditingQuestionIndex(-1);
  };

  // Add handler for reordering questions
  const handleReorderQuestions = (reorderedQuestions: Question[]) => {
    // Use the utility function to handle reordering properly with filtered questions
    const updatedQuestions = handleQuestionReordering(
      config.questions,
      reorderedQuestions
    );

    // Update the config with the new question order
    setConfig((prev) => {
      const newConfig = {
        ...prev,
        questions: updatedQuestions,
      };
      return newConfig;
    });
  };

  // Handle saving a question (new or edited)
  const handleSaveQuestion = (question: Question) => {
    // Prevent form submission

    // Only accept multiple choice questions
    if (question.type !== 'multiplechoice') {
      message.error('Trò chơi Ai Là Triệu Phú chỉ hỗ trợ câu hỏi trắc nghiệm!');
      return;
    }

    const updatedQuestions = [...config.questions];

    if (editingQuestionIndex >= 0) {
      // Update existing question
      updatedQuestions[editingQuestionIndex] = question;
    } else {
      // Add new question
      updatedQuestions.push(question);
    }

    setConfig({
      ...config,
      questions: updatedQuestions,
    });

    // Reset form state
    setShowQuestionForm(false);
    setEditingQuestion(undefined);
    setEditingQuestionIndex(-1);
  };

  // Render basic settings tab
  const renderBasicTab = () => {
    return (
      <div className="tailwind-space-y-6">
        <Paragraph type="secondary">
          Cấu hình cơ bản cho trò chơi Ai Là Triệu Phú.
        </Paragraph>

        <Form
          layout="vertical"
          className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-y-4 tailwind-gap-x-4 sm:tailwind-grid-cols-2"
        >
          {/* Title */}
          <Form.Item label="Tiêu đề" className="sm:tailwind-col-span-2">
            <Input
              name="title"
              id="title"
              value={config.titleProps.text || ''}
              onChange={(e) =>
                handleConfigChange({
                  target: { name: 'title', value: e.target.value },
                })
              }
              placeholder="Ví dụ: Ai Là Triệu Phú"
            />
          </Form.Item>

          {/* Description */}
          <Form.Item label="Mô tả" className="sm:tailwind-col-span-2">
            <TextArea
              name="description"
              id="description"
              rows={2}
              value={config.description || ''}
              onChange={(e) =>
                handleConfigChange({
                  target: { name: 'description', value: e.target.value },
                })
              }
            />
          </Form.Item>

          {/* Time limit */}
          <Form.Item label="Thời gian trả lời (giây)">
            <InputNumber
              name="timeLimit"
              id="timeLimit"
              min={5}
              value={config.timeLimit}
              onChange={(value) =>
                handleConfigChange({
                  target: { name: 'timeLimit', value: value || 30 },
                })
              }
              className="tailwind-w-full"
            />
          </Form.Item>

          {/* Award Type */}
          <Form.Item label="Loại phần thưởng">
            <Input
              name="awardType"
              id="awardType"
              value={config.awardType || ''}
              onChange={(e) =>
                handleConfigChange({
                  target: { name: 'awardType', value: e.target.value },
                })
              }
              placeholder="Ví dụ: Điểm, Tiền, Kẹo, Sách,..."
            />
          </Form.Item>

          {/* Game Options */}
          <div className="sm:tailwind-col-span-2">
            <Title level={5} className="tailwind-mb-2">
              Tùy chọn trò chơi
            </Title>
            <Space direction="vertical" className="tailwind-w-full">
              <Form.Item className="tailwind-mb-1">
                <Checkbox
                  checked={config.difficultyProgression}
                  onChange={(e) =>
                    handleConfigChange({
                      target: {
                        name: 'difficultyProgression',
                        value: e.target.checked,
                      },
                    })
                  }
                >
                  Tăng dần độ khó theo mức độ câu hỏi
                </Checkbox>
              </Form.Item>

              <Form.Item className="tailwind-mb-1">
                <Checkbox
                  checked={config.showExplanation}
                  onChange={(e) =>
                    handleConfigChange({
                      target: {
                        name: 'showExplanation',
                        value: e.target.checked,
                      },
                    })
                  }
                >
                  Hiển thị giải thích sau mỗi câu trả lời
                </Checkbox>
              </Form.Item>
            </Space>
          </div>
        </Form>

        {/* Lifelines */}
        <div className="tailwind-mt-6">
          <Title level={5} className="tailwind-mb-2">
            Quyền trợ giúp
          </Title>
          <Space direction="vertical" className="tailwind-w-full">
            <Checkbox
              checked={config.lifelines.includes('50-50')}
              onChange={() => handleLifelineToggle('50-50')}
            >
              50:50 (Loại bỏ hai đáp án sai)
            </Checkbox>

            <Checkbox
              checked={config.lifelines.includes('audience-poll')}
              onChange={() => handleLifelineToggle('audience-poll')}
            >
              Hỏi ý kiến khán giả
            </Checkbox>

            <Checkbox
              checked={config.lifelines.includes('phone-a-friend')}
              onChange={() => handleLifelineToggle('phone-a-friend')}
            >
              Gọi điện thoại cho người thân
            </Checkbox>
          </Space>
        </div>
      </div>
    );
  };

  // Render questions tab
  const renderQuestionsTab = () => {
    const multipleChoiceQuestions = config.questions.filter(
      (q) => q.type === 'multiplechoice'
    );

    const totalPrizeLevels = config.prizeTree.length;
    const questionCountClass =
      multipleChoiceQuestions.length > totalPrizeLevels
        ? 'tailwind-text-orange-500'
        : multipleChoiceQuestions.length === totalPrizeLevels
        ? 'tailwind-text-green-500'
        : 'tailwind-text-blue-500';

    return (
      <>
        <div className="mt-1">
          <Alert
            message={
              <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
                <span>
                  Tình trạng câu hỏi:
                  <span
                    className={`tailwind-font-bold tailwind-ml-2 ${questionCountClass}`}
                  >
                    {multipleChoiceQuestions.length} / {totalPrizeLevels}
                  </span>
                </span>
                {multipleChoiceQuestions.length !== totalPrizeLevels && (
                  <span className="tailwind-text-sm tailwind-text-gray-500">
                    {multipleChoiceQuestions.length < totalPrizeLevels
                      ? `Cần thêm ${
                          totalPrizeLevels - multipleChoiceQuestions.length
                        } câu hỏi để phù hợp với số mức phần thưởng`
                      : `Có ${
                          multipleChoiceQuestions.length - totalPrizeLevels
                        } câu hỏi dư so với số mức phần thưởng`}
                  </span>
                )}
              </div>
            }
            type={
              multipleChoiceQuestions.length > totalPrizeLevels
                ? 'warning'
                : multipleChoiceQuestions.length === totalPrizeLevels
                ? 'success'
                : 'info'
            }
            showIcon
            className="tailwind-mb-4"
          />
          <QuestionListComponent
            allowManualReordering={true}
            questions={config.questions}
            onEdit={handleEditQuestion}
            onDelete={handleDeleteQuestion}
            onAdd={handleShowAddQuestionForm}
            onReorder={handleReorderQuestions}
            tableHeight="300px"
            showFilters={true}
          />
        </div>
      </>
    );
  };

  // Render prizes tab
  const renderPrizesTab = () => {
    const multipleChoiceQuestions = config.questions.filter(
      (q) => q.type === 'multiplechoice'
    );

    const totalPrizeLevels = config.prizeTree.length;
    const questionCountClass =
      multipleChoiceQuestions.length > totalPrizeLevels
        ? 'tailwind-text-orange-500'
        : multipleChoiceQuestions.length === totalPrizeLevels
        ? 'tailwind-text-green-500'
        : 'tailwind-text-blue-500';

    const columns = [
      {
        title: 'Cấp độ',
        dataIndex: 'level',
        key: 'level',
        render: (level: number) => <span>{level}</span>,
      },
      {
        title: 'Giá trị',
        dataIndex: 'amount',
        key: 'amount',
        render: (_: any, record: PrizeLevel, index: number) => (
          <Space>
            <InputNumber
              name="amount"
              value={record.amount}
              onChange={(value) => {
                if (value !== null) {
                  handlePrizeChange(
                    {
                      target: {
                        name: 'amount',
                        value: value.toString(),
                        type: 'number',
                      },
                    },
                    index
                  );
                }
              }}
              min={0}
            />
          </Space>
        ),
      },
      {
        title: 'Loại phần thưởng',
        dataIndex: 'awardType',
        key: 'awardType',
        render: (_: any, record: PrizeLevel, index: number) => (
          <Input
            value={record.awardType || config.awardType || ''}
            onChange={(e) => {
              handlePrizeChange(
                {
                  target: {
                    name: 'awardType',
                    value: e.target.value,
                  },
                },
                index
              );
            }}
            placeholder="Ví dụ: Điểm, Tiền,..."
            style={{ width: 120 }}
          />
        ),
      },
      {
        title: 'Mốc an toàn',
        dataIndex: 'isMilestone',
        key: 'isMilestone',
        render: (_: any, record: PrizeLevel, index: number) => (
          <Checkbox
            checked={record.isMilestone}
            onChange={(e) => {
              handlePrizeChange(
                {
                  target: {
                    name: 'isMilestone',
                    checked: e.target.checked,
                    type: 'checkbox',
                  },
                },
                index
              );
            }}
          />
        ),
      },
      {
        title: 'Thao tác',
        key: 'action',
        align: 'right' as const,
        render: (_: any, _record: PrizeLevel, index: number) => (
          <Button
            type="link"
            danger
            onClick={(e) => {
              e.preventDefault();
              handleRemovePrize(index);
            }}
          >
            <DeleteIcon /> Xóa
          </Button>
        ),
      },
    ];

    return (
      <div className="tailwind-space-y-6">
        <Paragraph type="secondary">
          Thiết lập các mức phần thưởng cho trò chơi.
        </Paragraph>

        <Alert
          message={
            <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
              <span>
                Mức phần thưởng:
                <span
                  className={`tailwind-font-bold tailwind-ml-2 ${questionCountClass}`}
                >
                  {totalPrizeLevels} / {multipleChoiceQuestions.length}
                </span>{' '}
                câu hỏi
              </span>
              {multipleChoiceQuestions.length !== totalPrizeLevels && (
                <span className="tailwind-text-sm tailwind-text-gray-500">
                  {totalPrizeLevels < multipleChoiceQuestions.length
                    ? `Cần thêm ${
                        multipleChoiceQuestions.length - totalPrizeLevels
                      } mức phần thưởng để phù hợp với số câu hỏi`
                    : `Có ${
                        totalPrizeLevels - multipleChoiceQuestions.length
                      } mức phần thưởng dư so với số câu hỏi`}
                </span>
              )}
            </div>
          }
          type={
            multipleChoiceQuestions.length > totalPrizeLevels
              ? 'warning'
              : multipleChoiceQuestions.length === totalPrizeLevels
              ? 'success'
              : 'info'
          }
          showIcon
          className="tailwind-mb-4"
        />

        {/* Prize levels list */}
        {config.prizeTree && config.prizeTree.length > 0 ? (
          <Table
            columns={columns}
            dataSource={config.prizeTree}
            rowKey={(record) => record.level.toString()}
            pagination={false}
            size="small"
            scroll={{ y: 300 }}
            className="tailwind-mb-6"
          />
        ) : (
          <Paragraph type="secondary" italic>
            Chưa có mức phần thưởng nào. Vui lòng thêm mức phần thưởng mới.
          </Paragraph>
        )}

        {/* New prize level form */}
        <Card
          title="Thêm mức phần thưởng mới"
          size="small"
          className="tailwind-bg-gray-50"
        >
          <Form
            layout="vertical"
            className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-4 tailwind-gap-4"
          >
            <Form.Item label="Cấp độ">
              <InputNumber
                name="level"
                id="level"
                value={newPrize.level}
                onChange={(value) => {
                  if (value !== null) {
                    handlePrizeChange({
                      target: {
                        name: 'level',
                        value: value.toString(),
                        type: 'number',
                      },
                    });
                  }
                }}
                className="tailwind-w-full"
                min={1}
                disabled
              />
            </Form.Item>
            <Form.Item label="Giá trị">
              <InputNumber
                name="amount"
                id="amount"
                value={newPrize.amount}
                onChange={(value) => {
                  if (value !== null) {
                    handlePrizeChange({
                      target: {
                        name: 'amount',
                        value: value.toString(),
                        type: 'number',
                      },
                    });
                  }
                }}
                className="tailwind-w-full"
                min={0}
              />
            </Form.Item>
            <Form.Item label="Loại phần thưởng">
              <Input
                value={newPrize.awardType || config.awardType || ''}
                onChange={(e) => {
                  handlePrizeChange({
                    target: {
                      name: 'awardType',
                      value: e.target.value,
                    },
                  });
                }}
                placeholder="Ví dụ: Điểm, Tiền,..."
                style={{ width: '100%' }}
              />
            </Form.Item>
            <Form.Item label="Mốc an toàn">
              <Checkbox
                name="isMilestone"
                id="isMilestone"
                checked={newPrize.isMilestone}
                onChange={(e) => {
                  handlePrizeChange({
                    target: {
                      name: 'isMilestone',
                      checked: e.target.checked,
                      type: 'checkbox',
                    },
                  });
                }}
              >
                Đây là mốc an toàn
              </Checkbox>
            </Form.Item>
          </Form>
          <div className="tailwind-flex tailwind-justify-end">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={(e) => {
                e.preventDefault();
                handleAddPrize();
              }}
            >
              Thêm mức phần thưởng
            </Button>
          </div>
        </Card>
      </div>
    );
  };

  // Render advanced settings tab
  const renderAdvancedTab = () => {
    return (
      <div className="tailwind-space-y-6">
        <Paragraph type="secondary">
          Cấu hình nâng cao cho trò chơi Ai Là Triệu Phú.
        </Paragraph>

        <Form
          layout="vertical"
          className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-y-4 tailwind-gap-x-4 sm:tailwind-grid-cols-2"
        >
          {/* Max questions */}
          <Form.Item
            label="Số câu hỏi tối đa"
            help="Để trống để sử dụng tất cả câu hỏi đã thêm"
          >
            <InputNumber
              name="maxQuestions"
              id="maxQuestions"
              min={1}
              value={config.maxQuestions}
              onChange={(value) => {
                if (value === null) {
                  // Handle null case (when input is cleared)
                  setConfig((prev) => ({ ...prev, maxQuestions: undefined }));
                } else {
                  handleConfigChange({
                    target: { name: 'maxQuestions', value, type: 'number' },
                  });
                }
              }}
              className="tailwind-w-full"
            />
          </Form.Item>

          {/* Shuffle questions */}
          <Form.Item label="Xáo trộn câu hỏi ngẫu nhiên">
            <Checkbox
              checked={config.shuffleQuestions}
              onChange={(e) =>
                handleConfigChange({
                  target: {
                    name: 'shuffleQuestions',
                    value: e.target.checked,
                    type: 'checkbox',
                    checked: e.target.checked,
                  },
                })
              }
            >
              Xáo trộn câu hỏi
            </Checkbox>
          </Form.Item>
        </Form>
      </div>
    );
  };

  // Handle submission of configuration
  const handleSubmit = () => {
    // Validate required fields
    if (!config.titleProps.text || !config.description) {
      message.error('Vui lòng điền đầy đủ tiêu đề và mô tả!');
      setActiveTab('basic');
      return;
    }

    // Filter questions to only include multiple choice questions
    const multipleChoiceQuestions = config.questions.filter(
      (q): q is MultipleChoiceQuestion => q.type === 'multiplechoice'
    );

    // Check if there are any valid questions
    if (multipleChoiceQuestions.length === 0) {
      message.error(
        'Trò chơi Ai Là Triệu Phú chỉ hỗ trợ câu hỏi trắc nghiệm. Vui lòng thêm ít nhất 1 câu hỏi trắc nghiệm!'
      );
      setActiveTab('questions');
      return;
    }

    // Check if there are more questions than prize levels
    if (multipleChoiceQuestions.length > config.prizeTree.length) {
      message.warning(
        `Số lượng câu hỏi (${multipleChoiceQuestions.length}) nhiều hơn số mức phần thưởng (${config.prizeTree.length}). Chỉ ${config.prizeTree.length} câu hỏi đầu tiên sẽ được sử dụng.`
      );
    }

    // Sort prize tree by level
    const sortedPrizeTree = [...config.prizeTree].sort(
      (a, b) => a.level - b.level
    );

    // Create final config with only multiple choice questions and sorted prize tree
    const finalConfig = {
      ...config,
      questions: multipleChoiceQuestions,
      prizeTree: sortedPrizeTree,
    };

    // Submit form
    onSubmit(finalConfig);
  };

  // If modal is closed, don't render anything
  if (!isOpen) return null;

  return (
    <ModalAntdCustom
      title="Cấu hình trò chơi"
      open={isOpen}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Hủy
        </Button>,
        <Button
          key="submit"
          className="tailwind-bg-blue-500"
          type="primary"
          onClick={handleSubmit}
        >
          Lưu cấu hình
        </Button>,
      ]}
    >
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabType)}
      >
        <TabPane
          tab={
            <>
              <InfoCircleOutlined /> Cấu hình cơ bản
            </>
          }
          key="basic"
        >
          {renderBasicTab()}
        </TabPane>
        <TabPane
          tab={
            <>
              <QuestionCircleOutlined /> Câu hỏi
            </>
          }
          key="questions"
        >
          {renderQuestionsTab()}
        </TabPane>
        <TabPane
          tab={
            <>
              <TrophyOutlined /> Giải thưởng
            </>
          }
          key="prizes"
        >
          {renderPrizesTab()}
        </TabPane>
        <TabPane
          tab={
            <>
              <SettingOutlined /> Nâng cao
            </>
          }
          key="advanced"
        >
          {renderAdvancedTab()}
        </TabPane>
      </Tabs>

      {/* Question Form Modal */}
      <QuestionFormModal
        isOpen={showQuestionForm}
        onClose={handleCloseQuestionForm}
        onSave={handleSaveQuestion}
        question={editingQuestion}
        title={editingQuestion ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới'}
        initialQuestionType="multiplechoice" // Only allow multiple choice
        restrictQuestionType="multiplechoice" // Force use of multiple choice questions
      />
    </ModalAntdCustom>
  );
};

export default MillionaireGameModal;

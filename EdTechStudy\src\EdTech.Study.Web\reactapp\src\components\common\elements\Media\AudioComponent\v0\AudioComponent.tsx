// AudioComponent.tsx
import React from 'react';
import { EngineContainer } from '../../../../engines';
import '../../MediaComponentStyles.css';
import { IEdTechRenderProps } from '../../../../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { AudioComponentProps } from '../shared/types';
import { defaultProps } from '../shared/constants';
import { AudioPlayer } from './components';
import { AudioProvider } from './context/AudioContext';

const AudioComponent: React.FC<IEdTechRenderProps<AudioComponentProps>> = (
  props
) => {
  const {
    params = defaultProps,
    isEditing,
    addOrUpdateParamComponent,
    path,
  } = props;

  // Merge with default props
  const config: AudioComponentProps = { ...defaultProps, ...params };

  // Render the main component content
  const renderMainComponent = () => {
    return (
      <AudioProvider
        addOrUpdateParamComponent={addOrUpdateParamComponent}
        isEditing={isEditing}
        params={params}
        path={path}
      >
        <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full">
          <AudioPlayer addOrUpdateParamComponent={addOrUpdateParamComponent} />
        </div>
      </AudioProvider>
    );
  };

  // Handle title update
  const handleTitleUpdate = (newTitleProps: any) => {
    addOrUpdateParamComponent({
      ...config,
      titleProps: newTitleProps,
    });
  };

  return (
    <EngineContainer
      node={props}
      mainComponent={renderMainComponent()}
      allowConfiguration={false}
      showFullscreenButton={true}
      id={props.path}
      titleProps={config.titleProps}
      onTitleUpdate={handleTitleUpdate}
      onFullscreenChange={() => {}}
    />
  );
};

export default withEdComponentParams(AudioComponent);

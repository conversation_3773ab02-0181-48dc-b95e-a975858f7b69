/* BoxTextComponent Styles */

/* Card styles */
.box-text-engine-card {
  border: none;
  box-shadow: none;
  border-radius: 8px;
  overflow: visible;
  width: 100%;
}

/* Box container */
.box-container {
  border: 1px solid var(--edtt-color-primary);
  background-color: #ffffff;
  border-radius: 8px;
  margin-top: 10px;
  will-change: width, height;
  box-sizing: border-box;
  position: relative;
}

/* Box alignment styles */
.box-align-left {
  align-items: flex-start;
}

.box-align-center {
  align-items: center;
}

.box-align-right {
  align-items: flex-end;
}

/* Text area styles */
.box-text-textarea {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-family: inherit;
  overflow: auto;
  box-sizing: border-box;
  padding: 0;
}

/* Text content in view mode */
.box-text-content {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  padding: 0;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  z-index: 10;
  transition: background-color 0.2s ease;
}


.resize-right {
  width: 10px;
  top: 0;
  right: -5px;
  height: 100%;
  cursor: ew-resize;
}

.resize-bottom {
  height: 10px;
  left: 0;
  bottom: -5px;
  width: 100%;
  cursor: ns-resize;
}

.resize-corner {
  width: 16px;
  height: 16px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background-color: var(--edtt-color-primary);
  cursor: nwse-resize;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Drag handle */
.drag-handle {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.drag-handle:hover {
  opacity: 1;
}

/* Configuration controls */
.box-controls, .text-controls {
  width: 100%;
  margin-bottom: 10px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

/* Quick toolbar */
.box-quick-toolbar {
  z-index: 20;
}

/* Simplified controls layout */
.controls-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.control-label {
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
  white-space: nowrap;
  color: #333333;
}

/* Color picker styles */
.color-picker-container {
  display: inline-flex;
  align-items: center;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  margin-right: 4px;
}
/**
 * Utility functions for editor selection and content handling
 */

/**
 * Check if content is empty
 * @param content HTML content to check
 * @returns true if content is empty, false otherwise
 */
export const isContentEmpty = (content: string): boolean => {
  if (!content) return true;

  // Check for special content like images, tables, audio, video, formulas, etc.
  const hasSpecialContent =
    content &&
    (content.includes('<img') ||
      content.includes('<table') ||
      content.includes('<audio') ||
      content.includes('<video') ||
      content.includes('<iframe') ||
      content.includes('figure') ||
      content.includes('katex-formula') ||
      content.includes('mathjax-formula') ||
      content.includes('math-latex') ||
      content.includes('data-latex') ||
      content.includes('\\(') || // LaTeX inline formula delimiter
      content.includes('\\[') || // LaTeX display formula delimiter
      content.includes('$$')); // LaTeX display formula delimiter

  // Check for text content after removing HTML tags
  const hasTextContent = content.replace(/<[^>]*>/g, '').trim() !== '';

  // Content is empty if it has no text and no special content
  return !hasSpecialContent && !hasTextContent;
};

/**
 * Save the current selection in the editor
 * @returns The saved Range object or null if selection couldn't be saved
 */
export const saveSelection = (): Range | null => {
  try {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const savedRange = selection.getRangeAt(0).cloneRange();

      // Store additional information about the selection position
      const editorElement = document.querySelector(
        '.e-richtexteditor .e-content'
      );
      if (editorElement) {
        // Store the active element to check if we're in the editor
        const activeElement = document.activeElement;
        const isInEditor = editorElement.contains(activeElement);

        // Store this information with the range
        (savedRange as any).isInEditor = isInEditor;
        (savedRange as any).activeElementTagName = activeElement
          ? activeElement.tagName
          : null;

        // Store information about the paragraph we're in
        const currentParagraph = selection.anchorNode
          ? selection.anchorNode.nodeType === 3
            ? selection.anchorNode.parentElement
            : (selection.anchorNode as Element)
          : null;

        if (currentParagraph) {
          (savedRange as any).paragraphHTML =
            currentParagraph.outerHTML;
        }
      }
      
      return savedRange;
    }
  } catch (error) {
    console.error('Error saving selection:', error);
  }
  
  return null;
};

/**
 * Restore a previously saved selection
 * @param savedRange The Range object to restore
 */
export const restoreSelection = (savedRange: Range | null): void => {
  try {
    if (savedRange) {
      // First focus the editor to ensure we're in the right context
      const editorElement = document.querySelector(
        '.e-richtexteditor .e-content'
      );
      if (editorElement) {
        (editorElement as HTMLElement).focus();

        // Small delay to ensure focus is complete
        setTimeout(() => {
          try {
            const selection = window.getSelection();
            if (selection) {
              // Check if the range is still valid
              try {
                // This will throw if the range is no longer valid
                const testNode = savedRange.startContainer;
                const isConnected = testNode && testNode.isConnected;

                if (isConnected) {
                  selection.removeAllRanges();
                  selection.addRange(savedRange);
                } else {
                  // If the range is no longer valid, just focus the editor
                  (editorElement as HTMLElement).focus();
                }
              } catch (rangeError) {
                // Just focus the editor if there's an error
                (editorElement as HTMLElement).focus();
              }
            }
          } catch (innerError) {
            console.error('Error in delayed selection restore:', innerError);
          }
        }, 10);
      }
    }
  } catch (error) {
    console.error('Error restoring selection:', error);
  }
};

/**
 * Insert HTML content at the current cursor position
 * @param formulaHtml HTML content to insert
 * @param editorRef Reference to the editor component
 * @param savedRange Previously saved selection range
 * @returns true if insertion was successful, false otherwise
 */
export const insertHtmlAtCursor = (
  formulaHtml: string,
  editorRef: any,
  savedRange: Range | null
): boolean => {
  try {
    // Make sure the editor is focused
    if (editorRef && typeof editorRef.focus === 'function') {
      editorRef.focus();
    }

    // Primary method: use editor's executeCommand
    if (editorRef?.executeCommand) {
      editorRef.executeCommand('insertHTML', formulaHtml);
      return true;
    }

    // Fallback method if executeCommand fails or is not available
    try {
      const editorElement = document.querySelector(
        '.e-richtexteditor .e-content'
      );
      if (!editorElement) {
        return false;
      }

      // Focus the editor element
      (editorElement as HTMLElement).focus();

      // Get current selection or use saved range
      const selection = window.getSelection();
      if (!selection) {
        return false;
      }

      // Use saved range if available, otherwise use current selection
      if (savedRange) {
        selection.removeAllRanges();
        selection.addRange(savedRange);
      }

      if (selection.rangeCount === 0) {
        return false;
      }

      try {
        // Create a temporary element and insert it at the current selection
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formulaHtml;

        const range = selection.getRangeAt(0);
        range.deleteContents();

        // Insert each child node individually to ensure proper insertion
        const fragment = document.createDocumentFragment();
        while (tempDiv.firstChild) {
          fragment.appendChild(tempDiv.firstChild);
        }

        range.insertNode(fragment);

        // Move cursor after the inserted formula
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);

        return true;
      } catch (domError) {
        console.error('Error inserting with DOM API:', domError);

        // Last resort fallback using deprecated execCommand
        try {
          // @ts-ignore - We're explicitly ignoring the TypeScript warning about deprecation
          document.execCommand('insertHTML', false, formulaHtml);
          return true;
        } catch (execError) {
          console.error('execCommand also failed:', execError);
          return false;
        }
      }
    } catch (fallbackError) {
      console.error('Fallback method failed:', fallbackError);
      return false;
    }
  } catch (error) {
    console.error('Error in insertHtmlAtCursor:', error);
    return false;
  }
};

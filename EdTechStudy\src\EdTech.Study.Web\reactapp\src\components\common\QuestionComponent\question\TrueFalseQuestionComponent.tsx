import React from 'react';
import { Button } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Question } from '../QuestionBankConfig';

interface TrueFalseQuestionProps {
  question: Question;
  onAnswer: (answer: boolean) => void;
  disabled?: boolean;
}

const TrueFalseQuestionComponent: React.FC<TrueFalseQuestionProps> = ({
  onAnswer,
  disabled = false,
}) => {
  return (
    <div className="tailwind-flex tailwind-flex-col sm:tailwind-flex-row tailwind-justify-start tailwind-gap-4 tailwind-mb-5">
      <Button
        type="default"
        size="large"
        disabled={disabled}
        icon={<CheckCircleOutlined className="tailwind-text-green-500" />}
        onClick={() => onAnswer(true)}
        className="tailwind-flex-1 tailwind-flex tailwind-items-center tailwind-justify-start"
      >
        Đúng
      </Button>
      <Button
        type="default"
        size="large"
        icon={<CloseCircleOutlined className="tailwind-text-red-500" />}
        onClick={() => onAnswer(false)}
        className="tailwind-flex-1 tailwind-flex tailwind-items-center tailwind-justify-start"
      >
        Sai
      </Button>
    </div>
  );
};

export default TrueFalseQuestionComponent;

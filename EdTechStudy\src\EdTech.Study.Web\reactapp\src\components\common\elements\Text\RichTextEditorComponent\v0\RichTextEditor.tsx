// React imports
import React, { useState, useRef, useMemo, useCallback } from 'react';

// Component imports
import CustomRichText, { CustomRichTextRef } from './CustomRichText';
import { EngineContainer } from '../../../../engines';

// Type imports
import { IEdTechRenderProps } from '../../../../../../interfaces/AppComponents';
import { ITextProps } from '../../../../../core/title/CoreTitle';
import { RichTextEditorProps } from '../shared/types';

// HOC and constants
import { withEdComponentParams } from '../../../../../../hocs/withEdComponentParams/withEdComponentParams';
import { defaultProps } from '../shared/constants';

const RichTextEditor: React.FC<IEdTechRenderProps<RichTextEditorProps>> = (
  props
) => {
  const { params, isEditing, addOrUpdateParamComponent, id } = props;

  // State for fullscreen mode
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  // Reference to the CustomRichText component
  const editorRef = useRef<CustomRichTextRef>(null);

  // Merge provided params with defaults
  const config: RichTextEditorProps = useMemo(() => {
    return params || defaultProps;
  }, [params]);

  // State for the editor content
  const [editorContent, setEditorContent] = useState<string>(
    config.content || ''
  );
  const toolbarSettingItems = useMemo(() => {
    if (config.toolbar && config.toolbar.items) {
      return config.toolbar?.items;
    }
    return defaultProps.toolbar?.items;
  }, [config.toolbar?.items]);

  // Configure quick toolbar settings for images, videos, audio, and links
  const quickToolbarSettings = {
    image: [
      'Align',
      'Caption',
      'Remove',
      'InsertLink',
      'OpenImageLink',
      '-',
      'EditImageLink',
      'RemoveImageLink',
      'AltText',
      'Dimension',
    ],
    video: ['VideoAlign', 'VideoRemove', 'VideoDimension'],
    audio: ['AudioReplace', 'AudioRemove', 'AudioLayoutOption'],
    link: ['Open', 'Edit', 'UnLink'],
  };

  // Configure image settings
  const insertImageSettings = {
    saveFormat: 'Base64',
    width: 'auto',
    height: 'auto',
    display: 'inline',
    maxFileSize: config.uploadConfig?.maxFileSize || 200 * 1024 * 1024, // 200MB default
    allowedTypes: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'],
    resize: true,
  };

  // Configure video settings
  const insertVideoSettings = {
    saveFormat: 'Blob',
    width: config.videoConfig?.width || '560px',
    height: config.videoConfig?.height || '315px',
    layoutOption: 'Break', // Always use Break layout for videos
    resize: config.videoConfig?.allowResize !== false, // Default to true if not specified
    allowedTypes: [
      '.mp4',
      '.webm',
      '.ogg',
      '.mov',
      '.avi',
      '.wmv',
      '.flv',
      '.m4v',
      '.mkv',
      '.3gp',
      '.mpg',
      '.mpeg',
    ],
    minWidth: '300px',
    minHeight: '200px',
    maxFileSize: config.videoConfig?.maxFileSize || 1000 * 1024 * 1024, // 1GB default
  };

  // Configure audio settings
  const insertAudioSettings = {
    saveFormat: 'Blob',
    layoutOption: 'Break', // Always use Break layout for audio
    resize: config.audioConfig?.allowResize !== false,
    allowedTypes: ['.mp3', '.wav', '.ogg', '.aac', '.wma', '.flac', '.m4a'],
    maxFileSize: config.audioConfig?.maxFileSize || 500 * 1024 * 1024, // 500MB default
  };

  // Handle content change - only updates local state
  const handleEditorChange = (args: any) => {
    if (!isEditing) return;
    setEditorContent(args.value);
  };
  // Handle editor blur - saves content to params
  const handleEditorBlur = () => {
    if (!isEditing) return;
    // Update the component params when editor loses focus
    addOrUpdateParamComponent({
      ...config,
      content: editorContent,
    });
  };

  // Handle title updates
  const handleTitleUpdate = useCallback(
    (titleProps: Partial<ITextProps>) => {
      if (addOrUpdateParamComponent) {
        addOrUpdateParamComponent({
          ...config,
          titleProps: {
            ...config.titleProps,
            ...titleProps,
          },
        });
      }
    },
    [config, addOrUpdateParamComponent]
  );

  // Handle fullscreen change
  const handleFullscreenChange = useCallback(
    (fullscreen: boolean) => {
      setIsFullscreen(fullscreen);

      // When entering fullscreen, focus the editor after a short delay
      // to ensure the UI has updated
      if (fullscreen && isEditing && editorRef.current) {
        setTimeout(() => {
          editorRef.current?.focus();
        }, 300);
      }
    },
    [isEditing]
  );

  // Calculate the appropriate height based on fullscreen state
  const editorHeight = isFullscreen ? 'calc(100vh - 150px)' : '800px';

  // Render the main component
  return (
    <EngineContainer
      node={props}
      mainComponent={
        <CustomRichText
          ref={editorRef}
          id={`rich-text-editor-${id || 'default'}`}
          name={`rich-text-editor-${id || 'default'}`}
          value={editorContent}
          onChangeValue={(_name: string, value: string) =>
            handleEditorChange({ value })
          }
          handleBur={handleEditorBlur}
          toolbarSettingItems={toolbarSettingItems}
          quickToolbarSettings={quickToolbarSettings}
          insertImageSettings={insertImageSettings as any}
          insertVideoSettings={insertVideoSettings as any}
          insertAudioSettings={insertAudioSettings as any}
          placeholder={config.placeholder || 'Nhập nội dung của bạn tại đây...'}
          height={editorHeight}
          maxLength={
            config.maxLength && config.maxLength > 0
              ? config.maxLength
              : undefined
          }
          showCharCount={!!(config.maxLength && config.maxLength > 0)}
          enableXhtml={true}
          htmlAttributes={{ 'data-sample': 'RichTextEditor' }}
          disabled={!isEditing}
          isEditing={isEditing}
        />
      }
      allowConfiguration={false}
      showFullscreenButton={true}
      id={id}
      titleProps={config.titleProps}
      onTitleUpdate={handleTitleUpdate}
      onFullscreenChange={handleFullscreenChange}
    />
  );
};

export default withEdComponentParams(RichTextEditor);

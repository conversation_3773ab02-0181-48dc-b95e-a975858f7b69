import React, { useState, useEffect, useRef, lazy } from 'react';
import {
  Card,
  Typography,
  Button,
  message,
  Progress,
  Space,
  Input,
} from 'antd';
import {
  CheckCircleOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  ArrowLeftOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  Question,
  QuizAnswer,
  QuizQuestion,
} from '../../../interfaces/quizs/questionBase';
import { FireworksComponent } from '.';
import { createQuizQuestionTemplate } from '../practiceEngines/questionTemplates';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { Guid } from 'guid-typescript';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

const { Title, Text } = Typography;

export const simpleQuizWrapperSchema = createComponentSchema({
  paramsSchema: {
    question: z.string().optional(),
    options: z.array(z.string()).optional(),
    correctAnswer: z.number().optional(),
    showFeedback: z.boolean().optional(),
  },
});

export interface ISimpleQuizWrapperProps {
  id: string;
  question?: QuizQuestion;
  showFireworks?: boolean;
  showTitle?: boolean;
  congratsMessage?: string;
  timeLimit?: number; // Time limit in seconds
  showTimer?: boolean; // Whether to show the timer
  autoSubmitOnTimeEnd?: boolean; // Whether to auto-submit when time ends
  countDirection?: 'up' | 'down'; // Count up or down
  timerPosition?: 'top' | 'bottom'; // Timer position
  startButtonText?: string; // Text for the start button
  startScreenTitle?: string; // Title text for start screen
  startScreenDescription?: string; // Description text for start screen
}

const QuizComponent = lazy(() => import('./QuizComponent'));

const SimpleQuizWrapper: React.FC<
  IEdTechRenderProps<ISimpleQuizWrapperProps>
> = (props) => {
  const {
    params = {} as ISimpleQuizWrapperProps, // Cast empty object to expected props type
    path,
    order,
    addOrUpdateParamComponent,
  } = props;

  // Extract values from params with defaults
  const {
    id = `quiz-${Date.now()}`,
    question: initialQuestion,
    showFireworks = true,
    showTitle = true,
    congratsMessage = 'Chúc mừng bạn đã hoàn thành câu hỏi!',
    timeLimit: timeLimitParam = 20,
    showTimer = true,
    autoSubmitOnTimeEnd = true,
    countDirection = 'down',
    timerPosition = 'top',
    startButtonText = 'Bắt đầu',
    startScreenTitle = 'Bạn đã sẵn sàng?',
    startScreenDescription = 'Nhấn nút bắt đầu để làm bài.',
  } = params;

  // Create a default question if none is provided
  const [question, setQuestion] = useState<QuizQuestion>(
    initialQuestion || createQuizQuestionTemplate('Câu hỏi trắc nghiệm')
  );
  const [configMode, setConfigMode] = useState<boolean>(false);
  const [completed, setCompleted] = useState(false);
  const [showCongrats, setShowCongrats] = useState(false);
  const [showFireworksEffect, setShowFireworksEffect] = useState(false);

  // Start screen state
  const [started, setStarted] = useState(false);

  // Timer states
  const [timeLimit, setTimeLimit] = useState<number>(timeLimitParam);
  const [timeRemaining, setTimeRemaining] = useState(timeLimitParam);
  const [timerActive, setTimerActive] = useState(false);
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const firstCreate = useRef<number>(0);

  const handleUpdateParams = (values: { [key: string]: any }) => {
    addOrUpdateParamComponent({
      id,
      question,
      showFireworks,
      showTimer,
      showTitle,
      congratsMessage,
      timeLimit: timeLimitParam,
      autoSubmitOnTimeEnd,
      countDirection,
      startButtonText,
      startScreenDescription,
      startScreenTitle,
      timerPosition,
      ...values,
    });
  };

  // Start the quiz
  const handleStart = () => {
    setStarted(true);
    setConfigMode(false);
    // Start timer if applicable
    if (timeLimit > 0 && showTimer) {
      startTimer();
    }
  };

  // Handle question completion
  const handleComplete = (
    questionId: string,
    selectedOption: QuizAnswer | undefined
  ) => {
    setCompleted(true);

    // Stop timer if it's running
    stopTimer();

    // Update the question status
    const updatedQuestion = {
      ...question,
      isCompleted: true,
      status: 'incorrect',
      userSelect: selectedOption,
    } as QuizQuestion;

    setQuestion(updatedQuestion);

    // Save the updated question state
    handleUpdateParams({ question: updatedQuestion });

    // Show congratulations if correct
    // if (correct) {
    //   setShowCongrats(true);
    //   if (showFireworks) {
    //     setShowFireworksEffect(true);
    //     setTimeout(() => {
    //       setShowFireworksEffect(false);
    //     }, 2000);
    //   }
    // }
  };

  // Reset the quiz
  const handleReset = () => {
    setCompleted(false);
    setShowCongrats(false);

    // Return to start screen
    setStarted(false);

    // Reset question status
    const resetQuestion = {
      ...question,
      isCompleted: false,
      status: undefined,
      userSelect: undefined,
    };

    setQuestion(resetQuestion);

    // Reset timer if applicable
    resetTimer();

    // Save the reset state
    handleUpdateParams({ question: resetQuestion });
  };

  // Timer management
  const startTimer = () => {
    if (timeLimit > 0 && !timerActive) {
      setTimerActive(true);
      setTimeRemaining(countDirection === 'down' ? timeLimit : 0);

      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          const newValue = countDirection === 'down' ? prev - 1 : prev + 1;

          // Check if time's up for countdown timer
          if (countDirection === 'down' && newValue <= 0) {
            clearInterval(timerRef.current as ReturnType<typeof setInterval>);
            setTimerActive(false);

            // Auto-submit if enabled
            if (autoSubmitOnTimeEnd && !completed) {
              handleTimeUp();
            }

            return 0;
          }

          // For countup, check if we've reached the limit
          if (
            countDirection === 'up' &&
            timeLimit > 0 &&
            newValue >= timeLimit
          ) {
            clearInterval(timerRef.current as ReturnType<typeof setInterval>);
            setTimerActive(false);

            // Auto-submit if enabled
            if (autoSubmitOnTimeEnd && !completed) {
              handleTimeUp();
            }

            return timeLimit;
          }

          return newValue;
        });
      }, 1000);
    }
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setTimerActive(false);
    }
  };

  const resetTimer = () => {
    stopTimer();
    setTimeRemaining(countDirection === 'down' ? timeLimit : 0);
  };

  // Handle time up
  const handleTimeUp = () => {
    // Find the correct answer
    const correctAnswer = question.answers.find((answer) => answer.isCorrect);

    // Auto-submit with the correct answer if we want to always show correct,
    // or with a wrong answer if we want to show failure
    handleComplete(question.id, correctAnswer);
    message.warning('Hết thời gian làm bài!');
  };

  // Update local state when props change
  useEffect(() => {
    if (initialQuestion) {
      setQuestion(initialQuestion);
      setCompleted(!!initialQuestion.isCompleted);
      setShowCongrats(initialQuestion.status === 'correct');

      // If question was already completed, show in completed state
      if (initialQuestion.isCompleted) {
        setStarted(true);
      }
    }
  }, [initialQuestion]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      stopTimer();
    };
  }, []);

  // Stop timer when question is completed
  useEffect(() => {
    if (completed && timerActive) {
      stopTimer();
    }
  }, [completed, timerActive]);

  useEffect(() => {
    if (firstCreate.current < 1 && !!!props.params?.question) {
      setConfigMode(true);
      setStarted(true);
      firstCreate.current = firstCreate.current + 1;
    }
  }, [props.params?.question]);

  // Format time for display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  // Calculate timer percentage for progress bar
  const calculateTimerPercentage = (): number => {
    if (timeLimit <= 0) return 0;

    if (countDirection === 'down') {
      return (timeRemaining / timeLimit) * 100;
    } else {
      return (timeRemaining / timeLimit) * 100;
    }
  };

  // Determine status color based on time remaining
  const getTimerStatus = (): 'success' | 'normal' | 'exception' | 'active' => {
    if (countDirection === 'down') {
      if (timeRemaining <= timeLimit * 0.25) return 'exception';
      if (timeRemaining <= timeLimit * 0.5) return 'normal';
      return 'success';
    } else {
      if (timeRemaining >= timeLimit * 0.75) return 'exception';
      if (timeRemaining >= timeLimit * 0.5) return 'normal';
      return 'success';
    }
  };

  // Render timer component
  const renderTimer = () => {
    if (!showTimer || timeLimit <= 0) return null;

    return (
      <div className="quiz-timer" style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ClockCircleOutlined style={{ marginRight: '8px' }} />
          <span style={{ marginRight: '16px' }}>
            Thời gian: {formatTime(timeRemaining)}
          </span>
          <Progress
            percent={calculateTimerPercentage()}
            status={getTimerStatus()}
            showInfo={false}
            style={{ flex: 1 }}
          />
        </div>
      </div>
    );
  };

  // Render start screen
  const renderStartScreen = () => {
    return (
      <Card
        className="start-card"
        style={{
          textAlign: 'center',
          padding: '24px',
        }}
      >
        <div className="flex justify-end">
          <Button
            type="default"
            onClick={() => {
              setConfigMode(true);
              setStarted(true);
            }}
          >
            <SettingOutlined />
          </Button>
        </div>
        <Title level={3}>{startScreenTitle}</Title>
        <Text
          style={{ fontSize: '16px', display: 'block', marginBottom: '24px' }}
        >
          {startScreenDescription}
        </Text>

        {timeLimit > 0 && showTimer && (
          <Text
            style={{ display: 'block', marginBottom: '16px', color: '#1677ff' }}
          >
            <ClockCircleOutlined style={{ marginRight: '8px' }} />
            Thời gian làm bài: {formatTime(timeLimit)}
          </Text>
        )}

        <Button
          type="primary"
          size="large"
          onClick={handleStart}
          className="flex-1 bg-blue-500 hover:bg-blue-600"
          icon={<ClockCircleOutlined />}
        >
          {startButtonText}
        </Button>
      </Card>
    );
  };

  function handleChangeQuestion(update: Question) {
    setConfigMode(false);
    setStarted(false);
    setQuestion(update as QuizQuestion);
    handleUpdateParams({
      id: id,
      question: update as QuizQuestion,
    });
  }

  function handleDeleteQuestion(id: string) {
    throw new Error('Function not implemented.');
  }

  // Main render
  return (
    <PracticeEngineContext.Provider
      value={{
        handleChangePosition: () => {},
        handleChangeQuestion: handleChangeQuestion,
        handleDeleteQuestion: handleDeleteQuestion,
        handleToggleFullscreen: () => {},
        isFullscreen: false,
      }}
    >
      <div
        id={id}
        className="simple-quiz-wrapper bg-white rounded-lg shadow-md p-5 max-w-6xl mx-auto"
        style={{ position: 'relative' }}
      >
        {showTitle && (
          <Title level={4} style={{ marginBottom: '16px' }}>
            {question.title}
          </Title>
        )}

        {!started ? (
          renderStartScreen()
        ) : (
          <>
            {/* Render timer at top position if configured */}
            {timerPosition === 'top' && renderTimer()}

            {showCongrats ? (
              <Card
                className="congrats-card"
                style={{
                  textAlign: 'center',
                  padding: '24px',
                  backgroundColor: '#f6ffed',
                  borderColor: '#b7eb8f',
                }}
              >
                <TrophyOutlined
                  style={{
                    fontSize: '48px',
                    color: '#52c41a',
                    marginBottom: '16px',
                  }}
                />
                <Title level={3} style={{ color: '#52c41a' }}>
                  <CheckCircleOutlined /> {congratsMessage}
                </Title>
                <Text
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    marginBottom: '24px',
                  }}
                >
                  Bạn đã trả lời chính xác câu hỏi. Tiếp tục cố gắng nhé!
                </Text>
                <Button
                  type="primary"
                  onClick={handleReset}
                  className="flex-1 bg-blue-500 hover:bg-blue-600"
                >
                  Làm lại
                </Button>
              </Card>
            ) : (
              <>
                <Space className="flex mb-2 justify-between">
                  {configMode && (
                    <Input
                      type="number"
                      value={timeLimit}
                      onChange={(e) => {
                        const value = Number.parseInt(e.target.value);
                        setTimeLimit(value ?? 0);
                        setTimeRemaining(value ?? 0);
                        handleUpdateParams({
                          id,
                          question: question,
                          timeLimit: value,
                        });
                      }}
                      placeholder="Thời gian làm"
                    />
                  )}
                  <Button
                    type="default"
                    onClick={() => {
                      handleReset();
                    }}
                  >
                    <LeftOutlined />
                  </Button>
                </Space>
                <QuizComponent
                  id={Guid.create().toString()}
                  question={question}
                  onComplete={handleComplete}
                  showFeedback={true}
                  allowManyTimes={true}
                  configMode={configMode}
                  disabled={timeRemaining === 0}
                />
              </>
            )}

            {/* Render timer at bottom position if configured */}
            {timerPosition === 'bottom' && renderTimer()}
          </>
        )}

        {/* Fireworks effect when answered correctly */}
        <FireworksComponent show={showFireworksEffect} />
      </div>
    </PracticeEngineContext.Provider>
  );
};

export default withEdComponentParams(SimpleQuizWrapper);

import React from 'react';
import { Button } from 'antd';
import { RightOutlined, ReloadOutlined } from '@ant-design/icons';
import { MatchingControlsProps } from '../../../../interfaces/quizs/mapping.interface';

const MatchingControls: React.FC<MatchingControlsProps> = ({
  onCheck,
  onReset,
  isCompleted,
  isChecking,
}) => {
  return (
    <div className="matching-controls tailwind-w-[max-content]">
      <Button
        type="primary"
        onClick={onCheck}
        className="tailwind-flex-1 tailwind-bg-blue-500 hover:tailwind-bg-blue-600"
        disabled={isChecking && isCompleted}
        icon={<RightOutlined />}
      >
        Kiểm tra
      </Button>
      <Button icon={<ReloadOutlined />} onClick={onReset}>
        Làm lại
      </Button>
    </div>
  );
};

export default MatchingControls;

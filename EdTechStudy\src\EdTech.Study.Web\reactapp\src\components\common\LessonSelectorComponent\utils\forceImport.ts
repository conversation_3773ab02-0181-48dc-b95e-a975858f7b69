import React from 'react';

export const forceImportComponent = (
  component: React.LazyExoticComponent<any>
) => {
  // LazyExoticComponent is not directly a Promise
  // We can trigger loading by using the _init property or creating a temporary component
  try {
    // Create a hidden React element to force preloading
    const preloadComponent = React.createElement(component, {});
    // This line doesn't actually render but forces React to start loading the component
    void Object.assign({}, preloadComponent);
    return true;
  } catch (error: unknown) {
    console.error('Error preloading component:', error);
    return false;
  }
};

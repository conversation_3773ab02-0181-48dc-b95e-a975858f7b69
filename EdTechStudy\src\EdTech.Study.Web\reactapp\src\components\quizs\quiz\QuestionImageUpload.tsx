import React, { useState, useRef } from 'react';
import { Image, Button, message, Input } from 'antd';
import { UploadOutlined, LoadingOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import FileApi from '../../../api/fileApi';
import { DeleteIcon } from '../../icons/IconIndex';
interface QuestionImageUploadProps {
  imageUrl?: string;
  imageAlt?: string;
  onImageChange: (
    imageUrl: string | undefined,
    imageAlt: string | undefined
  ) => void;
  disabled?: boolean;
}

const QuestionImageUpload: React.FC<QuestionImageUploadProps> = ({
  imageUrl,
  imageAlt,
  onImageChange,
  disabled = false,
}) => {
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef<InputRef>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      message.error('Please upload an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      message.error('File size should not exceed 5MB');
      return;
    }

    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      const response = await FileApi.Upload(formData);
      if (response && response.data.url) {
        onImageChange(response.data.url, file.name);
        message.success('Image uploaded successfully');
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Upload error:', error);
      message.error('Failed to upload image');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadClick = () => {
    if (fileInputRef.current?.input) {
      fileInputRef.current.input.click();
    }
  };

  const handleRemove = () => {
    onImageChange(undefined, undefined);
  };
  return (
    <div className="question-image-container" style={{ marginBottom: '1rem' }}>
      {imageUrl ? (
        <div style={{ position: 'relative', display: 'inline-block' }}>
          <Image
            src={
              import.meta.env.PROD
                ? `/${imageUrl}`
                : `https://localhost:44348/${imageUrl}`
            }
            alt={imageAlt || 'Question image'}
            style={{
              maxWidth: '100%',
              maxHeight: '300px',
              objectFit: 'contain',
            }}
          />
          {!disabled && (
            <Button
              icon={<DeleteIcon />}
              onClick={handleRemove}
              danger
              style={{
                position: 'absolute',
                top: 8,
                right: 8,
                background: 'rgba(255, 255, 255, 0.8)',
              }}
            />
          )}
        </div>
      ) : !disabled ? (
        <div style={{ textAlign: 'center' }}>
          <Input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            style={{ display: 'none' }}
            ref={fileInputRef}
          />
          <Button
            icon={loading ? <LoadingOutlined /> : <UploadOutlined />}
            loading={loading}
            style={{ width: '100%' }}
            onClick={handleUploadClick}
          >
            Tải ảnh lên
          </Button>
        </div>
      ) : null}
    </div>
  );
};

export default QuestionImageUpload;

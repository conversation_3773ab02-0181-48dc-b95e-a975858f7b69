import React, { useContext, useEffect, useMemo, useState, useRef } from 'react';
import {
  Card,
  Button,
  Row,
  Col,
  message,
  Tooltip,
  PopconfirmProps,
  Input,
  Switch,
  Typography,
  Form,
  Space,
  Collapse,
} from 'antd';
import FireworksComponent from '../quiz/FireworksComponent';
import {
  DeleteFilled,
  QuestionOutlined,
  PlusOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  DragOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import './MatchingComponent.css';
import {
  MatchingQuestion,
  MatchingItemType,
  PracticeEngineContext,
  QuestionComponentBaseProps,
  MatchingQuestionAnswer,
} from '../../../interfaces/quizs/questionBase';

// Import custom components
import ClickableItem from './components/ClickableItem';
import MatchingControls from './components/MatchingControls';
import MatchingFeedback from './components/MatchingFeedback';
import ConnectionLines from './components/ConnectionLines';

// Import custom hooks
import { useMatchingLogic } from './hooks/useMatchingLogic';
import { useMatchResults } from './hooks/useMatchResults';
import { Guid } from 'guid-typescript';
import practiceLocalization, { quizLocalization } from '../localization';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';

// Import ReactQuill and needed modules/formats
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import DOMPurify from 'dompurify';

const { TextArea } = Input;
const { Text } = Typography;

// Define modules for the rich text editor
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image'],
    ['clean'],
  ],
};

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
];

export interface MatchingComponentProps extends QuestionComponentBaseProps {
  question: MatchingQuestion;
  onComplete?: (questionId: string, userSelect: any) => void;
}

const MatchingComponent: React.FC<MatchingComponentProps> = ({
  id: externalId,
  question,
  showFeedback = true,
  configMode = false,
  hideSaveButton = false,
  onComplete,
}) => {
  const [showFireworks, setShowFireworks] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const {
    handleChangeQuestion,
    handleDeleteQuestion: externalHandleDeleteQuestion,
  } = useContext(PracticeEngineContext);

  const [focus, setFocus] = useState<string | null>(null);
  const [resetTrigger, setResetTrigger] = useState(false);
  const [editableQuestion, setEditableQuestion] =
    useState<MatchingQuestion>(question);
  const id = useMemo(
    () => externalId || Guid.create().toString(),
    [externalId]
  );

  // Use custom hooks with configMode parameter
  const {
    leftItems,
    rightItems,
    leftIds,
    rightIds,
    selectedItemId,
    pairedItems,
    isItemPaired,
    getPairColor,
    handleItemClick,
    getPairs,
    resetExercise,
  } = useMatchingLogic({
    id: id,
    question,
    resetExternalTrigger: resetTrigger,
    configMode: configMode,
    handleChangeAnswer,
  });

  const { isChecking, completed, correctCount, checkAnswers, getMatchResult } =
    useMatchResults({
      id,
      question,
      leftIds,
      leftItems,
      rightItems,
      pairedItems,
      getPairs,
      onComplete: (isCorrect: boolean) => {
        if (isCorrect) {
          setTimeout(() => {
            setShowFireworks(true);
          }, 200);
        }
      },
    });

  // Handle completion and submit user answer
  function handleChangeAnswer(answer: MatchingQuestionAnswer) {
    if (onComplete) {
      const userAnswer = getPairs().map(
        (pair) =>
          ({
            id: Guid.create().toString(),
            left: pair.left,
            right: pair.right,
          } as MatchingQuestionAnswer)
      );
      let isNew = !userAnswer.some((item) => item.left === answer.left);
      if (isNew) {
        onComplete(question.id, [...userAnswer, answer]);
      } else {
        onComplete(question.id, userAnswer);
      }
    }
  }

  const { handleUpdateQuestion } = useUpdateQuestion();

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.id);
  };

  const handleResetExercise = () => {
    resetExercise();
    setResetTrigger((prev) => !prev);
  };

  // Config Mode Methods
  const handleUpdateTitle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedQuestion = {
      ...editableQuestion,
      title: e.target.value,
    };
    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleUpdateDescription = (content: string) => {
    const updatedQuestion = {
      ...editableQuestion,
      description: content,
    };
    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleToggleShuffle = (checked: boolean) => {
    const updatedQuestion = {
      ...editableQuestion,
      shuffleItems: checked,
    };
    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleUpdateItemContent = (
    side: 'left' | 'right',
    index: number,
    content: string
  ) => {
    const updatedQuestion = { ...editableQuestion };
    const itemsToUpdate =
      side === 'left' ? updatedQuestion.leftItems : updatedQuestion.rightItems;

    if (index < 0 || index >= itemsToUpdate.length) return;

    itemsToUpdate[index] = {
      ...itemsToUpdate[index],
      content,
    };

    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleAddItem = () => {
    const updatedQuestion = { ...editableQuestion };
    // Create new item pairs with matching IDs
    const leftId = `${editableQuestion.id}L${
      updatedQuestion.leftItems.length + 1
    }`;
    const rightId = `${editableQuestion.id}R${
      updatedQuestion.rightItems.length + 1
    }`;

    // Add new item to left column
    updatedQuestion.leftItems.push({
      id: leftId,
      content: `Mục ${updatedQuestion.leftItems.length + 1}`,
      type: 'text',
      matchId: rightId, // Match with corresponding right item
    });

    // Add new item to right column
    updatedQuestion.rightItems.push({
      id: rightId,
      content: `Mục ${updatedQuestion.rightItems.length + 1}`,
      type: 'text',
      matchId: leftId, // Match with corresponding left item
    });

    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleRemoveItemPair = (index: number) => {
    const updatedQuestion = { ...editableQuestion };

    // Make sure index is valid
    if (index < 0 || index >= updatedQuestion.leftItems.length) return;

    // Remove items at the same position from both columns
    updatedQuestion.leftItems.splice(index, 1);

    if (index < updatedQuestion.rightItems.length) {
      updatedQuestion.rightItems.splice(index, 1);
    }

    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  // Save the current configuration
  const handleSaveConfiguration = () => {
    // Update the matchIds based on current positions if needed
    const updatedQuestion = JSON.parse(
      JSON.stringify(editableQuestion)
    ) as MatchingQuestion;
    const currentLeftItems = updatedQuestion.leftItems;
    const currentRightItems = updatedQuestion.rightItems;
    // Ensure the matchIds are set correctly based on position
    const minLength = Math.min(
      currentLeftItems.length,
      currentRightItems.length
    );
    for (let i = 0; i < minLength; i++) {
      updatedQuestion.leftItems[i].matchId = currentRightItems[i].id;
      updatedQuestion.rightItems[i].matchId = currentLeftItems[i].id;
    }
    updatedQuestion.answers = [];
    updatedQuestion.leftItems.forEach((p) => {
      const right = updatedQuestion.rightItems.filter(
        (r) => r.matchId === p.id
      );
      if (right) {
        right.forEach((r) => {
          updatedQuestion.answers.push({
            id: Guid.create().toString(),
            left: p.id,
            right: r.id,
            isCorrect: true,
          });
        });
      }
    });
    // Submit the updated question to parent component
    handleChangeQuestion(updatedQuestion);
    message.success('Cấu hình đã được lưu!');
  };

  // Clear fireworks after display
  useEffect(() => {
    if (showFireworks) {
      const timer = setTimeout(() => {
        setShowFireworks(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showFireworks]);

  // Update the editableQuestion when leftItems or rightItems change in config mode
  useEffect(() => {
    if (configMode) {
      const updatedQuestion = {
        ...editableQuestion,
        leftItems: leftItems ?? [],
        rightItems: rightItems ?? [],
      };
      setEditableQuestion(updatedQuestion);
    }
  }, [leftItems, rightItems, configMode]);

  useEffect(() => {
    resetExercise();
  }, [id]);

  // Exercise mode rendering
  return (
    <>
      <Card
        title={configMode ? undefined : question.title}
        className="matching-component-card"
        extra={
          configMode ? null : (
            <>
              <Tooltip title="Hướng dẫn">
                <Button
                  icon={<QuestionOutlined />}
                  type="text"
                  onClick={() =>
                    message.info(
                      'Nhấn vào mỗi mục để chọn, sau đó nhấn vào mục khác để ghép cặp. Hãy ghép các mục có nội dung liên quan với nhau.'
                    )
                  }
                />
              </Tooltip>
            </>
          )
        }
      >
        {configMode ? (
          <Form layout="vertical">
            <Form.Item required>
              <Input
                variant="underlined"
                value={editableQuestion.title}
                onChange={handleUpdateTitle}
                placeholder={quizLocalization.form.questionTitle.placeholder}
                autoFocus={focus === 'title'}
              />
            </Form.Item>

            <Form.Item>
              <ReactQuill
                theme="snow"
                value={editableQuestion.description}
                onChange={handleUpdateDescription}
                placeholder="Nhập mô tả hoặc hướng dẫn cho bài tập"
                modules={modules}
                formats={formats}
              />
            </Form.Item>

            <Form.Item required>
              <div className="matching-pairs-config">
                {editableQuestion.leftItems.map((leftItem, index) => (
                  <div
                    key={leftItem.id}
                    className="matching-pair-row tailwind-flex"
                  >
                    <div className="matching-pair-inputs tailwind-flex tailwind-gap-4 tailwind-grow">
                      <Input
                        value={String(leftItem.content || '')}
                        onChange={(e) =>
                          handleUpdateItemContent('left', index, e.target.value)
                        }
                        placeholder={`Mục ${index + 1} (Cột A)`}
                        className="tailwind-flex-1"
                        autoFocus={focus === `left-${index}`}
                      />
                      <SwapOutlined
                        style={{
                          margin: '0 4px',
                          color: '#1890ff',
                          fontSize: '18px',
                        }}
                      />
                      <Input
                        variant="underlined"
                        value={String(
                          editableQuestion.rightItems[index]?.content || ''
                        )}
                        onChange={(e) =>
                          handleUpdateItemContent(
                            'right',
                            index,
                            e.target.value
                          )
                        }
                        placeholder={`Mục ${index + 1} (Cột B)`}
                        className="tailwind-flex-1"
                        autoFocus={focus === `right-${index}`}
                      />
                    </div>
                    <Button
                      type="text"
                      className="tailwind-flex-none tailwind-ml-2"
                      icon={<CloseOutlined />}
                      onClick={() => handleRemoveItemPair(index)}
                      disabled={editableQuestion.leftItems.length <= 2}
                      danger
                    />
                  </div>
                ))}
              </div>

              <Button
                type="dashed"
                onClick={handleAddItem}
                icon={<PlusOutlined />}
                style={{ width: '100%', marginTop: '8px' }}
              >
                Thêm cặp khớp mới
              </Button>
            </Form.Item>

            <Form.Item>
              <Collapse defaultActiveKey={[]} ghost>
                <Collapse.Panel
                  header={practiceLocalization['Advanced Options']}
                  key="1"
                >
                  <Form.Item
                    label={quizLocalization.questionType.label}
                    required
                  >
                    <Input
                      value={quizLocalization.questionType.matching}
                      variant="underlined"
                      disabled
                    />
                  </Form.Item>

                  <Form.Item label="Tùy chọn">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                        }}
                      >
                        <Switch
                          checked={editableQuestion.shuffleItems}
                          onChange={handleToggleShuffle}
                        />
                        <Text>Xáo trộn các mục khi làm bài</Text>
                        <Tooltip title="Khi bật, vị trí các mục sẽ được xáo trộn ngẫu nhiên mỗi lần làm bài">
                          <InfoCircleOutlined style={{ color: '#1890ff' }} />
                        </Tooltip>
                      </div>
                    </Space>
                  </Form.Item>
                </Collapse.Panel>
              </Collapse>
            </Form.Item>

            <Space>
              {!hideSaveButton && (
                <Button
                  type="primary"
                  onClick={handleSaveConfiguration}
                  className="tailwind-flex-1 tailwind-bg-blue-500 hover:tailwind-bg-blue-600"
                >
                  {quizLocalization.buttons.saveChanges}
                </Button>
              )}
              <PopconfirmAntdCustom
                title={quizLocalization.buttons.deleteQuestion.confirmTitle}
                onConfirm={deleteConfirm}
                onCancel={() => {}}
                okText={quizLocalization.buttons.deleteQuestion.yes}
                cancelText={quizLocalization.buttons.deleteQuestion.no}
              >
                <Button danger icon={<DeleteFilled />}>
                  {quizLocalization.buttons.deleteQuestion.button}
                </Button>
              </PopconfirmAntdCustom>
            </Space>
          </Form>
        ) : (
          <>
            {question.description && (
              <div
                className="matching-description item-config"
                onClick={() => setFocus('description')}
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(question.description),
                }}
              />
            )}

            <div className="matching-container item-config" ref={containerRef}>
              <Row gutter={24} className="tailwind-mt-4">
                <Col span={12}>
                  <div className="matching-column">
                    <h4>Cột A</h4>
                    {leftItems.map((item, index) => (
                      <ClickableItem
                        key={item.id}
                        id={`${item.id}`}
                        item={item}
                        isSelected={selectedItemId === item.id}
                        isPaired={isItemPaired(item.id)}
                        pairColor={getPairColor(item.id)}
                        isMatched={
                          isChecking && getMatchResult(item.id) === true
                        }
                        isIncorrect={
                          isChecking && getMatchResult(item.id) === false
                        }
                        isLeftItem={true}
                        onClick={(id) => {
                          handleItemClick(id);
                          setFocus(`left-${index}`);
                        }}
                      />
                    ))}
                  </div>
                </Col>

                <Col span={12}>
                  <div className="matching-column">
                    <h4>Cột B</h4>
                    {rightItems.map((item, index) => (
                      <ClickableItem
                        key={item.id}
                        id={`${item.id}`}
                        item={item}
                        isSelected={selectedItemId === item.id}
                        isPaired={isItemPaired(item.id)}
                        pairColor={getPairColor(item.id)}
                        isMatched={
                          isChecking && getMatchResult(item.id) === true
                        }
                        isIncorrect={
                          isChecking && getMatchResult(item.id) === false
                        }
                        isLeftItem={false}
                        onClick={(id) => {
                          handleItemClick(id);
                          setFocus(`right-${index}`);
                        }}
                      />
                    ))}
                  </div>
                </Col>
              </Row>

              {/* Connection Lines */}
              {!configMode && (
                <ConnectionLines
                  leftItems={leftItems}
                  rightItems={rightItems}
                  pairedItems={pairedItems}
                  containerRef={containerRef}
                />
              )}
            </div>

            <div className="tailwind-mt-5 tailwind-flex tailwind-justify-center">
              <Space>
                {/* <Button
                  type="primary"
                  onClick={() => {
                    checkAnswers();
                    handleComplete();
                  }}
                  className="tailwind-flex-1 tailwind-bg-blue-500 hover:tailwind-bg-blue-600"
                >
                  Kiểm tra
                </Button> */}
                <Button onClick={handleResetExercise}>Làm lại</Button>
              </Space>
            </div>

            <MatchingFeedback
              isChecking={isChecking}
              isCompleted={completed}
              correctCount={correctCount}
              totalCount={Math.min(leftItems.length, rightItems.length)}
              showFeedback={showFeedback}
            />
          </>
        )}
      </Card>

      {/* Display fireworks when answers are correct */}
      <FireworksComponent show={showFireworks} />
    </>
  );
};

export default MatchingComponent;

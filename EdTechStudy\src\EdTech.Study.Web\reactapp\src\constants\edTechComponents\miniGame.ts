import CoordinateFinderGame, {
  coordinateFinderSchema,
} from '../../components/miniGames/CoordinateFinderGame/CoordinateFinderGame';
import MillionaireGame, {
  millionaireSchema,
} from '../../components/miniGames/MillionaireGame/MillionaireGame';
import QuizCardGame, {
  quizCardSchema,
} from '../../components/miniGames/QuizCardGame/QuizCardGame';
import TreasureHuntGame, {
  treasureHuntSchema,
} from '../../components/miniGames/TreasureHuntGame/TreasureHuntGame';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

import CoordinateFinderGameThumbnail from '../../assets/images/miniGames/thumbnails/CoordinateFinderGame.jpg';
import TreasureHuntGameThumbnail from '../../assets/images/miniGames/thumbnails/TreasureHuntGame.jpg';
import QuizCardGameThumbnail from '../../assets/images/miniGames/thumbnails/QuizCardGame.jpg';
import MillionaireGameThumbnail from '../../assets/images/miniGames/thumbnails/MillionaireGame.jpg';

export const MINI_GAME_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'CoordinateFinderGame',
    title: 'Tìm địa điểm trên bản đồ',
    components: [
      {
        version: '1.0.0',
        component: CoordinateFinderGame,
        schema: undefined,
      },
    ],
    tags: [
      'Địa lý',
      'Lớp 6',
      'Tương tác',
      'Sơ cấp',
      'Giải quyết vấn đề',
      'Trực quan',
      'Học khái niệm',
      'Trò chơi hóa',
    ],
    type: ETypeEdTechComponent.MINI_GAMES,
    thumbnailUrl: CoordinateFinderGameThumbnail,
    description:
      'Mini-game giáo dục giúp học sinh rèn luyện kỹ năng xác định tọa độ địa lý trên bản đồ. Người chơi sẽ nhận được các thử thách yêu cầu tìm kiếm vị trí dựa trên vĩ độ và kinh độ hoặc ngược lại.',
    schema: coordinateFinderSchema,
  },
  {
    name: 'TreasureHuntGame',
    title: 'Truy tìm kho báu',
    components: [
      {
        version: '1.0.0',
        component: TreasureHuntGame,
        schema: undefined,
      },
    ],
    tags: [
      'Địa lý',
      'Lớp 6',
      'Tương tác',
      'Sơ cấp',
      'Tư duy phản biện',
      'Trực quan',
      'Thực tế',
      'Trò chơi hóa',
      'Tham gia',
    ],
    type: ETypeEdTechComponent.MINI_GAMES,
    thumbnailUrl: TreasureHuntGameThumbnail,
    description:
      'Mini-game giáo dục giúp học sinh rèn luyện kỹ năng phân biệt phương hướng trên bản đồ. Người chơi sẽ nhận được các gợi ý để tìm đường đến vị trí đặt kho báu',
    schema: treasureHuntSchema,
  },
  {
    name: 'QuizCardGame',
    title: 'Trò chơi lật thẻ',
    components: [
      {
        version: '1.0.0',
        component: QuizCardGame,
        schema: undefined,
      },
    ],
    tags: [
      'Câu đố',
      'Đánh giá',
      'Tương tác',
      'Làm việc nhóm',
      'Ghi nhớ',
      'Nhiều lựa chọn',
      'Ôn tập kiến thức',
      'Động lực',
    ],
    type: ETypeEdTechComponent.MINI_GAMES,
    thumbnailUrl: QuizCardGameThumbnail,
    description:
      'Mini-game giáo dục thú vị giúp người chơi rèn luyện trí nhớ và kiến thức qua các câu hỏi tương tác. Người chơi sẽ lật các thẻ để tìm cặp câu hỏi - đáp án phù hợp hoặc hoàn thành các thử thách theo chủ đề.',
    schema: quizCardSchema,
  },
  {
    name: 'MillionaireGame',
    title: 'Ai là triệu phú',
    components: [
      {
        version: '1.0.0',
        component: MillionaireGame,
        schema: undefined,
      },
    ],
    tags: [
      'Câu đố',
      'Đánh giá',
      'Tương tác',
      'Làm việc nhóm',
      'Nâng cao',
      'Tư duy phản biện',
      'Nhiều lựa chọn',
      'Đánh giá hiệu suất',
      'Tham gia',
      'Động lực',
    ],
    type: ETypeEdTechComponent.MINI_GAMES,
    thumbnailUrl: MillionaireGameThumbnail,
    description:
      'Mini-game giáo dục mô phỏng trò chơi truyền hình nổi tiếng, nơi người chơi sẽ trả lời chuỗi câu hỏi kiến thức theo cấp độ tăng dần để chinh phục đỉnh cao triệu phú.',
    schema: millionaireSchema,
  },
];

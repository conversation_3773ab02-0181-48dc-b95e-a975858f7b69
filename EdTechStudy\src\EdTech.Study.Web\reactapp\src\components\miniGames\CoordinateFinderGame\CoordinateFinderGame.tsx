import React, { useState, useEffect, memo, useMemo } from 'react';
import '../MiniGameResponsive.css';
import {
  CoordinateFinderGameConfig,
  defaultCoordinateFinderGameConfig,
} from './CoordinateFinderGameConfig';
import CoordinateFinderGameComponent from './CoordinateFinderGameComponent';
import CoordinateFinderGameModal from './CoordinateFinderGameModal';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { EngineContainer } from '../../common/engines';
import { ITextProps } from '../../core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { updateTitleAndSync } from '../../../utils/titleSyncUtils';
import { EdTechRootState } from '../../../store/store';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

// Extend the CoordinateFinderGameProps to include textProps for the title
interface CoordinateFinderGameProps extends CoordinateFinderGameConfig {}

export const coordinateFinderSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    mapUrl: z.string().optional(),
    coordinates: z
      .array(
        z.object({
          lat: z.number(),
          lng: z.number(),
          name: z.string(),
        })
      )
      .optional(),
    difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  },
});

const CoordinateFinderGame: React.FC<
  IEdTechRenderProps<CoordinateFinderGameProps>
> = (props) => {
  const { params, path, order, addOrUpdateParamComponent } = props;
  const [gameConfig, setGameConfig] = useState<CoordinateFinderGameConfig>({
    ...defaultCoordinateFinderGameConfig,
    ...params,
  });

  // State for config modal and fullscreen
  const [isShowConfigModal, setIsShowConfigModal] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Determine if configuration is allowed from the props, default to true if not specified
  const allowConfiguration =
    params && params.allowConfiguration !== undefined
      ? params.allowConfiguration
      : true;

  useEffect(() => {
    if (params) {
      const newConfig = {
        ...defaultCoordinateFinderGameConfig,
        ...params,
      };
      setGameConfig(newConfig);
    }
  }, [params]);

  // Handle config submit from modal
  const handleConfigSubmit = (config: CoordinateFinderGameConfig) => {
    if (addOrUpdateParamComponent) {
      // Merge the new config with existing titleProps to preserve them
      const updatedParams = {
        ...config,
        titleProps: gameConfig.titleProps,
      };
      addOrUpdateParamComponent(updatedParams);
      setGameConfig({ ...updatedParams });
    }
    // Close the modal after submitting
    setIsShowConfigModal(false);
  };

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Custom title update handler that also updates local state
  const handleGameTitleUpdate = (titleProps: Partial<ITextProps>) => {
    // Update the title in the game config and local state
    const updatedConfig = updateTitleAndSync(
      path,
      titleProps,
      gameConfig,
      renderTreeData,
      dispatch,
      addOrUpdateParamComponent
    );

    // Update local state
    if (updatedConfig !== gameConfig) {
      setGameConfig({ ...updatedConfig });
    }
  };

  // Handle fullscreen state changes
  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  // Memoize the structured instruction props
  const instructionProps = useMemo(
    () => ({
      objective: 'Tìm vị trí chính xác của địa điểm được yêu cầu trên bản đồ.',
      steps: [
        'Đọc tên địa điểm hiển thị ở trên bản đồ',
        'Tìm vị trí bằng cách click trực tiếp lên bản đồ hoặc nhập tọa độ vào ô nhập liệu',
        'Kiểm tra vị trí bằng cách nhấn nút "Kiểm tra vị trí"',
        'Nếu khó khăn, bạn có thể sử dụng gợi ý (sẽ bị trừ điểm)',
        'Hệ thống sẽ tự động chuyển sang câu hỏi tiếp theo sau khi bạn trả lời',
      ],
      notes: `Điểm chính xác được tính khi vị trí bạn chọn cách vị trí thực tế không quá ${gameConfig.proximityThreshold}km.`,
      isFullscreen: isFullscreen,
    }),
    [gameConfig.proximityThreshold, isFullscreen]
  );

  return (
    <EngineContainer
      node={props}
      titleProps={gameConfig.titleProps}
      allowConfiguration={allowConfiguration}
      onTitleUpdate={handleGameTitleUpdate}
      onFullscreenChange={handleFullscreenChange}
      isConfigModalOpen={isShowConfigModal}
      onConfigModalOpenChange={setIsShowConfigModal}
      id={path}
      mainComponent={
        <CoordinateFinderGameComponent
          config={gameConfig}
          isFullscreen={isFullscreen}
          allowConfiguration={allowConfiguration}
        />
      }
      configModal={
        <CoordinateFinderGameModal
          isOpen={isShowConfigModal}
          onClose={() => {
            setIsShowConfigModal(false);
          }}
          onSubmit={handleConfigSubmit}
          defaultConfig={gameConfig}
          path={path}
          order={order}
          addOrUpdateParamComponent={addOrUpdateParamComponent}
        />
      }
      instructionsContent={instructionProps}
    />
  );
};

export default memo(withEdComponentParams(CoordinateFinderGame));

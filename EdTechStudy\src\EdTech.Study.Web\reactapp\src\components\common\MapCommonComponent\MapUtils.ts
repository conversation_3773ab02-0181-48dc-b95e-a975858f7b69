import L from 'leaflet';

// Định nghĩa các interface
export interface MapPosition {
  lat: number;
  lng: number;
}

export interface MarkerOptions {
  position: MapPosition;
  title?: string;
  description?: string;
  icon?: L.Icon;
  draggable?: boolean;
}

export interface PolylineOptions {
  positions: MapPosition[];
  color?: string;
  weight?: number;
  opacity?: number;
  dashArray?: string;
}

export interface PolygonOptions {
  positions: MapPosition[];
  color?: string;
  fillColor?: string;
  fillOpacity?: number;
  weight?: number;
}

export interface CircleOptions {
  center: MapPosition;
  radius: number;
  color?: string;
  fillColor?: string;
  fillOpacity?: number;
}

export interface MeasurementResult {
  type: 'distance' | 'area';
  value: number;
  unit: string;
  points: MapPosition[];
}

// Thêm kiểu vị trí cho control
export type ControlPosition =
  | 'topleft'
  | 'topright'
  | 'bottomleft'
  | 'bottomright';

// <PERSON><PERSON><PERSON> tượ<PERSON> cấu hình cho các control
export interface MapControlsConfig {
  zoom?: {
    enabled: boolean;
    position?: ControlPosition;
  };
  scale?: {
    enabled: boolean;
    position?: ControlPosition;
    maxWidth?: number;
    offset?: ControlOffset;
  };
  compass?: {
    enabled: boolean;
    position?: ControlPosition;
    size?: number;
  };
  coordinates?: {
    enabled: boolean;
    position?: ControlPosition;
    decimals?: number;
  };
  search?: {
    enabled: boolean;
    position?: ControlPosition;
    placeholder?: string;
  };
  measure?: {
    enabled: boolean;
    position?: ControlPosition;
  };
  routing?: {
    enabled: boolean;
    position?: ControlPosition;
  };
  layers?: {
    enabled: boolean;
    position?: ControlPosition;
    baseLayers?: Record<string, string>;
    overlays?: Record<string, string>;
  };
}

// Mặc định cấu hình control
export const defaultMapControlsConfig: MapControlsConfig = {
  scale: {
    enabled: true,
    position: 'bottomright',
    maxWidth: 120,
  },
  zoom: {
    enabled: true,
    position: 'topright',
  },
  compass: {
    enabled: false,
    position: 'topright',
    size: 48,
  },
  coordinates: {
    enabled: false,
    position: 'bottomleft',
    decimals: 6,
  },
  search: {
    enabled: false,
    position: 'topleft',
    placeholder: 'Tìm kiếm địa điểm...',
  },
  measure: {
    enabled: false,
    position: 'topleft',
  },
  routing: {
    enabled: false,
    position: 'topleft',
  },
  layers: {
    enabled: false,
    position: 'topright',
    baseLayers: {
      OpenStreetMap: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      OpenTopoMap: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
      'Esri WorldImagery':
        'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    },
  },
};

// Thêm interface cho offset
export interface ControlOffset {
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
}

// Hàm tính toán offset dựa trên vị trí và thứ tự
export const calculateControlOffset = (
  position: ControlPosition,
  index: number,
  totalControls: number,
  reverse: boolean = false
): ControlOffset => {
  const baseOffset = 10; // Khoảng cách cơ bản giữa các control
  const controlHeight = 30; // Chiều cao trung bình của một control

  // Tính offset dựa trên index và có đảo ngược thứ tự hay không
  const offsetIndex = reverse ? totalControls - 1 - index : index;
  const offset = offsetIndex * (baseOffset + controlHeight);

  switch (position) {
    case 'topleft':
      return { top: offset };
    case 'topright':
      return { top: offset };
    case 'bottomleft':
      return { bottom: offset };
    case 'bottomright':
      return { bottom: offset };
    default:
      return {};
  }
};

// Hàm lấy vị trí CSS dựa trên ControlPosition
export const getPositionCSS = (
  position: ControlPosition
): React.CSSProperties => {
  const baseStyle: React.CSSProperties = {
    position: 'absolute',
    zIndex: 1000,
    margin: 0, // Loại bỏ margin mặc định
  };

  switch (position) {
    case 'topleft':
      return { ...baseStyle, top: '10px', left: '10px' };
    case 'topright':
      return { ...baseStyle, top: '10px', right: '10px' };
    case 'bottomleft':
      return { ...baseStyle, bottom: '10px', left: '10px' };
    case 'bottomright':
      return { ...baseStyle, bottom: '10px', right: '10px' };
    default:
      return baseStyle;
  }
};

/**
 * Tính khoảng cách giữa hai điểm (theo công thức Haversine)
 * @param point1 Điểm thứ nhất
 * @param point2 Điểm thứ hai
 * @returns Khoảng cách (km)
 */
export const calculateDistance = (
  point1: MapPosition,
  point2: MapPosition
): number => {
  const R = 6371; // Bán kính trái đất (km)
  const dLat = ((point2.lat - point1.lat) * Math.PI) / 180;
  const dLon = ((point2.lng - point1.lng) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((point1.lat * Math.PI) / 180) *
      Math.cos((point2.lat * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Khoảng cách (km)
};

/**
 * Tính khoảng cách theo đường đi trên đường
 * @param points Mảng các điểm trên đường đi
 * @returns Khoảng cách (km)
 */
export const calculateRouteDistance = (points: MapPosition[]): number => {
  let totalDistance = 0;

  for (let i = 1; i < points.length; i++) {
    totalDistance += calculateDistance(points[i - 1], points[i]);
  }

  return totalDistance;
};

/**
 * Tính diện tích của đa giác
 * @param points Mảng các điểm tạo thành đa giác
 * @returns Diện tích (km²)
 */
export const calculateArea = (points: MapPosition[]): number => {
  // Cần ít nhất 3 điểm để tạo thành đa giác
  if (points.length < 3) return 0;

  // Chuyển đổi sang định dạng mà Leaflet có thể sử dụng
  const latlngs = points.map((p) => [p.lat, p.lng]);

  // Tính diện tích sử dụng công thức Shoelace
  let area = 0;
  for (let i = 0; i < latlngs.length; i++) {
    const j = (i + 1) % latlngs.length;
    area += latlngs[i][0] * latlngs[j][1];
    area -= latlngs[j][0] * latlngs[i][1];
  }
  area = Math.abs(area) / 2;

  // Chuyển đổi sang km² (xấp xỉ)
  // Sử dụng hệ số chuyển đổi 111.32km cho mỗi độ vĩ độ
  return area * Math.pow(111.32, 2);
};

/**
 * Lấy địa chỉ từ tọa độ (reverse geocoding)
 * @param position Vị trí cần lấy địa chỉ
 * @returns Promise với địa chỉ
 */
export const getAddressFromPosition = async (
  position: MapPosition
): Promise<string> => {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.lat}&lon=${position.lng}`
    );
    const data = await response.json();
    return data.display_name || '';
  } catch (error) {
    console.error('Error getting address:', error);
    return '';
  }
};

/**
 * Tìm kiếm địa điểm dựa trên từ khóa
 * @param query Từ khóa tìm kiếm
 * @returns Promise với mảng kết quả
 */
export const searchLocations = async (query: string): Promise<any[]> => {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
        query
      )}`
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error searching locations:', error);
    return [];
  }
};

/**
 * Lấy đường đi từ điểm bắt đầu đến điểm kết thúc
 * @param startPoint Điểm bắt đầu
 * @param endPoint Điểm kết thúc
 * @param mode Phương tiện (driving, walking, cycling)
 * @returns Promise với dữ liệu route
 */
export const getRoute = async (
  startPoint: MapPosition,
  endPoint: MapPosition,
  mode: 'driving' | 'walking' | 'cycling' = 'driving'
): Promise<any> => {
  try {
    const response = await fetch(
      `https://router.project-osrm.org/route/v1/${mode}/${startPoint.lng},${startPoint.lat};${endPoint.lng},${endPoint.lat}?overview=full&geometries=geojson`
    );
    const data = await response.json();
    return data.routes && data.routes.length > 0 ? data.routes[0] : null;
  } catch (error) {
    console.error('Error getting route:', error);
    return null;
  }
};

/**
 * Lấy đường đi qua nhiều điểm
 * @param points Mảng các điểm cần đi qua
 * @param mode Phương tiện (driving, walking, cycling)
 * @returns Promise với dữ liệu route
 */
export const getRouteViaPoints = async (
  points: MapPosition[],
  mode: 'driving' | 'walking' | 'cycling' = 'driving'
): Promise<any> => {
  if (points.length < 2) return null;

  try {
    const coordinates = points.map((p) => `${p.lng},${p.lat}`).join(';');
    const response = await fetch(
      `https://router.project-osrm.org/route/v1/${mode}/${coordinates}?overview=full&geometries=geojson`
    );
    const data = await response.json();
    return data.routes && data.routes.length > 0 ? data.routes[0] : null;
  } catch (error) {
    console.error('Error getting route via points:', error);
    return null;
  }
};

/**
 * Tạo icon tùy chỉnh cho marker
 * @param color Màu sắc (blue, red, green, yellow, ...)
 * @param size Kích thước (small, medium, large)
 * @returns L.Icon đã tùy chỉnh
 */
export const createCustomIcon = (
  color: string = 'blue',
  size: 'small' | 'medium' | 'large' = 'medium'
): L.Icon => {
  // Kích thước dựa trên lựa chọn
  let iconSize: [number, number];
  let iconAnchor: [number, number];

  switch (size) {
    case 'small':
      iconSize = [20, 33];
      iconAnchor = [10, 33];
      break;
    case 'large':
      iconSize = [35, 57];
      iconAnchor = [17, 57];
      break;
    default: // medium
      iconSize = [25, 41];
      iconAnchor = [12, 41];
  }

  return new L.Icon({
    iconUrl: `https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-${color}.png`,
    shadowUrl:
      'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
    iconSize: iconSize,
    iconAnchor: iconAnchor,
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });
};

/**
 * Chuyển đổi đơn vị đo
 * @param value Giá trị cần chuyển đổi
 * @param fromUnit Đơn vị ban đầu
 * @param toUnit Đơn vị đích
 * @returns Giá trị sau khi chuyển đổi
 */
export const convertUnit = (
  value: number,
  fromUnit: 'm' | 'km' | 'm²' | 'km²' | 'ha',
  toUnit: 'm' | 'km' | 'm²' | 'km²' | 'ha'
): number => {
  // Chuyển đổi tất cả về đơn vị cơ bản (m hoặc m²)
  let baseValue: number;

  switch (fromUnit) {
    case 'km':
      baseValue = value * 1000; // km -> m
      break;
    case 'km²':
      baseValue = value * 1000000; // km² -> m²
      break;
    case 'ha':
      baseValue = value * 10000; // ha -> m²
      break;
    default:
      baseValue = value; // m hoặc m²
  }

  // Chuyển từ đơn vị cơ bản sang đơn vị đích
  switch (toUnit) {
    case 'km':
      return baseValue / 1000; // m -> km
    case 'km²':
      return baseValue / 1000000; // m² -> km²
    case 'ha':
      return baseValue / 10000; // m² -> ha
    default:
      return baseValue; // m hoặc m²
  }
};

/**
 * Định dạng số để hiển thị
 * @param value Giá trị cần định dạng
 * @param unit Đơn vị
 * @param decimals Số chữ số thập phân
 * @returns Chuỗi đã được định dạng
 */
export const formatMeasurement = (
  value: number,
  unit: string,
  decimals: number = 2
): string => {
  return `${value.toFixed(decimals)} ${unit}`;
};

/**
 * Thay đổi base layer của bản đồ dựa trên chế độ xem
 * @param map Đối tượng bản đồ Leaflet
 * @param mode Chế độ xem muốn thay đổi: 'standard', 'satellite', 'terrain', 'hybrid'
 * @returns Thành công hay không
 */
// MapUtils.ts (đã có sẵn, không thay đổi logic, chỉ đảm bảo tương thích)
export const changeBaseLayer = (map: any, mode: string): boolean => {
  try {
    if (!map) return false;

    const layerUrls: Record<string, string> = {
      standard: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      satellite:
        'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
      terrain: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
      hybrid:
        'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
    };

    if (!layerUrls[mode]) {
      console.warn(`Chế độ xem không hợp lệ: ${mode}`);
      return false;
    }

    map.eachLayer((layer: any) => {
      if (layer instanceof L.TileLayer) {
        map.removeLayer(layer);
      }
    });

    L.tileLayer(layerUrls[mode], {
      attribution:
        '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    }).addTo(map);

    return true;
  } catch (error) {
    console.error('Lỗi khi thay đổi base layer:', error);
    return false;
  }
};

/**
 * Utility function to apply map interaction styles dynamically, especially for fullscreen mode.
 * @param isFullscreen - Whether the map is in fullscreen mode.
 * @param styleId - Unique ID for the style element to avoid duplicates.
 */
export const applyMapInteractionStyles = (isFullscreen: boolean, styleId: string = 'map-interaction-styles') => {
  if (isFullscreen) {
    // Check if style element already exists
    let styleEl = document.getElementById(styleId) as HTMLStyleElement | null;
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = styleId;
      styleEl.innerHTML = `
        .leaflet-control-zoom a {
          font-size: 22px !important;
          width: 40px !important;
          height: 40px !important;
          line-height: 40px !important;
        }
        .leaflet-control-scale-line {
          font-size: 16px !important;
          padding: 5px 10px !important;
        }
        .leaflet-marker-icon {
          transform: scale(1.5);
        }
        .leaflet-popup-content {
          font-size: 18px !important;
          padding: 12px !important;
        }
        .leaflet-container {
          cursor: crosshair !important;
        }
        .leaflet-touch .leaflet-bar a {
          width: 40px !important;
          height: 40px !important;
          line-height: 40px !important;
        }
        .leaflet-touch .leaflet-control-layers,
        .leaflet-touch .leaflet-bar {
          border-width: 2px !important;
        }
        .leaflet-map-pane {
          pointer-events: auto !important;
        }
        .leaflet-container.leaflet-grab {
          cursor: grab !important;
        }
        .leaflet-container.leaflet-grabbing {
          cursor: grabbing !important;
        }
      `;
      document.head.appendChild(styleEl);
    }
  } else {
    // Remove the style element if it exists when not in fullscreen
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }
  }
};

/**
 * Zoom to a specific marker position on the map
 * @param map Leaflet map instance
 * @param position Position to zoom to
 * @param zoom Zoom level (optional, defaults to 15)
 * @param options Animation options (optional)
 */
export const zoomToPosition = (map: L.Map, position: MapPosition, zoom: number = 15, options: L.ZoomPanOptions = { animate: true }) => {
  if (!map) return false;

  try {
    map.setView([position.lat, position.lng], zoom, options);
    return true;
  } catch (error) {
    console.error('Error zooming to position:', error);
    return false;
  }
};

import React, { useState } from 'react';
import { Menu, Dropdown, Tooltip } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { useAudio } from '../context/AudioContext';

export const PlaybackRateSelector: React.FC = () => {
  const { config, handlePlaybackRateChange } = useAudio();
  const [visible, setVisible] = useState(false);

  // Get current playback rate from config or default to 1
  const currentPlaybackRate = config.playbackRate || 1;

  const menu = (
    <Menu
      selectedKeys={[`${currentPlaybackRate}`]}
      onClick={({ key }) => {
        handlePlaybackRateChange(parseFloat(key));
        setVisible(false);
      }}
    >
      <Menu.Item key="0.5">0.5x</Menu.Item>
      <Menu.Item key="0.75">0.75x</Menu.Item>
      <Menu.Item key="1">1x</Menu.Item>
      <Menu.Item key="1.25">1.25x</Menu.Item>
      <Menu.Item key="1.5">1.5x</Menu.Item>
      <Menu.Item key="2">2x</Menu.Item>
    </Menu>
  );

  return (
    <Tooltip title="Tốc độ phát">
      <Dropdown
        overlay={menu}
        visible={visible}
        onVisibleChange={setVisible}
        trigger={['click']}
      >
        <div className="edtech-playback-rate-selector">
          {currentPlaybackRate}x{' '}
          <DownOutlined style={{ fontSize: '10px', marginLeft: '4px' }} />
        </div>
      </Dropdown>
    </Tooltip>
  );
};

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Modal } from 'antd';
import { IEdTechRenderTreeData } from '../../../../interfaces/AppComponents';
import { useGridStructure } from '../../../../hooks/grid/useGridStructure';
import { DeleteIcon } from '../../../icons/IconRegister';

interface DeleteEngineButtonProps {
  /**
   * The node to be deleted
   */
  node: IEdTechRenderTreeData;

  /**
   * Optional callback to be called after deletion
   */
  onDeleted?: () => void;

  /**
   * Optional style for the delete button
   */
  buttonStyle?: React.CSSProperties;

  /**
   * Optional CSS class for the delete button
   */
  className?: string;

  /**
   * Optional click handler (useful for customizing behavior in certain components)
   */
  onClick?: (e: React.MouseEvent) => void;
}

/**
 * Reusable delete engine button component with confirm modal
 */
const DeleteEngineButton: React.FC<DeleteEngineButtonProps> = ({
  node,
  onDeleted,
  buttonStyle,
  onClick,
}) => {
  const { deleteItemWithGridCheck } = useGridStructure();

  // Handle delete button click
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();

    // If a custom click handler is provided, use it and skip the modal
    if (onClick) {
      onClick(e);
      return;
    }
    console.log('delete', node);
    ``;
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: `Bạn có chắc chắn muốn xóa "${node.title || node.name}" không?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk: () => {
        // Use intelligent grid deletion to properly handle column/row relationships
        deleteItemWithGridCheck(node);

        // Call callback if provided
        if (onDeleted) {
          onDeleted();
        }
      },
    });
  };

  return (
    <Tooltip title={`Xóa ${node.title.toLocaleLowerCase() || ''}`}>
      <Button
        icon={<DeleteIcon />}
        onClick={handleDelete}
        shape="default"
        size="middle"
        danger
        style={buttonStyle}
        className="!tailwind-w-[26px] !tailwind-h-[26px] tailwind-p-0 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-rounded-[4px]"
      />
    </Tooltip>
  );
};

export default DeleteEngineButton;

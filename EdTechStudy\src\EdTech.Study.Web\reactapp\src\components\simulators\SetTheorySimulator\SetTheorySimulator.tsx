import React, { useState, memo } from 'react';
import SetTheorySimulatorComponent from './SetTheorySimulatorComponent';
import { SetTheoryConfig, defaultSetTheoryConfig } from './SetTheoryConfig';
import SetTheoryConfigModal from './SetTheoryConfigModal';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import EngineContainer from '../../common/engines/EngineContainer';
import { ITextProps } from '../../core/title/CoreTitle';
import { useDispatch, useSelector } from 'react-redux';
import { updateTitleAndSync } from '../../../utils/titleSyncUtils';
import { EdTechRootState } from '../../../store/store';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';
// import { ITextProps } from '../../core/title/CoreTitle';

interface SetTheorySimulatorProps extends SetTheoryConfig {}

export const setTheorySimulatorSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    sets: z
      .array(
        z.object({
          name: z.string(),
          elements: z.array(z.string()),
          color: z.string().optional(),
        })
      )
      .optional(),
    operations: z
      .array(z.enum(['union', 'intersection', 'difference', 'complement']))
      .optional(),
    showLabels: z.boolean().optional(),
  },
});

const SetTheorySimulator: React.FC<
  IEdTechRenderProps<SetTheorySimulatorProps>
> = (props) => {
  const { id, params, addOrUpdateParamComponent } = props;
  // State for configuration
  const [config, setConfig] = useState<SetTheoryConfig>({
    ...defaultSetTheoryConfig,
    ...params,
  });

  // State for UI
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [currentSetType, setCurrentSetType] = useState<string>('numbers');

  // Apply configuration changes from modal
  const handleConfigSubmit = (newConfig: Partial<SetTheoryConfig>) => {
    // Đảm bảo rằng tất cả các thuộc tính cần thiết đều được giữ lại
    const updatedConfig = {
      ...config,
      ...newConfig,
      options: {
        ...config.options,
        ...(newConfig.options || {}),
      },
      theme: {
        ...config.theme,
        ...(newConfig.theme || {}),
      },
      elementSets: {
        ...config.elementSets,
        ...(newConfig.elementSets || {}),
      },
    };

    setConfig(updatedConfig);
    setIsConfigModalOpen(false);

    // Cập nhật component cha
    addOrUpdateParamComponent(updatedConfig);
  };

  // Apply changes when set type changes
  const handleSetTypeChange = (value: string) => {
    setCurrentSetType(value);

    const updatedConfig = {
      ...config,
    };

    setConfig(updatedConfig);

    // Add update to parent component
    addOrUpdateParamComponent({
      ...defaultSetTheoryConfig,
      ...updatedConfig,
    });
  };

  // Get titleProps from config
  const titleProps = config.titleProps || {
    text: 'Quan Hệ Thuộc Tính Trong Tập Hợp',
    fontSize: 24,
    align: 'left',
    bold: true,
  };

  // Đảm bảo rằng allowDragDrop luôn được bật mặc định
  if (!config.options) {
    config.options = { ...defaultSetTheoryConfig.options };
  }
  if (config.options.allowDragDrop === undefined) {
    config.options.allowDragDrop = true;
  }

  // Get tree data from Redux store
  const dispatch = useDispatch();
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );

  // Custom title update handler that also updates local state
  const handleSetTheoryTitleUpdate = (updates: Partial<ITextProps>) => {
    // Update the title in the config and local state
    const updatedConfig = updateTitleAndSync(
      id,
      updates,
      config,
      renderTreeData,
      dispatch,
      addOrUpdateParamComponent
    );

    // Update local state
    if (updatedConfig !== config) {
      setConfig(updatedConfig);
    }
  };

  // Instructions content for EngineContainer using structured approach
  const instructionsContent = {
    objective:
      'Mô phỏng này giúp bạn khám phá khái niệm về thuộc tính và quan hệ giữa phần tử và tập hợp trong lý thuyết tập hợp.',
    steps: [
      'Chọn loại tập hợp để làm việc với các phần tử số hoặc chữ cái',
      'Kéo và thả các phần tử từ danh sách phần tử vào tập hợp A',
      'Kiểm tra thuộc tính để xác định xem một phần tử có thuộc tập hợp hay không',
      'Quan sát ví dụ để hiểu rõ hơn về quan hệ thuộc tính',
    ],
    notes:
      'Bạn có thể tạo thêm các loại tập hợp mới với các phần tử tùy chỉnh để mở rộng việc học tập. Kéo và thả các phần tử giữa các tập hợp để thực hành.',
  };

  // Main component for EngineContainer
  const mainComponent = (
    <>
      <SetTheorySimulatorComponent
        config={{
          ...config,
          elementSets: {
            ...config.elementSets,
          },
          options: {
            ...config.options,
            // Đảm bảo rằng allowDragDrop được truyền đúng cách
            allowDragDrop: config.options?.allowDragDrop !== false,
          },
          theme: {
            ...config.theme,
          },
        }}
        currentSetType={currentSetType}
        onSetTypeChange={handleSetTypeChange}
      />

      {/* New Set Type Modal */}
      {/* Modal thêm loại tập hợp mới - đã bỏ vì không còn nút thêm loại tập hợp bên ngoài */}
    </>
  );

  // Configuration modal for EngineContainer
  const configModal = (
    <SetTheoryConfigModal
      isOpen={isConfigModalOpen}
      onClose={() => setIsConfigModalOpen(false)}
      onSubmit={handleConfigSubmit}
      defaultConfig={config as Partial<SetTheoryConfig>}
    />
  );

  return (
    <EngineContainer
      node={props}
      titleProps={titleProps}
      onTitleUpdate={handleSetTheoryTitleUpdate}
      mainComponent={mainComponent}
      configModal={configModal}
      instructionsContent={instructionsContent}
      isConfigModalOpen={isConfigModalOpen}
      onConfigModalOpenChange={setIsConfigModalOpen}
      id={id}
    />
  );
};

export default memo(withEdComponentParams(SetTheorySimulator));

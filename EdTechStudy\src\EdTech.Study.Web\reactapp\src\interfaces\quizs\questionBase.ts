import { createContext } from 'react';
export interface BaseQuestion {
  id: string;
  type: string;
  title: string;
  description?: string;
  points?: number;
  isCompleted?: boolean;
  status?: string;
  statusEntity?: QuestionStatus;
  statusEntityString?: string;
  images?: { url: string; alt?: string }[];
  answers?: BaseAnswer[];
  userSelect?: BaseAnswer | BaseAnswer[];
  subjectId?: string;
  subject?: Subject;
  tags?: string | string[];
  topics?: string | string[];
  [key: string]: any;
}

export interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
}

export enum QuestionStatus {
  Draft = 0,
  Submitted = 1,
  Approved = 2,
  Published = 3,
  Rejected = 4,
}

export const getQuestionStatus = (status: QuestionStatus) => {
  return status === QuestionStatus.Draft
    ? 'Bản nháp'
    : status === QuestionStatus.Submitted
    ? 'Đã gửi'
    : status === QuestionStatus.Approved
    ? 'Đã duyệt'
    : status === QuestionStatus.Published
    ? 'Đã xuất bản'
    : 'Đã từ chối';
};

export interface BaseAnswer {
  id: string;
  isCorrect: boolean;
  order?: number;
}

export enum QuestionType {
  SingleChoice = 0,
  MultipleChoice = 1,
  Essay = 2,
  Matching = 3,
  Fillblank = 4,
}

// Interface for Quiz questions
export interface QuizQuestion extends BaseQuestion {
  type: 'quiz';
  question: string;
  explanation?: string;
  answers?: QuizAnswer[];
  userSelect?: QuizAnswer;
  points?: number;
}
// Interface for Quiz answers
export interface QuizAnswer extends BaseAnswer {
  id: string;
  text: string;
  isCorrect: boolean;
}

// Interface for Matching questions
export interface MatchingQuestion extends BaseQuestion {
  type: 'matching';
  leftItems: MatchingItemType[];
  rightItems: MatchingItemType[];
  shuffleItems?: boolean;
  isCompleted?: boolean;
  answers: MatchingQuestionAnswer[];
  userSelect?: MatchingQuestionAnswer[];
}

export interface MatchingQuestionAnswer extends BaseAnswer {
  left: string;
  right: string;
}

// Interface for Matching items
export interface MatchingItemType {
  id: string;
  content: string | React.ReactNode;
  type: 'text' | 'image';
  matchId: string;
}

// Kết hợp các kiểu câu hỏi
export type Question = BaseQuestion;

export abstract class QuestionComponentBaseProps {
  id?: string;
  question!: BaseQuestion; // Required property
  showFeedback?: boolean;
  configMode?: boolean;
  disabled?: boolean;
  showResult?: boolean;
  hideSaveButton?: boolean;
  hideDeleteButton?: boolean;

  abstract onComplete?(
    questionId: string,
    answer?: BaseAnswer | BaseAnswer[]
  ): void;
}

export interface PracticeEngineJsonData {
  id: string;
  question: BaseQuestion[];
}

export const KEY_PRACTICE_DEFAULT = 'practice-engine-test-id';
export const getKeyPracticeData = (id: string) => {
  return `data-${id}`;
};

export interface IPracticeEngineContext {
  handleChangeQuestion: (update: Question) => any;
  handleDeleteQuestion: (id: string) => any;
  handleChangePosition: (id: string) => any;
  handleSaveAllQuestions?: (id: string, questions: BaseQuestion[]) => any;
  handleToggleFullscreen: () => any;
  isFullscreen: boolean;
}
export const PracticeEngineContext = createContext<IPracticeEngineContext>({
  handleChangeQuestion: () => {},
  handleDeleteQuestion: (_: string) => {},
  handleChangePosition: (id: string) => {},
  handleSaveAllQuestions: (id: string, questions: BaseQuestion[]) => {},
  handleToggleFullscreen: () => {},
  isFullscreen: false,
});

export type IQuestionComponentInfo = {
  name: string;
  component:
    | React.FC<QuestionComponentBaseProps>
    | React.LazyExoticComponent<React.FC<QuestionComponentBaseProps>>;
  description: string;
};

export const mapQuestionComponents: Map<string, IQuestionComponentInfo> =
  new Map();

export const registerQuestionComponents = (
  type: string,
  component: IQuestionComponentInfo
) => {
  mapQuestionComponents.set(type, component);
};

import {
  IEdTechRenderParam,
  IEdTechRenderTreeData,
} from '../interfaces/AppComponents';
import { BaseQuestion } from '../interfaces/quizs/questionBase';

// Biến file này thành một module bằng cách thêm export
export {};

/**
 * Interface định nghĩa cấu trúc của một bản ghi cấu hình bài học
 */
interface LessonData {
  id: string;
  config: any; // C<PERSON> thể thay thế bằng interface cụ thể cho cấu trúc config
  timestamp: number;
}

/**
 * Interface định nghĩa API của module LessonConfigDB
 */
interface LessonConfigDBModule {
  init(): Promise<IDBDatabase>;
  saveConfig(id: string, config: object): Promise<string>;
  getConfig(id: string): Promise<LessonData | null>;
  getAllConfigs(): Promise<LessonData[]>;
  deleteConfig(id: string): Promise<boolean>;
  getConfigsByTimeRange(
    startTime: number,
    endTime: number
  ): Promise<LessonData[]>;
  clearAllConfigs(): Promise<boolean>;
  createId(grade: string, subject: string, lesson: string): string;
  updateEdComponentParams(id: string, config: object): Promise<string>;
  updateEdTechRenderTreeData(id: string, config: object): Promise<string>;
}

/**
 * Interface định nghĩa cấu trúc của một bản ghi cấu hình bài học
 */
interface ILessonInitData {
  edComponentParams: IEdTechRenderParam[];
  edTechRenderTreeData: IEdTechRenderTreeData;
}

/**
 * Interface định nghĩa API của module QuestionsManager
 */
interface QuestionsManagerModule {
  getDefaultQuestions: () => BaseQuestion[];
}

/**
 * Mở rộng interface Window để bao gồm module LessonConfigDB
 */
declare global {
  interface Window {
    LessonConfigDB: LessonConfigDBModule;
    LessonInitModule: {
      setDefaultLessonData: (data: ILessonInitData, title?: string) => void;
      getDefaultLessonData: () => ILessonInitData;
      backToPreviousPage: () => void;
      title?: string;
    };
    QuestionsInitModule: QuestionsManagerModule;
    //Key của bài học
    jsonKey: string;
  }
}

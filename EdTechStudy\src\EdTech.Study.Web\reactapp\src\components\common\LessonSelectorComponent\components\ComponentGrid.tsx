import React from 'react';
import { Empty, Typography } from 'antd';
import { IEdTechComponent } from '../../../../interfaces/AppComponents';
import ComponentCard from './ComponentCard';

const { Title, Text } = Typography;

interface ComponentGridProps {
  components: IEdTechComponent[];
  selectedComponents: IEdTechComponent[];
  onSelectComponent: (component: IEdTechComponent) => void;
}

const ComponentGrid: React.FC<ComponentGridProps> = ({
  components,
  selectedComponents,
  onSelectComponent,
}) => {
  if (components.length === 0) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={
          <div className="tailwind-text-center tailwind-py-6">
            <Title
              level={5}
              className="tailwind-mt-2 tailwind-font-medium tailwind-text-gray-900"
            >
              Không tìm thấy component phù hợp.
              <div className="tailwind-mt-3 tailwind-text-blue-500">
                <PERSON><PERSON>n có thể chọn nhiều component cùng lúc!
              </div>
            </Title>
            <Text className="tailwind-text-gray-500">
              Thử tìm kiếm với từ khóa khác hoặc chọn danh mục khác
            </Text>
          </div>
        }
      />
    );
  }

  return (
    <div
      className="tailwind-mt-4 tailwind-overflow-y-auto"
      style={{
        maxHeight: 'calc(90vh - 350px)',
        scrollbarWidth: 'none',
        msOverflowStyle: 'none',
        WebkitOverflowScrolling: 'touch',
      }}
    >
      <style>
        {`
        .component-grid::-webkit-scrollbar {
          display: none;
        }
        `}
      </style>
      <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-2 lg:tailwind-grid-cols-3 tailwind-gap-4 sm:tailwind-gap-6 component-grid">
        {components.map((component) => (
          <div key={component.name}>
            <ComponentCard
              component={component}
              isSelected={selectedComponents.some(
                (c) => c.name === component.name
              )}
              onSelect={onSelectComponent}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ComponentGrid;

import React, { useState, useEffect, useRef } from 'react';
import '../MiniGameResponsive.css';
import { QuizCardGameConfig, GameSummary } from './QuizCardGameConfig';
import {
  Question,
  QuestionResult,
  QuestionAnswer,
} from '../../common/QuestionComponent/QuestionBankConfig';
import {
  Card,
  Button,
  Typography,
  Progress,
  Space,
  Row,
  Col,
  Statistic,
  Badge,
  Table,
  Tag,
  Alert,
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  TrophyOutlined,
  RightOutlined,
} from '@ant-design/icons';
import QuestionComponent from '../../common/QuestionComponent/QuestionComponent';
import QuestionResultComponent from '../../common/QuestionComponent/QuestionResultComponent';
import { checkAnswer } from '../../common/QuestionComponent/questionUtils';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';

const { Title, Text, Paragraph } = Typography;

// Props cho component game
interface QuizCardGameComponentProps {
  config: QuizCardGameConfig;
  onGameComplete?: (summary: GameSummary) => void;
  isFullscreen?: boolean;
}

const QuizCardGameComponent: React.FC<QuizCardGameComponentProps> = ({
  config,
  onGameComplete,
}) => {
  // States
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<
    number | null
  >(null);
  const [questionResults, setQuestionResults] = useState<QuestionResult[]>([]);
  const [score, setScore] = useState<number>(0);
  const [timeLeft, setTimeLeft] = useState<number | undefined>(
    config.timeLimit
  );
  const [gameStatus, setGameStatus] = useState<
    'ready' | 'playing' | 'complete'
  >('ready');
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showAnswer, setShowAnswer] = useState<boolean>(false);
  const [userAnswer, setUserAnswer] = useState<QuestionAnswer>(null);
  const [treasureOpened, setTreasureOpened] = useState<number[]>([]);
  const [message, setMessage] = useState<string>('');
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState<
    number | null
  >(null);
  const [showTimer, setShowTimer] = useState<boolean>(true);

  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Danh sách câu hỏi hiệu quả (có thể đã xáo trộn)
  const [effectiveQuestions, setEffectiveQuestions] = useState<Question[]>([]);

  // Khởi tạo danh sách câu hỏi khi component mount hoặc cấu hình thay đổi
  useEffect(() => {
    if (!config.questions || config.questions.length === 0) {
      setEffectiveQuestions([]);
      return;
    }

    let questions = [...config.questions];

    // Xáo trộn câu hỏi nếu cần
    if (config.shuffleQuestions) {
      questions = [...questions].sort(() => Math.random() - 0.5);
    }

    // Giới hạn số lượng câu hỏi nếu có maxQuestions
    if (config.maxQuestions && config.maxQuestions < questions.length) {
      questions = questions.slice(0, config.maxQuestions);
    }

    setEffectiveQuestions(questions);

    // Reset game state when questions change
    setCurrentQuestionIndex(null);
    setQuestionResults([]);
    setTreasureOpened([]);
    setScore(0);
  }, [config.questions]);

  // Khởi động bộ đếm thời gian nếu có giới hạn thời gian và đang hiển thị câu hỏi
  useEffect(() => {
    if (
      config.timeLimit &&
      gameStatus === 'playing' &&
      currentQuestionIndex !== null &&
      showModal
    ) {
      // Ghi lại thời gian bắt đầu
      setCurrentQuestionStartTime(Date.now());

      setTimeLeft(config.timeLimit);
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev && prev > 0) {
            return prev - 1;
          } else {
            // Hết thời gian - xử lý tự động trả lời
            clearInterval(timerRef.current as ReturnType<typeof setInterval>);
            // Nếu chưa trả lời, coi như trả lời sai
            if (userAnswer === null) {
              handleConfirmAnswer(false);
            }
            return 0;
          }
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [
    currentQuestionIndex,
    config.timeLimit,
    gameStatus,
    showModal,
    userAnswer,
  ]);
  // Xử lý chọn câu hỏi
  const handleSelectQuestion = (index: number) => {
    // Kiểm tra nếu thẻ đã lật
    if (treasureOpened.includes(index)) {
      setMessage('Thẻ này đã được lật!');
      return;
    }

    // Kiểm tra nếu game chưa bắt đầu
    if (gameStatus === 'ready') {
      setMessage('Vui lòng nhấn "Bắt đầu trò chơi" để bắt đầu!');
      return;
    }

    setCurrentQuestionIndex(index);
    setUserAnswer(null);
    setShowAnswer(false);
    setShowModal(true);
    setTimeLeft(config.timeLimit);
    setShowTimer(true);
    setMessage('');
  };

  // Xử lý xác nhận câu trả lời
  const handleConfirmAnswer = (answer: QuestionAnswer) => {
    if (currentQuestionIndex === null) return;

    const currentQuestion = effectiveQuestions[currentQuestionIndex];

    // Lưu đáp án người dùng
    setUserAnswer(answer);

    // Tính thời gian đã dùng
    const timeSpent = currentQuestionStartTime
      ? Math.round((Date.now() - currentQuestionStartTime) / 1000)
      : undefined;

    // Kiểm tra đáp án dựa vào loại câu hỏi - sử dụng hàm utility chung
    const isCorrect = checkAnswer(currentQuestion, answer);

    // Tính điểm
    let pointsEarned = 0;
    if (isCorrect) {
      // Điểm cho câu trả lời đúng
      pointsEarned = currentQuestion.points || config.correctAnswerScore;
    } else if (config.incorrectAnswerPenalty) {
      // Trừ điểm cho câu trả lời sai nếu có penalty
      pointsEarned = -config.incorrectAnswerPenalty;
    }

    // Cập nhật tổng điểm
    setScore((prev) => prev + pointsEarned);

    // Thêm kết quả vào danh sách
    const result: QuestionResult = {
      question: currentQuestion,
      userAnswer: answer,
      isCorrect,
      timeSpent,
      points: pointsEarned,
    };

    setQuestionResults((prev) => [...prev, result]);

    // Hiển thị kết quả và giải thích
    setShowAnswer(true);
    // Hide timer after answering
    setShowTimer(false);
    // Reset timer to 0
    setTimeLeft(0);

    // Hiển thị thông báo
    if (isCorrect) {
      setMessage(`Chính xác! ${currentQuestion.explanation}`);
    } else {
      setMessage(`Không chính xác. ${currentQuestion.explanation}`);
    }

    // Dừng timer nếu có
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  // Xử lý khi người dùng nhấn nút Tiếp tục sau khi xem kết quả
  const handleContinueAfterAnswer = () => {
    if (currentQuestionIndex === null) return;

    // Đánh dấu thẻ đã lật
    setTreasureOpened((prev) => [...prev, currentQuestionIndex]);

    // Kiểm tra xem trò chơi đã kết thúc chưa (tất cả câu hỏi đã được trả lời)
    if (treasureOpened.length + 1 >= effectiveQuestions.length) {
      setShowModal(false);
      setGameStatus('complete');

      // Gọi callback kết thúc game nếu có
      if (onGameComplete) {
        const summary: GameSummary = {
          totalScore: score,
          correctAnswers: questionResults.filter((r) => r.isCorrect).length,
          totalQuestions: effectiveQuestions.length,
          accuracy:
            (questionResults.filter((r) => r.isCorrect).length /
              effectiveQuestions.length) *
            100,
          averageTimePerQuestion:
            questionResults
              .filter((r) => r.timeSpent !== undefined)
              .reduce((sum, r) => sum + (r.timeSpent || 0), 0) /
            effectiveQuestions.length,
          totalTimeTaken: questionResults.reduce(
            (sum, r) => sum + (r.timeSpent || 0),
            0
          ),
          results: questionResults,
        };

        onGameComplete(summary);
      }
    } else {
      setShowModal(false);
    }
  };

  // Xử lý bắt đầu game
  const handleStartGame = () => {
    setGameStatus('playing');
    setScore(0);
    setQuestionResults([]);
    setTreasureOpened([]);
    setMessage('Chọn một thẻ câu hỏi để bắt đầu!');
  };

  // Xử lý reset game
  const handleResetGame = () => {
    // Reset lại các trạng thái
    setCurrentQuestionIndex(null);
    setUserAnswer(null);
    setShowAnswer(false);
    setShowModal(false);
    setScore(0);
    setQuestionResults([]);
    setTreasureOpened([]);
    setTimeLeft(config.timeLimit);
    setGameStatus('ready');
    setMessage('');

    // Xáo trộn lại câu hỏi nếu cần
    if (config.shuffleQuestions) {
      setEffectiveQuestions(
        [...config.questions].sort(() => Math.random() - 0.5)
      );
    }
  };

  // Xử lý đóng modal
  const handleCloseModal = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setShowModal(false);
  };

  // Add new function to end game early
  const handleEndGameEarly = () => {
    // End the game and show results
    setGameStatus('complete');
  };

  // Render the game board with cards
  const renderGameBoard = () => {
    if (gameStatus === 'complete') return null;

    // Use card colors based on display mode
    const cardBgColors =
      config.theme?.displayMode === 'simple'
        ? ['tailwind-bg-blue-500'] // Just one color for simple mode
        : [
            'tailwind-bg-blue-500', // Blue
            'tailwind-bg-purple-600', // Purple
            'tailwind-bg-green-500', // Green
            'tailwind-bg-rose-500', // Rose
            'tailwind-bg-amber-500', // Amber
            'tailwind-bg-cyan-500', // Cyan
            'tailwind-bg-violet-600', // Violet
            'tailwind-bg-lime-500', // Lime
            'tailwind-bg-pink-500', // Pink
            'tailwind-bg-indigo-500', // Indigo
            'tailwind-bg-red-500', // Red
            'tailwind-bg-teal-500', // Teal
          ];

    // Determine card size class based on config
    let cardWidthClass = '';
    switch (config.theme?.cardSize) {
      case 'small':
        cardWidthClass = 'tailwind-w-[120px]';
        break;
      case 'large':
        cardWidthClass = 'tailwind-w-[200px]';
        break;
      case 'medium':
      default:
        cardWidthClass = 'tailwind-w-[160px]';
        break;
    }

    // Get aspect ratio from config
    const aspectRatio = config.theme?.cardAspectRatio || '2/3';

    // Parse the aspect ratio for style calculation
    let aspectRatioStyle = {};
    if (aspectRatio) {
      const [width, height] = aspectRatio.split('/').map(Number);
      if (!isNaN(width) && !isNaN(height) && height > 0) {
        aspectRatioStyle = {
          aspectRatio: `${width} / ${height}`,
        };
      }
    }
    // Create grid style with responsive column adjustment
    const gridStyle: React.CSSProperties = {
      marginBottom: '1.5rem',
    };

    // We'll use the mini-game-card-grid class from our responsive CSS instead

    return (
      <div className="tailwind-mb-6 mini-game-card-grid" style={gridStyle}>
        {effectiveQuestions.map((question, index) => {
          // Find the matching question result
          const questionResult = questionResults.find(
            (result) => result.question === question
          );

          const isOpened = treasureOpened.includes(index);
          const colorIndex =
            config.theme?.displayMode === 'simple'
              ? 0
              : index % cardBgColors.length;

          return (
            <Card
              key={index}
              className={`tailwind-relative ${cardWidthClass} ${
                gameStatus === 'ready'
                  ? 'tailwind-cursor-not-allowed'
                  : 'tailwind-cursor-pointer'
              } tailwind-shadow-xl hover:tailwind-shadow-2xl tailwind-transition-all tailwind-overflow-hidden
                ${
                  !isOpened
                    ? `${cardBgColors[colorIndex]}`
                    : questionResult?.isCorrect
                    ? 'tailwind-bg-gradient-to-br tailwind-from-green-50 tailwind-to-emerald-100'
                    : 'tailwind-bg-gradient-to-br tailwind-from-red-50 tailwind-to-rose-100'
                } tailwind-rounded-xl tailwind-border-2 ${
                !isOpened
                  ? 'tailwind-border-transparent'
                  : questionResult?.isCorrect
                  ? 'tailwind-border-green-400'
                  : 'tailwind-border-red-400'
              }`}
              style={aspectRatioStyle}
              styles={{
                body: {
                  height: '100%',
                  padding: isOpened ? '16px' : '0',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '0.75rem',
                },
              }}
              onClick={() => !isOpened && handleSelectQuestion(index)}
            >
              {isOpened ? (
                <div className="tailwind-h-full tailwind-w-full tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-p-4 tailwind-text-center">
                  {questionResult?.isCorrect ? (
                    <>
                      <div className="tailwind-w-16 tailwind-h-16 tailwind-bg-green-100 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-mb-3 tailwind-shadow-md tailwind-border-2 tailwind-border-green-300">
                        <CheckCircleOutlined className="tailwind-text-3xl tailwind-text-green-600" />
                      </div>
                      <Text
                        strong
                        className="tailwind-text-green-800 tailwind-text-lg"
                      >
                        Đúng
                      </Text>
                      <Text
                        type="success"
                        className="tailwind-text-sm tailwind-mt-1"
                      >
                        +{question.points || config.correctAnswerScore} điểm
                      </Text>
                    </>
                  ) : (
                    <>
                      <div className="tailwind-w-16 tailwind-h-16 tailwind-bg-red-100 tailwind-rounded-full tailwind-flex tailwind-items-center tailwind-justify-center tailwind-mb-3 tailwind-shadow-md tailwind-border-2 tailwind-border-red-300">
                        <CloseCircleOutlined className="tailwind-text-3xl tailwind-text-red-600" />
                      </div>
                      <Text
                        strong
                        className="tailwind-text-red-800 tailwind-text-lg"
                      >
                        Sai
                      </Text>
                      {config.incorrectAnswerPenalty &&
                      config.incorrectAnswerPenalty > 0 ? (
                        <Text
                          type="danger"
                          className="tailwind-text-sm tailwind-mt-1"
                        >
                          -{config.incorrectAnswerPenalty} điểm
                        </Text>
                      ) : (
                        <Text
                          type="secondary"
                          className="tailwind-text-sm tailwind-mt-1"
                        >
                          0 điểm
                        </Text>
                      )}
                    </>
                  )}
                </div>
              ) : (
                <div className="tailwind-h-full tailwind-w-full tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-p-4 tailwind-text-white tailwind-relative">
                  {/* Decorative background patterns */}
                  <div className="tailwind-absolute tailwind-top-0 tailwind-right-0 tailwind-w-16 tailwind-h-16 tailwind-rounded-bl-full tailwind-bg-white tailwind-bg-opacity-20"></div>
                  <div className="tailwind-absolute tailwind-bottom-0 tailwind-left-0 tailwind-w-16 tailwind-h-16 tailwind-rounded-tr-full tailwind-bg-white tailwind-bg-opacity-20"></div>

                  {/* Small decorative circles */}
                  <div className="tailwind-absolute tailwind-top-4 tailwind-left-4 tailwind-w-4 tailwind-h-4 tailwind-rounded-full tailwind-bg-white tailwind-bg-opacity-20"></div>
                  <div className="tailwind-absolute tailwind-bottom-4 tailwind-right-4 tailwind-w-4 tailwind-h-4 tailwind-rounded-full tailwind-bg-white tailwind-bg-opacity-20"></div>
                  <div className="tailwind-absolute tailwind-top-12 tailwind-right-12 tailwind-w-3 tailwind-h-3 tailwind-rounded-full tailwind-bg-white tailwind-bg-opacity-20"></div>

                  {/* Number circle with glowing effect */}
                  <div className="tailwind-w-24 tailwind-h-24 tailwind-rounded-full tailwind-bg-white tailwind-bg-opacity-25 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-mb-3 tailwind-shadow-lg tailwind-border tailwind-border-white tailwind-border-opacity-40 tailwind-relative">
                    {/* Inner glow */}
                    <div className="tailwind-absolute tailwind-inset-0 tailwind-rounded-full tailwind-bg-opacity-20 tailwind-animate-pulse"></div>
                    <Title
                      level={1}
                      className="tailwind-text-white tailwind-m-0 tailwind-drop-shadow-md"
                      style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
                    >
                      {index + 1}
                    </Title>
                  </div>
                  <Text
                    className="tailwind-text-white tailwind-font-medium tailwind-text-base tailwind-drop-shadow-md"
                    style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
                  >
                    {gameStatus === 'ready' ? 'Chờ bắt đầu' : 'Click để mở'}
                  </Text>
                </div>
              )}
            </Card>
          );
        })}
      </div>
    );
  };

  // Render game results
  const renderGameResults = () => {
    if (gameStatus !== 'complete') return null;

    const columns = [
      {
        title: 'Câu hỏi',
        dataIndex: 'question',
        key: 'question',
        render: (question: Question) => (
          <Text ellipsis className="tailwind-font-medium tailwind-max-w-xs">
            {question.questionText}
          </Text>
        ),
      },
      {
        title: 'Trả lời',
        dataIndex: 'userAnswer',
        key: 'userAnswer',
        render: (answer: QuestionAnswer) => {
          if (typeof answer === 'boolean') {
            return answer ? 'Đúng' : 'Sai';
          } else if (typeof answer === 'number') {
            return `Đáp án ${String.fromCharCode(65 + answer)}`;
          } else {
            return String(answer);
          }
        },
      },
      {
        title: 'Kết quả',
        dataIndex: 'isCorrect',
        key: 'isCorrect',
        render: (isCorrect: boolean) => (
          <Tag color={isCorrect ? 'success' : 'error'}>
            {isCorrect ? 'Chính xác' : 'Không chính xác'}
          </Tag>
        ),
      },
      {
        title: 'Điểm',
        dataIndex: 'points',
        key: 'points',
        align: 'right' as 'right',
        render: (points: number) => (
          <Text
            strong
            className={
              points > 0 ? 'tailwind-text-green-600' : 'tailwind-text-red-600'
            }
          >
            {points > 0 ? `+${points}` : points}
          </Text>
        ),
      },
    ];

    return (
      <Card
        title={
          <Space>
            <TrophyOutlined className="tailwind-text-yellow-500" />
            <span className="tailwind-text-xl tailwind-font-bold tailwind-text-blue-800">
              Kết Quả Trò Chơi
            </span>
          </Space>
        }
        className="tailwind-mt-6 tailwind-shadow-sm"
      >
        <Row gutter={[16, 16]} className="tailwind-mb-6">
          <Col xs={24} sm={8}>
            <Card className="tailwind-bg-gray-50">
              <Statistic
                title="Tổng Điểm"
                value={score}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card className="tailwind-bg-gray-50">
              <Statistic
                title="Số câu đúng"
                value={
                  questionResults.filter((result) => result.isCorrect).length
                }
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card className="tailwind-bg-gray-50">
              <Statistic
                title="Tỷ lệ chính xác"
                value={Math.round(
                  (questionResults.filter((result) => result.isCorrect).length /
                    effectiveQuestions.length) *
                    100
                )}
                suffix="%"
                precision={0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        <Table
          dataSource={questionResults}
          columns={columns}
          rowKey={(_, index) => index?.toString() || '0'}
          pagination={false}
          summary={() => (
            <Table.Summary>
              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={3}
                  className="tailwind-text-right"
                >
                  <Text strong>Tổng điểm:</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <Text strong>{score}</Text>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
          className="tailwind-mb-4"
        />
      </Card>
    );
  };

  // Render question modal
  const renderQuestionModal = () => {
    if (!showModal || currentQuestionIndex === null) return null;

    const currentQuestion = effectiveQuestions[currentQuestionIndex];
    if (!currentQuestion) return null;

    const timerProgress =
      config.timeLimit && timeLeft ? (timeLeft / config.timeLimit) * 100 : 0;
    const timerStatus = timerProgress <= 30 ? 'exception' : 'active';

    return (
      <ModalAntdCustom
        open={showModal}
        title={
          <div className="tailwind-text-white tailwind-p-4 tailwind-m-0 tailwind-rounded-t-lg">
            <Title
              level={4}
              className="tailwind-text-white tailwind-m-0 tailwind-text-left"
            >
              Câu hỏi #{currentQuestionIndex + 1}
            </Title>
          </div>
        }
        onCancel={handleCloseModal}
        footer={null}
        width={1000}
        centered
        maskClosable={false}
        className={`tailwind-rounded-lg tailwind-overflow-hidden mini-game-question-modal`}
        styles={{ body: { padding: '24px' } }}
        zIndex={9999}
      >
        {/* Timer if enabled and should be shown */}
        {config.timeLimit && timeLeft !== undefined && showTimer && (
          <div className="tailwind-w-full tailwind-mb-4">
            <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-1">
              <Text type="secondary">Thời gian còn lại:</Text>
              <Text type={timeLeft < 10 ? 'danger' : 'secondary'} strong>
                {timeLeft}s
              </Text>
            </div>
            <Progress
              percent={timerProgress}
              status={timerStatus as 'exception' | 'active'}
              showInfo={false}
              strokeColor={timeLeft < 10 ? '#ff4d4f' : '#1890ff'}
              className="tailwind-mb-2"
            />
          </div>
        )}

        {/* Question content */}
        {!showAnswer ? (
          <div className="tailwind-mb-6">
            <QuestionComponent
              question={currentQuestion}
              onAnswer={handleConfirmAnswer}
              disabled={false}
            />
          </div>
        ) : (
          <div className="tailwind-text-left">
            <QuestionResultComponent
              question={currentQuestion}
              userAnswer={userAnswer}
              isCorrect={
                questionResults.length > 0
                  ? questionResults[questionResults.length - 1].isCorrect
                  : false
              }
              showExplanation={config.showExplanation}
            />
          </div>
        )}

        {/* Action buttons */}
        <div className="tailwind-flex tailwind-justify-end tailwind-mt-4 tailwind-gap-2">
          {!showAnswer ? (
            config.allowSkip && (
              <Button onClick={handleCloseModal}>Bỏ qua</Button>
            )
          ) : (
            <Button type="primary" onClick={handleContinueAfterAnswer}>
              Tiếp tục
            </Button>
          )}
        </div>
      </ModalAntdCustom>
    );
  };

  return (
    <div
      className="tailwind-w-full tailwind-max-w-full tailwind-mx-auto mini-game-container"
      id="quizcard-game-component-container"
    >
      {/* Game info bar */}
      <Card
        size="small"
        className="tailwind-mb-4 tailwind-flex tailwind-justify-between tailwind-items-center tailwind-bg-gray-50"
      >
        <Space>
          <Badge
            count={score}
            showZero
            overflowCount={9999}
            className="tailwind-mr-3"
            style={{ backgroundColor: '#1890ff' }}
          >
            <Button type="primary" size="small">
              Điểm
            </Button>
          </Badge>

          <Badge
            count={`${treasureOpened.length}/${effectiveQuestions.length}`}
            showZero
            className="tailwind-mr-3"
            style={{ backgroundColor: '#52c41a' }}
          >
            <Button
              type="primary"
              size="small"
              className="tailwind-bg-blue-500"
            >
              Câu hỏi
            </Button>
          </Badge>
        </Space>
      </Card>

      {/* Message for player */}
      {message && (
        <Alert
          message={message}
          type="info"
          showIcon
          className="tailwind-mb-4"
        />
      )}

      {/* Game start screen */}
      {gameStatus === 'ready' && (
        <Card className="tailwind-mb-6 tailwind-bg-gradient-to-r tailwind-from-blue-50 tailwind-to-blue-100 tailwind-text-center">
          <Title level={3} className="tailwind-text-blue-800 tailwind-mb-4">
            Sẵn sàng lật thẻ câu hỏi?
          </Title>
          <Paragraph className="tailwind-mb-6 tailwind-text-blue-700">
            Lật các thẻ câu hỏi bằng cách trả lời đúng các câu hỏi. Càng trả lời
            đúng, bạn càng kiếm được nhiều điểm!
          </Paragraph>
          <Button
            type="primary"
            size="large"
            className="tailwind-bg-blue-600 hover:tailwind-bg-blue-700"
            onClick={handleStartGame}
          >
            Bắt đầu trò chơi
          </Button>
        </Card>
      )}

      {/* Game board */}
      {renderGameBoard()}

      {/* Game controls */}
      <div className="tailwind-flex tailwind-justify-center tailwind-mb-4 tailwind-gap-4">
        {gameStatus === 'complete' && (
          <Button
            type="primary"
            icon={<RightOutlined />}
            onClick={handleResetGame}
            className="tailwind-bg-blue-600 hover:tailwind-bg-blue-700"
          >
            Bắt đầu trò chơi mới
          </Button>
        )}
        {gameStatus === 'playing' && (
          <Button
            type="primary"
            danger
            onClick={handleEndGameEarly}
            className="tailwind-bg-red-600 hover:tailwind-bg-red-700"
          >
            Kết thúc trò chơi
          </Button>
        )}
      </div>

      {/* Game results */}
      {renderGameResults()}

      {/* Question modal */}
      {renderQuestionModal()}
    </div>
  );
};

export default QuizCardGameComponent;

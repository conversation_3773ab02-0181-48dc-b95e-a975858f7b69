.question-editor-modal .ant-modal-body {
  padding: 16px;
  overflow: auto;
  max-height: calc(90vh - 110px);
}

.question-editor-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.question-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-with-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 60%;
}

.limited-title {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.status-tag {
  margin-left: 8px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-tag .anticon {
  font-size: 12px;
}

.question-editor-content {
  height: 100%;
}

/* Split view container for resizable panels */
.question-split-view {
  display: flex;
  position: relative;
  height: 100%;
  overflow: hidden;
}

.question-preview-container,
.question-config-container {
  height: max-content;
  overflow: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
  transition: width 0.1s ease;
}

/* Adjust padding for the resizable panels */
.question-split-view .question-preview-container {
  padding-right: 20px;
}

.question-split-view .question-config-container {
  padding-left: 20px;
}

.question-preview-container h3,
.question-config-container h3 {
  margin-top: 0;
  margin-bottom: 16px;
  display: inline-block;
}

/* Prevent practice engine from overflowing */
.question-preview-container .practice-engines-container,
.question-config-container .practice-engines-container {
  max-height: calc(70vh - 90px);
}

/* Override some PracticeEngine styles when inside editor */
.question-editor-modal .practice-content {
  overflow-y: auto;
  max-height: calc(70vh - 90px);
}

.question-editor-modal .practice-navigation {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 8px;
  z-index: 10;
}

/* Type selector dropdown */
.question-type-selector,
.question-subject-selector {
  margin-bottom: 16px;
  width: 100%;
  max-width: 300px;
}

.subject-badge {
  display: inline-flex;
  align-items: center;
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 8px;
}

.subject-badge .anticon {
  margin-right: 4px;
}

.type-change-warning {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-change-icon {
  color: #faad14;
  font-size: 16px;
}

/* Improved styles for question handling in different modes */
.question-not-found {
  padding: 24px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #999;
  margin: 16px 0;
}

/* Metadata panel styles */
.metadata-panel {
  background-color: #f9f9f9;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.metadata-section {
  margin-bottom: 16px;
}

.metadata-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #444;
}

.tags-container,
.topics-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .question-editor-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .question-editor-header .ant-space {
    width: 100%;
    justify-content: space-between;
  }

  .question-type-selector {
    max-width: 100%;
  }

  .question-split-view {
    flex-direction: column;
  }

  .question-split-view .question-preview-container,
  .question-split-view .question-config-container {
    width: 100% !important;
    padding: 16px;
  }
}

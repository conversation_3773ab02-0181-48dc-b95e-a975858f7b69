/**
 * Utility functions for media handling in the RichTextEditor
 */

/**
 * Extract YouTube video ID from a URL
 * @param url YouTube URL
 * @returns YouTube video ID or empty string if not found
 */
export const extractYoutubeId = (url: string): string => {
  const regExp =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);
  return match && match[2].length === 11 ? match[2] : '';
};

/**
 * Enhance audio elements in view mode
 */
export const enhanceAudioElements = (): void => {
  // Process standalone audio elements
  const audioElements = document.querySelectorAll('.rich-text-content audio');
  audioElements.forEach((audio) => {
    audio.setAttribute('controls', 'true');
    (audio as HTMLElement).style.minHeight = '54px';
    (audio as HTMLElement).style.display = 'block';
  });

  // Process audio elements inside figures
  const audioFigures = document.querySelectorAll(
    '.rich-text-content figure.e-audio-wrap'
  );
  audioFigures.forEach((figure) => {
    (figure as HTMLElement).style.display = 'block';
    const audio = figure.querySelector('audio');
    if (audio) {
      audio.setAttribute('controls', 'true');
      (audio as HTMLElement).style.minHeight = '54px';
      (audio as HTMLElement).style.display = 'block';
    }
  });
};

/**
 * Enhance video elements in view mode
 */
export const enhanceVideoElements = (): void => {
  const videoElements = document.querySelectorAll(
    '.rich-text-content video, .rich-text-content iframe'
  );
  videoElements.forEach((video) => {
    if (video.tagName.toLowerCase() === 'video') {
      video.setAttribute('controls', 'true');
    } else if (video.tagName.toLowerCase() === 'iframe') {
      // Handle YouTube iframes
      const iframe = video as HTMLIFrameElement;
      const src = iframe.getAttribute('src') || '';

      // Check if it's a YouTube embed
      if (src.includes('youtube.com/embed/')) {
        // Ensure it has proper attributes
        iframe.setAttribute('allowfullscreen', 'true');
        iframe.setAttribute(
          'allow',
          'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
        );

        // Add parameters to the URL if they don't exist
        if (!src.includes('?')) {
          iframe.setAttribute('src', src + '?autoplay=0&controls=1');
        }

        // Set proper dimensions if not already set
        if (!iframe.style.width) iframe.style.width = '560px';
        if (!iframe.style.height) iframe.style.height = '315px';
      }
    }

    // Set display block for all video elements
    (video as HTMLElement).style.display = 'block';
  });
};

/**
 * Enhance image elements in view mode
 */
export const enhanceImageElements = (): void => {
  const imageElements = document.querySelectorAll('.rich-text-content img');
  imageElements.forEach((img) => {
    (img as HTMLElement).style.display = 'inline-block';
    (img as HTMLElement).style.maxWidth = '100%';
    (img as HTMLElement).style.height = 'auto';
  });
};

/**
 * Enhance table elements in view mode
 */
export const enhanceTableElements = (): void => {
  const tableElements = document.querySelectorAll('.rich-text-content table');
  tableElements.forEach((table) => {
    (table as HTMLElement).style.display = 'table';
    (table as HTMLElement).style.maxWidth = '100%';
    (table as HTMLElement).style.borderCollapse = 'collapse';
    (table as HTMLElement).style.margin = '10px 0';

    // Process table cells
    const cells = table.querySelectorAll('td, th');
    cells.forEach((cell) => {
      (cell as HTMLElement).style.border = '1px solid #ddd';
      (cell as HTMLElement).style.padding = '8px';
    });
  });
};

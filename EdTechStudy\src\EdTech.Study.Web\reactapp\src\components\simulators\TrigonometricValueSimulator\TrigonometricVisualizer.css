/* TrigonometricVisualizer.css */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tailwind-animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.tailwind-animate-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Hover effects for trigonometric value cards */
.trig-value-card {
  transition: all 0.2s ease-in-out;
}

.trig-value-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Improve table styling */
.ant-table-thead > tr > th {
  background-color: #f0f4ff !important;
  color: #3b82f6 !important;
  font-weight: 600 !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f8fafc !important;
}

/* Button animations */
.toggle-button {
  transition: all 0.2s ease-in-out;
}

.toggle-button:hover {
  transform: scale(1.05);
}

.toggle-button:active {
  transform: scale(0.95);
}

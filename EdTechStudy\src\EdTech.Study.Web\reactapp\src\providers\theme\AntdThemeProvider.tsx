import React from 'react';
import { ConfigProvider, theme } from 'antd';
import type { ThemeConfig } from 'antd';

interface AntdThemeProviderProps {
  children: React.ReactNode;
}

const AntdThemeProvider: React.FC<AntdThemeProviderProps> = ({ children }) => {
  // Common component configurations
  const componentConfig: ThemeConfig['components'] = {
    Card: {
      colorText: 'var(--edtt-color-text-default)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      boxShadow: 'var(--edtt-shadow)',
    },
    Layout: {
      headerBg: 'var(--edtt-color-bg-default)',
      siderBg: 'var(--edtt-color-bg-default)',
    },
    Button: {
      // Primary button customization
      colorPrimary: 'var(--edtt-color-primary)',
    },
    Switch: {
      colorPrimary: 'var(--edtt-color-text-default)',
      handleSize: 27,
      trackHeight: 32,
      innerMaxMargin: 32,
    },
    Select: {
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorBorder: 'var(--edtt-color-primary)',
      hoverBorderColor: 'var(--edtt-color-quaternary-base)',
      activeBorderColor: 'var(--edtt-color-secondary-base)',
      colorText: 'var(--edtt-color-text)',
      colorTextPlaceholder: 'var(--edtt-color-disabled-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      colorBgContainerDisabled: 'var(--edtt-color-disabled-bg)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorBgElevated: 'var(--edtt-color-bg-default)',
      optionSelectedBg: 'var(--edtt-color-selected)',
      optionActiveBg: 'var(--edtt-color-selected)',
    },
    Table: {
      colorBgContainer: 'var(--edtt-color-bg-default)',
      headerBg: 'var(--edtt-color-bg-secondary)',
      colorText: 'var(--edtt-color-text)',
      colorTextHeading: 'var(--edtt-color-text)',
      colorBorderSecondary: 'var(--edtt-color-primary)',
      colorBorder: 'var(--edtt-color-primary)',
      colorFillAlter: 'var(--edtt-color-selected)',
      colorFillContent: 'var(--edtt-color-disabled-bg)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
    },
    Menu: {
      colorItemBg: 'var(--edtt-color-bg-default)',
      colorItemText: 'var(--edtt-color-text)',
      colorItemTextSelected: 'var(--edtt-color-primary)',
      colorItemTextHover: 'var(--edtt-color-primary)',
      colorItemBgSelected: 'var(--edtt-color-selected)',
      colorItemBgHover: 'var(--edtt-color-selected)',
      itemSelectedBg: 'var(--edtt-color-selected)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorItemTextDisabled: 'var(--edtt-color-disabled-text)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorPrimaryHover: 'var(--edtt-color-quaternary-base)',
      colorPrimaryActive: 'var(--edtt-color-secondary-base)',
      colorBgTextHover: 'var(--edtt-color-selected)',
      colorBgTextActive: 'var(--edtt-color-selected)',
      colorErrorOutline: 'var(--edtt-color-status-error)',
      colorFillContent: 'var(--edtt-color-selected)',
      colorFillContentHover: 'var(--edtt-color-quaternary-base)',
      colorBorder: 'var(--edtt-color-primary)',
      colorBorderSecondary: 'var(--edtt-color-primary)',
      colorTextDescription: 'var(--edtt-color-disabled-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      boxShadow: 'var(--edtt-shadow)',
      motionDurationSlow: '0.3s',
      motionDurationMid: '0.2s',
    },
    Drawer: {
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorText: 'var(--edtt-color-text)',
      colorBorder: 'var(--edtt-color-primary)',
      colorIcon: 'var(--edtt-color-text)',
      colorIconHover: 'var(--edtt-color-primary)',
      colorBgMask: 'rgba(0, 0, 0, 0.45)',
      colorTextHeading: 'var(--edtt-color-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
    },
    Modal: {
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorText: 'var(--edtt-color-text)',
      colorBorder: 'var(--edtt-color-primary)',
      colorIcon: 'var(--edtt-color-text)',
      colorIconHover: 'var(--edtt-color-primary)',
      colorBgMask: 'rgba(0, 0, 0, 0.45)',
      colorTextHeading: 'var(--edtt-color-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorPrimary: 'var(--edtt-color-primary)',
    },
    ColorPicker: {
      colorTextQuaternary: 'var(--edtt-color-text)',
      colorBorder: 'var(--edtt-color-primary)',
      colorText: 'var(--edtt-color-text)',
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
    },
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        components: componentConfig,
        token: {
          colorText: 'var(--edtt-color-text-default)',
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export default AntdThemeProvider;

import React, { useState } from 'react';
import {
  CoordinateFinderGameConfig,
  Coordinate,
  defaultCoordinateFinderGameConfig,
} from './CoordinateFinderGameConfig';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Table,
  Tabs,
  Typography,
  Switch,
  Divider,
  message,
} from 'antd';
import {
  PlusOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { DeleteIcon } from '../../icons/IconIndex';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface CoordinateFinderGameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: CoordinateFinderGameConfig) => void;
  defaultConfig?: Partial<CoordinateFinderGameConfig>;
  path?: string;
  order?: number;
  addOrUpdateParamComponent?: (data: any) => void;
}

// Định nghĩa các tab
type TabType = 'basic' | 'coordinates' | 'advanced';

const CoordinateFinderGameModal: React.FC<CoordinateFinderGameModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig = {},
  path,
  order,
  addOrUpdateParamComponent,
}) => {
  // State cho việc chuyển tab
  const [activeTab, setActiveTab] = useState<TabType>('basic');

  // State cho form cấu hình
  const [config, setConfig] = useState<Partial<CoordinateFinderGameConfig>>({
    ...defaultCoordinateFinderGameConfig,
    ...defaultConfig,
  });

  // State cho việc thêm tọa độ mới
  const [newCoordinate, setNewCoordinate] = useState<Partial<Coordinate>>({
    name: '',
    latitude: 0,
    longitude: 0,
    hint: '',
  });

  // Xử lý thay đổi giá trị tọa độ mới với Ant Design components
  const handleAntInputChange = (
    value: string | number | null,
    name: string
  ) => {
    if (value === null) return;

    setNewCoordinate({
      ...newCoordinate,
      [name]: value,
    });
  };

  // Xử lý thay đổi giá trị config với Ant Design components
  const handleAntConfigChange = (value: any, name: string) => {
    setConfig({
      ...config,
      [name]: value,
    });
  };

  // Xử lý thay đổi giá trị center với Ant Design components
  const handleAntCenterChange = (
    value: number | null,
    field: 'lat' | 'lng'
  ) => {
    if (value === null) return;

    setConfig({
      ...config,
      initialCenter: {
        lat: field === 'lat' ? value : config.initialCenter?.lat ?? 16.0,
        lng: field === 'lng' ? value : config.initialCenter?.lng ?? 106.0,
      },
    });
  };

  // Thêm tọa độ mới vào danh sách
  const handleAddCoordinate = () => {
    if (
      newCoordinate.name &&
      newCoordinate.latitude !== undefined &&
      newCoordinate.longitude !== undefined
    ) {
      setConfig({
        ...config,
        coordinates: [
          ...(config.coordinates || []),
          {
            name: newCoordinate.name,
            latitude: newCoordinate.latitude,
            longitude: newCoordinate.longitude,
            hint: newCoordinate.hint || undefined,
          } as Coordinate,
        ],
      });

      // Reset form
      setNewCoordinate({
        name: '',
        latitude: 0,
        longitude: 0,
        hint: '',
      });

      message.success('Đã thêm tọa độ mới');
    } else {
      message.error('Vui lòng điền đầy đủ thông tin tọa độ');
    }
  };

  // Xóa tọa độ khỏi danh sách
  const handleRemoveCoordinate = (index: number) => {
    setConfig({
      ...config,
      coordinates: (config.coordinates || []).filter((_, i) => i !== index),
    });
    message.success('Đã xóa tọa độ');
  };

  // Submit form
  const handleSubmit = () => {
    // Kiểm tra các trường bắt buộc
    if (
      !config.coordinates ||
      config.coordinates.length === 0 ||
      config.correctAnswerScore === undefined ||
      config.proximityThreshold === undefined
    ) {
      message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
      return;
    }

    // Sử dụng addOrUpdateParamComponent để cập nhật cấu hình vào state nếu có
    if (
      addOrUpdateParamComponent &&
      path !== undefined &&
      order !== undefined
    ) {
      addOrUpdateParamComponent({
        ...defaultConfig, // Giữ lại các trường khác trong params
        ...config, // Cập nhật các trường từ cấu hình mới
      });
    }
    onSubmit(config as CoordinateFinderGameConfig);
    onClose();
  };

  // Define columns for coordinates table
  const coordinateColumns: ColumnsType<Coordinate> = [
    {
      title: 'Tên địa điểm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Vĩ độ',
      dataIndex: 'latitude',
      key: 'latitude',
    },
    {
      title: 'Kinh độ',
      dataIndex: 'longitude',
      key: 'longitude',
    },
    {
      title: 'Gợi ý',
      dataIndex: 'hint',
      key: 'hint',
      render: (text) => text || '(Không có)',
    },
    {
      title: 'Thao tác',
      key: 'action',
      render: (_, __, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteIcon />}
          onClick={() => handleRemoveCoordinate(index)}
        >
          Xóa
        </Button>
      ),
    },
  ];

  const items = [
    {
      key: 'basic',
      label: (
        <span>
          <InfoCircleOutlined /> Cấu hình cơ bản
        </span>
      ),
      children: (
        <div className="tailwind-space-y-6">
          <Text type="secondary">
            Cấu hình cơ bản cho trò chơi tìm tọa độ. Sử dụng OpenStreetMap -
            không cần API key.
          </Text>

          <Form layout="vertical">
            <Form.Item label="Tiêu đề trò chơi">
              <Input
                value={config.titleProps?.text || 'Tìm Tọa Độ Bí Ẩn'}
                onChange={(e) => handleAntConfigChange(e.target.value, 'title')}
                placeholder="Nhập tiêu đề trò chơi"
              />
            </Form.Item>

            <Form.Item label="Mô tả">
              <TextArea
                rows={2}
                value={
                  config.description ||
                  'Tìm vị trí trên bản đồ dựa vào tọa độ bí ẩn.'
                }
                onChange={(e) =>
                  handleAntConfigChange(e.target.value, 'description')
                }
                placeholder="Nhập mô tả trò chơi"
              />
            </Form.Item>

            <div className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-2 tailwind-gap-4">
              <Form.Item label="Mức zoom ban đầu">
                <InputNumber
                  min={1}
                  max={20}
                  value={config.initialZoom || 6}
                  onChange={(value) =>
                    handleAntConfigChange(value, 'initialZoom')
                  }
                  className="tailwind-w-full"
                />
              </Form.Item>

              <Form.Item label="Độ khó">
                <Select
                  value={config.difficultyLevel || 'medium'}
                  onChange={(value) =>
                    handleAntConfigChange(value, 'difficultyLevel')
                  }
                  className="tailwind-w-full"
                >
                  <Option value="easy">Dễ</Option>
                  <Option value="medium">Trung bình</Option>
                  <Option value="hard">Khó</Option>
                </Select>
              </Form.Item>

              <Form.Item label="Vĩ độ trung tâm ban đầu">
                <InputNumber
                  step={0.000001}
                  value={config.initialCenter?.lat || 16.0}
                  onChange={(value) => handleAntCenterChange(value, 'lat')}
                  className="tailwind-w-full"
                />
              </Form.Item>

              <Form.Item label="Kinh độ trung tâm ban đầu">
                <InputNumber
                  step={0.000001}
                  value={config.initialCenter?.lng || 106.0}
                  onChange={(value) => handleAntCenterChange(value, 'lng')}
                  className="tailwind-w-full"
                />
              </Form.Item>
            </div>
          </Form>
        </div>
      ),
    },
    {
      key: 'coordinates',
      label: (
        <span>
          <EnvironmentOutlined /> Tọa độ bí ẩn
        </span>
      ),
      children: (
        <div className="tailwind-space-y-6">
          <div>
            <Title level={4} style={{ marginBottom: '16px' }}>
              Danh sách tọa độ bí ẩn{' '}
              <span className="tailwind-text-red-500">*</span>
            </Title>

            {/* Danh sách tọa độ đã thêm */}
            {config.coordinates && config.coordinates.length > 0 ? (
              <Table
                columns={coordinateColumns}
                dataSource={config.coordinates}
                size="small"
                pagination={false}
                rowKey={(_, index) => index?.toString() || '0'}
                className="tailwind-mb-6"
              />
            ) : (
              <div className="tailwind-mb-4 tailwind-p-4 tailwind-bg-red-50 tailwind-border tailwind-border-red-200 tailwind-rounded-md">
                <Text type="danger">
                  Chưa có tọa độ nào được thêm. Vui lòng thêm ít nhất một tọa
                  độ.
                </Text>
              </div>
            )}

            {/* Form thêm tọa độ mới */}
            <div className="tailwind-bg-gray-50 tailwind-p-4 tailwind-rounded-md tailwind-border">
              <Title level={5} style={{ marginBottom: '16px' }}>
                Thêm tọa độ mới
              </Title>

              <Form layout="vertical">
                <div className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-2 tailwind-gap-4">
                  <Form.Item
                    label={
                      <>
                        Tên địa điểm{' '}
                        <span className="tailwind-text-red-500">*</span>
                      </>
                    }
                  >
                    <Input
                      value={newCoordinate.name || ''}
                      onChange={(e) =>
                        handleAntInputChange(e.target.value, 'name')
                      }
                      placeholder="Nhập tên địa điểm"
                    />
                  </Form.Item>

                  <Form.Item label="Gợi ý (không bắt buộc)">
                    <Input
                      value={newCoordinate.hint || ''}
                      onChange={(e) =>
                        handleAntInputChange(e.target.value, 'hint')
                      }
                      placeholder="Nhập gợi ý (không bắt buộc)"
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <>
                        Vĩ độ <span className="tailwind-text-red-500">*</span>
                      </>
                    }
                  >
                    <InputNumber
                      step={0.000001}
                      value={
                        newCoordinate.latitude === undefined
                          ? 0
                          : newCoordinate.latitude
                      }
                      onChange={(value) =>
                        handleAntInputChange(value, 'latitude')
                      }
                      className="tailwind-w-full"
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <>
                        Kinh độ <span className="tailwind-text-red-500">*</span>
                      </>
                    }
                  >
                    <InputNumber
                      step={0.000001}
                      value={
                        newCoordinate.longitude === undefined
                          ? 0
                          : newCoordinate.longitude
                      }
                      onChange={(value) =>
                        handleAntInputChange(value, 'longitude')
                      }
                      className="tailwind-w-full"
                    />
                  </Form.Item>
                </div>

                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddCoordinate}
                >
                  Thêm tọa độ
                </Button>
              </Form>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'advanced',
      label: (
        <span>
          <SettingOutlined /> Nâng cao
        </span>
      ),
      children: (
        <div className="tailwind-space-y-6">
          <Form layout="vertical">
            <div className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-2 tailwind-gap-4">
              <Form.Item
                label="Giới hạn thời gian (giây)"
                help="Nhập 0 để không giới hạn thời gian"
              >
                <InputNumber
                  min={0}
                  value={
                    config.timeLimit === undefined ? 120 : config.timeLimit
                  }
                  onChange={(value) =>
                    handleAntConfigChange(value, 'timeLimit')
                  }
                  className="tailwind-w-full"
                />
              </Form.Item>

              <Form.Item label="Điểm bị trừ khi dùng gợi ý">
                <InputNumber
                  min={0}
                  value={
                    config.hintPenalty === undefined ? 5 : config.hintPenalty
                  }
                  onChange={(value) =>
                    handleAntConfigChange(value, 'hintPenalty')
                  }
                  className="tailwind-w-full"
                />
              </Form.Item>

              <Form.Item
                label={
                  <>
                    Điểm thưởng cho câu trả lời đúng{' '}
                    <span className="tailwind-text-red-500">*</span>
                  </>
                }
              >
                <InputNumber
                  min={1}
                  value={
                    config.correctAnswerScore === undefined
                      ? 10
                      : config.correctAnswerScore
                  }
                  onChange={(value) =>
                    handleAntConfigChange(value, 'correctAnswerScore')
                  }
                  className="tailwind-w-full"
                />
              </Form.Item>

              <Form.Item
                label={
                  <>
                    Ngưỡng khoảng cách (km){' '}
                    <span className="tailwind-text-red-500">*</span>
                  </>
                }
                help="Khoảng cách tối đa (km) giữa vị trí người dùng chọn và vị trí đúng để được tính là chính xác"
              >
                <InputNumber
                  min={0.1}
                  step={0.1}
                  value={
                    config.proximityThreshold === undefined
                      ? 10
                      : config.proximityThreshold
                  }
                  onChange={(value) =>
                    handleAntConfigChange(value, 'proximityThreshold')
                  }
                  className="tailwind-w-full"
                />
              </Form.Item>
            </div>

            <Divider orientation="left">Tùy chọn bản đồ</Divider>

            <div className="tailwind-grid tailwind-grid-cols-1 sm:tailwind-grid-cols-2 tailwind-gap-4">
              <Form.Item
                label="Cho phép zoom bằng chuột"
                valuePropName="checked"
              >
                <Switch
                  checked={config.mapOptions?.scrollWheelZoom}
                  onChange={(checked) =>
                    setConfig({
                      ...config,
                      mapOptions: {
                        ...(config.mapOptions || {}),
                        scrollWheelZoom: checked,
                      },
                    })
                  }
                />
              </Form.Item>

              <Form.Item
                label="Cho phép zoom bằng double-click"
                valuePropName="checked"
              >
                <Switch
                  checked={config.mapOptions?.doubleClickZoom}
                  onChange={(checked) =>
                    setConfig({
                      ...config,
                      mapOptions: {
                        ...(config.mapOptions || {}),
                        doubleClickZoom: checked,
                      },
                    })
                  }
                />
              </Form.Item>
            </div>
          </Form>
        </div>
      ),
    },
  ];

  return (
    <ModalAntdCustom
      title="Cấu hình trò chơi"
      open={isOpen}
      onCancel={onClose}
      width={1000}
      styles={{ body: { padding: 0 } }}
      className="tailwind-max-h-[90vh]"
      zIndex={10000}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Hủy
        </Button>,
        <Button
          key="submit"
          type="primary"
          className="tailwind-bg-blue-500"
          onClick={handleSubmit}
        >
          Lưu cấu hình
        </Button>,
      ]}
    >
      <Tabs
        items={items}
        activeKey={activeTab}
        onChange={(activeKey) => setActiveTab(activeKey as TabType)}
        type="card"
        className="tailwind-px-6 tailwind-pt-4"
      />
    </ModalAntdCustom>
  );
};

export default CoordinateFinderGameModal;

import React from 'react';
import { Question, MultipleChoiceQuestion } from './QuestionBankConfig';
import MultipleChoiceQuestionComponent from './question/MultipleChoiceQuestionComponent';
import TrueFalseQuestionComponent from './question/TrueFalseQuestionComponent';
import FillInBlankQuestionComponent from './question/FillInBlankQuestionComponent';
import MatchingQuestionComponent from './question/MatchingQuestionComponent';
import OrderingQuestionComponent from './question/OrderingQuestionComponent';

interface QuestionComponentProps {
  question: Question;
  onAnswer: (answer: any) => void;
  disabled?: boolean;
  configMode?: boolean;
  onOptionTextChange?: (index: number, text: string) => void;
  onCorrectAnswerChange?: (index: number) => void;
  onAddOption?: () => void;
  onRemoveOption?: (index: number) => void;
}

const QuestionComponent: React.FC<QuestionComponentProps> = ({
  question,
  onAnswer,
  disabled = false,
  configMode = false,
  onOptionTextChange,
  onCorrectAnswerChange,
  onAddOption,
  onRemoveOption,
}) => {
  const renderQuestionContent = () => {
    switch (question.type) {
      case 'multiplechoice':
        return (
          <MultipleChoiceQuestionComponent
            question={question as MultipleChoiceQuestion}
            onAnswer={onAnswer}
            disabled={disabled}
            configMode={configMode}
            onOptionTextChange={onOptionTextChange}
            onCorrectAnswerChange={onCorrectAnswerChange}
            onAddOption={onAddOption}
            onRemoveOption={onRemoveOption}
          />
        );
      case 'truefalse':
        return (
          <TrueFalseQuestionComponent
            question={question}
            onAnswer={onAnswer}
            disabled={disabled}
          />
        );
      case 'fillin':
        return (
          <FillInBlankQuestionComponent
            question={question}
            onAnswer={onAnswer}
            disabled={disabled}
          />
        );
      case 'matching':
        return (
          <MatchingQuestionComponent
            question={question}
            onAnswer={onAnswer}
            disabled={disabled}
          />
        );
      case 'ordering':
        return (
          <OrderingQuestionComponent
            question={question}
            onAnswer={onAnswer}
            disabled={disabled}
          />
        );
      default:
        return <div>Loại câu hỏi không được hỗ trợ</div>;
    }
  };

  return (
    <div className="w-full">
      {!configMode && (
        <div className="mb-4">
          <h3 className="text-lg font-medium">{question.questionText}</h3>
        </div>
      )}
      {renderQuestionContent()}
    </div>
  );
};

export default QuestionComponent;

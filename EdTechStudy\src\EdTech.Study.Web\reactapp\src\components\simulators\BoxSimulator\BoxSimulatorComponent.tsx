import { useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { ThreeEvent } from '@react-three/fiber';
import { Mesh } from 'three';

// Định nghĩa các loại hình dạng
export type ShapeType = 'box' | 'semiSphere' | 'cone' | 'torus';

interface ShapeProps {
  position?: [number, number, number];
  color?: string;
  shapeType: ShapeType;
  rotationSpeed: number;
  isAnimating: boolean;
  [key: string]: any;
}

// Component cho các hình dạng 3D
function Shape(props: ShapeProps) {
  const { shapeType, color, rotationSpeed, isAnimating, ...restProps } = props;
  const ref = useRef<Mesh>(null);
  const [hovered, hover] = useState<boolean>(false);
  const [clicked, click] = useState<boolean>(false);

  // Xoay hình dạng theo tốc độ được cấu hình
  useFrame((_, delta: number) => {
    if (ref.current && isAnimating) {
      ref.current.rotation.x += delta * rotationSpeed;
      ref.current.rotation.y += delta * rotationSpeed * 0.5;
    }
  });

  // Hiển thị hình dạng tương ứng
  const renderGeometry = () => {
    switch (shapeType) {
      case 'semiSphere':
        // Sử dụng sphereGeometry với thông số để tạo bán nguyệt
        return (
          <sphereGeometry
            args={[0.7, 32, 32, 0, Math.PI * 2, 0, Math.PI / 2]}
          />
        );
      case 'cone':
        return <coneGeometry args={[0.7, 1.5, 32]} />;
      case 'torus':
        return <torusGeometry args={[0.5, 0.2, 16, 32]} />;
      case 'box':
      default:
        return <boxGeometry args={[1, 1, 1]} />;
    }
  };

  return (
    <mesh
      {...restProps}
      ref={ref}
      scale={clicked ? 1.5 : 1}
      onClick={(_: ThreeEvent<MouseEvent>) => click(!clicked)}
      onPointerOver={(event: ThreeEvent<PointerEvent>) => {
        event.stopPropagation();
        hover(true);
      }}
      onPointerOut={(_: ThreeEvent<PointerEvent>) => hover(false)}
    >
      {renderGeometry()}
      <meshStandardMaterial
        color={hovered ? 'hotpink' : color || 'orange'}
        side={shapeType === 'semiSphere' ? 2 : 0} // Hiển thị cả hai mặt cho bán nguyệt
      />
    </mesh>
  );
}

export interface BoxSimulatorComponentProps {
  leftShapeType: ShapeType;
  rightShapeType: ShapeType;
  leftShapeColor: string;
  rightShapeColor: string;
  rotationSpeed: number;
  isAnimating: boolean;
}

const BoxSimulatorComponent: React.FC<BoxSimulatorComponentProps> = ({
  leftShapeType,
  rightShapeType,
  leftShapeColor,
  rightShapeColor,
  rotationSpeed,
  isAnimating,
}) => {
  return (
    <Canvas style={{ height: '500px', width: '100%' }}>
      <ambientLight intensity={Math.PI / 2} />
      <spotLight
        position={[10, 10, 10]}
        angle={0.15}
        penumbra={1}
        decay={0}
        intensity={Math.PI}
      />
      <pointLight position={[-10, -10, -10]} decay={0} intensity={Math.PI} />
      <Shape
        position={[-1.2, 0, 0]}
        shapeType={leftShapeType}
        color={leftShapeColor}
        rotationSpeed={rotationSpeed}
        isAnimating={isAnimating}
      />
      <Shape
        position={[1.2, 0, 0]}
        shapeType={rightShapeType}
        color={rightShapeColor}
        rotationSpeed={rotationSpeed}
        isAnimating={isAnimating}
      />
      <OrbitControls />
    </Canvas>
  );
};

export default BoxSimulatorComponent;

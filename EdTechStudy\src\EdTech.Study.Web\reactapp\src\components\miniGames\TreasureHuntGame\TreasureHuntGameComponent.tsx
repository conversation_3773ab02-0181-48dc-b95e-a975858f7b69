import React, { useState, useEffect, useRef } from 'react';
import 'leaflet/dist/leaflet.css';
import '../MiniGameResponsive.css';
import {
  createCustomIcon,
  MapPosition,
  calculateDistance,
} from '../../common/MapCommonComponent/MapUtils';
import MapComponent from '../../common/MapCommonComponent/components/MapComponent';
import {
  StepResult,
  TreasureClue,
  TreasureHuntGameConfig,
  defaultTreasureHuntGameConfig,
} from './TreasureHuntGameConfig';
import { Button, Card, Typography, Row, Col, Tag, Alert, List } from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CompassOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined,
  RedoOutlined,
  RightOutlined,
  TrophyOutlined,
  BulbOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { applyMapInteractionStyles } from '../../common/MapCommonComponent/MapUtils';

const { Title, Text, Paragraph } = Typography;

interface TreasureHuntGameComponentProps {
  config?: TreasureHuntGameConfig;
  isFullscreen?: boolean;
}

const TreasureHuntGameComponent: React.FC<TreasureHuntGameComponentProps> = ({
  config = defaultTreasureHuntGameConfig,
  isFullscreen = false,
}) => {
  // States
  const [currentPosition, setCurrentPosition] = useState<MapPosition>(
    config.initialPosition
  );
  const [userMarkerPosition, setUserMarkerPosition] =
    useState<MapPosition | null>(null);
  const [currentClueIndex, setCurrentClueIndex] = useState<number>(0);
  const [stepResults, setStepResults] = useState<StepResult[]>([]);
  const [gameStatus, setGameStatus] = useState<
    'ready' | 'playing' | 'complete'
  >('ready');
  const [hintsUsed, setHintsUsed] = useState<number>(0);
  const [timeLeft, setTimeLeft] = useState<number | undefined>(
    config.timeLimit
  );
  const [message, setMessage] = useState<string>('');
  const [showTreasure, setShowTreasure] = useState<boolean>(false);
  const [targetPosition, setTargetPosition] = useState<MapPosition | null>(
    null
  );
  const [score, setScore] = useState<number>(100); // Start with high score, deduct points
  const [distanceToTreasure, setDistanceToTreasure] = useState<number | null>(
    null
  );

  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Get current clue
  const currentClue = config.clues[currentClueIndex];

  // Start timer if time limit is set
  useEffect(() => {
    if (config.timeLimit && gameStatus === 'playing') {
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev && prev > 0) {
            return prev - 1;
          } else {
            // Time's up
            clearInterval(timerRef.current as ReturnType<typeof setInterval>);
            handleTimeUp();
            return 0;
          }
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [currentClueIndex, config.timeLimit, gameStatus]);

  // Custom CSS for map controls in fullscreen mode
  useEffect(() => {
    applyMapInteractionStyles(
      isFullscreen,
      'fullscreen-map-styles-treasure-hunt'
    );
  }, [isFullscreen]);

  // Handle time's up
  const handleTimeUp = () => {
    setGameStatus('complete');
    setMessage('Hết thời gian! Bạn chưa tìm được kho báu.');

    // Show treasure location
    setShowTreasure(true);
  };

  // Calculate target position based on clue
  useEffect(() => {
    if (currentClue && currentPosition) {
      let target: MapPosition | null = null;

      switch (currentClue.type) {
        case 'direction':
          // Calculate new position based on direction and distance
          if (currentClue.direction && currentClue.distance) {
            target = calculatePositionFromDirection(
              currentPosition,
              currentClue.direction,
              currentClue.distance
            );
          }
          break;

        case 'latitudinal':
          // Move by latitude
          if (currentClue.distance !== undefined) {
            target = {
              lat: currentPosition.lat + currentClue.distance,
              lng: currentPosition.lng,
            };
          }
          break;

        case 'longitudinal':
          // Move by longitude
          if (currentClue.distance !== undefined) {
            target = {
              lat: currentPosition.lat,
              lng: currentPosition.lng + currentClue.distance,
            };
          }
          break;

        case 'coordinates':
          // Direct coordinates
          const [lat, lng] = currentClue.value.split(',').map(Number);
          if (!isNaN(lat) && !isNaN(lng)) {
            target = { lat, lng };
          }
          break;
      }

      setTargetPosition(target);
    }
  }, [currentClue, currentPosition]);

  // Handle starting game
  const handleStartGame = () => {
    setGameStatus('playing');
    setTimeLeft(config.timeLimit);
    setMessage('Trò chơi đã bắt đầu! Làm theo hướng dẫn để tìm kho báu.');
  };

  // Handle map click
  const handleMapClick = (position: MapPosition) => {
    if (gameStatus === 'playing') {
      setUserMarkerPosition(position);
    }
  };

  // Removed unused handleLocationFound function

  // Handle submitting guess
  const handleSubmitGuess = () => {
    if (!userMarkerPosition) {
      setMessage('Vui lòng chọn vị trí trên bản đồ.');
      return;
    }

    // If this is the final clue, check if treasure is found
    if (currentClueIndex === config.clues.length - 1) {
      const distanceToFinalTarget = calculateDistance(
        userMarkerPosition,
        config.treasurePosition
      );

      setDistanceToTreasure(distanceToFinalTarget);

      // Check if close enough to treasure
      const foundTreasure = distanceToFinalTarget <= config.proximityThreshold;

      // Calculate final step result
      const finalStepResult: StepResult = {
        clue: currentClue,
        userPosition: userMarkerPosition,
        distance: distanceToFinalTarget,
        correct: foundTreasure,
        hintsUsed,
      };

      setStepResults([...stepResults, finalStepResult]);

      // Show treasure location
      setShowTreasure(true);

      // Update game status
      setGameStatus('complete');

      // Calculate final score
      const finalScore = Math.max(
        0,
        score - hintsUsed * (config.hintPenalty || 5)
      );
      setScore(finalScore);
      if (foundTreasure) {
        // Success
        setMessage(
          `Chúc mừng! Bạn đã tìm thấy kho báu! Khoảng cách: ${distanceToFinalTarget.toFixed(
            2
          )} km.`
        );
      } else {
        // Failure
        setMessage(
          `Bạn chưa tìm thấy kho báu. Khoảng cách tới kho báu: ${distanceToFinalTarget.toFixed(
            2
          )} km.`
        );
      }
    } else {
      // Not the final step, check if user is going in the right direction
      if (targetPosition) {
        const distanceToTarget = calculateDistance(
          userMarkerPosition,
          targetPosition
        );

        // User has reached the correct location (within threshold)
        const isCorrect = distanceToTarget <= config.proximityThreshold;

        // Save this step's result
        const stepResult: StepResult = {
          clue: currentClue,
          userPosition: userMarkerPosition,
          distance: distanceToTarget,
          correct: isCorrect,
          hintsUsed,
        };

        setStepResults([...stepResults, stepResult]);

        if (isCorrect) {
          // Correct, move to next step
          setCurrentPosition(userMarkerPosition);
          setCurrentClueIndex(currentClueIndex + 1);
          setHintsUsed(0);
          setUserMarkerPosition(null);

          // Update score
          const newScore = Math.max(
            0,
            score - hintsUsed * (config.hintPenalty || 5)
          );
          setScore(newScore);
          setMessage(
            `Đúng hướng! Bạn đã đến đúng vị trí. Hãy tiếp tục với hướng dẫn tiếp theo.`
          );
        } else {
          // Wrong, notify and allow retry
          setMessage(
            `Vị trí chưa chính xác. Khoảng cách tới vị trí đúng: ${distanceToTarget.toFixed(
              2
            )} km. Hãy thử lại.`
          );
        }
      }
    }
  };

  // Handle using hint
  const handleUseHint = () => {
    if (currentClue.hint) {
      setHintsUsed(hintsUsed + 1);
      setMessage(`Gợi ý: ${currentClue.hint}`);
    } else {
      setMessage('Không có gợi ý cho bước này.');
    }
  };

  // Handle reset game
  const handleResetGame = () => {
    setCurrentPosition(config.initialPosition);
    setUserMarkerPosition(null);
    setCurrentClueIndex(0);
    setStepResults([]);
    setGameStatus('playing');
    setHintsUsed(0);
    setTimeLeft(config.timeLimit);
    setMessage('Bắt đầu hành trình! Hãy đặt marker tại vị trí đầu tiên.');
    setShowTreasure(false);
    setTargetPosition(null);
    setScore(100);
    setDistanceToTreasure(null);
  };

  // Convert direction and distance to new position
  const calculatePositionFromDirection = (
    startPos: MapPosition,
    direction: string,
    distance: number
  ): MapPosition => {
    // Conversion constants
    const KM_PER_LAT = 111.32; // ~ 111.32 km/degree latitude
    const getKmPerLng = (lat: number) =>
      111.32 * Math.cos((lat * Math.PI) / 180); // Km/degree longitude depends on latitude

    const kmPerLng = getKmPerLng(startPos.lat);

    let latChange = 0;
    let lngChange = 0;

    // Calculate changes based on direction
    switch (direction) {
      case 'N': // North
        latChange = distance / KM_PER_LAT;
        break;
      case 'NE': // Northeast
        latChange = distance / Math.sqrt(2) / KM_PER_LAT;
        lngChange = distance / Math.sqrt(2) / kmPerLng;
        break;
      case 'E': // East
        lngChange = distance / kmPerLng;
        break;
      case 'SE': // Southeast
        latChange = -(distance / Math.sqrt(2)) / KM_PER_LAT;
        lngChange = distance / Math.sqrt(2) / kmPerLng;
        break;
      case 'S': // South
        latChange = -distance / KM_PER_LAT;
        break;
      case 'SW': // Southwest
        latChange = -(distance / Math.sqrt(2)) / KM_PER_LAT;
        lngChange = -(distance / Math.sqrt(2)) / kmPerLng;
        break;
      case 'W': // West
        lngChange = -distance / kmPerLng;
        break;
      case 'NW': // Northwest
        latChange = distance / Math.sqrt(2) / KM_PER_LAT;
        lngChange = -(distance / Math.sqrt(2)) / kmPerLng;
        break;
    }

    return {
      lat: startPos.lat + latChange,
      lng: startPos.lng + lngChange,
    };
  };

  // Format clue for display
  const formatClueText = (clue: TreasureClue): string => {
    switch (clue.type) {
      case 'direction':
        const directionMap: Record<string, string> = {
          N: 'Bắc',
          NE: 'Đông Bắc',
          E: 'Đông',
          SE: 'Đông Nam',
          S: 'Nam',
          SW: 'Tây Nam',
          W: 'Tây',
          NW: 'Tây Bắc',
        };
        const directionText = clue.direction
          ? directionMap[clue.direction] || ''
          : '';

        return `Đi về phía ${directionText} ${clue.distance} km`;

      case 'latitudinal':
        return `Di chuyển ${
          clue.distance && clue.distance > 0 ? 'lên' : 'xuống'
        } ${Math.abs(clue.distance || 0)} độ vĩ độ`;

      case 'longitudinal':
        return `Di chuyển ${
          clue.distance && clue.distance > 0 ? 'sang phải' : 'sang trái'
        } ${Math.abs(clue.distance || 0)} độ kinh độ`;

      case 'coordinates':
        return `Đi đến tọa độ ${clue.value}`;

      default:
        return clue.value;
    }
  };

  // Prepare markers for map
  const mapMarkers = [];

  // Marker for current position
  mapMarkers.push({
    position: currentPosition,
    title: 'Vị trí hiện tại',
    icon: createCustomIcon('blue', 'medium'),
  });

  // Marker for user's chosen position
  if (userMarkerPosition) {
    mapMarkers.push({
      position: userMarkerPosition,
      title: 'Vị trí bạn chọn',
      icon: createCustomIcon('red', 'medium'),
    });
  }

  // Marker for treasure (only shown when game ends)
  if (showTreasure) {
    mapMarkers.push({
      position: config.treasurePosition,
      title: 'Kho báu',
      icon: createCustomIcon('gold', 'medium'),
      description: 'Đây là vị trí kho báu!',
    });
  }

  // Mảng rỗng vì đã bỏ tính năng vẽ đường chỉ dẫn
  const polylines: {
    positions: MapPosition[];
    color?: string;
    weight?: number;
  }[] = [];

  return (
    <div className="tailwind-w-full tailwind-max-w-full tailwind-mx-auto mini-game-container">
      {/* Game info section - always visible */}
      <Row gutter={[16, 16]} className="tailwind-mb-4">
        <Col xs={24} md={gameStatus === 'ready' ? 24 : 16}>
          {gameStatus === 'ready' ? (
            <Card className="tailwind-bg-gradient-to-r tailwind-from-indigo-50 tailwind-to-blue-50 tailwind-border-indigo-200 tailwind-shadow-sm">
              <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-gap-4">
                <Title
                  level={3}
                  className="tailwind-m-0 tailwind-text-indigo-800 tailwind-flex tailwind-items-center tailwind-gap-2"
                  style={{ fontSize: isFullscreen ? '1.8rem' : '1.5rem' }}
                >
                  <CompassOutlined className="tailwind-text-indigo-600" />
                  Truy Tìm Kho Báu
                </Title>

                <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-w-full">
                  <Tag
                    color="blue"
                    className="tailwind-flex tailwind-items-center tailwind-gap-1"
                  >
                    <EnvironmentOutlined /> {config.clues.length} bước
                  </Tag>
                  <Tag
                    color={
                      config.difficultyLevel === 'easy'
                        ? 'success'
                        : config.difficultyLevel === 'medium'
                        ? 'processing'
                        : 'error'
                    }
                    className="tailwind-flex tailwind-items-center tailwind-gap-1"
                  >
                    <InfoCircleOutlined />
                    {config.difficultyLevel === 'easy'
                      ? 'Dễ'
                      : config.difficultyLevel === 'medium'
                      ? 'Trung bình'
                      : 'Khó'}
                  </Tag>
                  <Tag
                    color="blue"
                    className="tailwind-flex tailwind-items-center tailwind-gap-1"
                  >
                    <ClockCircleOutlined />
                    {config.timeLimit
                      ? `${config.timeLimit}s`
                      : 'Không giới hạn'}
                  </Tag>
                  <Tag
                    color="purple"
                    className="tailwind-flex tailwind-items-center tailwind-gap-1"
                  >
                    <CheckCircleOutlined /> {config.proximityThreshold}km
                  </Tag>
                </div>

                <Paragraph
                  className="tailwind-text-indigo-700 tailwind-text-center tailwind-mb-0 tailwind-max-w-2xl"
                  style={{ fontSize: isFullscreen ? '1rem' : '0.9rem' }}
                >
                  Hãy làm theo các hướng dẫn để tìm được kho báu bí ẩn. Mỗi bước
                  sẽ đưa bạn đến gần hơn với vị trí của kho báu. Sử dụng bản đồ
                  để di chuyển và đánh dấu vị trí bạn nghĩ là đúng.
                </Paragraph>

                <Button
                  type="primary"
                  size={isFullscreen ? 'large' : 'middle'}
                  icon={
                    <PlayCircleOutlined
                      style={{ fontSize: isFullscreen ? '20px' : '16px' }}
                    />
                  }
                  onClick={handleStartGame}
                  className="tailwind-shadow-md tailwind-px-8 tailwind-bg-gradient-to-r tailwind-from-indigo-500 tailwind-to-blue-600 hover:tailwind-from-indigo-600 hover:tailwind-to-blue-700 tailwind-border-none"
                >
                  Bắt đầu trò chơi
                </Button>
              </div>
            </Card>
          ) : (
            <>
              {/* Current clue - only visible when game is playing */}
              <Alert
                type="info"
                showIcon
                icon={<CompassOutlined className="tailwind-text-lg" />}
                message={
                  <Title level={5} className="tailwind-m-0">
                    Hướng dẫn:
                  </Title>
                }
                description={formatClueText(currentClue)}
                className="tailwind-mb-4 tailwind-shadow-sm"
              />

              {/* Message */}
              {message && (
                <Alert
                  type="warning"
                  showIcon
                  message={message}
                  className="tailwind-mb-4 tailwind-shadow-sm"
                />
              )}
            </>
          )}
        </Col>

        {gameStatus !== 'ready' && (
          <Col xs={24} md={8}>
            <Card
              className="tailwind-h-full tailwind-shadow-sm tailwind-bg-gradient-to-r tailwind-from-blue-50 tailwind-to-indigo-50"
              title={
                <div className="tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-text-indigo-700">
                  <InfoCircleOutlined />
                  <span>Thông tin hành trình</span>
                </div>
              }
              size="small"
            >
              <div className="tailwind-flex tailwind-flex-col tailwind-gap-2">
                <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
                  <Text className="tailwind-text-gray-600">Bước hiện tại:</Text>
                  <Tag color="blue">
                    {currentClueIndex + 1}/{config.clues.length}
                  </Tag>
                </div>
                <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
                  <Text className="tailwind-text-gray-600">Điểm hiện tại:</Text>
                  <Tag color="success">{score}</Tag>
                </div>
                {config.timeLimit && (
                  <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
                    <Text className="tailwind-text-gray-600">
                      Thời gian còn lại:
                    </Text>
                    <Tag
                      color={timeLeft && timeLeft < 30 ? 'error' : 'processing'}
                    >
                      {timeLeft}s
                    </Tag>
                  </div>
                )}
                <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
                  <Text className="tailwind-text-gray-600">Gợi ý đã dùng:</Text>
                  <Tag color={hintsUsed > 0 ? 'warning' : 'default'}>
                    {hintsUsed}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
        )}
      </Row>

      {/* Map - always visible */}
      <Card
        className="tailwind-mb-4 tailwind-overflow-hidden tailwind-shadow-sm"
        styles={{ body: { padding: 0 } }}
      >
        <div className={`mini-game-map ${isFullscreen ? 'fullscreen' : ''}`}>
          <MapComponent
            initialCenter={currentPosition}
            initialZoom={config.initialZoom}
            markers={mapMarkers}
            polylines={polylines}
            onMapClick={gameStatus === 'playing' ? handleMapClick : undefined}
            scrollWheelZoom={config.mapOptions?.scrollWheelZoom}
            doubleClickZoom={config.mapOptions?.doubleClickZoom}
            className="tailwind-rounded-lg tailwind-overflow-hidden tailwind-shadow-sm"
            controls={{
              scale: { enabled: true, position: 'bottomright' },
              zoom: { enabled: true, position: 'topright' },
              coordinates: { enabled: true, position: 'bottomleft' },
            }}
          />
        </div>
      </Card>

      {/* Controls */}
      <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-3 tailwind-justify-center tailwind-mb-4 mini-game-controls">
        {gameStatus === 'playing' && (
          <>
            <Button
              type="primary"
              icon={
                <RightOutlined
                  style={{ fontSize: isFullscreen ? '16px' : '14px' }}
                />
              }
              onClick={handleSubmitGuess}
              disabled={!userMarkerPosition}
              className="tailwind-shadow-sm"
              size={isFullscreen ? 'large' : 'middle'}
            >
              Kiểm tra
            </Button>

            {currentClue.hint && (
              <Button
                type="default"
                icon={
                  <BulbOutlined
                    style={{ fontSize: isFullscreen ? '16px' : '14px' }}
                  />
                }
                onClick={handleUseHint}
                className="tailwind-bg-amber-500 tailwind-text-white hover:tailwind-bg-amber-600 tailwind-border-amber-500 hover:tailwind-border-amber-600 tailwind-shadow-sm"
                size={isFullscreen ? 'large' : 'middle'}
              >
                Gợi ý {config.hintPenalty ? `(-${config.hintPenalty}đ)` : ''}
              </Button>
            )}
          </>
        )}
      </div>

      {/* Results when game ends */}
      {gameStatus === 'complete' && (
        <Card
          className="tailwind-mt-6 tailwind-bg-gradient-to-br tailwind-from-indigo-50 tailwind-to-blue-50 tailwind-shadow-md tailwind-border-indigo-200"
          title={
            <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-gap-3">
              <TrophyOutlined className="tailwind-text-yellow-500 tailwind-text-2xl" />
              <Title
                level={3}
                className="tailwind-m-0 tailwind-text-indigo-700"
              >
                Kết Quả Trò Chơi
              </Title>
            </div>
          }
        >
          <Row gutter={[16, 16]} className="tailwind-mb-4">
            <Col xs={24} md={8}>
              <Card className="tailwind-h-full tailwind-shadow-sm tailwind-bg-gradient-to-br tailwind-from-indigo-50 tailwind-to-blue-50 tailwind-border-indigo-100">
                <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-gap-2">
                  <div className="tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-text-indigo-700">
                    <TrophyOutlined className="tailwind-text-xl tailwind-text-yellow-500" />
                    <span className="tailwind-text-sm tailwind-uppercase tailwind-tracking-wider tailwind-font-bold">
                      Tổng Điểm
                    </span>
                    {distanceToTreasure !== null &&
                      distanceToTreasure <= config.proximityThreshold && (
                        <CheckCircleOutlined className="tailwind-text-green-500" />
                      )}
                  </div>

                  <div className="tailwind-text-4xl tailwind-font-bold tailwind-text-indigo-700">
                    {score}
                  </div>

                  <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2 tailwind-justify-center tailwind-mt-2">
                    <Tag
                      color="blue"
                      className="tailwind-flex tailwind-items-center tailwind-gap-1"
                    >
                      <EnvironmentOutlined /> {currentClueIndex}/
                      {config.clues.length} bước
                    </Tag>
                    {hintsUsed > 0 && (
                      <Tag
                        color="orange"
                        className="tailwind-flex tailwind-items-center tailwind-gap-1"
                      >
                        <BulbOutlined /> {hintsUsed} gợi ý
                      </Tag>
                    )}
                    {config.timeLimit && (
                      <Tag
                        color="cyan"
                        className="tailwind-flex tailwind-items-center tailwind-gap-1"
                      >
                        <ClockCircleOutlined />{' '}
                        {config.timeLimit - (timeLeft || 0)}s
                      </Tag>
                    )}
                  </div>

                  <Button
                    type="primary"
                    icon={<RedoOutlined />}
                    onClick={handleResetGame}
                    className="tailwind-mt-3 tailwind-bg-gradient-to-r tailwind-from-green-500 tailwind-to-emerald-600 hover:tailwind-from-green-600 hover:tailwind-to-emerald-700 tailwind-border-none tailwind-shadow-md"
                  >
                    Bắt đầu lại
                  </Button>
                </div>
              </Card>
            </Col>

            <Col xs={24} md={16}>
              <Card
                className="tailwind-h-full tailwind-shadow-sm tailwind-bg-gradient-to-r tailwind-from-blue-50 tailwind-to-indigo-50 tailwind-border-blue-100"
                title={
                  <div className="tailwind-flex tailwind-items-center tailwind-gap-2 tailwind-text-indigo-700">
                    <EnvironmentOutlined />
                    <span>Hành trình tìm kho báu</span>
                  </div>
                }
                size="small"
              >
                {stepResults.length > 0 ? (
                  <div className="tailwind-overflow-auto tailwind-max-h-60">
                    <List
                      itemLayout="horizontal"
                      dataSource={stepResults}
                      renderItem={(step, index) => (
                        <List.Item
                          className={`tailwind-rounded tailwind-shadow-sm ${
                            step.correct
                              ? 'tailwind-bg-green-50 tailwind-border tailwind-border-green-100'
                              : 'tailwind-bg-red-50 tailwind-border tailwind-border-red-100'
                          } tailwind-mb-2 tailwind-p-2`}
                        >
                          <List.Item.Meta
                            avatar={
                              <div
                                className={`tailwind-flex tailwind-items-center tailwind-justify-center tailwind-w-8 tailwind-h-8 tailwind-rounded-full ${
                                  step.correct
                                    ? 'tailwind-bg-green-100'
                                    : 'tailwind-bg-red-100'
                                }`}
                              >
                                {step.correct ? (
                                  <CheckCircleOutlined className="tailwind-text-green-600 tailwind-text-lg" />
                                ) : (
                                  <CloseCircleOutlined className="tailwind-text-red-600 tailwind-text-lg" />
                                )}
                              </div>
                            }
                            title={
                              <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                                <Text strong>Bước {index + 1}</Text>
                                <Tag color={step.correct ? 'success' : 'error'}>
                                  {step.correct ? 'Đúng' : 'Sai'}
                                </Tag>
                                <Tag
                                  color="blue"
                                  className="tailwind-flex tailwind-items-center tailwind-gap-1"
                                >
                                  <CompassOutlined /> {step.distance.toFixed(2)}{' '}
                                  km
                                </Tag>
                                {step.hintsUsed > 0 && (
                                  <Tag
                                    color="orange"
                                    className="tailwind-flex tailwind-items-center tailwind-gap-1"
                                  >
                                    <BulbOutlined /> {step.hintsUsed} gợi ý
                                  </Tag>
                                )}
                              </div>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </div>
                ) : (
                  <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-p-4 tailwind-text-gray-500">
                    <InfoCircleOutlined className="tailwind-mr-2" />
                    <Text italic>Chưa có dữ liệu hành trình</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>

          {/* Removed duplicate button - now included in the score card */}
        </Card>
      )}
    </div>
  );
};

export default TreasureHuntGameComponent;

import {
  AppstoreOutlined,
  PlayCircleOutlined,
  ExperimentOutlined,
  LayoutOutlined,
  QuestionCircleOutlined,
  ToolOutlined,
  BlockOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { ETypeEdTechComponent } from '../../../../enums/AppEnums';

export const typeIcons: Record<string, any> = {
  [ETypeEdTechComponent.MINI_GAMES]: PlayCircleOutlined,
  [ETypeEdTechComponent.STRUCTURE]: BlockOutlined,
  [ETypeEdTechComponent.COMMON]: ToolOutlined,
  [ETypeEdTechComponent.SIMULATORS]: ExperimentOutlined,
  [ETypeEdTechComponent.LAYOUT]: LayoutOutlined,
  [ETypeEdTechComponent.DEMO]: RobotOutlined,
  [ETypeEdTechComponent.QUIZ]: QuestionCircleOutlined,
  [ETypeEdTechComponent.CONTENT]: AppstoreOutlined,
};

import { Popconfirm, PopconfirmProps } from 'antd';
import { getFullscreenElement } from '../../common/Fullscreen';

export interface IPopconfirmAntdCustomProps extends PopconfirmProps {}

const PopconfirmAntdCustom = (props: IPopconfirmAntdCustomProps) => {
  const { getPopupContainer = getFullscreenElement as any, ...rest } = props;
  return <Popconfirm {...rest} getPopupContainer={getPopupContainer} />;
};

export default PopconfirmAntdCustom;

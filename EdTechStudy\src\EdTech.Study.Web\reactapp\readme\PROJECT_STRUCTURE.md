# EdTech Pro - Cấu trúc Dự án

## Tổng quan
Tài liệu này mô tả cấu trúc và tổ chức của dự án EdTech Pro, một nền tảng học tập tương tác hiện đại được xây dựng bằng React.

## Công nghệ sử dụng
- **Frontend Core**: React, TypeScript, Redux Toolkit
- **Styling**: Tailwind CSS, Styled Components
- **3D Graphics**: Three.js, React Three Fiber
- **Data Visualization**: D3.js, Recharts
- **Maps & Geo**: Google Maps API, Mapbox
- **Drag & Drop**: React DnD, react-beautiful-dnd
- **API Integration**: Axios, React Query
- **Testing**: Jest, React Testing Library
- **Build Tools**: Vite, Webpack

## Cấu trúc Dự án Hiện tại

```
reactApp/
├── node_modules/                # Thư viện dependencies
├── public/                      # File tĩnh
├── src/                         # Mã nguồn
│   ├── assets/                  # Tài nguyên tĩnh
│   │
│   ├── components/             # Components tái sử dụng
│   │   ├── common/            # Components UI chung
│   │   ├── icons/            # Components icon
│   │   ├── lessonContents/   # Components nội dung bài học
│   │   ├── miniGames/        # Components mini game giáo dục
│   │   ├── quizs/           # Components câu hỏi và đánh giá
│   │   ├── simulators/      # Components mô phỏng 3D
│   │   └── index.ts         # File export components
│   │
│   ├── constants/            # Các hằng số của ứng dụng
│   │
│   ├── hooks/               # Custom React hooks
│   │
│   ├── pages/              # Các trang của ứng dụng
│   │   └── TestPage.tsx    # Trang test
│   │
│   ├── providers/          # React Context providers
│   │
│   ├── redux/             # Quản lý state với Redux
│   │
│   ├── services/          # Các service gọi API
│   │
│   ├── store/            # Cấu hình Redux store
│   │
│   ├── styles/           # Style toàn cục
│   │
│   ├── types/           # Định nghĩa TypeScript types
│   │
│   └── index.css        # CSS toàn cục
│
├── .gitignore          # Cấu hình Git ignore
├── .npmrc             # Cấu hình NPM
├── .prettierrc        # Cấu hình Prettier
├── package.json       # Cấu hình package
├── postcss.config.js  # Cấu hình PostCSS
├── tailwind.config.js # Cấu hình Tailwind CSS
├── tsconfig.json      # Cấu hình TypeScript
├── webpack.config.js  # Cấu hình Webpack
└── README.md         # Tài liệu dự án
```

## Chi tiết các Module

### Components
1. **common/**
   - Chứa các components UI dùng chung trong ứng dụng
   - Cấu trúc thư mục:
     ```
     common/
     ├── MapCommonComponent/     # Components liên quan đến bản đồ
     │   └── component/         # Các thành phần bản đồ cơ bản
     └── LessonCommonComponent/ # Components dùng chung cho bài học
     ```
   - **MapCommonComponent**: Chứa các components liên quan đến bản đồ như:
     - Điều khiển bản đồ (zoom, pan)
     - Marker và điểm đánh dấu
     - Layer controls
     - Công cụ đo đạc và vẽ
   
   - **LessonCommonComponent**: Chứa các components dùng chung cho bài học như:
     - Navigation bài học
     - Progress indicators
     - Công cụ tương tác
     - Components hiển thị nội dung

2. **icons/**
   - Chứa các components icon
   - Định dạng SVG và icon components

3. **lessonContents/**
   - Components liên quan đến nội dung bài học
   - Bao gồm trình chiếu, video player

4. **miniGames/**
   - Quản lý và triển khai các trò chơi giáo dục
   - Cấu trúc thư mục:
     ```
     miniGames/
     ├── GameRegistry.tsx        # Đăng ký và quản lý danh sách game
     ├── GameStore.tsx          # State management cho games
     ├── GameManager.tsx        # Quản lý vòng đời game
     ├── LessonGameManager.tsx  # Quản lý game trong bài học
     ├── GameContainer.tsx      # Container cho game
     ├── GameLoader.tsx         # Loading và khởi tạo game
     ├── GameConfigContainer.tsx # Cấu hình game
     └── CoordinateFinderGame/  # Game tìm tọa độ
     ```
   - **Core Components**:
     - `GameRegistry`: Đăng ký và quản lý tất cả các game trong hệ thống
     - `GameStore`: Quản lý state của game sử dụng Redux
     - `GameManager`: Xử lý logic chính của game (start, pause, end)
     - `LessonGameManager`: Tích hợp game vào bài học
     - `GameContainer`: Component container cho game
     - `GameLoader`: Xử lý loading assets và khởi tạo game
     - `GameConfigContainer`: Giao diện cấu hình game

   - **Implemented Games**:
     - **CoordinateFinderGame**: 
       - Game tìm tọa độ trên bản đồ
       - Rèn luyện kỹ năng đọc bản đồ
       - Tích hợp với MapCommonComponent

   - **Game Development Guidelines**:
     - Sử dụng GameManager để quản lý vòng đời
     - Implement các interface chuẩn
     - Tách biệt logic game và UI
     - Hỗ trợ save/load trạng thái
     - Tích hợp với hệ thống điểm và đánh giá

5. **quizs/**
   - Hệ thống câu hỏi và kiểm tra
   - Bao gồm tạo quiz và làm quiz

6. **simulators/**
   - Các mô phỏng 3D
   - Mô phỏng vật lý, hóa học, thiên văn

### Các Module Khác

1. **providers/**
   - Quản lý state toàn cục với Context API
   - Authentication, Theme, etc.

2. **services/**
   - Xử lý gọi API
   - Tương tác với backend

3. **store/**
   - Cấu hình Redux store
   - Quản lý state phức tạp

4. **styles/**
   - Style sheets toàn cục
   - Theme và các biến CSS

## Hướng dẫn Phát triển

1. **Tổ chức Component**
   - Tuân thủ nguyên tắc thiết kế atomic
   - Đặt components vào đúng thư mục
   - Tạo index.ts để export

2. **Quản lý State**
   - Redux cho state toàn cục
   - Context cho theme/auth
   - Local state cho component

3. **Style Guide**
   - Sử dụng Tailwind CSS
   - Styled Components cho styling phức tạp
   - Tuân thủ BEM khi cần thiết

4. **Performance**
   - Lazy loading cho routes
   - Code splitting cho components lớn
   - Tối ưu assets 
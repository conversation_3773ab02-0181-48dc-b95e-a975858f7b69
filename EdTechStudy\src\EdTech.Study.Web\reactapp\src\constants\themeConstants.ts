import { EThemeType } from '../enums/AppEnums';
import { IAppThemeColors } from '../interfaces/ThemeAppInterface';
import '../styles/theme-variables.css';

/**
 * Theme Constants
 * --------------
 * This file defines the theme constants used in the application.
 * The actual CSS variables are defined in src/styles/theme-variables.css
 */

/**
 * Default Theme (Light)
 * --------------------
 * Default theme with light background and primary color
 */
const defaultTheme: IAppThemeColors = {
  'edtt-color-bg-default': 'var(--edtt-color-bg-default)',
  'edtt-color-text': 'var(--edtt-color-text)',
  'edtt-color-disabled-text': 'var(--edtt-color-disabled-text)',
  'edtt-color-disabled-bg': 'var(--edtt-color-disabled-bg)',
  'edtt-color-selected': 'var(--edtt-color-selected)',
  'edtt-shadow': 'var(--edtt-shadow)',
};

/**
 * Learning Theme
 * -------------
 * Theme optimized for learning with tertiary color as primary
 */
const learningTheme: IAppThemeColors = {
  ...defaultTheme,
};

/**
 * Dark Theme
 * ----------
 * Dark theme with dark background
 */
const darkTheme: IAppThemeColors = {
  ...defaultTheme,
};

/**
 * Export Theme Colors
 * ------------------
 * Export themes for use in the application
 */
export const ED_TECH_THEME_COLORS: Record<EThemeType, IAppThemeColors> = {
  [EThemeType.DEFAULT]: defaultTheme,
  [EThemeType.DARK]: darkTheme,
  [EThemeType.LEARNING]: learningTheme,
};

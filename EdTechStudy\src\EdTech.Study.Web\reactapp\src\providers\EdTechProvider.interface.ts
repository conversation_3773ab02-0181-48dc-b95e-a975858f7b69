import { ETypeMode } from '../enums/AppEnums';
import { IEdTechRenderTreeData } from '../interfaces/AppComponents';

export interface IEdTechContext {
  edTechRenderTreeData: IEdTechRenderTreeData | undefined;
}

export interface IEdTechProviderPropsMapStateToProps {
  mode: ETypeMode;
  edTechRenderTreeData: IEdTechRenderTreeData | undefined;
  isInitialized: boolean;
}
export interface IEdTechProviderPropsMapDispatchToProps {}
export interface IEdTechProviderProps
  extends IEdTechProviderPropsMapStateToProps,
    IEdTechProviderPropsMapDispatchToProps {}

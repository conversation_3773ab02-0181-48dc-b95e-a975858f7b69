// schema.ts
import { z } from 'zod';
import { createComponentSchema } from '../../../../../../utils/schema/createComponentSchema';

/**
 * Zod schema for title properties
 */
const titlePropsSchema = z.object({
  text: z.string().optional(),
  fontSize: z.number().optional(),
  color: z.string().optional(),
  align: z.enum(['left', 'center', 'right']).optional(),
  bold: z.boolean().optional(),
  italic: z.boolean().optional(),
  underline: z.boolean().optional(),
  style: z.any().optional(),
}).optional();

/**
 * Zod schema for upload configuration
 */
const uploadConfigSchema = z.object({
  apiUrl: z.string().url().optional(),
  maxFileSize: z.number().positive().optional(),
  allowedTypes: z.array(z.string()).optional(),
}).optional();

/**
 * Zod schema for video configuration
 */
const videoConfigSchema = z.object({
  saveUrl: z.string().url().optional(),
  path: z.string().optional(),
  maxFileSize: z.number().positive().optional(),
  width: z.string().optional(),
  height: z.string().optional(),
  allowResize: z.boolean().optional(),
  layoutOption: z.enum(['Inline', 'Break']).optional(),
}).optional();

/**
 * Zod schema for audio configuration
 */
const audioConfigSchema = z.object({
  saveUrl: z.string().url().optional(),
  path: z.string().optional(),
  maxFileSize: z.number().positive().optional(),
  allowResize: z.boolean().optional(),
  layoutOption: z.enum(['Inline', 'Break']).optional(),
}).optional();

/**
 * Zod schema for RichTextEditor validation
 */
export const richTextEditorSchema = createComponentSchema({
  paramsSchema: {
    // Title properties
    titleProps: titlePropsSchema,

    // Editor properties
    content: z.string().optional(),
    placeholder: z.string().optional(),
    height: z.union([z.number(), z.string()]).optional(),
    width: z.union([z.number(), z.string()]).optional(),
    showToolbar: z.boolean().optional(),
    toolbar: z.object({
      items: z.array(z.string()).optional(),
    }).optional(),
    customStyles: z.record(z.string(), z.any()).optional(),

    // Editor behavior
    readOnly: z.boolean().optional(),
    autoHeight: z.boolean().optional(),
    resizable: z.boolean().optional(),
    defaultHeight: z.number().positive().optional(),
    minHeight: z.number().positive().optional(),
    maxHeight: z.number().positive().optional(),
    maxLength: z.number().positive().optional(),

    // Theme
    primaryColor: z.string().optional(),

    // Collaboration features
    enableCollaboration: z.boolean().optional(),
    enableComments: z.boolean().optional(),
    enableTrackChanges: z.boolean().optional(),
    enableRevisionHistory: z.boolean().optional(),

    // Upload configuration
    uploadConfig: uploadConfigSchema,

    // Video configuration
    videoConfig: videoConfigSchema,

    // Audio configuration
    audioConfig: audioConfigSchema,
  },
});

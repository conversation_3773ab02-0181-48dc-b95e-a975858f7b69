// constants.ts
import { RichTextEditorProps } from './types';

export const defaultProps: RichTextEditorProps = {
  /**
   * Core Editor Properties
   */
  titleProps: {
    text: 'Soạn thảo văn bản',
    fontSize: 18,
    color: '#000000',
    align: 'left',
    bold: true
  },
  content: '',
  placeholder: 'Bắt đầu nhập nội dung...',
  maxLength: 0, // 0 = không giới hạn số lượng ký tự
  toolbar: {
    items: [
      'Undo',
      'Redo',
      '|',
      'Formats',
      '|',
      'Bold',
      'Italic',
      'Underline',
      'StrikeThrough',
      '|',
      'FontName',
      'FontSize',
      'FontColor',
      'BackgroundColor',
      '|',
      'LowerCase',
      'UpperCase',
      '|',
      'Superscript',
      'Subscript',
      '|',
      'Alignments',
      '|',
      'OrderedList',
      'UnorderedList',
      '|',
      'Outdent',
      'Indent',
      '|',
      'CreateLink',
      'Image',
      'Video',
      'Audio',
      'CreateTable',
      '|',
      'ClearFormat',
    ]
  },
  uploadConfig: {
    maxFileSize: 200 * 1024 * 1024, // 200MB - tăng dung lượng tối đa cho file ảnh
    allowedTypes: ['image/*']
  },
  videoConfig: {
    maxFileSize: 1000 * 1024 * 1024, // 1GB - tăng dung lượng tối đa cho file video
    width: '560px',
    height: '315px',
    allowResize: true,
    layoutOption: 'Break', // Mặc định video ở bố cục Break
  },
  audioConfig: {
    maxFileSize: 500 * 1024 * 1024, // 500MB - dung lượng tối đa cho file audio
    allowResize: true,
    layoutOption: 'Break', // Mặc định audio ở bố cục Break
  },
  enableCollaboration: false,
  enableComments: false,
  enableTrackChanges: false,
  enableRevisionHistory: false
};
export const defaultConfig = {
  ...defaultProps
};

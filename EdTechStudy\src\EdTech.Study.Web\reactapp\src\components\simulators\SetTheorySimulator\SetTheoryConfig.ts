// SetTheoryConfig.ts
import { ITextProps } from '../../../components/core/title/CoreTitle';

export interface SetElement {
  id: string;
  value: string;
  type: 'number' | 'letter' | 'custom';
}

export interface SetTheoryConfig {
  titleProps: ITextProps;
  description: string;

  // Available element sets
  elementSets: Record<string, SetElement[]>;

  // Configuration options
  options: {
    showMembershipTesting: boolean;
    showExamples: boolean;
    showSetNotation: boolean;
    allowDragDrop: boolean;
    maxElementsPerSet: number;
  };

  // Theme options
  theme: {
    setAColor: string;
    setBColor?: string;
    elementColor: string;
    highlightColor: string;
  };
}

export const defaultSetTheoryConfig: SetTheoryConfig = {
  titleProps: {
    text: 'Quan Hệ Thuộc Tính Trong Tập Hợp',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  description:
    '<PERSON>h<PERSON><PERSON> phá mối quan hệ giữa các phần tử và tập hợp thông qua quan hệ thuộc tính',

  elementSets: {
    numbers: Array.from({ length: 10 }, (_, i) => ({
      id: `num-${i + 1}`,
      value: (i + 1).toString(),
      type: 'number' as const,
    })),
    letters: Array.from({ length: 10 }, (_, i) => ({
      id: `letter-${i}`,
      value: String.fromCharCode(65 + i), // A-J
      type: 'letter' as const,
    })),
  },

  options: {
    showMembershipTesting: true,
    showExamples: true,
    showSetNotation: true,
    allowDragDrop: true,
    maxElementsPerSet: 10,
  },

  theme: {
    setAColor: '#1890ff', // Ant Design blue
    setBColor: '#52c41a', // Ant Design green
    elementColor: '#f5f5f5', // Light gray
    highlightColor: '#faad14', // Ant Design warning color
  },
};

import { z } from 'zod';
import { createComponentSchema } from '../../../../../../utils/schema/createComponentSchema';

/**
 * Zod schema for a single 3D model item
 */
export const model3DItemSchema = z.object({
  modelUrl: z.string(),
  originalUrl: z.string().optional(),
  modelType: z.enum(['upload', 'embed']).optional(),
  format: z.enum(['glb', 'obj', 'fbx']).optional(),
  name: z.string().optional(),
});

/**
 * Zod schema for title properties
 */
const titlePropsSchema = z.object({
  text: z.string().optional(),
  fontSize: z.number().optional(),
  color: z.string().optional(),
  align: z.enum(['left', 'center', 'right']).optional(),
  bold: z.boolean().optional(),
  italic: z.boolean().optional(),
  underline: z.boolean().optional(),
  style: z.any().optional(),
}).optional();

/**
 * Zod schema for Model3DComponent validation
 * Shared between all versions of the Model3DComponent
 */
export const model3DComponentSchema = createComponentSchema({
  paramsSchema: {
    // Title properties
    titleProps: titlePropsSchema,

    // Multi-model support
    medias: z.array(model3DItemSchema).optional(),

    // Legacy single model support
    mediaUrl: z.string().optional(),
    mediaType: z.enum(['upload', 'embed']).optional(),
    format: z.enum(['glb', 'obj', 'fbx']).optional(),

    // Display options
    backgroundColor: z.string().optional(),
    autoRotate: z.boolean().optional(),
    rotationSpeed: z.number().min(0.1).max(5).optional(),

    // Container dimensions
    width: z.union([z.number(), z.string()]).optional(),
    height: z.union([z.number(), z.string()]).optional(),

    // Description
    description: z.string().optional(),

    // Display mode
    displayMode: z.enum(['horizontal', 'vertical']).optional(),
  },
});
import { axiosClient } from '../services/axiosClient';
import { message } from 'antd';

class OfflineExportApi {
  /**
   * Export a lesson for offline use
   * @param key The lesson key in format grade-subject-lesson
   * @returns Promise with the result of the operation
   */
  public async exportLessonForOfflineUse(key: string, jsonConfig?: string) {
    try {
      // Use axios with responseType blob to handle binary data
      const response = await axiosClient({
        method: 'post',
        url: `api/offline-export/${key}`,
        data: JSON.stringify(jsonConfig),
        responseType: 'blob',
      });

      // Create a download link for the zip file
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;

      // Set the filename from the Content-Disposition header if available
      const contentDisposition = response.headers['content-disposition'];
      let filename = `offline-lesson-${key}.zip`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch.length > 1) {
          filename = filenameMatch[1];
        }
      }

      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return {
        success: true,
        message: 'Lesson exported successfully for offline use',
      };
    } catch (error) {
      console.error('Error exporting lesson for offline use:', error);
      message.error('Failed to export lesson for offline use');

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export default new OfflineExportApi();

.save-controls {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.save-controls-top {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  padding: 16px 24px;
  background-color: #fff;
  width: 100%;
  max-width: 100%;
}

.save-controls-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-top: 1px solid #f0f0f0;
  border-radius: 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 0;
  padding: 16px 24px;
  background-color: #fff;
  width: 100%;
  max-width: 100%;
}

.save-status {
  color: #8c8c8c;
  font-size: 14px;
  margin-left: 12px;
}

.save-controls .ant-btn-primary {
  background-color: #1677ff;
  transition: all 0.3s;
}

.save-controls .ant-btn-primary:hover {
  background-color: #4096ff;
}

.save-controls-tabs {
  display: flex;
  align-items: center;
}

.save-controls-actions {
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .save-controls {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .save-controls-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .save-controls-top,
  .save-controls-bottom {
    padding: 16px;
  }
}

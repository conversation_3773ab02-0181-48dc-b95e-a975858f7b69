import React from 'react';
import Tree from 'rc-tree';
import { Tooltip } from 'antd';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import './RcTreeSidebar.css';

import { ETypeEdTechComponent } from '../../../enums/AppEnums';
import { IEdTechRenderTreeData } from '../../../interfaces/AppComponents';

// Hooks
import { useTreeOperations } from './LessonSidebarWithRcTree/hooks';

// Types
import { TreeMenuItem } from './LessonSidebarWithRcTree/types';

export interface MenuItem {
  key: string;
  title: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  type?: ETypeEdTechComponent;
  isLeaf?: boolean;
  disableCheckbox?: boolean;
  name?: string;
  path?: string;
  pos?: string;
}

interface LessonSidebarMenuProps {
  items?: MenuItem[];
  subitems?: IEdTechRenderTreeData[];
  title?: string;
  onSelect?: (key: string) => void;
  isEditing?: boolean;
  root?: IEdTechRenderTreeData;
  onFullscreen?: () => void;
  onImport?: () => void;
  onExport?: () => void;
  onClose?: () => void;
}

const LessonSidebarMenu: React.FC<LessonSidebarMenuProps> = ({
  items,
  subitems,
  root,
  onSelect,
  isEditing = false,
}) => {
  // Convert subitems to menu items if provided
  const menuItems = React.useMemo(() => {
    if (subitems && subitems.length > 0) {
      // Process subitems to create a nested structure
      const processItems = (
        items: IEdTechRenderTreeData[],
        parentPathMenu: string = ''
      ): TreeMenuItem[] => {
        const result: TreeMenuItem[] = [];

        items.forEach((item) => {
          // Skip items with names LessonRowComponent and LessonColumnComponent
          if (
            item.name === 'LessonRowComponent' ||
            item.name === 'LessonColumnComponent'
          ) {
            // If the item has children, process them and add directly to result
            if (item.subItems && item.subItems.length > 0) {
              const children = processItems(item.subItems, parentPathMenu);
              result.push(...children);
            }
            // Skip adding this item to the result
            return;
          }

          // Construct pathMenu for this item
          const itemId = item.id || '';
          const itemPathMenu = parentPathMenu
            ? `${parentPathMenu}/${itemId}`
            : itemId;

          // Create menu item
          const menuItem: TreeMenuItem = {
            key: itemId,
            title: item.title || item.name,
            type: item.type as ETypeEdTechComponent,
            path: item.path,
            pathMenu: itemPathMenu, // Add pathMenu construction
            isLeaf: !item.subItems || item.subItems.length === 0,
            disableCheckbox: true,
          };

          // Add children if any
          if (item.subItems && item.subItems.length > 0) {
            menuItem.children = processItems(item.subItems, itemPathMenu);
          }

          result.push(menuItem);
        });

        return result;
      };

      const processedItems = processItems(subitems);
      return processedItems;
    }
    return (items as TreeMenuItem[]) || [];
  }, [items, subitems]);

  // Tree operations hook
  const {
    expandedKeys,
    selectedKey: internalSelectedKey,
    autoExpandParent,
    handleExpand,
    setExpandedKeys,
    handleTreeSelect,
  } = useTreeOperations(menuItems, root, isEditing, onSelect);

  // Title render function
  const titleRender = (nodeData: TreeMenuItem) => {
    const isObjective = nodeData.key.includes('objective');
    const hasChildren = nodeData.children && nodeData.children.length > 0;
    const isExpanded = expandedKeys.includes(nodeData.key);

    // Get the pathMenu for debugging or display if needed
    const pathMenu = nodeData.pathMenu || '';

    // Calculate level from path
    const level = pathMenu ? pathMenu.split('/').length - 1 : 0;
    const paddingStyle = {
      paddingLeft: `${level * 16}px`,
    };

    const handleExpandClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (isExpanded) {
        setExpandedKeys(expandedKeys.filter((key) => key !== nodeData.key));
      } else {
        setExpandedKeys([...expandedKeys, nodeData.key]);
      }
    };

    return (
      <div
        className={`rc-tree-node-content ${
          nodeData.type === ETypeEdTechComponent.LAYOUT
            ? `tailwind-font-roboto tailwind-font-semibold tailwind-text-[#908588]`
            : ''
        }`}
        style={paddingStyle}
      >
        <Tooltip title={nodeData.title}>
          <span className="node-title tailwind-pl-2">
            {hasChildren && (
              <span className="expand-icon" onClick={handleExpandClick}>
                {isExpanded ? <DownOutlined /> : <RightOutlined />}
              </span>
            )}
            {isObjective && <span className="objective-marker">★</span>}
            {nodeData.title}
          </span>
        </Tooltip>
      </div>
    );
  };

  return (
    <div className="sidebar-content">
      <div className="lesson-sidebar-menu-title">Nội dung</div>
      <div className="tree-container">
        <Tree
          className="custom-rc-tree"
          treeData={menuItems}
          selectedKeys={internalSelectedKey ? [internalSelectedKey] : []}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onExpand={(keys) => handleExpand(keys as string[])}
          onSelect={handleTreeSelect}
          titleRender={titleRender}
          showIcon={false}
          showLine={false}
          switcherIcon={false}
        />
      </div>
    </div>
  );
};

export default React.memo(LessonSidebarMenu);

import React from 'react';
import '../styles/component-highlight.css';
import { Tooltip } from 'antd';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import AddButton from './AddButton';
import DeleteEngineButton from '../../../components/common/engines/components/DeleteEngineButton';

interface EngineHeaderProps {
  props: IEdTechRenderProps;
}

/**
 * Component hiển thị header của engine khi active
 */
const EngineHeader: React.FC<EngineHeaderProps> = ({ props }) => {
  // Luôn hiển thị label và toolbar
  const { title, type } = props;
  const displayTitle = title || String(type);

  return (
    <>
      {/* Header mới theo design */}
      <div className="engine-simple-header">
        <Tooltip title={displayTitle} className="tailwind-mr-2">
          <div className="engine-title">{displayTitle}</div>
        </Tooltip>

        <div className="engine-buttons">
          <AddButton props={props} />
          <DeleteEngineButton
            node={props as any}
            buttonStyle={{
              border: 'none',
              background: 'none',
              boxShadow: 'none',
            }}
          />
        </div>
      </div>
    </>
  );
};

export default React.memo(EngineHeader);

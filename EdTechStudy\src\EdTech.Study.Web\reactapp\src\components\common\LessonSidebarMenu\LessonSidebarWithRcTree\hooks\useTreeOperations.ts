import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { message, TreeProps } from 'antd';
import { TreeMenuItem } from '../types';
import { IEdTechRenderTreeData } from '../../../../../interfaces/AppComponents';
import { EdTechRootState } from '../../../../../store/store';
import { findNodeById } from '../../../../../utils/treeUtils';
import { updateComponentTitle } from '../../../../../utils/titleSyncUtils';
import { updateItems } from '../../../../../store/slices/AppSlices/EdTechRenderDataSlice';
import { findParentKeys, convertToRcTreeFormat } from '../utils/treeUtils';
import { EEngineInteractionMode } from '../../../../../enums/AppEnums';

export const useTreeOperations = (
  items: TreeMenuItem[],
  root?: IEdTechRenderTreeData,
  isEditing: boolean = false,
  onSelect?: (key: string) => void
) => {
  const dispatch = useDispatch();
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedKey, setSelectedKey] = useState<string>();
  const [normalModeExpandedKeys, setNormalModeExpandedKeys] = useState<
    string[]
  >([]);
  const [editModeExpandedKeys, setEditModeExpandedKeys] = useState<string[]>(
    []
  );
  const [editingItemKey, setEditingItemKey] = useState<string | null>(null);
  const [editingItemValue, setEditingItemValue] = useState<string>('');
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [convertedItems, setConvertedItems] = useState<TreeMenuItem[]>([]);
  const [selectNode, setSelectNode] = useState<any>();
  const [isShowSelectComponent, setIsShowSelectComponent] = useState(false);

  const paramData = useSelector(
    (state: EdTechRootState) => state.edComponentParams.data
  );
  const engineState = useSelector(
    (state: EdTechRootState) => state.appConfig.engineState
  );

  useEffect(() => {
    if (engineState.interactionMode === EEngineInteractionMode.CONFIGURATION) {
      const listKeyExpanded = engineState.pathEngineActive?.split('/') || [];
      setSelectedKey(listKeyExpanded[listKeyExpanded.length - 1]);
      setExpandedKeys((prev) => {
        let newExpandedKeys = [...prev];
        listKeyExpanded.forEach((key) => {
          if (!newExpandedKeys.includes(key)) {
            newExpandedKeys.push(key);
          }
        });
        return newExpandedKeys;
      });
    }
  }, [engineState.pathEngineActive]);
  // Convert items to rc-tree format
  useEffect(() => {
    setConvertedItems(convertToRcTreeFormat(items, isEditing));
  }, [items, isEditing]);

  // Handle expanded keys when switching between edit and normal modes
  useEffect(() => {
    if (isEditing) {
      setNormalModeExpandedKeys(expandedKeys);
      if (editModeExpandedKeys.length > 0) {
        setExpandedKeys(editModeExpandedKeys);
      }
    } else {
      setEditModeExpandedKeys(expandedKeys);
      if (normalModeExpandedKeys.length > 0) {
        setExpandedKeys(normalModeExpandedKeys);
      }
    }
  }, [isEditing]);

  // Lưu trạng thái expandedKeys khi nó thay đổi
  useEffect(() => {
    if (isEditing) {
      setEditModeExpandedKeys(expandedKeys);
    } else {
      setNormalModeExpandedKeys(expandedKeys);
    }
  }, [expandedKeys, isEditing]);

  // Auto-expand parent nodes when a node is selected
  useEffect(() => {
    if (selectedKey) {
      const parentKeys: string[] = findParentKeys(convertedItems, selectedKey);
      setExpandedKeys((prevExpandedKeys) => {
        const newExpandedKeys = [...prevExpandedKeys];
        parentKeys.forEach((key) => {
          if (!newExpandedKeys.includes(key)) {
            newExpandedKeys.push(key);
          }
        });

        if (isEditing) {
          setEditModeExpandedKeys(newExpandedKeys);
        } else {
          setNormalModeExpandedKeys(newExpandedKeys);
        }

        return newExpandedKeys;
      });
    }
  }, [selectedKey, convertedItems, isEditing]);

  const handleExpand = useCallback(
    (newExpandedKeys: string[]) => {
      // Cập nhật expandedKeys và lưu trạng thái
      setExpandedKeys(newExpandedKeys);
      setAutoExpandParent(false);

      // Lưu trạng thái mở rộng tương ứng với chế độ hiện tại
      if (isEditing) {
        setEditModeExpandedKeys(newExpandedKeys);
      } else {
        setNormalModeExpandedKeys(newExpandedKeys);
      }
    },
    [isEditing]
  );

  const handleStartEdit = useCallback((key: string, label: string) => {
    setEditingItemKey(key);
    setEditingItemValue(label);
  }, []);

  const handleSaveEdit = useCallback(
    (key: string) => {
      if (!root || !root.subItems) return;

      if (!editingItemValue.trim()) {
        message.error('Tiêu đề không được để trống');
        return;
      }

      const nodeToUpdate = findNodeById(root.subItems, key);
      if (!nodeToUpdate) return;

      dispatch(
        updateItems({
          items: [
            {
              path: nodeToUpdate.path || key,
              data: { title: editingItemValue },
            },
          ],
        })
      );

      updateComponentTitle(
        key,
        editingItemValue,
        {
          data: paramData,
        },
        dispatch
      );

      setEditingItemKey(null);
    },
    [root, editingItemValue, paramData, dispatch]
  );

  const handleCancelEdit = useCallback(() => {
    setEditingItemKey(null);
  }, []);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEditingItemValue(e.target.value);
    },
    []
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent, key: string) => {
      if (e.key === 'Enter') {
        handleSaveEdit(key);
      } else if (e.key === 'Escape') {
        handleCancelEdit();
      }
    },
    [handleSaveEdit, handleCancelEdit]
  );

  const handleAddNode = useCallback(
    (parentKey: string) => {
      if (!root || !root.subItems) return;

      const parentNode = findNodeById(root.subItems, parentKey);
      if (!parentNode) return;

      setSelectNode(parentNode);
      setIsShowSelectComponent(true);

      if (!expandedKeys.includes(parentKey)) {
        setExpandedKeys([...expandedKeys, parentKey]);
        setAutoExpandParent(true);
      }
    },
    [root, expandedKeys]
  );

  const handleAddRootNode = useCallback(() => {
    setSelectNode(root);
    setIsShowSelectComponent(true);
  }, [root]);

  const handleComponentSelect = useCallback(() => {
    setIsShowSelectComponent(false);
  }, []);

  const handleTreeSelect: TreeProps['onSelect'] = (keys) => {
    const selectedKey = keys[0] as string;
    setSelectedKey(selectedKey);
    if (onSelect) {
      onSelect(selectedKey);
    }
  };

  return {
    expandedKeys,
    selectedKey,
    editingItemKey,
    editingItemValue,
    autoExpandParent,
    convertedItems,
    selectNode,
    isShowSelectComponent,
    handleExpand,
    handleStartEdit,
    handleSaveEdit,
    handleCancelEdit,
    handleInputChange,
    handleKeyDown,
    handleAddNode,
    handleAddRootNode,
    handleComponentSelect,
    setIsShowSelectComponent,
    setExpandedKeys,
    setAutoExpandParent,
    setSelectedKey,
    handleTreeSelect,
  };
};

import {
  Question,
  getQuestionsForGame,
  QuestionResult,
} from '../../common/QuestionComponent/QuestionBankConfig';
import { ITextProps } from '../../core/title/CoreTitle';

// Define lifeline types
export type LifelineType = '50-50' | 'audience-poll' | 'phone-a-friend';

// Define the prize levels
export interface PrizeLevel {
  level: number;
  amount: number;
  isMilestone: boolean;
  awardType?: string;
}

// Define the configuration for the Millionaire game
export interface MillionaireGameConfig {
  titleProps: ITextProps;
  description: string;
  questions: Question[];
  timeLimit: number;
  theme?: 'classic' | 'dark' | 'light' | 'education';
  difficultyProgression: boolean;
  showExplanation: boolean;
  lifelines: LifelineType[];
  prizeTree: PrizeLevel[];
  currency?: string;
  awardType?: string;
  maxQuestions?: number;
  groupMode?: boolean;
  soundEffects?: boolean;
  shuffleQuestions?: boolean;
  allowConfiguration?: boolean;
}

// Define the game summary
export interface GameSummary {
  totalScore: number;
  correctAnswers: number;
  totalQuestions: number;
  accuracy: number;
  averageTimePerQuestion?: number;
  totalTimeTaken?: number;
  finalPrizeAmount: number;
  finalLevel: number;
  lifelinesUsed: LifelineType[];
  results: QuestionResult[];
}

// Define the state of each lifeline
export interface LifelineState {
  type: LifelineType;
  used: boolean;
}

// Define the default configuration for the game
export const defaultMillionaireGameConfig: MillionaireGameConfig = {
  titleProps: {
    text: 'Ai Là Triệu Phú',
    fontSize: 24,
    align: 'left',
    bold: true,
  },
  description:
    'Trả lời các câu hỏi để trở thành triệu phú. Mỗi câu hỏi đúng sẽ đưa bạn đến với giải thưởng lớn hơn!',
  questions: getQuestionsForGame(
    15,
    ['easy', 'medium', 'hard'],
    ['multiplechoice']
  ),
  timeLimit: 30,
  theme: 'classic',
  difficultyProgression: true,
  showExplanation: true,
  lifelines: ['50-50', 'audience-poll', 'phone-a-friend'],
  prizeTree: [
    { level: 1, amount: 200000, isMilestone: false, awardType: 'Điểm' },
    { level: 2, amount: 400000, isMilestone: false, awardType: 'Điểm' },
    { level: 3, amount: 600000, isMilestone: false, awardType: 'Điểm' },
    { level: 4, amount: 1000000, isMilestone: false, awardType: 'Điểm' },
    { level: 5, amount: 2000000, isMilestone: true, awardType: 'Điểm' },
    { level: 6, amount: 3000000, isMilestone: false, awardType: 'Điểm' },
    { level: 7, amount: 5000000, isMilestone: false, awardType: 'Điểm' },
    { level: 8, amount: 10000000, isMilestone: false, awardType: 'Điểm' },
    { level: 9, amount: 15000000, isMilestone: false, awardType: 'Điểm' },
    { level: 10, amount: 22000000, isMilestone: true, awardType: 'Điểm' },
    { level: 11, amount: 30000000, isMilestone: false, awardType: 'Điểm' },
    { level: 12, amount: 40000000, isMilestone: false, awardType: 'Điểm' },
    { level: 13, amount: 60000000, isMilestone: false, awardType: 'Điểm' },
    { level: 14, amount: 85000000, isMilestone: false, awardType: 'Điểm' },
    { level: 15, amount: 150000000, isMilestone: true, awardType: 'Điểm' },
  ],
  currency: 'VND',
  awardType: 'Điểm',
};

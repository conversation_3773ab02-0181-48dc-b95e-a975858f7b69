import { memo } from 'react';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';

interface LessonColumnProps {}
/// làm tương tự LessonCardComponent

const LessonColumnComponent: React.FC<IEdTechRenderProps<LessonColumnProps>> = (
  props
) => {
  return (
    <div className="tailwind-flex tailwind-bg-default tailwind-w-full">
      <div className="tailwind-w-full">{props.children}</div>
    </div>
  );
};
export default memo(withEdComponentParams(LessonColumnComponent));

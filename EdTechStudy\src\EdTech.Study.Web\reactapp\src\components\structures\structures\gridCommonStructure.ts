import { nanoid } from 'nanoid';
import { ETypeEdTechComponent } from '../../../enums/AppEnums';
import {
  IEdTechComponent,
  IEdTechRenderProps,
  IEdTechRenderTreeData,
  IEdTechRenderTreeStructureData,
} from '../../../interfaces/AppComponents';

/**
 * Xây dựng subItem từ IEdTechComponent
 * @param component Component gốc
 * @param basePath Đường dẫn cơ sở
 * @param index Chỉ số của component
 */
const buildSubItem = (
  component: IEdTechComponent,
  basePath: string,
  index: number
): IEdTechRenderTreeData => {
  const childId = nanoid(8);
  return {
    ...component,
    id: childId,
    order: index,
    path: `${basePath}/${childId}`,
    version: '1.0.0',
  };
};

/**
 * Hàm GridCommonStructure linh hoạt - tự động xác định trường hợp và tạo cấu trúc phù hợp
 * 
 * @param node Node hiện tại hoặc mảng component (cho tương thích ngược)
 * @param subNodes Các component con cần thêm vào
 * @param additionalColumns Số lượng cột bổ sung cần tạo
 */
export const GridCommonStructure = (
  node: IEdTechRenderProps | IEdTechComponent[] | null | undefined,
  subNodes: IEdTechComponent[] = [],
  additionalColumns: number = 0
): IEdTechRenderTreeData | IEdTechRenderTreeStructureData | IEdTechRenderTreeData[] => {
  // Trường hợp 1: node là mảng IEdTechComponent[] (tương thích với signature cũ)
  if (Array.isArray(node)) {
    const children = node;
    return createNewGrid(children, 0, additionalColumns);
  }
  
  // Trường hợp 2: node không được cung cấp hoặc null
  if (!node) {
    return createNewGrid(subNodes, 0, additionalColumns);
  }
  
  // Trường hợp 3: node là IEdTechRenderProps
  const currentNode = node as IEdTechRenderProps;
  
  // Tự động xác định chế độ dựa trên loại và trạng thái của node
  if (currentNode.name === 'LessonRowComponent') {
    // Node là hàng -> Thêm cột vào hàng
    return addColumnToRow(currentNode, subNodes);
  } else if (currentNode.name === 'LessonColumnComponent') {
    // Node là cột -> Kiểm tra thêm trạng thái
    const hasRowComponent = (currentNode.subItems || []).some(item => item.name === 'LessonRowComponent');
    
    if (hasRowComponent) {
      // Cột đã có hàng -> Thêm hàng mới
      return addRowToColumn(currentNode, subNodes);
    } else if (subNodes && subNodes.length > 0) {
      // Cột chưa có hàng nhưng có subNodes -> Thêm items vào cột
      return addItemsToColumn(currentNode, subNodes);
    } else {
      // Cột chưa có hàng và không có subNodes -> Chuyển đổi thành grid
      return transformColumnToGrid(currentNode);
    }
  } else {
    // Node không phải hàng hoặc cột -> Mặc định tạo grid mới
    return createNewGrid(subNodes, 0, additionalColumns);
  }
};

/**
 * Tạo một cấu trúc grid mới hoàn toàn
 */
const createNewGrid = (
  children: IEdTechComponent[],
  rowIndex: number = 0,
  additionalColumns: number = 0
): IEdTechRenderTreeStructureData => {
  const rowId = nanoid(8);
  const row: IEdTechRenderTreeStructureData = {
    id: rowId,
    name: 'LessonRowComponent',
    title: `Hàng ${rowIndex + 1}`,
    type: ETypeEdTechComponent.LAYOUT,
    order: rowIndex,
    path: `${rowId}`,
    version: '1.0.0',
  };

  // Tạo cột chính đầu tiên
  const columnId = nanoid(8);
  const column: IEdTechRenderTreeStructureData = {
    id: columnId,
    name: 'LessonColumnComponent',
    title: 'Cột 1',
    type: ETypeEdTechComponent.LAYOUT,
    order: 0,
    path: `${rowId}/${columnId}`,
    version: '1.0.0',
    subItems: children.map((child, childIndex) => {
      const childId = nanoid(8);
      return {
        ...child,
        id: childId,
        order: childIndex,
        path: `${rowId}/${columnId}/${childId}`,
        version: '1.0.0',
      };
    }),
  };

  // Mảng các cột
  const columns = [column];

  // Thêm các cột bổ sung nếu được yêu cầu
  if (additionalColumns > 0) {
    for (let i = 0; i < additionalColumns; i++) {
      const additionalColumnId = nanoid(8);
      const additionalColumn: IEdTechRenderTreeStructureData = {
        id: additionalColumnId,
        name: 'LessonColumnComponent',
        title: `Cột ${i + 2}`, // Bắt đầu từ Cột 2
        type: ETypeEdTechComponent.LAYOUT,
        order: i + 1, // Bắt đầu từ order 1
        path: `${rowId}/${additionalColumnId}`,
        version: '1.0.0',
        subItems: [], // Mảng subItems trống cho các cột bổ sung
      };
      columns.push(additionalColumn);
    }
  }

  // Thêm các cột làm subItems cho hàng
  row.subItems = columns;

  return row;
};

/**
 * Thêm cột vào hàng (trống hoặc có subItems)
 */
const addColumnToRow = (
  node: IEdTechRenderProps,
  subNodes: IEdTechComponent[] = []
): IEdTechRenderTreeData => {
  const { path, subItems = [] } = node;
  const columnId = nanoid(8);
  const columnCount = subItems.filter(x => x.name === 'LessonColumnComponent').length;
  
  return {
    id: columnId,
    name: 'LessonColumnComponent',
    title: `Cột ${columnCount + 1}`,
    type: ETypeEdTechComponent.LAYOUT,
    order: columnCount,
    path: `${path}/${columnId}`,
    version: '1.0.0',
    subItems: subNodes.length > 0 
      ? subNodes.map((component, index) => buildSubItem(component, `${path}/${columnId}`, index))
      : []
  };
};

/**
 * Chuyển đổi cột đơn thành cấu trúc hàng-cột
 */
const transformColumnToGrid = (
  node: IEdTechRenderProps
): IEdTechRenderTreeData => {
  const { path, subItems = [] } = node;
  const rowId = nanoid(8);
  const column1Id = nanoid(8);
  const column2Id = nanoid(8);
  
  return {
    id: rowId,
    name: 'LessonRowComponent',
    title: 'Hàng 1',
    type: ETypeEdTechComponent.LAYOUT,
    order: 0,
    path: `${path}/${rowId}`,
    version: '1.0.0',
    subItems: [
      // Cột đầu tiên chứa nội dung hiện tại
      {
        id: column1Id,
        name: 'LessonColumnComponent',
        title: 'Cột 1',
        type: ETypeEdTechComponent.LAYOUT,
        order: 0,
        path: `${path}/${rowId}/${column1Id}`,
        version: '1.0.0',
        subItems: subItems // Di chuyển các subitem hiện có vào cột đầu tiên
      },
      // Cột thứ hai (trống)
      {
        id: column2Id,
        name: 'LessonColumnComponent',
        title: 'Cột 2',
        type: ETypeEdTechComponent.LAYOUT,
        order: 1,
        path: `${path}/${rowId}/${column2Id}`,
        version: '1.0.0',
        subItems: []
      }
    ]
  };
};

/**
 * Thêm subNodes vào subItems của cột
 */
const addItemsToColumn = (
  node: IEdTechRenderProps,
  subNodes: IEdTechComponent[] = []
): IEdTechRenderTreeData[] => {
  const { path, subItems = [] } = node;
  return subNodes.map((component, index) => 
    buildSubItem(component, path, subItems.length + index)
  );
};

/**
 * Thêm hàng mới vào cột với subNodes là subItems của cột đầu tiên
 * Nếu cột đã có hàng rồi, thì hàng mới chỉ có 1 cột
 */
const addRowToColumn = (
  node: IEdTechRenderProps,
  subNodes: IEdTechComponent[] = []
): IEdTechRenderTreeData => {
  const { path, subItems = [] } = node;
  const rowId = nanoid(8);
  const column1Id = nanoid(8);
  const rowCount = subItems.filter(x => x.name === 'LessonRowComponent').length;
  
  // Tạo hàng mới chỉ với một cột duy nhất
  return {
    id: rowId,
    name: 'LessonRowComponent',
    title: `Hàng ${rowCount + 1}`,
    type: ETypeEdTechComponent.LAYOUT,
    order: subItems.length,
    path: `${path}/${rowId}`,
    version: '1.0.0',
    subItems: [
      // Chỉ tạo 1 cột duy nhất
      {
        id: column1Id,
        name: 'LessonColumnComponent',
        title: 'Cột 1',
        type: ETypeEdTechComponent.LAYOUT,
        order: 0,
        path: `${path}/${rowId}/${column1Id}`,
        version: '1.0.0',
        subItems: subNodes.map((component, index) => 
          buildSubItem(component, `${path}/${rowId}/${column1Id}`, index)
        )
      }
    ]
  };
};

// Export các hàm chi tiết để tương thích với mã nguồn hiện có
export const addEmptyColumnToRow = (node: IEdTechRenderProps): IEdTechRenderTreeData => {
  return addColumnToRow(node, []);
};

export const addColumnWithItemsToRow = (node: IEdTechRenderProps, subNodes: IEdTechComponent[]): IEdTechRenderTreeData => {
  return addColumnToRow(node, subNodes);
};

export const transformColumnToGridFn = (node: IEdTechRenderProps): IEdTechRenderTreeData => {
  return transformColumnToGrid(node);
};

export const addItemsToColumnFn = (node: IEdTechRenderProps, subNodes: IEdTechComponent[]): IEdTechRenderTreeData[] => {
  return addItemsToColumn(node, subNodes);
};

export const addRowWithItemsToColumn = (node: IEdTechRenderProps, subNodes: IEdTechComponent[]): IEdTechRenderTreeData => {
  return addRowToColumn(node, subNodes);
};

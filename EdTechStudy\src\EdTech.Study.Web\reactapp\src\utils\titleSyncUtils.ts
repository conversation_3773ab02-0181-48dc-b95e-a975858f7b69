import { ITextProps } from '../components/core/title/CoreTitle';
import { updateItems } from '../store/slices/AppSlices/EdTechRenderDataSlice';
import { addOrUpdateParam } from '../store/slices/AppSlices/EdComponentParamsSlice';
import { findNodeById } from './treeUtils';

/**
 * Cập nhật title trong menu item khi title của component thay đổi
 * @param id ID của component
 * @param newTitle Title mới
 * @param renderTreeData Dữ liệu cây từ Redux store
 * @param dispatch Hàm dispatch từ Redux
 */
export const updateMenuItemTitle = (
  id: string,
  newTitle: string,
  renderTreeData: any,
  dispatch: any
) => {
  if (!newTitle || !renderTreeData) return;

  const nodeToUpdate = findNodeById(renderTreeData.subItems || [], id);
  if (nodeToUpdate) {
    dispatch(
      updateItems({
        items: [
          {
            path: nodeToUpdate.path || id,
            data: { title: newTitle },
          },
        ],
      })
    );
  }
};

/**
 * Cập nhật title trong component params khi menu item được đổi tên
 * @param key ID của menu item
 * @param newTitle Title mới
 * @param edComponentParams Dữ liệu params từ Redux store
 * @param dispatch Hàm dispatch từ Redux
 */
export const updateComponentTitle = (
  key: string,
  newTitle: string,
  edComponentParams: any,
  dispatch: any
) => {
  if (!edComponentParams || !edComponentParams.data) return;

  const params = edComponentParams.data.find((param: any) => param.id === key);

  // Nếu không tìm thấy params hoặc params không có thuộc tính params
  if (!params || !params.params) {
    // Tạo một params mới với titleProps
    const newParams = {
      titleProps: {
        text: newTitle,
        editable: true,
      },
    };

    // Dispatch action để thêm params mới
    dispatch(addOrUpdateParam({ id: key, params: newParams }));
    return;
  }

  // Kiểm tra xem params có chứa titleProps không
  if (params.params.titleProps) {
    // Cập nhật text trong titleProps
    const updatedParams = {
      ...params.params,
      titleProps: {
        ...params.params.titleProps,
        text: newTitle,
      },
    };

    // Dispatch action để cập nhật params
    dispatch(addOrUpdateParam({ id: key, params: updatedParams }));
  }
  // Kiểm tra xem params có chứa title không
  else if ('title' in params.params) {
    const updatedParams = {
      ...params.params,
      title: newTitle,
    };

    // Dispatch action để cập nhật params
    dispatch(addOrUpdateParam({ id: key, params: updatedParams }));
  }
  // Nếu không có cả titleProps và title, thêm titleProps mới
  else {
    const updatedParams = {
      ...params.params,
      titleProps: {
        text: newTitle,
        editable: true,
      },
    };

    // Dispatch action để cập nhật params
    dispatch(addOrUpdateParam({ id: key, params: updatedParams }));
  }
};

/**
 * Cập nhật title trong component và menu item
 * @param id ID của component
 * @param updates Các cập nhật cho title
 * @param config Config hiện tại của component
 * @param renderTreeData Dữ liệu cây từ Redux store
 * @param dispatch Hàm dispatch từ Redux
 * @param addOrUpdateParamComponent Hàm cập nhật params của component
 * @returns Config đã được cập nhật
 */
export const updateTitleAndSync = (
  id: string,
  updates: Partial<ITextProps>,
  config: any,
  renderTreeData: any,
  dispatch: any,
  addOrUpdateParamComponent?: (params: any) => void
) => {
  // Cập nhật title trong menu item nếu có text mới
  if (updates.text) {
    updateMenuItemTitle(id, updates.text, renderTreeData, dispatch);
  }

  // Nếu có addOrUpdateParamComponent (bất kể có config hay không)
  if (addOrUpdateParamComponent) {
    let updatedConfig = config || {}; // Sử dụng empty object nếu config là null hoặc undefined

    // Kiểm tra xem config có chứa titleProps không
    if (updatedConfig.titleProps) {
      updatedConfig = {
        ...updatedConfig,
        titleProps: {
          ...updatedConfig.titleProps,
          ...updates,
        },
      };
    }
    // Kiểm tra xem config có chứa title không
    else if ('title' in updatedConfig) {
      updatedConfig = {
        ...updatedConfig,
        title: updates.text,
      };
    }
    // Nếu không có cả hai, tạo mới titleProps với updates
    else {
      updatedConfig = {
        ...updatedConfig,
        titleProps: {
          ...updates,
        },
      };
    }

    // Cập nhật params của component
    addOrUpdateParamComponent(updatedConfig);
    return updatedConfig;
  }

  return config;
};

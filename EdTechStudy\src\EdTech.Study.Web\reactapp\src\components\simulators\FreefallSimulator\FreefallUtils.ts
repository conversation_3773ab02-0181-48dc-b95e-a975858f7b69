// FreefallUtils.ts
import {
  FreefallEnvironment,
  FreefallObject,
  FreefallDataPoint,
  FreefallSimulationResult,
} from './FreefallSimulatorConfig';

/**
 * Calculate the drag force based on velocity, air density, drag coefficient, and object properties
 * @param velocity Current velocity (m/s)
 * @param airDensity Air density (kg/m^3)
 * @param dragCoefficient Drag coefficient (dimensionless)
 * @param object Object properties
 * @returns Drag force (N)
 */
export const calculateDragForce = (
  velocity: number,
  airDensity: number,
  dragCoefficient: number,
  object: FreefallObject
): number => {
  // Drag equation: F_d = 0.5 * ρ * v^2 * C_d * A
  // Where:
  // ρ = air density
  // v = velocity
  // C_d = drag coefficient
  // A = cross-sectional area (approximated as πr²)

  const area = Math.PI * Math.pow(object.radius, 2);
  const dragForce =
    0.5 * airDensity * Math.pow(velocity, 2) * dragCoefficient * area;

  // Drag force always opposes motion, so we need the sign
  return velocity > 0 ? -dragForce : dragForce;
};

/**
 * Calculate one step of free fall motion
 * @param currentPosition Current position (m)
 * @param currentVelocity Current velocity (m/s)
 * @param timeStep Time step for calculation (s)
 * @param environment Physics environment
 * @param object Object properties
 * @returns New position and velocity
 */
export const calculateFreefallStep = (
  currentPosition: number,
  currentVelocity: number,
  timeStep: number,
  environment: FreefallEnvironment,
  object: FreefallObject
): { position: number; velocity: number; acceleration: number } => {
  // Start with gravitational acceleration
  let acceleration = -environment.gravity; // Negative because down is negative in our coordinate system (y-axis points up)

  // Add air resistance if enabled
  if (environment.airResistance) {
    const dragForce = calculateDragForce(
      currentVelocity,
      environment.airDensity,
      environment.dragCoefficient,
      object
    );

    // F = ma, so a = F/m
    const dragAcceleration = dragForce / object.mass;
    acceleration += dragAcceleration;
  }

  // Update velocity: v = v₀ + at
  const newVelocity = currentVelocity + acceleration * timeStep;

  // Update position: s = s₀ + v₀t + ½at²
  const newPosition =
    currentPosition +
    currentVelocity * timeStep +
    0.5 * acceleration * Math.pow(timeStep, 2);

  return {
    position: newPosition,
    velocity: newVelocity,
    acceleration: acceleration,
  };
};

/**
 * Simulate the entire free fall motion
 * @param initialHeight Initial height (m)
 * @param initialVelocity Initial velocity (m/s)
 * @param environment Physics environment
 * @param object Object properties
 * @param _duration Maximum simulation duration (s)
 * @param timeStep Time step for calculation (s)
 * @returns Complete simulation results
 */
export const simulateFreeFall = (
  initialHeight: number,
  initialVelocity: number,
  environment: FreefallEnvironment,
  object: FreefallObject,
  timeStep: number
): FreefallSimulationResult => {
  let time = 0;
  let position = initialHeight;
  let velocity = initialVelocity;
  let maxHeight = initialHeight;
  let maxVelocity = 0;
  const dataPoints: FreefallDataPoint[] = [];

  // Add initial data point
  dataPoints.push({
    time: 0,
    position: initialHeight,
    velocity: initialVelocity,
    acceleration: -environment.gravity, // Initial acceleration is just gravity
  });

  // Continue simulation until object hits ground (accounting for object radius)
  // We need to stop when the bottom of the object touches the ground, which means
  // the position (center of object) is equal to the object's radius
  // Will continue until ground is reached, but with a safety limit to prevent infinite loops
  const MAX_ITERATIONS = 100000; // Safety limit
  let iterations = 0;
  while (position > object.radius && iterations < MAX_ITERATIONS) {
    iterations++;
    // Calculate next step
    time += timeStep;
    const step = calculateFreefallStep(
      position,
      velocity,
      timeStep,
      environment,
      object
    );
    position = step.position;
    velocity = step.velocity;

    // Record data point
    dataPoints.push({
      time,
      position,
      velocity,
      acceleration: step.acceleration,
    });

    // Update max values
    maxHeight = Math.max(maxHeight, position);
    maxVelocity = Math.max(maxVelocity, Math.abs(velocity));
  }

  // Calculate impact (you might need to interpolate for more accuracy)
  const impactVelocity = velocity;
  const impactTime = time;

  return {
    timeElapsed: time,
    maxHeight,
    maxVelocity,
    impactVelocity,
    impactTime,
    dataPoints,
  };
};

/**
 * Calculate the time to reach the ground in a perfect free fall (no air resistance)
 * @param initialHeight Initial height (m)
 * @param initialVelocity Initial velocity (m/s)
 * @param gravity Gravity acceleration (m/s²)
 * @returns Time to hit ground (s)
 */
export const calculateFreefallTime = (
  initialHeight: number,
  initialVelocity: number,
  gravity: number
): number => {
  // Solving for t in the equation: h₀ + v₀t - ½gt² = 0

  // Use quadratic formula: t = (-v₀ ± √(v₀² + 2gh₀)) / g
  // We want the positive solution since time can't be negative
  return (
    (initialVelocity +
      Math.sqrt(Math.pow(initialVelocity, 2) + 2 * gravity * initialHeight)) /
    gravity
  );
};

/**
 * Calculate the impact velocity in a perfect free fall (no air resistance)
 * @param initialHeight Initial height (m)
 * @param initialVelocity Initial velocity (m/s)
 * @param gravity Gravity acceleration (m/s²)
 * @returns Impact velocity (m/s)
 */
export const calculateFreefallImpactVelocity = (
  initialHeight: number,
  initialVelocity: number,
  gravity: number
): number => {
  // Using energy conservation: v² = v₀² + 2gh
  // where v is impact velocity, v₀ is initial velocity, g is gravity, h is height
  return Math.sqrt(Math.pow(initialVelocity, 2) + 2 * gravity * initialHeight);
};

/**
 * Format a number with proper units
 * @param value Numeric value
 * @param unit Unit string
 * @param precision Number of decimal places
 * @returns Formatted string with units
 */
export const formatWithUnits = (
  value: number,
  unit: string,
  precision: number = 2
): string => {
  return `${value.toFixed(precision)} ${unit}`;
};

/**
 * Find the index of the data point closest to a given time
 * @param dataPoints Array of data points
 * @param time Target time
 * @returns Index of closest data point
 */
export const findClosestDataPointIndex = (
  dataPoints: FreefallDataPoint[],
  time: number
): number => {
  if (dataPoints.length === 0) return -1;

  return dataPoints.reduce((closestIndex, point, currentIndex, points) => {
    const currentDistance = Math.abs(point.time - time);
    const closestDistance = Math.abs(points[closestIndex].time - time);

    return currentDistance < closestDistance ? currentIndex : closestIndex;
  }, 0);
};

/**
 * Convert between different units (metric to imperial and vice versa)
 * @param value Value to convert
 * @param fromUnit Source unit
 * @param toUnit Target unit
 * @returns Converted value
 */
export const convertFreefallUnits = (
  value: number,
  fromUnit: string,
  toUnit: string
): number => {
  // Define conversion factors
  const conversions: Record<string, Record<string, number>> = {
    m: { ft: 3.28084, in: 39.3701 },
    ft: { m: 0.3048, in: 12 },
    in: { m: 0.0254, ft: 1 / 12 },
    kg: { lb: 2.20462 },
    lb: { kg: 0.453592 },
    'm/s': { 'ft/s': 3.28084, mph: 2.23694 },
    'ft/s': { 'm/s': 0.3048, mph: 0.681818 },
    mph: { 'm/s': 0.44704, 'ft/s': 1.46667 },
    'm/s²': { 'ft/s²': 3.28084 },
    'ft/s²': { 'm/s²': 0.3048 },
  };

  // Same unit, no conversion needed
  if (fromUnit === toUnit) return value;

  // Check if conversion exists
  if (conversions[fromUnit] && conversions[fromUnit][toUnit]) {
    return value * conversions[fromUnit][toUnit];
  }

  // If direct conversion doesn't exist, try to find an intermediate conversion
  for (const intermediateUnit in conversions) {
    if (
      conversions[fromUnit] &&
      conversions[fromUnit][intermediateUnit] &&
      conversions[intermediateUnit] &&
      conversions[intermediateUnit][toUnit]
    ) {
      return (
        value *
        conversions[fromUnit][intermediateUnit] *
        conversions[intermediateUnit][toUnit]
      );
    }
  }

  // If no conversion found, return original value
  console.warn(`No conversion found from ${fromUnit} to ${toUnit}`);
  return value;
};

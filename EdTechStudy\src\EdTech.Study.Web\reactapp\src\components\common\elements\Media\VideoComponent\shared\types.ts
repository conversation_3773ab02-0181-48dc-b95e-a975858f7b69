import { ITextProps } from '../../../../../core/title/CoreTitle';
import { MediaComponentProps, MediaItem } from '../../shared/type';
export interface VideoItem extends MediaItem {
}

export interface VideoComponentProps extends MediaComponentProps {
  /** Title properties for the component */
  titleProps?: ITextProps;

  /** Array of videos to display */
  medias?: VideoItem[];

  /** Whether to autoplay the video */
  autoPlay?: boolean;

  /** Whether to show video controls */
  controls?: boolean;

  /** Whether to loop the video */
  loop?: boolean;

  /** Start time in seconds */
  startTime?: number;

  /** End time in seconds */
  endTime?: number;

  /** Width of the video container */
  width?: number | string;

  /** Height of the video container */
  height?: number | string;

  /** Display mode for multiple videos */
  displayMode?: 'horizontal' | 'vertical';
}

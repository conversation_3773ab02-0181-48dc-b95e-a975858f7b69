import { axiosClient } from '../services/axiosClient';

import { camelCaseKeys } from '../utils/parsers';

// Helper functions for OData filtering
function getQuestionTypeFilter(type: QuestionType): string {
  const typeName = QuestionType[type];
  if (typeName === undefined) {
    return `1 eq 1`;
  }
  return `Type eq cast('${typeName}', 'EdTech.Study.Questions.QuestionType')`;
}

function getQuestionTypeName(type: QuestionType): string {
  return QuestionType[type];
}

function getQuestionStatusFilter(status: QuestionStatus): string {
  const statusName = QuestionStatus[status];
  if (statusName === undefined) {
    // console.error(`Invalid QuestionStatus value: ${status}`);
    // Return a filter that will match all items (effectively removing the filter)
    return `1 eq 1`;
  }
  return `Status eq cast('${statusName}', 'EdTech.Study.Questions.QuestionStatus')`;
}

function getQuestionStatusName(status: QuestionStatus): string {
  return QuestionStatus[status];
}

// Helper function to format GUIDs for OData queries
function formatGuid(guid: string): string {
  // OData requires GUIDs to be formatted with single quotes
  return `'${guid}'`;
}

// Define enums to match C# side
export enum QuestionType {
  SingleChoice = 0,
  MultipleChoice = 1,
  Essay = 2,
  Matching = 3,
  Fillblank = 4,
}

export enum QuestionStatus {
  Draft = 0,
  Submitted = 1,
  Approved = 2,
  Published = 3,
  Rejected = 4,
}

// Updated interface to match QuestionDraftEntity.cs
export interface QuestionDraft {
  id?: string;
  content: string;
  contentFormat: string;
  type: QuestionType;
  difficultyLevel: number;
  description?: string;
  topics?: string;
  tags?: string;
  status: QuestionStatus;
  comment?: string;
  idempotentKey?: string;
  source?: string;
  subjectId?: string;
  subject?: {
    id: string;
    code: string;
    name: string;
  };
  options?: QuestionOptionDraft[];
  extraProperties?: Record<string, any>;
  concurrencyStamp?: string;
  creationTime?: string;
  creatorId?: string;
  lastModificationTime?: string;
  lastModifierId?: string;
}

// Interface for question options matching QuestionOptionDraftEntity.cs
export interface QuestionOptionDraft {
  id?: string;
  content: string;
  contentFormat: string;
  isCorrect: boolean;
  order: number;
  explanation?: string;
  questionDraftId?: string;
}

export interface QuestionDraftParams {
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: string;
  searchText?: string;
  type?: QuestionType | QuestionType[];
  status?: QuestionStatus | QuestionStatus[];
  subjectId?: string;
  subjectIds?: string[]; // Add this line
  [key: string]: any;
}

// Helper function to build subject IDs filter for OData
function getSubjectIdsFilter(subjectIds: string[]): string {
  if (!subjectIds || subjectIds.length === 0) return '';

  // If there's only one subject ID
  if (subjectIds.length === 1) {
    return `SubjectId eq ${formatGuid(subjectIds[0])}`;
  }

  // If there are multiple subject IDs, create an OR condition
  const subjectFilters = subjectIds
    .map((id) => `SubjectId eq ${formatGuid(id)}`)
    .join(' or ');
  return `(${subjectFilters})`;
}

// Interface for updating questions
export interface UpdateQuestionsRequest {
  questions: QuestionDraft[];
}

class QuestionDraftApi {
  // Map for translating frontend field names to backend OData entity property names
  private fieldMapping: Record<string, string> = {
    title: 'Content',
    points: 'DifficultyLevel',
    statusEntity: 'Status',
    type: 'Type',
    // Add more mappings as needed
  };

  // Common OData parameters to include Subject in all requests
  private getCommonODataParams(): Record<string, string> {
    return {
      $expand: 'Subject,Options',
      $count: 'true',
    };
  }

  public async getQuestions(params: QuestionDraftParams) {
    // Use standard OData parameters for pagination
    const skip =
      params.page && params.pageSize ? (params.page - 1) * params.pageSize : 0;
    const top = params.pageSize || 10;

    // Build sorting
    let orderby = '';
    if (params.sortField) {
      // Map frontend field name to backend property name if needed
      let backendFieldName = params.sortField;
      if (this.fieldMapping[backendFieldName]) {
        backendFieldName = this.fieldMapping[backendFieldName];
      }

      // Convert sortOrder from frontend format to OData format
      const sortDir = params.sortOrder || 'asc';
      orderby = `${backendFieldName} ${sortDir}`;

      // console.log(`Applying sort: ${orderby}`); // Debug log
    }

    // Build filtering
    const filters = [];

    // Add search text filter if provided
    if (params.searchText) {
      filters.push(
        `contains(Content,'${params.searchText}') or contains(Description,'${params.searchText}')`
      );
    }

    // Add type filter if provided
    if (params.type !== undefined) {
      if (Array.isArray(params.type)) {
        const typeFilters = params.type
          .map((t) => getQuestionTypeFilter(t))
          .join(' or ');
        filters.push(`(${typeFilters})`);
      } else {
        filters.push(getQuestionTypeFilter(params.type));
      }
    }

    // Add status filter if provided
    if (params.status !== undefined) {
      if (Array.isArray(params.status)) {
        const statusFilters = params.status
          .map((s) => getQuestionStatusFilter(s))
          .join(' or ');
        filters.push(`(${statusFilters})`);
      } else {
        filters.push(getQuestionStatusFilter(params.status));
      }
    }

    // Add single subject filter if provided
    if (params.subjectId) {
      filters.push(`SubjectId eq ${formatGuid(params.subjectId)}`);
    }

    // Add multiple subjects filter if provided
    if (params.subjectIds && params.subjectIds.length > 0) {
      // Skip if we already have a single subject filter
      if (!params.subjectId) {
        if (params.subjectIds.length === 1) {
          // For a single subject ID, use simple equality
          filters.push(`SubjectId eq ${formatGuid(params.subjectIds[0])}`);
        } else {
          // For multiple subject IDs, use OR condition
          const subjectFilters = params.subjectIds
            .map((id) => `SubjectId eq ${formatGuid(id)}`)
            .join(' or ');
          filters.push(`(${subjectFilters})`);
        }
      }
    }

    // Combine all filters with AND operator
    const filter = filters.length > 0 ? filters.join(' and ') : '';

    // Build the request parameters
    const requestParams: Record<string, any> = {
      ...this.getCommonODataParams(),
      $skip: skip,
      $top: top,
    };

    if (orderby) {
      requestParams['$orderby'] = orderby;
    }

    if (filter) {
      requestParams['$filter'] = filter;
    }

    try {
      // console.log('Sending OData request with params:', requestParams); // Debug log

      const response = await axiosClient({
        method: 'get',
        url: `/api/odata/questionDrafts`,
        params: requestParams,
      });

      // Extract total count and items from OData response
      const total = response.data['@odata.count'] || response.data.length || 0;
      const items = camelCaseKeys(
        response.data.value || response.data || []
      ) as QuestionDraft[];

      return {
        success: true,
        data: items,
        total: total,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        total: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async getQuestionById(id: string) {
    try {
      const response = await axiosClient({
        method: 'get',
        url: `/api/odata/questionDrafts/${id}`,
        params: this.getCommonODataParams(),
      });

      return {
        success: true,
        data: camelCaseKeys(response.data),
      };
    } catch (error) {
      console.error(`Error fetching question with id ${id}:`, error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async getPaginationInfo() {
    const response = await axiosClient({
      method: 'get',
      url: `/api/odata/questionDrafts/paginationInfo`,
    });

    return {
      success: true,
      data: response.data,
    };
  }

  /**
   * Cập nhật nhiều câu hỏi cùng lúc
   * @param questions Danh sách câu hỏi cần cập nhật
   * @returns Kết quả trả về từ API
   */
  public async updateQuestions(questions: QuestionDraft[]) {
    try {
      const response = await axiosClient({
        method: 'post',
        url: `/api/odata/questionDrafts/update-list-question`,
        data: {
          questions: questions,
        } as UpdateQuestionsRequest,
      });

      return {
        success: true,
        data: response.data.data,
      };
    } catch (error) {
      console.error('Error updating questions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cập nhật một câu hỏi
   * @param question Câu hỏi cần cập nhật
   * @returns Kết quả trả về từ API
   */
  public async updateQuestion(question: QuestionDraft) {
    return this.updateQuestions([question]);
  }

  public async deleteQuestions(questionIds: string[]) {
    try {
      const response = await axiosClient({
        method: 'delete',
        url: `/api/odata/questionDrafts/delete-list-question`,
        data: {
          ids: questionIds,
        },
      });

      return {
        success: true,
        data: response.data.data,
      };
    } catch (error) {
      console.error('Error updating questions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export default new QuestionDraftApi();

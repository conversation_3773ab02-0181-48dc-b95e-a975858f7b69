import { Guid } from 'guid-typescript';
import {
  BaseQuestion,
  PracticeEngineContext,
  Subject,
} from '../../../interfaces/quizs/questionBase';
import {
  QuestionDataManagerState,
  setQuestionData,
  handleChangeQuestion,
  fetchQuestions,
  getPaginationInfo,
  updateQuestions,
  deleteQuestions,
} from '../../../store/slices/QuestionSlices/questionDataManagerSlice';
import HashHelper from '../../../utils/HashHelper';
import { FullscreenContainer, useFullscreenAdapter } from '../Fullscreen';
import { connect } from 'react-redux';
import {
  StrictMode,
  useEffect,
  useCallback,
  useState,
  useMemo,
  Suspense,
} from 'react';
import QuestionStoreManager from '../../quizs/storeManager/QuestionStoreManager';
import {
  FilterValue,
  SorterResult,
  TablePaginationConfig,
} from 'antd/es/table/interface';
import { message, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

import { QuestionDraft } from '../../../api/questionDraftApi';
import questionDraftAdapter from '../../../utils/adapters/questionDraftAdapter';
import ErrorBoundary from 'antd/es/alert/ErrorBoundary';
import QuestionEditor from '../../quizs/questionEditor/QuestionEditor';

export interface IQuestionRenderComponentProps {
  questions: BaseQuestion[] | null;
  loading?: boolean;
  totalCount?: number;
  error?: string | null;
  subjects?: Subject[];
  setQuestionData: (questionData: BaseQuestion[]) => void;
  updateQuestions: (questions: QuestionDraft[]) => void;
  handleChangeQuestion: (id: string, question: BaseQuestion) => void;
  fetchQuestions?: (params: any) => void;
  onEditQuestion?: (question: BaseQuestion) => void;
  onDeleteQuestions?: (questionId: string[]) => void;
  onPreviewQuestion?: (question: BaseQuestion) => void;
  onExportQuestions?: () => void;
  onImportQuestions?: () => void;
  getPaginationInfo?: () => void;
}

function QuestionRenderComponent(props: IQuestionRenderComponentProps) {
  const {
    questions,
    loading,
    totalCount,
    subjects,
    updateQuestions,
    handleChangeQuestion: storeHandleChangeQuestion,
    fetchQuestions,
    onEditQuestion,
    onDeleteQuestions,
    onPreviewQuestion,
    onExportQuestions,
    onImportQuestions,
    getPaginationInfo,
  } = props;

  const id =
    questions && questions.length > 0
      ? HashHelper.computeHash(questions.map((q) => q.id))
      : Guid.create().toString();

  const localQuestions = useMemo(() => {
    return questions
      ? (questions.map((question) => {
          return {
            ...question,
          };
        }) as BaseQuestion[])
      : [];
  }, [questions]);

  const { isFullscreen, fullscreenRef, handleToggleFullscreen } =
    useFullscreenAdapter();

  // State for subject filtering
  const [selectedSubjectIds, setSelectedSubjectIds] = useState<string[]>([]);

  // State for question editor
  const [editorVisible, setEditorVisible] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<BaseQuestion | null>(
    null
  );

  // Initial data fetch when component mounts
  useEffect(() => {
    if (fetchQuestions) {
      fetchQuestions({
        page: 1,
        pageSize: 10,
      });
    }
    if (getPaginationInfo) {
      getPaginationInfo();
    }
  }, [fetchQuestions, getPaginationInfo]);

  // Handle subject filter change
  const handleSubjectFilterChange = useCallback(
    (subjectIds: string[]) => {
      setSelectedSubjectIds(subjectIds);

      // Only trigger a fetch if we have the fetchQuestions function
      if (fetchQuestions) {
        fetchQuestions({
          page: 1, // Reset to first page when changing filters
          pageSize: 10,
          subjectIds: subjectIds.length > 0 ? subjectIds : undefined,
        });
      }
    },
    [fetchQuestions]
  );

  // Handle table change (pagination, sorting, filtering)
  const handleTableChange = useCallback(
    (
      pagination: TablePaginationConfig,
      filters: Record<string, FilterValue | null>,
      sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
    ) => {
      if (fetchQuestions) {
        const singleSorter = Array.isArray(sorter) ? sorter[0] : sorter;

        fetchQuestions({
          page: pagination.current,
          pageSize: pagination.pageSize,
          sortField: singleSorter.field,
          sortOrder: singleSorter.order,
          subjectIds:
            selectedSubjectIds.length > 0 ? selectedSubjectIds : undefined,
          ...filters,
        });
      }
    },
    [fetchQuestions, selectedSubjectIds]
  );

  const handleSearch = useCallback(
    (searchText: string) => {
      if (fetchQuestions) {
        fetchQuestions({
          page: 1,
          pageSize: 10,
          searchText,
          subjectIds:
            selectedSubjectIds.length > 0 ? selectedSubjectIds : undefined,
        });
      }
    },
    [fetchQuestions, selectedSubjectIds]
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    if (fetchQuestions) {
      fetchQuestions({
        page: 1,
        pageSize: 10,
        subjectIds:
          selectedSubjectIds.length > 0 ? selectedSubjectIds : undefined,
      });
    }
    if (getPaginationInfo) {
      getPaginationInfo();
    }
  }, [fetchQuestions, getPaginationInfo, selectedSubjectIds]);

  // Handle edit question
  const handleEditQuestion = useCallback((question: BaseQuestion) => {
    setCurrentQuestion(question);
    setEditorVisible(true);
  }, []);

  // Handle save question from editor
  const handleSaveQuestion = useCallback(
    (updatedQuestion: BaseQuestion) => {
      // debugger;
      if (onEditQuestion) {
        onEditQuestion(updatedQuestion);
      } else {
        storeHandleChangeQuestion(updatedQuestion.id, updatedQuestion);
        message.success('Câu hỏi đã được cập nhật thành công!');
      }
      setEditorVisible(false);
      setCurrentQuestion(null);
      const questionDraft =
        questionDraftAdapter.toQuestionDraftEntity(updatedQuestion);
      console.log('Saving question draft:', questionDraft);
      updateQuestions([questionDraft]);
    },
    [onEditQuestion, storeHandleChangeQuestion, updateQuestions]
  );

  // Handle delete question with confirmation
  const handleDeleteQuestion = useCallback(
    (questionIds: string[]) => {
      // Create confirmation message
      const confirmMessage =
        questionIds.length === 1
          ? 'Bạn có chắc chắn muốn xóa câu hỏi này không?'
          : `Bạn có chắc chắn muốn xóa ${questionIds.length} câu hỏi đã chọn không?`;

      // Show confirmation dialog
      Modal.confirm({
        title: 'Xác nhận xóa',
        icon: <ExclamationCircleOutlined />,
        content: confirmMessage,
        okText: 'Xóa',
        okType: 'danger',
        cancelText: 'Hủy',
        onOk: () => {
          if (onDeleteQuestions) {
            onDeleteQuestions(questionIds);
            message.success(
              questionIds.length === 1
                ? 'Xóa câu hỏi thành công'
                : `Xóa ${questionIds.length} câu hỏi thành công`
            );
          } else {
            message.info('Chức năng xóa chưa được cài đặt');
          }
        },
      });
    },
    [onDeleteQuestions]
  );

  // Handle preview question - now opens the question editor
  const handlePreviewQuestion = useCallback(
    (question: BaseQuestion) => {
      setCurrentQuestion(question);
      setEditorVisible(true);
      console.log('Preview question:', question);
      if (onPreviewQuestion) {
        onPreviewQuestion(question);
      }
    },
    [onPreviewQuestion]
  );

  const handleChangeQuestion = useCallback(
    (update: BaseQuestion) => {
      storeHandleChangeQuestion(update.id, update);
    },
    [storeHandleChangeQuestion]
  );

  // New function to handle single question deletion
  const handleSingleQuestionDelete = useCallback(
    (id: string) => {
      handleDeleteQuestion([id]);
    },
    [handleDeleteQuestion]
  );

  return (
    <StrictMode>
      <PracticeEngineContext.Provider
        value={{
          handleChangeQuestion: handleChangeQuestion,
          handleDeleteQuestion: handleSingleQuestionDelete,
          handleChangePosition: () => {},
          handleToggleFullscreen: handleToggleFullscreen,
          isFullscreen: isFullscreen,
        }}
      >
        <FullscreenContainer ref={fullscreenRef}>
          <QuestionStoreManager
            id={id}
            displayMode="table"
            questions={localQuestions}
            loading={loading}
            subjects={subjects}
            totalCount={totalCount}
            onEditQuestion={handleEditQuestion}
            onDeleteQuestions={handleDeleteQuestion}
            onPreviewQuestion={handlePreviewQuestion}
            onExportQuestions={onExportQuestions}
            onImportQuestions={onImportQuestions}
            onTableChange={fetchQuestions ? handleTableChange : undefined}
            onSearch={fetchQuestions ? handleSearch : undefined}
            onRefresh={fetchQuestions ? handleRefresh : undefined}
            onSubjectFilterChange={handleSubjectFilterChange}
          />

          {/* Question Editor Modal */}
          {currentQuestion && (
            <ErrorBoundary>
              <Suspense fallback={<div>Loading...</div>}>
                <QuestionEditor
                  visible={editorVisible}
                  question={currentQuestion}
                  onCancel={() => {
                    setEditorVisible(false);
                    setCurrentQuestion(null);
                  }}
                  onSave={handleSaveQuestion}
                  title={`Xem và chỉnh sửa: ${currentQuestion.title}`}
                  subjects={subjects ?? []}
                />
              </Suspense>
            </ErrorBoundary>
          )}
        </FullscreenContainer>
      </PracticeEngineContext.Provider>
    </StrictMode>
  );
}

const mapStateToProps = (state: {
  questionDataManager: QuestionDataManagerState;
}) => {
  return {
    questions: state.questionDataManager.questionData,
    loading: state.questionDataManager.loading,
    totalCount: state.questionDataManager.totalCount,
    error: state.questionDataManager.error,
    subjects: state.questionDataManager.subjects,
  };
};

const mapDispatchToProps = (dispatch: any) => {
  return {
    setQuestionData: (questionData: BaseQuestion[]) =>
      dispatch(setQuestionData(questionData)),
    handleChangeQuestion: (id: string, question: BaseQuestion) =>
      dispatch(handleChangeQuestion({ id, question })),
    fetchQuestions: (params: any) => dispatch(fetchQuestions(params)),
    getPaginationInfo: () => dispatch(getPaginationInfo()),
    updateQuestions: (questions: QuestionDraft[]) =>
      dispatch(updateQuestions(questions)),
    onDeleteQuestions: (questionIds: string[]) =>
      dispatch(deleteQuestions(questionIds)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(QuestionRenderComponent);

import { ETypeEdTechComponent } from '../../../../enums/AppEnums';
import DemoThumbNail from '../../../../assets/images/defaultThumbnails/Demo.png';
import LayoutThumbNail from '../../../../assets/images/defaultThumbnails/Layout.png';
import GameThumbNail from '../../../../assets/images/defaultThumbnails/Game.jpg';
import SimulatorThumbNail from '../../../../assets/images/defaultThumbnails/Simulator.jpg';
import QuizThumbNail from '../../../../assets/images/defaultThumbnails/Quiz.jpg';
// Reusing existing thumbnails for new types
import CommonThumbNail from '../../../../assets/images/defaultThumbnails/Demo.png';
import StructureThumbNail from '../../../../assets/images/defaultThumbnails/Layout.png';

export const getDefaultThumbNail = (value: string): string => {
  const displayNames: Record<string, string> = {
    [ETypeEdTechComponent.MINI_GAMES]: GameThumbNail,
    [ETypeEdTechComponent.SIMULATORS]: SimulatorThumbNail,
    [ETypeEdTechComponent.LAYOUT]: LayoutThumbNail,
    [ETypeEdTechComponent.DEMO]: DemoThumbNail,
    [ETypeEdTechComponent.QUIZ]: QuizThumbNail,
    [ETypeEdTechComponent.STRUCTURE]: StructureThumbNail,
    [ETypeEdTechComponent.COMMON]: CommonThumbNail,
    [ETypeEdTechComponent.CONTENT]: LayoutThumbNail,
  };
  return displayNames[value] || LayoutThumbNail; // Default to Layout thumbnail
};

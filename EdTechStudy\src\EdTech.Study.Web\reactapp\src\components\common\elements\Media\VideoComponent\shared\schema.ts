import { z } from 'zod';
import { createComponentSchema } from '../../../../../../utils/schema/createComponentSchema';

/**
 * Zod schema for a single video item
 */
export const videoItemSchema = z.object({
  meidaUrl: z.string(),
  mediaType: z.enum(['upload', 'embed']).optional(),
  blobKey: z.string().optional(),
  name: z.string().optional(),
});

/**
 * Zod schema for title properties
 */
const titlePropsSchema = z.object({
  text: z.string().optional(),
  fontSize: z.number().optional(),
  color: z.string().optional(),
  align: z.enum(['left', 'center', 'right']).optional(),
  bold: z.boolean().optional(),
  italic: z.boolean().optional(),
  underline: z.boolean().optional(),
  style: z.any().optional(),
}).optional();


export const videoComponentSchema = createComponentSchema({
  paramsSchema: {
    // Title properties
    titleProps: titlePropsSchema,

    // New multi-video support
    medias: z.array(videoItemSchema).optional(),

    // Legacy single video support
    mediaUrl: z.string().optional(),
    mediaType: z.enum(['upload', 'embed']).optional(),

    // Common properties
    autoPlay: z.boolean().optional(),
    controls: z.boolean().optional(),
    loop: z.boolean().optional(),
    muted: z.boolean().optional(),
    startTime: z.number().optional(),
    endTime: z.number().optional(),
    width: z.union([z.number(), z.string()]).optional(),
    height: z.union([z.number(), z.string()]).optional(),
    aspectRatio: z.string().optional(),
    borderRadius: z.number().optional(),
    shadow: z.boolean().optional(),
    displayMode: z.enum(['horizontal', 'vertical']).optional(),
  },
});

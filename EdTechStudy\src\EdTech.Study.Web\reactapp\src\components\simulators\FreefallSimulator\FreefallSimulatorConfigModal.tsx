import React, { useState } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Tabs,
  Button,
  message,
  Select,
  Divider,
  Row,
  Col,
  Tooltip,
  ColorPicker,
} from 'antd';
import {
  QuestionCircleOutlined,
  PlusOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  defaultFreefallSimulatorConfig,
  FreefallSimulatorConfig,
  FreefallEnvironment,
} from './FreefallSimulatorConfig';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { DeleteIcon } from '../../icons/IconIndex';

interface FreefallSimulatorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: FreefallSimulatorConfig) => void;
  defaultConfig?: FreefallSimulatorConfig;
}

const FreefallSimulatorConfigModal: React.FC<FreefallSimulatorModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig = defaultFreefallSimulatorConfig,
}) => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<FreefallSimulatorConfig>(defaultConfig);

  React.useEffect(() => {
    if (isOpen) {
      form.setFieldsValue(defaultConfig);
    }
  }, [isOpen, defaultConfig, form]);

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const processedValues = { ...values };
        // Process the color value from the ColorPicker if it exists
        if (processedValues.object && processedValues.object.color) {
          if (
            typeof processedValues.object.color === 'object' &&
            processedValues.object.color.toHexString
          ) {
            processedValues.object.color =
              processedValues.object.color.toHexString();
          } else if (
            typeof processedValues.object.color === 'object' &&
            processedValues.object.color.toHex
          ) {
            processedValues.object.color = processedValues.object.color.toHex();
          } else if (typeof processedValues.object.color === 'object') {
            console.warn(
              'Unrecognized color format:',
              processedValues.object.color
            );
            processedValues.object.color = '#FF5733';
          }
        }

        const updatedConfig = deepMerge(config, processedValues);
        onSubmit(updatedConfig);
        message.success('Cấu hình mô phỏng đã được lưu');
        onClose();
      })
      .catch(() => {
        message.error('Vui lòng kiểm tra lại thông tin cấu hình');
      });
  };

  const deepMerge = (prevObj: any, newValues: any) => {
    const result = { ...prevObj };
    Object.keys(newValues).forEach((key) => {
      if (Array.isArray(newValues[key])) {
        if (Array.isArray(result[key])) {
          newValues[key].forEach((item: any, index: number) => {
            if (item !== undefined && index < result[key].length) {
              result[key][index] = { ...result[key][index], ...item };
            }
          });
        }
      } else if (
        typeof newValues[key] === 'object' &&
        newValues[key] !== null &&
        typeof result[key] === 'object' &&
        result[key] !== null
      ) {
        result[key] = deepMerge(result[key], newValues[key]);
      } else {
        result[key] = newValues[key];
      }
    });
    return result;
  };

  const handleAddEnvironment = () => {
    const newEnvironment: FreefallEnvironment = {
      id: `env-${Date.now()}`,
      name: `Môi trường mới ${config.environments.length + 1}`,
      gravity: 9.8,
      airResistance: false,
      airDensity: 1.225,
      dragCoefficient: 0.5,
    };
    const updatedConfig = {
      ...config,
      environments: [...config.environments, newEnvironment],
    };
    setConfig(updatedConfig);
    form.setFieldsValue({ environments: updatedConfig.environments });
  };

  const handleRemoveEnvironment = (index: number) => {
    if (config.environments.length <= 1) {
      message.warning('Phải có ít nhất một môi trường');
      return;
    }
    const updatedEnvironments = [...config.environments];
    updatedEnvironments.splice(index, 1);
    let activeEnvironmentId = config.activeEnvironmentId;
    if (config.environments[index].id === activeEnvironmentId) {
      activeEnvironmentId = updatedEnvironments[0].id;
    }
    const updatedConfig = {
      ...config,
      environments: updatedEnvironments,
      activeEnvironmentId,
    };
    setConfig(updatedConfig);
    form.setFieldsValue({
      environments: updatedEnvironments,
      activeEnvironmentId,
    });
  };

  const tabItems = [
    {
      key: '1',
      label: 'Thông tin cơ bản',
      children: (
        <>
          {/* Title configuration removed as per requirements */}
          <Form.Item name="description" label="Mô tả">
            <Input.TextArea rows={3} placeholder="Mô tả chi tiết về mô phỏng" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="initialHeight"
                label="Chiều cao ban đầu (m)"
                rules={[{ required: true, message: 'Vui lòng nhập chiều cao' }]}
              >
                <InputNumber min={1} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="initialVelocity"
                label={
                  <span>
                    Vận tốc ban đầu (m/s)
                    <Tooltip title="Giá trị dương hướng lên trên, âm hướng xuống dưới">
                      <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </span>
                }
                rules={[{ required: true, message: 'Vui lòng nhập vận tốc' }]}
              >
                <InputNumber min={-50} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="simulationDuration"
                label="Thời gian mô phỏng tối đa (s)"
                rules={[{ required: true, message: 'Vui lòng nhập thời gian' }]}
              >
                <InputNumber min={1} max={60} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timeStep"
                label={
                  <span>
                    Bước thời gian (s)
                    <Tooltip title="Giá trị càng nhỏ càng chính xác nhưng tốn tài nguyên hơn">
                      <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </span>
                }
              >
                <InputNumber
                  min={0.001}
                  max={0.1}
                  step={0.001}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="activeEnvironmentId"
            label="Môi trường mặc định"
            rules={[{ required: true, message: 'Vui lòng chọn môi trường' }]}
          >
            <Select>
              {config.environments.map((env) => (
                <Select.Option key={env.id} value={env.id}>
                  {env.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </>
      ),
    },
    {
      key: '2',
      label: 'Môi trường',
      children: (
        <>
          <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-4">
            <h3 className="tailwind-text-base tailwind-font-medium">
              Cài đặt môi trường
            </h3>
            <Button icon={<PlusOutlined />} onClick={handleAddEnvironment}>
              Thêm môi trường
            </Button>
          </div>
          {config.environments.map((env, index) => (
            <div
              key={env.id}
              className="tailwind-bg-gray-50 tailwind-p-3 tailwind-rounded-lg tailwind-mb-4 tailwind-border"
            >
              <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
                <h4 className="tailwind-text-base tailwind-font-medium">
                  Môi trường {index + 1}
                </h4>
                <Button
                  danger
                  icon={<DeleteIcon />}
                  onClick={() => handleRemoveEnvironment(index)}
                  size="small"
                >
                  Xóa
                </Button>
              </div>
              <Form.Item
                name={['environments', index, 'name']}
                label="Tên môi trường"
                rules={[{ required: true, message: 'Vui lòng nhập tên' }]}
              >
                <Input placeholder="Tên môi trường" />
              </Form.Item>
              <Form.Item
                name={['environments', index, 'gravity']}
                label={
                  <span>
                    Gia tốc trọng trường (m/s²)
                    <Tooltip title="Trái đất: 9.8, Mặt trăng: 1.625, Sao Hỏa: 3.72">
                      <InfoCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </span>
                }
                rules={[{ required: true, message: 'Vui lòng nhập gia tốc' }]}
              >
                <InputNumber
                  min={0.1}
                  max={20}
                  step={0.1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item
                name={['environments', index, 'airResistance']}
                label="Kích hoạt lực cản không khí"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              {form.getFieldValue(['environments', index, 'airResistance']) && (
                <>
                  <Form.Item
                    name={['environments', index, 'airDensity']}
                    label={
                      <span>
                        Mật độ không khí (kg/m³)
                        <Tooltip title="Trái đất: ~1.225, Sao Hỏa: ~0.020">
                          <InfoCircleOutlined style={{ marginLeft: 4 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <InputNumber
                      min={0}
                      max={5}
                      step={0.001}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item
                    name={['environments', index, 'dragCoefficient']}
                    label="Hệ số cản"
                  >
                    <InputNumber
                      min={0}
                      max={2}
                      step={0.01}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </>
              )}
            </div>
          ))}
        </>
      ),
    },
    {
      key: '3',
      label: 'Vật thể',
      children: (
        <>
          <div className="tailwind-bg-gray-50 tailwind-p-3 tailwind-rounded-lg tailwind-mb-4 tailwind-border">
            <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
              <h4 className="tailwind-text-base tailwind-font-medium">
                Thông số vật thể
              </h4>
            </div>
            <Form.Item
              name={['object', 'name']}
              label="Tên vật thể"
              rules={[{ required: true, message: 'Vui lòng nhập tên' }]}
            >
              <Input placeholder="Tên vật thể" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['object', 'mass']}
                  label="Khối lượng (kg)"
                  rules={[
                    { required: true, message: 'Vui lòng nhập khối lượng' },
                  ]}
                >
                  <InputNumber
                    min={0.001}
                    max={1000}
                    step={0.001}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['object', 'radius']}
                  label="Bán kính (m)"
                  rules={[
                    { required: true, message: 'Vui lòng nhập bán kính' },
                  ]}
                >
                  <InputNumber
                    min={0.01}
                    max={1}
                    step={0.01}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item name={['object', 'color']} label="Màu sắc">
              <ColorPicker showText />
            </Form.Item>
          </div>
        </>
      ),
    },
    {
      key: '4',
      label: 'Tùy chọn hiển thị',
      children: (
        <>
          <Divider orientation="left">Tùy chọn đồ họa</Divider>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['displayOptions', 'showGrid']}
                label="Hiển thị lưới"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['displayOptions', 'showRuler']}
                label="Hiển thị thước đo"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['displayOptions', 'showVelocityVector']}
                label="Hiển thị vector vận tốc"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['displayOptions', 'showAccelerationVector']}
                label="Hiển thị vector gia tốc"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['displayOptions', 'showForceVectors']}
                label="Hiển thị vector lực"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['displayOptions', 'showTimer']}
                label="Hiển thị thời gian"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Divider orientation="left">Tùy chọn dữ liệu</Divider>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['displayOptions', 'showDataTable']}
                label="Hiển thị bảng dữ liệu"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['displayOptions', 'realTimeGraph']}
                label="Hiển thị đồ thị theo thời gian"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Divider orientation="left">Tính năng đặc biệt</Divider>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['specialFeatures', 'showFormulas']}
                label="Hiển thị công thức"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['specialFeatures', 'enableExport']}
                label="Cho phép xuất dữ liệu"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <ModalAntdCustom
      title="Cấu hình mô phỏng rơi tự do"
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Hủy
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          Áp dụng
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={(changedValues) => {
          setConfig((prevConfig) => deepMerge(prevConfig, changedValues));
        }}
      >
        <Tabs defaultActiveKey="1" items={tabItems} />
      </Form>
    </ModalAntdCustom>
  );
};

export default FreefallSimulatorConfigModal;

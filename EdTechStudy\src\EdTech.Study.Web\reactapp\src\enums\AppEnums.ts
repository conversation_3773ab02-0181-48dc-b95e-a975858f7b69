export enum ETypeEdTechComponent {
  DEMO = 'DEMO',
  COMMON = 'COMMON',
  LAYOUT = 'LAYOUT',
  SIMULATORS = 'SIMULATORS',
  MINI_GAMES = 'MINI_GAMES',
  QUIZ = 'QUIZ',
  STRUCTURE = 'STRUCTURE',
  CONTENT = 'CONTENT',
  PRESENTATION = 'PRESENTATION',
}

export enum ETypeMode {
  DEFAULT = 'DEFAULT',
  CONFIGURATION = 'CONFIGURATION',
  PRESENT = 'PRESENT',
}

/**
 * Enum định nghĩa các chế độ tương tác với engine
 */
export enum EEngineInteractionMode {
  DEFAULT = 'DEFAULT', // Chế độ mặc định (chỉ xem)
  CONFIGURATION = 'CONFIGURATION', // Chế độ cấu hình (cho phép click, hover)
}

export enum EThemeType {
  DEFAULT = 'default',
  DARK = 'dark',
  LEARNING = 'learning',
}

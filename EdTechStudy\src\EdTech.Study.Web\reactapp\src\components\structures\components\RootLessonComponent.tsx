import React from 'react';
import { Alert } from 'antd';
import { IEdTechRenderTreeData } from '../../../interfaces/AppComponents';
import LessonRenderComponent from '../../common/LessonRenderComponent/LessonRenderComponent';
import LessonSelectorComponent from '../../common/LessonSelectorComponent/LessonSelectorComponent';
import { z } from 'zod';
import { createComponentSchema } from '../../../utils/schema/createComponentSchema';

export const rootLessonSchema = createComponentSchema({
  paramsSchema: {
    title: z.string().optional(),
    description: z.string().optional(),
    subItems: z.array(z.any()).optional(),
    showNavigation: z.boolean().optional(),
    showProgress: z.boolean().optional(),
  },
});

export const RootLessonComponent: React.FC<IEdTechRenderTreeData> = (props) => {
  return (
    <>
      {/* Main Content */}
      <div className="tailwind-flex tailwind-flex-col tailwind-gap-6 tailwind-rounded-lg tailwind-shadow-sm tailwind-p-6">
        <h2 className="tailwind-text-2xl tailwind-font-semibold tailwind-text-gray-800 tailwind-mb-4">
          {props.title || 'Lesson Content'}
        </h2>

        {props.subItems && props.subItems.length > 0 ? (
          <div className="tailwind-space-y-6">
            {props.subItems.map((item) => (
              <LessonRenderComponent key={item.id} data={item} />
            ))}
          </div>
        ) : (
          <Alert
            message="Không có nội dung"
            description="Không có nội dung bài học, vui lòng thêm nội dung bài học hoặc chọn cấu trúc mặc định"
            type="warning"
            showIcon
            className="tailwind-mb-4"
          />
        )}

        <LessonSelectorComponent parentNode={props} />
      </div>
    </>
  );
};

export default RootLessonComponent;

import BoxSimulator, {
  boxSimulatorSchema,
} from '../../components/simulators/BoxSimulator/BoxSimulator';
import FreefallSimulator, {
  freefallSimulatorSchema,
} from '../../components/simulators/FreefallSimulator/FreefallSimulator';
import MapSimulator, {
  mapSimulatorSchema,
} from '../../components/simulators/MapSimulator/MapSimulator';
import SetTheorySimulator, {
  setTheorySimulatorSchema,
} from '../../components/simulators/SetTheorySimulator/SetTheorySimulator';
import TrigonometricVisualizerSimulator, {
  trigonometricVisualizerSchema,
} from '../../components/simulators/TrigonometricValueSimulator/TrigonometricVisualizerSimulator';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const SIMULATOR_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'MapSimulator',
    title: '<PERSON><PERSON> Phỏng Bản <PERSON>',
    components: [
      {
        version: '1.0.0',
        component: MapSimulator,
        schema: undefined,
      },
    ],
    tags: [
      'Địa lý',
      'Lớp 10',
      'Lớp 11',
      'Trực quan hóa',
      'Trung cấp',
      'Mô phỏng',
      'Trực quan',
      'Thực tế',
      'Học khái niệm',
      'Phân tích',
    ],
    type: ETypeEdTechComponent.SIMULATORS,
    schema: mapSimulatorSchema,
  },
  {
    name: 'FreefallSimulator',
    title: 'Mô Phỏng Rơi Tự Do',
    components: [
      {
        version: '1.0.0',
        component: FreefallSimulator,
        schema: undefined,
      },
    ],
    tags: [
      'Vật lý',
      'Lớp 10',
      'Lớp 11',
      'Trực quan hóa',
      'Thí nghiệm',
      'Trung cấp',
      'Mô phỏng',
      'Trực quan',
      'Thực tế',
      'Học khái niệm',
      'Phân tích',
      'Thực hành',
    ],
    type: ETypeEdTechComponent.SIMULATORS,
    schema: freefallSimulatorSchema,
  },
  {
    name: 'BoxSimulator',
    title: 'Mô Phỏng Hộp',
    components: [
      {
        version: '1.0.0',
        component: BoxSimulator,
        schema: undefined,
      },
    ],
    tags: [
      'Toán học',
      'Lớp 6',
      'Lớp 7',
      'Trực quan hóa',
      'Sơ cấp',
      'Mô phỏng',
      'Trực quan',
      'Trừu tượng',
      'Học khái niệm',
      'Tư duy không gian',
    ],
    type: ETypeEdTechComponent.SIMULATORS,
    schema: boxSimulatorSchema,
  },
  {
    name: 'TrigonometricVisualizer',
    title: 'TrigonometricVisualizer',
    components: [
      {
        version: '1.0.0',
        component: TrigonometricVisualizerSimulator,
        schema: undefined,
      },
    ],
    tags: [],
    type: ETypeEdTechComponent.SIMULATORS,
    schema: trigonometricVisualizerSchema,
  },
  {
    name: 'SetTheorySimulator',
    title: 'Mô Phỏng Lý Thuyết Tập Hợp',
    components: [
      {
        version: '1.0.0',
        component: SetTheorySimulator,
        schema: undefined,
      },
    ],
    tags: [
      'Toán học',
      'Lớp 10',
      'Lớp 11',
      'Trực quan hóa',
      'Lý thuyết',
      'Nâng cao',
      'Mô phỏng',
      'Trực quan',
      'Trừu tượng',
      'Học khái niệm',
      'Phân tích',
      'Tổng hợp',
    ],
    type: ETypeEdTechComponent.SIMULATORS,
    schema: setTheorySimulatorSchema,
  },
];

import { useState, useCallback } from 'react';
import { Question } from './QuestionBankConfig';

interface UseQuestionProps {
  question: Question;
  onAnswer?: (answer: any) => void;
}

interface UseQuestionReturn {
  answer: any;
  isAnswered: boolean;
  isCorrect: boolean;
  handleAnswer: (answer: any) => void;
  reset: () => void;
}

export const useQuestion = ({
  question,
  onAnswer,
}: UseQuestionProps): UseQuestionReturn => {
  const [answer, setAnswer] = useState<any>(null);
  const [isAnswered, setIsAnswered] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);

  const handleAnswer = useCallback(
    (newAnswer: any) => {
      setAnswer(newAnswer);
      setIsAnswered(true);

      let correct = false;
      switch (question.type) {
        case 'truefalse':
          correct = newAnswer === question.isCorrect;
          break;
        case 'multiplechoice':
          correct = newAnswer === question.correctOptionIndex;
          break;
        case 'fillin':
          if (question.caseSensitive) {
            correct = newAnswer === question.correctAnswer;
          } else {
            correct =
              newAnswer.toLowerCase() === question.correctAnswer.toLowerCase();
          }
          break;
        case 'ordering':
          correct =
            JSON.stringify(newAnswer) ===
            JSON.stringify(question.correctOrder);
          break;
        case 'matching':
          correct =
            JSON.stringify(newAnswer) ===
            JSON.stringify(question.correctLeftToRight);
          break;
      }

      setIsCorrect(correct);
      onAnswer?.(newAnswer);
    },
    [question, onAnswer]
  );

  const reset = useCallback(() => {
    setAnswer(null);
    setIsAnswered(false);
    setIsCorrect(false);
  }, []);

  return {
    answer,
    isAnswered,
    isCorrect,
    handleAnswer,
    reset,
  };
}; 
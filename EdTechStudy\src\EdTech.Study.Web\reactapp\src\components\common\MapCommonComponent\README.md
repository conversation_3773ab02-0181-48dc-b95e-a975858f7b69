# Map Common Component

Thư viện bản đồ dựa trên React-Leaflet với các control trực quan và dễ dàng tùy chỉnh.

## Sử dụng cơ bản

```jsx
import MapComponent from './components/common/MapCommonComponent/components/MapComponent';

const MyMap = () => {
  return (
    <MapComponent
      initialCenter={{ lat: 21.028511, lng: 105.804817 }} // Hà Nội
      initialZoom={13}
    />
  );
};
```

## Tùy chỉnh Control

Bạn có thể tùy chỉnh các control trên bản đồ thông qua thuộc tính `controls`:

```jsx
<MapComponent
  initialCenter={{ lat: 21.028511, lng: 105.804817 }}
  initialZoom={13}
  controls={{
    zoom: { enabled: true, position: 'bottomright' },
    scale: { enabled: true, position: 'bottomright', maxWidth: 150 },
    compass: { enabled: true, position: 'topright', size: 50 },
    coordinates: { enabled: true, position: 'bottomleft', decimals: 4 },
    search: { 
      enabled: true, 
      position: 'topleft',
      placeholder: 'Tìm địa điểm...'
    }
  }}
/>
```

## Control có sẵn

### ZoomControl

Control điều khiển mức zoom của bản đồ.

```jsx
zoom: { 
  enabled: true, // Bật/tắt control
  position: 'bottomright' // Vị trí: 'topleft', 'topright', 'bottomleft', 'bottomright'
}
```

### ScaleControl

Control hiển thị tỷ lệ của bản đồ.

```jsx
scale: { 
  enabled: true, // Bật/tắt control
  position: 'bottomright', // Vị trí
  maxWidth: 120 // Chiều rộng tối đa (pixels)
}
```

### CompassControl

Control hiển thị la bàn và cho phép định hướng bản đồ về hướng Bắc.

```jsx
compass: { 
  enabled: true, // Bật/tắt control
  position: 'topright', // Vị trí
  size: 48 // Kích thước (pixels)
}
```

### CoordinatesControl

Control hiển thị tọa độ con trỏ chuột.

```jsx
coordinates: { 
  enabled: true, // Bật/tắt control
  position: 'bottomleft', // Vị trí
  decimals: 6 // Số chữ số thập phân hiển thị
}
```

### SearchControl

Control cho phép tìm kiếm địa điểm.

```jsx
search: { 
  enabled: true, // Bật/tắt control
  position: 'topleft', // Vị trí
  placeholder: 'Tìm kiếm địa điểm...' // Placeholder cho ô tìm kiếm
}
```

## Markers, Polylines, và các đối tượng khác

Bạn có thể dễ dàng thêm các đối tượng khác vào bản đồ:

```jsx
<MapComponent
  initialCenter={{ lat: 21.028511, lng: 105.804817 }}
  initialZoom={13}
  // Thêm markers
  markers={[
    {
      position: { lat: 21.028511, lng: 105.804817 },
      title: 'Hà Nội',
      description: 'Thủ đô của Việt Nam'
    }
  ]}
  // Thêm đường polyline
  polylines={[
    {
      positions: [
        { lat: 21.028511, lng: 105.804817 },
        { lat: 21.038511, lng: 105.814817 }
      ],
      color: 'blue',
      weight: 3
    }
  ]}
  // Thêm đa giác
  polygons={[
    {
      positions: [
        { lat: 21.028511, lng: 105.804817 },
        { lat: 21.038511, lng: 105.814817 },
        { lat: 21.048511, lng: 105.804817 }
      ],
      color: 'red',
      fillColor: 'red',
      fillOpacity: 0.2
    }
  ]}
  // Thêm hình tròn
  circles={[
    {
      center: { lat: 21.028511, lng: 105.804817 },
      radius: 500, // Bán kính (mét)
      color: 'green'
    }
  ]}
/>
```

## Bắt sự kiện

Bản đồ hỗ trợ nhiều sự kiện khác nhau:

```jsx
<MapComponent
  initialCenter={{ lat: 21.028511, lng: 105.804817 }}
  initialZoom={13}
  // Xử lý khi click vào bản đồ
  onMapClick={(position) => console.log('Clicked at:', position)}
  // Xử lý khi kéo bản đồ
  onMapDrag={(center) => console.log('Map dragged to:', center)}
  // Xử lý khi thay đổi mức zoom
  onZoomChange={(zoom) => console.log('New zoom level:', zoom)}
  // Xử lý khi click vào marker
  onMarkerClick={(marker) => console.log('Clicked marker:', marker)}
  // Xử lý khi kéo marker
  onMarkerDragEnd={(marker, newPosition) => 
    console.log('Marker dragged to:', newPosition)
  }
  // Xử lý khi tìm thấy vị trí (từ SearchControl)
  onLocationFound={(position) => console.log('Location found:', position)}
/>
```

## Tùy chỉnh thêm

Bạn có thể truyền các thuộc tính bổ sung để tùy chỉnh bản đồ:

```jsx
<MapComponent
  initialCenter={{ lat: 21.028511, lng: 105.804817 }}
  initialZoom={13}
  width="800px" // Chiều rộng
  height="600px" // Chiều cao
  scrollWheelZoom={true} // Cho phép zoom bằng cuộn chuột
  doubleClickZoom={true} // Cho phép zoom bằng double-click
  className="my-custom-map" // Class CSS tùy chỉnh
/>
```

## Mở rộng Component

Bạn có thể thêm các component con tùy chỉnh vào bản đồ:

```jsx
import { MapContainer, TileLayer, Marker, Popup } from 'react-leaflet';
import MapComponent from './components/common/MapCommonComponent/components/MapComponent';

const CustomMapComponent = () => {
  return (
    <MapComponent
      initialCenter={{ lat: 21.028511, lng: 105.804817 }}
      initialZoom={13}
    >
      {/* Thêm các component tùy chỉnh */}
      <MyCustomLayerComponent />
    </MapComponent>
  );
};
```

## Hiển thị bản đồ Hoàng Sa và Trường Sa

MapComponent bao gồm sẵn dữ liệu quần đảo Hoàng Sa và Trường Sa từ file GeoJSON như một phần không thể tách rời của bản đồ:

```jsx
import MapComponent from './components/common/MapCommonComponent/components/MapComponent';

const MyMap = () => {
  return (
    <MapComponent 
      width="100%" 
      height="600px"
      initialCenter={{ lat: 12.5, lng: 114 }}
      initialZoom={6}
    />
  );
};
```

Dữ liệu GeoJSON sẽ tự động được tải và hiển thị trên bản đồ với màu sắc phân biệt:
- Quần đảo Hoàng Sa: màu đỏ
- Quần đảo Trường Sa: màu xanh

Không cần cấu hình thêm, tất cả các đảo và đá trong hai quần đảo sẽ được hiển thị tự động như một phần cố định của bản đồ. 
import React, { useState } from 'react';
import { Button, Modal, Upload, message, Typography, Space } from 'antd';
import {
  ImportOutlined,
  ExportOutlined,
  UploadOutlined,
  SaveOutlined,
  CloudDownloadOutlined,
} from '@ant-design/icons';
import ToggleDefaultButton from './components/ToggleDefaultButton';
import { useDispatch, useSelector } from 'react-redux';
import { EdTechRootState } from '../../../../../../store/store';
import { AppFunctions } from '../../../../../../utils/AppFunctions';
import { IErrorValidateComponent } from '../../../../../../utils/validation/validateSchema';
import ValidationErrorTable from './ValidationErrorTable';
import DefaultLessonConfigApi from '../../../../../../api/defaultLessonConfigApi';
import OfflineExportApi from '../../../../../../api/offlineExportApi';
import { ConfigManager } from '../../../../../../utils/ConfigManager';
import ThemeSelector from '../../../../../themes/ThemeSelector';

const { Title, Text } = Typography;

interface LessonImportExportProps {
  className?: string;
}

const LessonImportExport: React.FC<LessonImportExportProps> = ({
  className,
}) => {
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    IErrorValidateComponent[]
  >([]);
  const dispatch = useDispatch();

  // Get data from Redux store
  const renderTreeData = useSelector(
    (state: EdTechRootState) => state.edTechRenderTreeData.data
  );
  const paramData = useSelector(
    (state: EdTechRootState) => state.edComponentParams.data
  );

  // Xử lý sự kiện Import
  const handleImport = () => {
    setIsImportModalVisible(true);
    setValidationErrors([]); // Reset lỗi khi mở modal
  };

  // Xử lý khi file được tải lên thông qua UI
  const handleFileUpload = async (file: File) => {
    const result = await AppFunctions.importFromEdTechRenderFile(
      file,
      dispatch
    );

    if (!result.isValid) {
      setValidationErrors(result.errors);
      return;
    }

    setIsImportModalVisible(false);
  };

  // Xử lý sự kiện Export
  const handleExport = () => {
    AppFunctions.exportToFileEdTechRenderData();
  };

  // Cấu hình upload cho chức năng Import
  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.json',
    beforeUpload: (file: File) => {
      if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
        message.error(`${file.name} không phải là file JSON`);
        return Upload.LIST_IGNORE;
      }
      handleFileUpload(file);
      return false; // Ngăn upload tự động
    },
    showUploadList: false,
  };

  // Xử lý lưu cấu hình mặc định
  const handleSaveDefault = async () => {
    // Hiển thị modal xác nhận trước khi lưu
    Modal.confirm({
      title: 'Xác nhận lưu mặc định',
      content:
        'Bạn có chắc chắn muốn lưu cấu hình hiện tại làm mặc định không?',
      onOk: async () => {
        try {
          const result = await DefaultLessonConfigApi.saveDefaultConfig({
            Key: window.jsonKey,
            Data: {
              edComponentParams: paramData,
              edTechRenderTreeData: renderTreeData,
            },
          });
          if (!result) {
            message.error('Lưu cấu hình mặc định thất bại');
          }
          message.success('Đã lưu cấu hình mặc định thành công');
          const idConfig = ConfigManager.getIdConfigFromParams();
          window.LessonConfigDB.deleteConfig(idConfig);
        } catch (error) {
          console.error('Error saving default config:', error);
          message.error(
            `Lưu cấu hình mặc định thất bại: ${(error as Error).message}`
          );
        }
      },
    });
  };

  // Đặt lại mặc định đã được chuyển sang component ToggleDefaultButton

  // Xử lý xuất bài giảng cho sử dụng offline
  const handleOfflineExport = async () => {
    try {
      // Lấy jsonKey từ window object
      const jsonKey = window.jsonKey;
      if (!jsonKey) {
        message.error('Không thể xác định khóa bài giảng');
        return;
      }

      message.loading({
        content: 'Đang xuất bài giảng cho sử dụng offline...',
        key: 'offlineExport',
      });
      // Kết hợp dữ liệu từ hai slice
      const exportData = await AppFunctions.getCurrentJsonData();

      // Chuyển đổi dữ liệu thành chuỗi JSON
      const jsonString = JSON.stringify(exportData, null, 2);

      const result = await OfflineExportApi.exportLessonForOfflineUse(
        jsonKey,
        jsonString
      );

      if (result.success) {
        message.success({
          content: 'Xuất bài giảng thành công!',
          key: 'offlineExport',
        });
      } else {
        message.error({
          content: 'Xuất bài giảng thất bại!',
          key: 'offlineExport',
        });
      }
    } catch (error) {
      console.error('Error exporting lesson for offline use:', error);
      message.error({
        content: 'Xuất bài giảng thất bại!',
        key: 'offlineExport',
      });
    }
  };

  return (
    <Space className={`lesson-import-export ${className || ''}`}>
      <ThemeSelector showCustomizer={true} />
      {/* Lưu cấu hình mặc định button - Pink */}
      <Button
        type="primary"
        icon={<SaveOutlined />}
        onClick={handleSaveDefault}
        className="save-default-button"
      >
        Lưu cấu hình mặc định
      </Button>

      {/* Nhập cấu hình button - Teal */}
      <Button
        icon={<ImportOutlined />}
        onClick={handleImport}
        className="import-button"
      >
        Nhập cấu hình
      </Button>

      {/* Xuất cấu hình button - Green */}
      <Button
        icon={<ExportOutlined />}
        onClick={handleExport}
        className="export-button"
      >
        Xuất cấu hình
      </Button>

      {/* Xuất bài giảng cho sử dụng offline - Blue */}
      <Button
        icon={<CloudDownloadOutlined />}
        onClick={handleOfflineExport}
        className="offline-export-button"
      >
        Xuất bài giảng offline
      </Button>

      {/* Sử dụng component ToggleDefaultButton thay thế */}
      <ToggleDefaultButton className="toggle-button" />

      {/* Import Modal */}
      <Modal
        title={
          <Space>
            <UploadOutlined className="tailwind-text-blue-500" />
            <Title level={4} className="tailwind-mb-0">
              Import Dữ Liệu
            </Title>
          </Space>
        }
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
        width={900}
      >
        <Upload.Dragger {...uploadProps}>
          <Space direction="vertical" size="middle" className="tailwind-py-8">
            <UploadOutlined className="tailwind-text-4xl tailwind-text-blue-500" />
            <Text className="tailwind-text-lg tailwind-font-medium">
              Nhấp hoặc kéo file vào khu vực này để tải lên
            </Text>
            <Text type="secondary">
              Hỗ trợ tải lên một file JSON duy nhất. Hãy đảm bảo file của bạn
              đúng định dạng.
            </Text>
          </Space>
        </Upload.Dragger>

        <ValidationErrorTable errors={validationErrors} />
      </Modal>
    </Space>
  );
};

export default LessonImportExport;

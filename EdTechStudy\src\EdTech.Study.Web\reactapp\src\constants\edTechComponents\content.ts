import LessonCardComponent, {
  lessonCardSchema,
} from '../../components/contents/LessonCardComponent';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const CONTENT_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'LessonCardComponent',
    title: 'Thẻ Bài Học',
    components: [
      {
        version: '1.0.0',
        component: LessonCardComponent,
        schema: lessonCardSchema,
      },
    ],
    type: ETypeEdTechComponent.CONTENT,
    tags: undefined,
    description:
      'Component hiển thị nội dung bài học trong một card có tiêu đề, giúp tổ chức và trình bày nội dung một cách rõ ràng và có cấu trúc.',
    schema: lessonCardSchema,
  },
];

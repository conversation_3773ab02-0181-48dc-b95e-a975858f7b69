import { z } from 'zod';
import { createComponentSchema } from '../../../../../../utils/schema/createComponentSchema';

/**
 * Zod schema for a single image item
 */
const imageItemSchema = z.object({
  imageUrl: z.string(),
  alt: z.string().optional(),
  type: z.enum(['upload', 'embed']).optional(),
});

/**
 * Zod schema for title properties
 */
const titlePropsSchema = z.object({
  text: z.string().optional(),
  fontSize: z.number().optional(),
  color: z.string().optional(),
  align: z.enum(['left', 'center', 'right']).optional(),
  bold: z.boolean().optional(),
  italic: z.boolean().optional(),
  underline: z.boolean().optional(),
  style: z.any().optional(),
}).optional();

/**
 * Zod schema for ImageComponent validation
 * Shared between all versions of the ImageComponent
 */
export const imageComponentSchema = createComponentSchema({
  paramsSchema: {
    // Title properties
    titleProps: titlePropsSchema,

    // New multi-image support
    medias: z.array(imageItemSchema).optional(),

    // Legacy single image support
    mediaUrl: z.string().optional(),
    mediaType: z.enum(['upload', 'embed']).optional(),
    alt: z.string().optional(),

    // Common properties
    width: z.union([z.number(), z.string()]).optional(),
    height: z.union([z.number(), z.string()]).optional(),
    aspectRatio: z.string().optional(),
    objectFit: z.enum(['contain', 'cover', 'fill']).optional(),
    borderRadius: z.number().optional(),
    shadow: z.boolean().optional(),
    displayMode: z.enum(['horizontal', 'vertical']).optional(),
  },
});

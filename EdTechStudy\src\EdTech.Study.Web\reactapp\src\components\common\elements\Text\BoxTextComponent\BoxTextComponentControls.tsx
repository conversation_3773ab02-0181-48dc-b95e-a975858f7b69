import React, { useCallback, useMemo, memo } from 'react';
import { Button, ColorPicker, Input } from 'antd';
import {
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  BoldOutlined,
  ItalicOutlined,
} from '@ant-design/icons';
import { BoxTextComponentProps } from './BoxTextComponentConfig';
import { debounce } from 'lodash';

interface BoxTextComponentControlsProps {
  config: BoxTextComponentProps;
  updateConfig: (updates: Partial<BoxTextComponentProps>) => void;
}

// Tách các control thành các component riêng biệt để tránh render lại không cần thiết
const AlignmentControl = memo(
  ({
    value,
    onChange,
    label,
    options = ['left', 'center', 'right'],
  }: {
    value: string;
    onChange: (value: string) => void;
    label: string;
    options?: string[];
  }) => {
    const handleClick = useCallback(
      (newValue: string) => {
        onChange(newValue);
      },
      [onChange]
    );

    return (
      <div className="control-group">
        <label className="control-label">{label}</label>
        <div className="tailwind-flex tailwind-gap-1">
          {options.includes('left') && (
            <Button
              type={value === 'left' ? 'primary' : 'default'}
              icon={<AlignLeftOutlined />}
              size="small"
              onClick={() => handleClick('left')}
            />
          )}
          {options.includes('center') && (
            <Button
              type={value === 'center' ? 'primary' : 'default'}
              icon={<AlignCenterOutlined />}
              size="small"
              onClick={() => handleClick('center')}
            />
          )}
          {options.includes('right') && (
            <Button
              type={value === 'right' ? 'primary' : 'default'}
              icon={<AlignRightOutlined />}
              size="small"
              onClick={() => handleClick('right')}
            />
          )}
        </div>
      </div>
    );
  }
);

const TextStyleControl = memo(
  ({
    fontWeight,
    fontStyle,
    onUpdate,
  }: {
    fontWeight: string | number;
    fontStyle: string;
    onUpdate: (updates: Partial<BoxTextComponentProps>) => void;
  }) => {
    const toggleBold = useCallback(() => {
      onUpdate({
        fontWeight: fontWeight === 'bold' ? 'normal' : 'bold',
      });
    }, [fontWeight, onUpdate]);

    const toggleItalic = useCallback(() => {
      onUpdate({
        fontStyle: fontStyle === 'italic' ? 'normal' : 'italic',
      });
    }, [fontStyle, onUpdate]);

    return (
      <div className="control-group">
        <label className="control-label">Kiểu chữ:</label>
        <div className="tailwind-flex tailwind-gap-1">
          <Button
            type={fontWeight === 'bold' ? 'primary' : 'default'}
            icon={<BoldOutlined />}
            size="small"
            onClick={toggleBold}
          />
          <Button
            type={fontStyle === 'italic' ? 'primary' : 'default'}
            icon={<ItalicOutlined />}
            size="small"
            onClick={toggleItalic}
          />
        </div>
      </div>
    );
  }
);

const ColorControl = memo(
  ({
    color,
    onChange,
    label,
  }: {
    color: string;
    onChange: (color: string) => void;
    label: string;
  }) => {
    // Sử dụng useCallback để tránh tạo lại hàm khi component render lại
    const handleColorChange = useCallback(
      (colorObj: any) => {
        onChange(colorObj.toHexString());
      },
      [onChange]
    );

    return (
      <div className="control-group">
        <label className="control-label">{label}</label>
        <div className="color-picker-container">
          <div className="color-preview" style={{ backgroundColor: color }} />
          <ColorPicker value={color} onChange={handleColorChange} />
        </div>
      </div>
    );
  }
);

const NumberInput = memo(
  ({
    value,
    onChange,
    label,
    suffix = 'px',
    className = 'tailwind-w-24',
  }: {
    value: number;
    onChange: (value: number) => void;
    label: string;
    suffix?: string;
    className?: string;
  }) => {
    // Sử dụng debounce để tránh cập nhật liên tục khi người dùng nhập
    const debouncedChange = useMemo(
      () =>
        debounce((value: number) => {
          onChange(value);
        }, 300),
      [onChange]
    );

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        if (!isNaN(value)) {
          debouncedChange(value);
        }
      },
      [debouncedChange]
    );

    return (
      <div className="control-group">
        <label className="control-label">{label}</label>
        <Input
          type="number"
          className={className}
          defaultValue={value}
          onChange={handleChange}
          addonAfter={suffix}
        />
      </div>
    );
  }
);

const BoxTextComponentControls: React.FC<BoxTextComponentControlsProps> = memo(
  ({ config, updateConfig }) => {
    // Tạo các hàm callback để xử lý cập nhật cấu hình
    const handleBackgroundColorChange = useCallback(
      (color: string) => {
        updateConfig({ backgroundColor: color });
      },
      [updateConfig]
    );

    const handleBorderColorChange = useCallback(
      (color: string) => {
        updateConfig({ borderColor: color });
      },
      [updateConfig]
    );

    const handleTextColorChange = useCallback(
      (color: string) => {
        updateConfig({ textColor: color });
      },
      [updateConfig]
    );

    const handleBoxAlignChange = useCallback(
      (align: string) => {
        updateConfig({ boxAlign: align as 'left' | 'center' | 'right' });
      },
      [updateConfig]
    );

    const handleTextAlignChange = useCallback(
      (align: string) => {
        updateConfig({ textAlign: align as 'left' | 'center' | 'right' });
      },
      [updateConfig]
    );

    const handleFontSizeChange = useCallback(
      (size: number) => {
        updateConfig({ fontSize: size });
      },
      [updateConfig]
    );

    // Sử dụng useMemo để tránh tạo lại các component con khi không cần thiết
    const controlsContent = useMemo(
      () => (
        <div className="controls-container">
          {/* Box styling */}
          <ColorControl
            color={config.backgroundColor || '#ffffff'}
            onChange={handleBackgroundColorChange}
            label="Màu nền:"
          />

          <ColorControl
            color={config.borderColor || 'var(--edtt-color-primary)'}
            onChange={handleBorderColorChange}
            label="Màu viền:"
          />

          {/* Box alignment */}
          <AlignmentControl
            value={config.boxAlign || 'left'}
            onChange={handleBoxAlignChange}
            label="Căn lề hộp:"
          />

          {/* Text styling */}
          <TextStyleControl
            fontWeight={config.fontWeight || 'normal'}
            fontStyle={config.fontStyle || 'normal'}
            onUpdate={updateConfig}
          />

          {/* Text color */}
          <ColorControl
            color={config.textColor || '#333333'}
            onChange={handleTextColorChange}
            label="Màu chữ:"
          />

          {/* Font size */}
          <NumberInput
            value={config.fontSize || 16}
            onChange={handleFontSizeChange}
            label="Cỡ chữ:"
          />

          {/* Text alignment */}
          <AlignmentControl
            value={config.textAlign || 'left'}
            onChange={handleTextAlignChange}
            label="Căn chỉnh văn bản:"
          />
        </div>
      ),
      [
        config.backgroundColor,
        config.borderColor,
        config.boxAlign,
        config.fontWeight,
        config.fontStyle,
        config.textColor,
        config.fontSize,
        config.textAlign,
        handleBackgroundColorChange,
        handleBorderColorChange,
        handleBoxAlignChange,
        handleTextColorChange,
        handleFontSizeChange,
        handleTextAlignChange,
        updateConfig,
      ]
    );

    return (
      <div className="box-controls tailwind-p-3 tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-bg-gray-50">
        <h4 className="tailwind-text-sm tailwind-font-medium tailwind-mb-2">
          Cấu hình hộp văn bản
        </h4>
        {controlsContent}
      </div>
    );
  }
);

export default memo(BoxTextComponentControls);

import {
  PreviewLessonComponent,
  previewLessonSchema,
} from '../../components/structures/components/PreviewLessonComponent/PreviewLessonComponent';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const PRESENTATION_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'PreviewLessonComponent',
    title: 'Trình chiếu bài học',
    components: [
      {
        version: '1.0.0',
        component: PreviewLessonComponent,
        schema: previewLessonSchema,
      },
    ],
    type: ETypeEdTechComponent.PRESENTATION,
    tags: undefined,
    description:
      'Component hiển thị nội dung bài học trong chế độ trình chiếu, gi<PERSON>p người dùng xem bài học trong chế độ trình chiếu.',
    schema: previewLessonSchema,
  },
];

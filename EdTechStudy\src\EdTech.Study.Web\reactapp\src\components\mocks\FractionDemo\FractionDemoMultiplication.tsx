import React from 'react';
import { Input, Typography, Space, Divider } from 'antd';
import { withEdComponentParams } from '../../../hocs/withEdComponentParams/withEdComponentParams';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';
import { EngineContainer } from '../../common/engines';

const { Text, Title } = Typography;

// Giao diện cho phép nhân phân số
interface FractionPropsMultiplication {
  numerator?: number;
  denominator?: number;
  showDecimal?: boolean;
  // Thêm operation type nhưng component này chỉ sử dụng phép nhân
  operation?: 'multiply';
}

const FractionDemoMultiplication: React.FC<
  IEdTechRenderProps<FractionPropsMultiplication>
> = (props) => {
  const { params, isEditing, addOrUpdateParamComponent } = props;

  // Gi<PERSON> trị mặc định
  const numerator = params?.numerator || 1;
  const denominator = params?.denominator || 2;
  const showDecimal =
    params?.showDecimal !== undefined ? params.showDecimal : true;

  // Cập nhật tham số
  const handleParamChange = (
    newParams: Partial<FractionPropsMultiplication>
  ) => {
    addOrUpdateParamComponent({
      ...params,
      operation: 'multiply',
      ...newParams,
    });
  };

  // Định nghĩa kiểu kết quả
  type CalculationResult = {
    numerator: number;
    denominator: number;
    error?: string;
  };

  // Tìm ước chung lớn nhất (GCD)
  const findGCD = (a: number, b: number): number => {
    return b === 0 ? a : findGCD(b, a % b);
  };

  // Rút gọn phân số
  const simplifyFraction = (
    numerator: number,
    denominator: number
  ): CalculationResult => {
    if (denominator === 0)
      return { numerator, denominator, error: 'Mẫu số không thể bằng 0' };
    if (numerator === 0) return { numerator: 0, denominator: 1 };

    const gcd = Math.abs(findGCD(numerator, denominator));
    return {
      numerator: Math.round(numerator / gcd),
      denominator: Math.round(denominator / gcd),
    };
  };

  // Thực hiện phép nhân
  const calculateMultiplication = (): CalculationResult => {
    if (denominator === 0) {
      return { numerator: 0, denominator: 0, error: 'Mẫu số không thể bằng 0' };
    }

    // 2 * (numerator/denominator) = (2 * numerator) / denominator
    return simplifyFraction(1 * numerator, denominator);
  };

  // Hiển thị phân số
  const renderFraction = (numerator: number, denominator: number) => {
    return (
      <div className="tailwind-flex tailwind-flex-col tailwind-items-center">
        <div className="tailwind-text-2xl tailwind-font-bold">{numerator}</div>
        <div className="tailwind-w-12 tailwind-border-b-2 tailwind-border-black tailwind-my-1"></div>
        <div className="tailwind-text-2xl tailwind-font-bold">
          {denominator}
        </div>
      </div>
    );
  };

  // Kết quả tính toán
  const result = calculateMultiplication();

  // Hiển thị nội dung
  const renderContent = () => {
    return (
      <div className="tailwind-p-6 tailwind-border tailwind-border-gray-200 tailwind-rounded">
        <Title level={4} className="tailwind-text-center tailwind-mb-6">
          Phép nhân phân số
        </Title>

        <div className="tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-4 tailwind-mb-8">
          <div className="tailwind-text-2xl tailwind-font-bold">1</div>
          <Text className="tailwind-text-2xl tailwind-font-bold">×</Text>
          {renderFraction(numerator, denominator)}
          <Text className="tailwind-text-2xl tailwind-font-bold">=</Text>
          {result.error ? (
            <Text className="tailwind-text-red-500 tailwind-text-lg">
              {result.error}
            </Text>
          ) : (
            renderFraction(result.numerator, result.denominator)
          )}
        </div>

        {showDecimal && !result.error && result.denominator !== 0 && (
          <div className="tailwind-text-center tailwind-mb-4">
            <Text className="tailwind-text-lg">
              Giá trị thập phân:{' '}
              <strong>
                {(result.numerator / result.denominator).toFixed(4)}
              </strong>
            </Text>
          </div>
        )}

        {isEditing && (
          <>
            <Divider />
            <div className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-4">
              <div>
                <Title level={5}>Phân số</Title>
                <div className="tailwind-flex tailwind-items-center tailwind-gap-4 tailwind-w-full">
                  <div className="tailwind-flex-1">
                    <Text strong>Tử số:</Text>
                    <Input
                      type="number"
                      value={numerator}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value)) {
                          handleParamChange({ numerator: value });
                        }
                      }}
                      className="tailwind-mt-1 tailwind-w-full"
                    />
                  </div>

                  <div className="tailwind-flex-1">
                    <Text strong>Mẫu số:</Text>
                    <Input
                      type="number"
                      value={denominator}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value)) {
                          handleParamChange({ denominator: value });
                        }
                      }}
                      className="tailwind-mt-1 tailwind-w-full"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="tailwind-mt-4">
              <Space direction="vertical" className="tailwind-w-full">
                <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                  <Text strong>Hiển thị giá trị thập phân:</Text>
                  <input
                    type="checkbox"
                    checked={showDecimal}
                    onChange={(e) =>
                      handleParamChange({ showDecimal: e.target.checked })
                    }
                    className="tailwind-ml-2"
                  />
                </div>
              </Space>
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <EngineContainer
      node={props}
      titleProps={{ text: 'Phép nhân phân số' }}
      showFullscreenButton={false}
      mainComponent={renderContent()}
    />
  );
};

export default withEdComponentParams(FractionDemoMultiplication);

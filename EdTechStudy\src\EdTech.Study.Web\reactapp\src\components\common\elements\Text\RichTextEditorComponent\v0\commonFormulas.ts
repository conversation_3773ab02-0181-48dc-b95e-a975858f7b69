// <PERSON>h sách các công thức phổ biến theo danh mục
export const commonFormulas = {
  basic: [
    { name: '<PERSON><PERSON> số', latex: '\\frac{a}{b}', description: '<PERSON><PERSON> số a/b' },
    { name: '<PERSON><PERSON><PERSON> bậc 2', latex: '\\sqrt{x}', description: '<PERSON><PERSON><PERSON> bậc 2 của x' },
    {
      name: '<PERSON><PERSON><PERSON> bậc n',
      latex: '\\sqrt[n]{x}',
      description: '<PERSON><PERSON><PERSON> bậc n của x',
    },
    { name: '<PERSON><PERSON><PERSON> thừa', latex: 'x^{n}', description: 'x mũ n' },
    { name: 'Chỉ số dưới', latex: 'x_{i}', description: 'x chỉ số i' },
    {
      name: 'Chỉ số trên và dưới',
      latex: 'x_{i}^{j}',
      description: 'x chỉ số i mũ j',
    },
    {
      name: '<PERSON><PERSON> số lớn',
      latex: '\\dfrac{a}{b}',
      description: '<PERSON><PERSON> số lớn a/b',
    },
  ],
  algebra: [
    {
      name: 'Tổ<PERSON>',
      latex: '\\sum_{i=1}^{n} x_i',
      description: 'Tổng từ i=1 đến n của x_i',
    },
    {
      name: 'Tích',
      latex: '\\prod_{i=1}^{n} x_i',
      description: 'Tích từ i=1 đến n của x_i',
    },
    {
      name: 'Giới hạn',
      latex: '\\lim_{x \\to a} f(x)',
      description: 'Giới hạn của f(x) khi x tiến đến a',
    },
    {
      name: 'Đạo hàm',
      latex: '\\frac{d}{dx}f(x)',
      description: 'Đạo hàm của f(x) theo x',
    },
    {
      name: 'Đạo hàm riêng',
      latex: '\\frac{\\partial f}{\\partial x}',
      description: 'Đạo hàm riêng theo x',
    },
    {
      name: 'Tích phân',
      latex: '\\int_{a}^{b} f(x) dx',
      description: 'Tích phân từ a đến b của f(x)',
    },
    {
      name: 'Tích phân kép',
      latex: '\\iint_{D} f(x,y) dA',
      description: 'Tích phân kép trên miền D',
    },
    {
      name: 'Ma trận',
      latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}',
      description: 'Ma trận 2x2',
    },
  ],
  geometry: [
    { name: 'Góc', latex: '\\angle ABC', description: 'Góc ABC' },
    { name: 'Độ', latex: '90^\\circ', description: 'Góc 90 độ' },
    { name: 'Tam giác', latex: '\\triangle ABC', description: 'Tam giác ABC' },
    {
      name: 'Song song',
      latex: 'A \\parallel B',
      description: 'A song song với B',
    },
    {
      name: 'Vuông góc',
      latex: 'A \\perp B',
      description: 'A vuông góc với B',
    },
    {
      name: 'Đường thẳng',
      latex: '\\overleftrightarrow{AB}',
      description: 'Đường thẳng AB',
    },
    {
      name: 'Đoạn thẳng',
      latex: '\\overline{AB}',
      description: 'Đoạn thẳng AB',
    },
    {
      name: 'Diện tích',
      latex: 'S = \\pi r^2',
      description: 'Diện tích hình tròn',
    },
    {
      name: 'Thể tích',
      latex: 'V = \\frac{4}{3}\\pi r^3',
      description: 'Thể tích hình cầu',
    },
  ],
  symbols: [
    { name: 'Alpha', latex: '\\alpha', description: 'Chữ cái Hy Lạp alpha' },
    { name: 'Beta', latex: '\\beta', description: 'Chữ cái Hy Lạp beta' },
    { name: 'Gamma', latex: '\\gamma', description: 'Chữ cái Hy Lạp gamma' },
    { name: 'Delta', latex: '\\delta', description: 'Chữ cái Hy Lạp delta' },
    { name: 'Pi', latex: '\\pi', description: 'Số pi' },
    { name: 'Vô cùng', latex: '\\infty', description: 'Vô cùng' },
    { name: 'Thuộc', latex: 'a \\in A', description: 'a thuộc tập A' },
    { name: 'Hợp', latex: 'A \\cup B', description: 'Hợp của A và B' },
    { name: 'Giao', latex: 'A \\cap B', description: 'Giao của A và B' },
    {
      name: 'Tương đương',
      latex: 'A \\equiv B',
      description: 'A tương đương với B',
    },
    { name: 'Tập số thực', latex: '\\mathbb{R}', description: 'Tập số thực' },
    {
      name: 'Tập số nguyên',
      latex: '\\mathbb{Z}',
      description: 'Tập số nguyên',
    },
  ],
};

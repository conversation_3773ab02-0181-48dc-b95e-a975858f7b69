import React, { useState } from 'react';
import { useMap } from 'react-leaflet';
import { ControlPosition, MapPosition, searchLocations } from '../../MapUtils';

interface SearchControlProps {
  position?: ControlPosition;
  placeholder?: string;
  onLocationFound?: (position: MapPosition) => void;
  zoomLevel?: number;
}

/**
 * Control cho phép người dùng tìm kiếm vị trí trên bản đồ
 */
const SearchControl: React.FC<SearchControlProps> = ({
  position = 'topleft',
  placeholder = 'Tìm kiếm địa điểm...',
  onLocationFound,
  zoomLevel = 16,
}) => {
  const map = useMap();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searching, setSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const handleSearch = async () => {
    if (!searchTerm) return;

    setSearching(true);
    try {
      const results = await searchLocations(searchTerm);
      setSearchResults(results);
      setShowResults(true);
    } catch (error) {
      console.error('Error searching location:', error);
    } finally {
      setSearching(false);
    }
  };

  const handleResultClick = (result: any) => {
    const position = {
      lat: parseFloat(result.lat),
      lng: parseFloat(result.lon),
    };

    map.setView([position.lat, position.lng], zoomLevel);

    if (onLocationFound) {
      onLocationFound(position);
    }

    setShowResults(false);
    setSearchTerm(result.display_name);
  };

  // Tạo style cho container
  const containerStyle: React.CSSProperties = {
    position: 'absolute',
    zIndex: 1000,
    margin: '10px',
    backgroundColor: 'white',
    boxShadow: '0 1px 5px rgba(0,0,0,0.4)',
    borderRadius: '4px',
    padding: '8px',
  };

  // Xác định vị trí dựa trên position
  const positionStyle: React.CSSProperties = {
    ...(position.includes('top') ? { top: '10px' } : { bottom: '10px' }),
    ...(position.includes('left') ? { left: '10px' } : { right: '10px' }),
  };

  const mergedStyles = { ...containerStyle, ...positionStyle };

  return (
    <div style={mergedStyles}>
      <div className="tailwind-flex">
        <input
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          className="tailwind-px-2 tailwind-py-1 tailwind-border tailwind-border-gray-300 tailwind-rounded-l-md focus:tailwind-outline-none focus:tailwind-ring-2 focus:tailwind-ring-blue-500 tailwind-w-48"
        />
        <button
          onClick={handleSearch}
          className="tailwind-bg-blue-500 tailwind-text-white tailwind-px-3 tailwind-border-none tailwind-py-1 tailwind-rounded-r-md hover:tailwind-bg-blue-600 focus:tailwind-outline-none focus:tailwind-ring-2 focus:tailwind-ring-blue-700"
        >
          {searching ? '...' : 'Tìm'}
        </button>
      </div>

      {showResults && searchResults.length > 0 && (
        <div className="tailwind-mt-2 tailwind-bg-white tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-max-h-60 tailwind-overflow-y-auto">
          {searchResults.map((result, index) => (
            <div
              key={index}
              onClick={() => handleResultClick(result)}
              className="tailwind-p-2 hover:tailwind-bg-gray-100 tailwind-cursor-pointer tailwind-border-b tailwind-border-gray-100 tailwind-text-sm"
            >
              {result.display_name}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchControl;
